#!/usr/bin/env node

// Load environment variables
require('dotenv').config({ path: '.env.local' });

const puppeteer = require('puppeteer');
const fs = require('fs');
const path = require('path');

async function testFileUploadInBrowser() {
  console.log('🧪 Testing File Upload in Browser');
  console.log('================================\n');

  let browser;
  try {
    browser = await puppeteer.launch({ 
      headless: false, // Show browser for debugging
      devtools: true,
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });
    
    const page = await browser.newPage();
    
    // Listen for console logs and errors
    page.on('console', msg => {
      console.log('🔍 Browser Console:', msg.type(), msg.text());
    });
    
    page.on('pageerror', error => {
      console.error('❌ Page Error:', error.message);
    });
    
    // Navigate to the app
    console.log('📱 Navigating to application...');
    await page.goto('http://localhost:3000', { waitUntil: 'networkidle2' });
    
    // Create a test file
    const testPDFContent = `%PDF-1.4
1 0 obj<</Type/Catalog/Pages 2 0 R>>endobj
2 0 obj<</Type/Pages/Kids[3 0 R]/Count 1>>endobj  
3 0 obj<</Type/Page/Parent 2 0 R/MediaBox[0 0 612 792]/Contents 4 0 R>>endobj
4 0 obj<</Length 44>>stream
BT/F1 12 Tf 100 700 Td(Test Medical Report)Tj ET
endstream endobj
xref 0 5
********** 65535 f 
********** 00000 n 
********** 00000 n 
********** 00000 n 
********** 00000 n 
trailer<</Size 5/Root 1 0 R>>
startxref 276
%%EOF`;

    const testFilePath = '/tmp/test-medical-report.pdf';
    fs.writeFileSync(testFilePath, testPDFContent);
    console.log('📄 Created test file:', testFilePath);

    // Set up authentication tokens manually
    await page.evaluate(() => {
      const accessToken = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************************************************************************************************************************************************.j3YwR3gDXFIvaWGuPKF56NAJdcFarVH-t6m8NVEyxKk";
      const user = {
        id: "898a9e7b-6873-4c4c-b21c-f4786ee281ad",
        phone: "+919819304846",
        name: "Test User"
      };
      
      localStorage.setItem('access_token', accessToken);
      localStorage.setItem('user', JSON.stringify(user));
      
      return 'Tokens set';
    });

    // Refresh the page to apply authentication
    await page.reload({ waitUntil: 'networkidle2' });
    
    await page.waitForTimeout(3000); // Wait for WebSocket connection
    
    await page.screenshot({ path: 'test-file-upload-1-authenticated.png' });
    console.log('📸 Screenshot taken: authenticated state');
    
    // Look for file upload elements
    const fileInputs = await page.$$('input[type="file"]');
    console.log('📎 Found file inputs:', fileInputs.length);
    
    if (fileInputs.length === 0) {
      console.log('🔍 Looking for attachment buttons...');
      const attachButtons = await page.$$('[class*="attach"], [class*="file"], button[aria-label*="attach"]');
      console.log('📎 Found potential attachment buttons:', attachButtons.length);
      
      if (attachButtons.length > 0) {
        console.log('🖱️ Clicking first attachment button...');
        await attachButtons[0].click();
        await page.waitForTimeout(1000);
        
        // Look for file inputs again after clicking
        const newFileInputs = await page.$$('input[type="file"]');
        console.log('📎 File inputs after click:', newFileInputs.length);
      }
    }
    
    // Try to find file input in a different way
    const allInputs = await page.$$('input');
    console.log('🔍 Total inputs found:', allInputs.length);
    
    for (let i = 0; i < allInputs.length; i++) {
      const inputType = await page.evaluate(el => el.type, allInputs[i]);
      if (inputType === 'file') {
        console.log(`📎 Found file input at index ${i}`);
        
        try {
          // Upload the file
          await allInputs[i].uploadFile(testFilePath);
          console.log('✅ File uploaded successfully!');
          
          await page.waitForTimeout(2000); // Wait for processing
          await page.screenshot({ path: 'test-file-upload-2-file-uploaded.png' });
          console.log('📸 Screenshot taken: file uploaded');
          
          break;
        } catch (uploadError) {
          console.error('❌ Upload failed:', uploadError.message);
        }
      }
    }
    
    // Check for any upload progress or status
    const uploadStatus = await page.evaluate(() => {
      const statusElements = document.querySelectorAll('[class*="upload"], [class*="progress"], [class*="loading"]');
      return Array.from(statusElements).map(el => ({
        text: el.textContent,
        className: el.className
      }));
    });
    
    console.log('📊 Upload status elements:', uploadStatus);
    
    // Check console for any WebSocket messages
    await page.waitForTimeout(5000);
    
    await page.screenshot({ path: 'test-file-upload-3-final-state.png' });
    console.log('📸 Screenshot taken: final state');
    
    // Clean up
    if (fs.existsSync(testFilePath)) {
      fs.unlinkSync(testFilePath);
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error(error.stack);
  } finally {
    if (browser) {
      await browser.close();
    }
  }
}

if (require.main === module) {
  testFileUploadInBrowser();
}

module.exports = { testFileUploadInBrowser };