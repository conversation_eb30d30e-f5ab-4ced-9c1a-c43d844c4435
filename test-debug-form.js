const puppeteer = require('puppeteer');

async function debugFormSubmission() {
  console.log('=== Debug Form Submission Test ===\n');
  
  const browser = await puppeteer.launch({
    headless: false,
    devtools: true,
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });
  
  try {
    const page = await browser.newPage();
    
    // Log all console messages
    page.on('console', msg => {
      console.log(`[CONSOLE ${msg.type().toUpperCase()}]`, msg.text());
    });
    
    // Log all requests
    page.on('request', request => {
      if (!request.url().includes('.woff') && !request.url().includes('.css')) {
        console.log(`[REQUEST] ${request.method()} ${request.url()}`);
        if (request.postData()) {
          console.log(`[REQUEST BODY] ${request.postData()}`);
        }
      }
    });
    
    // Log all responses
    page.on('response', async response => {
      if (!response.url().includes('.woff') && !response.url().includes('.css')) {
        console.log(`[RESPONSE] ${response.status()} ${response.url()}`);
        if (response.url().includes('/api/')) {
          try {
            const text = await response.text();
            console.log(`[RESPONSE BODY] ${text}`);
          } catch (e) {}
        }
      }
    });
    
    // Navigate to page
    console.log('Navigating to http://localhost:3000...\n');
    await page.goto('http://localhost:3000', { waitUntil: 'networkidle0' });
    
    // Wait for form to load
    await page.waitForSelector('form');
    
    // Get form details
    const formInfo = await page.evaluate(() => {
      const form = document.querySelector('form');
      const submitButton = document.querySelector('button[type="submit"]');
      
      // Add event listeners in the browser
      if (form) {
        form.addEventListener('submit', (e) => {
          console.log('FORM SUBMIT EVENT TRIGGERED');
          console.log('Form action:', form.action);
          console.log('Form method:', form.method);
        });
      }
      
      if (submitButton) {
        submitButton.addEventListener('click', (e) => {
          console.log('SUBMIT BUTTON CLICKED');
          console.log('Button text:', e.target.textContent);
        });
      }
      
      return {
        hasForm: !!form,
        formAction: form?.action || 'none',
        formMethod: form?.method || 'none',
        hasSubmitButton: !!submitButton,
        submitButtonText: submitButton?.textContent || 'none'
      };
    });
    
    console.log('\nForm Info:', formInfo);
    
    // Fill phone number
    console.log('\nFilling phone number...');
    const phoneInput = await page.$('input[placeholder*="98193"]');
    await phoneInput.click({ clickCount: 3 });
    await phoneInput.type('+919819304846');
    
    // Get current form values
    const formValues = await page.evaluate(() => {
      const inputs = Array.from(document.querySelectorAll('input'));
      return inputs.map(input => ({
        type: input.type,
        name: input.name,
        value: input.value,
        placeholder: input.placeholder
      }));
    });
    console.log('\nForm values:', formValues);
    
    // Try to click the submit button
    console.log('\nClicking submit button...');
    const submitClicked = await page.evaluate(() => {
      const submitBtn = document.querySelector('button[type="submit"]');
      if (submitBtn) {
        console.log('About to click submit button');
        submitBtn.click();
        return true;
      }
      return false;
    });
    
    console.log('Submit clicked:', submitClicked);
    
    // Wait for any activity
    await new Promise(resolve => setTimeout(resolve, 5000));
    
    // Check final state
    const finalState = await page.evaluate(() => {
      return {
        url: window.location.href,
        hasOtpInput: !!document.querySelector('input[maxlength="6"]'),
        errors: Array.from(document.querySelectorAll('.error, [role="alert"]')).map(el => el.textContent),
        formStillVisible: !!document.querySelector('form')
      };
    });
    
    console.log('\nFinal state:', finalState);
    
    // Try direct API call from browser
    console.log('\n\nTrying direct API call from browser console...');
    const apiResult = await page.evaluate(async () => {
      try {
        const response = await fetch('http://localhost:3000/api/auth/request-otp', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ phoneNumber: '+919819304846' })
        });
        const data = await response.text();
        return {
          status: response.status,
          statusText: response.statusText,
          data: data
        };
      } catch (error) {
        return { error: error.message };
      }
    });
    
    console.log('Direct API call result:', apiResult);
    
    // Keep browser open
    console.log('\nKeeping browser open for manual inspection...');
    await new Promise(resolve => setTimeout(resolve, 30000));
    
  } catch (error) {
    console.error('Test error:', error);
  } finally {
    await browser.close();
  }
}

debugFormSubmission();