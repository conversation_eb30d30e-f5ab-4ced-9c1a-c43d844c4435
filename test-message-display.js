#!/usr/bin/env node

const puppeteer = require('puppeteer');

async function testMessageDisplay() {
  console.log('🧪 Testing Message Display Issue');
  console.log('================================\n');

  let browser;
  try {
    browser = await puppeteer.launch({ 
      headless: false,
      devtools: true,
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });
    
    const page = await browser.newPage();
    
    // Capture ALL console logs
    const logs = [];
    page.on('console', msg => {
      const text = msg.text();
      logs.push({ type: msg.type(), text });
      console.log(`[${msg.type().toUpperCase()}] ${text}`);
    });
    
    page.on('pageerror', error => {
      console.error('❌ Page Error:', error.message);
    });

    // Navigate to app
    console.log('1️⃣ Navigating to app...');
    await page.goto('http://localhost:3000', { waitUntil: 'networkidle2' });
    
    // Set authentication directly
    console.log('2️⃣ Setting authentication...');
    await page.evaluate(() => {
      localStorage.setItem('authToken', 'test-token');
      localStorage.setItem('access_token', 'test-token');
      localStorage.setItem('user', JSON.stringify({ 
        id: '898a9e7b-6873-4c4c-b21c-f4786ee281ad', 
        userId: '898a9e7b-6873-4c4c-b21c-f4786ee281ad',
        name: 'Test User',
        phone: '+919819304846'
      }));
    });
    
    // Reload to apply auth
    await page.reload({ waitUntil: 'networkidle2' });
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Check current page state
    console.log('\n3️⃣ Checking page state...');
    const pageInfo = await page.evaluate(() => {
      return {
        url: window.location.href,
        hasTextarea: !!document.querySelector('textarea'),
        hasChatInterface: !!document.querySelector('[class*="ChatInterface"]'),
        hasThreadList: !!document.querySelector('[class*="ThreadList"]'),
        visibleText: Array.from(document.querySelectorAll('h1, h2, h3, h4, h5, h6, p'))
          .map(el => el.textContent?.trim())
          .filter(text => text && text.length > 0)
          .slice(0, 5)
      };
    });
    
    console.log('Page info:', JSON.stringify(pageInfo, null, 2));
    
    // Try to find and type in textarea
    console.log('\n4️⃣ Looking for message input...');
    const textarea = await page.$('textarea');
    
    if (textarea) {
      console.log('✅ Found textarea');
      
      // Type a message
      const testMessage = `Test ${Date.now()}`;
      await textarea.click();
      await textarea.type(testMessage);
      console.log('📝 Typed:', testMessage);
      
      // Check state before sending
      console.log('\n5️⃣ Checking state before sending...');
      const beforeSend = await page.evaluate(() => {
        const messages = Array.from(document.querySelectorAll('[class*="MuiBox"]'))
          .filter(el => {
            const text = el.textContent || '';
            return text.length > 10 && text.length < 200 && !text.includes('Doctor Dashboard');
          })
          .map(el => el.textContent);
        
        return {
          messageCount: messages.length,
          messages: messages.slice(-5)
        };
      });
      console.log('Before send:', beforeSend);
      
      // Send message
      console.log('\n6️⃣ Sending message...');
      await page.keyboard.press('Enter');
      
      // Wait a bit
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Check state after sending
      console.log('\n7️⃣ Checking state after sending...');
      const afterSend = await page.evaluate((msg) => {
        const messages = Array.from(document.querySelectorAll('[class*="MuiBox"]'))
          .filter(el => {
            const text = el.textContent || '';
            return text.length > 5 && text.length < 200;
          })
          .map(el => el.textContent);
        
        const foundMessage = messages.find(m => m.includes(msg));
        
        return {
          messageCount: messages.length,
          messages: messages.slice(-10),
          foundOurMessage: !!foundMessage,
          foundMessageText: foundMessage
        };
      }, testMessage);
      
      console.log('After send:', JSON.stringify(afterSend, null, 2));
      
      // Take screenshot
      await page.screenshot({ path: 'message-display-test.png' });
      console.log('\n📸 Screenshot saved: message-display-test.png');
      
      // Check for specific elements
      const elements = await page.evaluate(() => {
        return {
          hasAIThinking: !!Array.from(document.querySelectorAll('*')).find(el => 
            el.textContent && el.textContent.includes('AI is thinking')),
          hasTypography: document.querySelectorAll('[class*="MuiTypography"]').length,
          hasPaper: document.querySelectorAll('[class*="MuiPaper"]').length,
          hasBox: document.querySelectorAll('[class*="MuiBox"]').length
        };
      });
      console.log('\n📊 Element counts:', elements);
      
    } else {
      console.log('❌ No textarea found');
      
      // Check what's on the page
      const pageContent = await page.evaluate(() => {
        return {
          title: document.title,
          bodyText: document.body.innerText.substring(0, 500),
          forms: document.querySelectorAll('form').length,
          inputs: document.querySelectorAll('input').length,
          buttons: Array.from(document.querySelectorAll('button')).map(b => b.textContent)
        };
      });
      console.log('Page content:', JSON.stringify(pageContent, null, 2));
    }
    
    // Print relevant console logs
    console.log('\n📋 Relevant console logs:');
    logs.filter(log => 
      log.text.includes('message') || 
      log.text.includes('WebSocket') || 
      log.text.includes('error') ||
      log.text.includes('optimistic')
    ).forEach(log => {
      console.log(`  [${log.type}] ${log.text}`);
    });
    
  } catch (error) {
    console.error('\n❌ Test failed:', error.message);
    console.error(error.stack);
  } finally {
    if (browser) {
      console.log('\n🔄 Keeping browser open. Press Ctrl+C to close.');
      await new Promise(() => {});
    }
  }
}

testMessageDisplay();