import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  reactStrictMode: false, 
  // External packages for server components
  serverExternalPackages: ['@azure/storage-blob', 'redis', 'socket.io'],
  
  // Configure allowed dev origins for cross-origin requests
  allowedDevOrigins: [
    'ec2-3-111-56-167.ap-south-1.compute.amazonaws.com',
    'localhost:3000',
    'localhost:3001',
    'http://ec2-3-111-56-167.ap-south-1.compute.amazonaws.com:3000', // Added for WebSocket connection error fix
  ],
  // Add experimental features for better stability
  experimental: {
    // Improve WebSocket stability
    serverComponentsExternalPackages: ['@azure/storage-blob', 'redis', 'socket.io'],
  },
};

export default nextConfig;
