#!/bin/bash

echo "🚀 Starting Doctor Dashboard with Redis Connection Fix"
echo "=================================================="

# Check if Redis credentials are configured
if [ -z "$REDIS_URL" ] || [ -z "$REDIS_PASSWORD" ]; then
    echo "⚠️  Redis not configured, checking .env file..."
    source .env
    if [ -z "$REDIS_URL" ] || [ -z "$REDIS_PASSWORD" ]; then
        echo "❌ Redis credentials not found in .env file"
        echo "🔄 Starting in development mode without Redis..."
        npm run dev
        exit 0
    fi
fi

echo "✅ Redis credentials found"
echo "🔍 Testing Redis connection..."

# Test Redis connection first
node test-redis-fix.js &
REDIS_TEST_PID=$!

# Wait for Redis test to complete or timeout
sleep 5

# Kill the Redis test process
kill $REDIS_TEST_PID 2>/dev/null

echo "🔄 Starting Redis connection manager..."
node redis-connection-manager.js &
REDIS_MANAGER_PID=$!

# Wait a moment for Redis to stabilize
sleep 3

echo "🚀 Starting application server..."
npm run dev &
APP_PID=$!

# Function to cleanup processes
cleanup() {
    echo "🛑 Shutting down services..."
    kill $APP_PID 2>/dev/null
    kill $REDIS_MANAGER_PID 2>/dev/null
    echo "✅ Services shut down"
    exit 0
}

# Trap signals for graceful shutdown
trap cleanup SIGINT SIGTERM

echo "✅ All services started successfully"
echo "📊 Application: http://localhost:3000"
echo "🔍 Health Check: http://localhost:3000/health"
echo "🔍 Redis Health: http://localhost:3000/health/redis"
echo "Press Ctrl+C to stop all services"

# Wait for either process to exit
wait $APP_PID
