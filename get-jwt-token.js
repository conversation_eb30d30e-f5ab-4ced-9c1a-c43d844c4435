#!/usr/bin/env node

/**
 * Get JWT Token for Test User
 * Gets a valid JWT token for the test phone number
 */

const axios = require('axios');

const GATEKEEPER_BASE_URL = 'https://gatekeeper-staging.getbeyondhealth.com';
const TENANT = 'practitioner-dashboard';
const TEST_PHONE = '+919819304846';
const TEST_OTP = '123456';

console.log('🔐 GETTING JWT TOKEN FOR TEST USER');
console.log('===================================');
console.log(`Phone: ${TEST_PHONE}`);
console.log(`OTP: ${TEST_OTP}`);
console.log(`Gatekeeper URL: ${GATEKEEPER_BASE_URL}`);
console.log(`Tenant: ${TENANT}`);
console.log('===================================\n');

async function getJWTToken() {
  try {
    // Step 1: Request OTP
    console.log('📱 STEP 1: Request OTP');
    console.log('----------------------');
    
    const otpRequestPayload = {
      phoneNumber: TEST_PHONE
    };
    
    console.log('Request URL:', `${GATEKEEPER_BASE_URL}/auth/${TENANT}/request-otp`);
    console.log('Request Method: POST');
    console.log('Request Headers: Content-Type: application/json');
    console.log('Request Body:', JSON.stringify(otpRequestPayload, null, 2));
    
    const otpResponse = await axios.post(`${GATEKEEPER_BASE_URL}/auth/${TENANT}/request-otp`, otpRequestPayload, {
      headers: {
        'Content-Type': 'application/json',
      },
    });

    console.log('\nResponse Status:', otpResponse.status);
    console.log('Response Headers:', JSON.stringify(otpResponse.headers, null, 2));
    console.log('Response Body:', JSON.stringify(otpResponse.data, null, 2));
    console.log('✅ OTP request successful\n');

    // Step 2: Verify OTP and get JWT
    console.log('🔐 STEP 2: Verify OTP and Get JWT');
    console.log('----------------------------------');
    
    const verifyPayload = {
      phone: TEST_PHONE,
      otp: TEST_OTP,
      source: 'web'
    };
    
    console.log('Request URL:', `${GATEKEEPER_BASE_URL}/auth/${TENANT}/verify-otp`);
    console.log('Request Method: POST');
    console.log('Request Headers: Content-Type: application/json');
    console.log('Request Body:', JSON.stringify(verifyPayload, null, 2));

    const verifyResponse = await axios.post(`${GATEKEEPER_BASE_URL}/auth/${TENANT}/verify-otp`, verifyPayload, {
      headers: {
        'Content-Type': 'application/json',
      },
    });

    console.log('\nResponse Status:', verifyResponse.status);
    console.log('Response Headers:', JSON.stringify(verifyResponse.headers, null, 2));
    console.log('Response Body:', JSON.stringify(verifyResponse.data, null, 2));

    if (verifyResponse.data.accessToken) {
      console.log('\n🎉 JWT TOKEN OBTAINED SUCCESSFULLY!');
      console.log('====================================');
      console.log('Access Token:', verifyResponse.data.accessToken);
      console.log('Refresh Token:', verifyResponse.data.refreshToken ? '[RECEIVED]' : 'NOT RECEIVED');
      console.log('User Access:', verifyResponse.data.access);
      console.log('User Info:', JSON.stringify(verifyResponse.data.user, null, 2));
      
      return {
        accessToken: verifyResponse.data.accessToken,
        refreshToken: verifyResponse.data.refreshToken,
        user: verifyResponse.data.user,
        access: verifyResponse.data.access
      };
    } else {
      console.log('❌ No access token received');
      return null;
    }

  } catch (error) {
    console.error('❌ Error getting JWT token:');
    console.error('Error Message:', error.message);
    
    if (error.response) {
      console.error('Error Status:', error.response.status);
      console.error('Error Headers:', JSON.stringify(error.response.headers, null, 2));
      console.error('Error Body:', JSON.stringify(error.response.data, null, 2));
    }
    
    if (error.response?.status === 429) {
      console.log('\n💡 Rate limited - this is expected after multiple tests');
      console.log('The token should still work if you received it in previous attempts');
    }
    
    return null;
  }
}

// Run the function
getJWTToken()
  .then(result => {
    if (result) {
      console.log('\n✅ Ready to send messages with this JWT token!');
    } else {
      console.log('\n❌ Failed to get JWT token');
    }
  })
  .catch(error => {
    console.error('💥 Script failed:', error);
  });