# AWS Deployment WebSocket Authentication Debug Guide

## Issue: Working locally but failing on AWS instance

### Common AWS vs Local Differences

#### 1. Environment Variables Loading
```bash
# Check if .env.local is loaded on AWS
ls -la .env*

# Verify environment variables are set
echo "GATEKEEPER_URL: $GATEKEEPER_URL"
echo "JWT_ACCESS_SECRET: $JWT_ACCESS_SECRET" 
echo "REDIS_URL: $REDIS_URL"
echo "NODE_ENV: $NODE_ENV"
```

#### 2. Port Configuration
```bash
# Check what port the server is actually running on
netstat -tlnp | grep :3000
ps aux | grep custom-server
```

#### 3. File Permissions
```bash
# Check file permissions
ls -la custom-server.js
ls -la package.json
ls -la .env*
```

#### 4. Process Management
```bash
# Check if process is running correctly
pm2 status  # if using PM2
systemctl status doctor-dashboard  # if using systemd

# Check server logs
journalctl -u doctor-dashboard -f
# OR
pm2 logs
```

### WebSocket Specific AWS Issues

#### 1. Load Balancer Configuration
If using ALB/ELB, ensure WebSocket support:
- Sticky sessions enabled
- WebSocket upgrade headers allowed
- Target group health checks configured

#### 2. Security Groups
Ensure port 3000 is open:
```bash
# Check current security group rules
aws ec2 describe-security-groups --group-ids sg-xxxxx
```

#### 3. CORS Configuration
Check if CORS is properly configured for AWS domain:
```javascript
// In custom-server.js, verify CORS allows your AWS domain
origin: (origin, callback) => {
  const allowedOrigins = [
    'http://localhost:3000',
    'https://your-aws-domain.com',  // Add your AWS domain
    'http://your-aws-ip:3000'       // Add your AWS IP
  ];
  // ...
}
```

### Debug Commands for AWS

#### 1. Check WebSocket Connection
```bash
# Test WebSocket connection from AWS instance
curl -i -N -H "Connection: Upgrade" \
     -H "Upgrade: websocket" \
     -H "Host: localhost:3000" \
     -H "Origin: http://localhost:3000" \
     http://localhost:3000/socket.io/
```

#### 2. Check Environment Loading
```bash
# Run this on AWS to verify environment
node -e "
require('dotenv').config({ path: '.env.local' });
console.log('GATEKEEPER_URL:', process.env.GATEKEEPER_URL);
console.log('JWT_ACCESS_SECRET:', process.env.JWT_ACCESS_SECRET ? 'SET' : 'NOT SET');
console.log('REDIS_URL:', process.env.REDIS_URL ? 'SET' : 'NOT SET');
"
```

#### 3. Check Redis Connection
```bash
# Test Redis connection manually
node -e "
const { createClient } = require('redis');
const client = createClient({
  url: \`rediss://\${process.env.REDIS_URL}:6380\`,
  password: process.env.REDIS_PASSWORD,
  socket: { tls: true, rejectUnauthorized: false }
});
client.connect().then(() => console.log('Redis OK')).catch(console.error);
"
```

### Quick AWS Fixes

#### 1. Restart with explicit environment loading
```bash
# Stop current process
pkill -f custom-server

# Start with explicit environment
NODE_ENV=production node custom-server.js
```

#### 2. Check if using correct server
```bash
# Verify which server is running
ps aux | grep node
# Should show custom-server.js, not server.js
```

#### 3. Verify build assets
```bash
# Ensure Next.js is built for production
npm run build
npm start
```

### Environment Variables Checklist for AWS

Required variables that often fail on AWS:
- [ ] `GATEKEEPER_URL`
- [ ] `JWT_ACCESS_SECRET` 
- [ ] `JWT_REFRESH_SECRET`
- [ ] `REDIS_URL`
- [ ] `REDIS_PASSWORD`
- [ ] `BEARER_TOKEN`
- [ ] `AZURE_STORAGE_ACCOUNT_NAME`
- [ ] `AZURE_STORAGE_ACCOUNT_KEY`

### AWS-Specific Next.js Configuration

Check if `next.config.ts` needs AWS-specific settings:
```javascript
// May need to add for AWS deployment
module.exports = {
  output: 'standalone', // For Docker/container deployment
  generateBuildId: async () => {
    return 'build-' + Date.now()
  }
}
```

### Common AWS Deployment Patterns

1. **Using PM2 (recommended)**:
```bash
pm2 start custom-server.js --name doctor-dashboard
pm2 save
pm2 startup
```

2. **Using systemd**:
Create `/etc/systemd/system/doctor-dashboard.service`

3. **Using Docker**:
Ensure WebSocket ports are properly exposed

### Debugging Steps

1. SSH into AWS instance
2. Navigate to project directory
3. Run environment variable check
4. Restart server with logging
5. Check browser network tab for WebSocket connection errors
6. Compare local vs AWS console logs

The issue is likely one of:
- Environment variables not loading
- Wrong server file running  
- Port/networking configuration
- File permissions or paths

### Most Common AWS Issues:

1. **Environment file missing on AWS**
2. **PM2 or process manager using wrong server file** 
3. **Security groups blocking WebSocket connections**
4. **CORS not allowing AWS domain/IP**
5. **Redis connection failing due to network configuration**
