const { TestSuite, CONFIG } = require('../e2e-test-suite');

class PatientCaseTestSuite extends TestSuite {
  constructor() {
    super('patient-cases');
  }

  async execute() {
    // Ensure we're authenticated first
    await this.runTest('Authenticate', async () => {
      const authenticated = await this.utils.ensureAuthenticated();
      if (!authenticated) {
        throw new Error('Failed to authenticate');
      }
    });
    
    // Test 1: Handle new conversation modal if present
    await this.runTest('Handle new conversation modal', async () => {
      // Check if there's a modal open
      const modalPresent = await this.page.evaluate(() => {
        const modal = document.querySelector('[role="dialog"], .modal, [class*="modal"]');
        return modal && modal.offsetParent !== null;
      });
      
      if (modalPresent) {
        this.logger.info('Modal detected, closing it');
        // Click Cancel or press Escape
        const cancelClicked = await this.page.evaluate(() => {
          const buttons = Array.from(document.querySelectorAll('button'));
          const cancelBtn = buttons.find(btn => 
            btn.textContent?.toLowerCase().includes('cancel')
          );
          if (cancelBtn) {
            cancelBtn.click();
            return true;
          }
          return false;
        });
        
        if (!cancelClicked) {
          await this.page.keyboard.press('Escape');
        }
        
        await this.utils.waitForTimeout(1000);
        this.logger.info('Modal closed');
      } else {
        this.logger.info('No modal to close');
      }
    });

    // Test 2: Send patient case message (this creates a Patient Case type thread)
    await this.runTest('Send patient case message', async () => {
      // Ensure we're on the chat interface
      await this.utils.waitForTimeout(1000);
      
      // Find message input area
      let messageInput = await this.page.$('textarea[placeholder*="message" i], textarea[placeholder*="ask" i], textarea[placeholder*="type" i], textarea');
      
      if (!messageInput) {
        // Wait a bit more and try again
        await this.utils.waitForTimeout(2000);
        messageInput = await this.page.$('textarea');
        if (!messageInput) {
          throw new Error('Message input not found');
        }
      }
      
      // Type a patient case  
      const patientCase = `Patient Case: 45-year-old male presenting with Type 2 Diabetes. 
HbA1c is 9.5. Research everything about hba1c levels and treatment options.`;
      
      await messageInput.click();
      await messageInput.type(patientCase);
      await this.utils.screenshot('patient-case-typed');
      
      // Send the message
      const sendButton = await this.page.$('button[type="submit"], button[aria-label*="send" i]');
      if (sendButton) {
        await sendButton.click();
      } else {
        // Try pressing Enter
        await this.page.keyboard.press('Enter');
      }
      
      this.logger.info('Patient case message sent');
    });

    // Test 3: Wait for AI response
    await this.runTest('Check AI response', async () => {
      // Wait for thinking indicator to appear and disappear
      try {
        await this.page.waitForSelector('[class*="thinking"], [class*="typing"], [class*="loading"], [class*="pending"]', { 
          timeout: 5000 
        });
        this.logger.debug('AI is processing...');
        
        // Wait for it to disappear
        await this.page.waitForFunction(() => {
          const indicators = document.querySelectorAll('[class*="thinking"], [class*="typing"], [class*="loading"], [class*="pending"]');
          return indicators.length === 0;
        }, { timeout: 45000 });
      } catch (e) {
        this.logger.debug('No visible thinking indicator');
      }
      
      // Give it more time for the response to fully render
      await this.utils.waitForTimeout(5000);
      await this.utils.screenshot('ai-response-received');
      
      // Check if response contains medical information
      const hasResponse = await this.page.evaluate(() => {
        // Get all text content on the page
        const pageText = document.body.textContent?.toLowerCase() || '';
        
        // Look for AI response indicators
        const hasAIResponse = pageText.includes('based on the clinical presentation') ||
                             pageText.includes('recommend') ||
                             pageText.includes('diagnostic') ||
                             pageText.includes('treatment') ||
                             pageText.includes('approach');
        
        // Also check for medical terms related to the query
        const hasMedicalContent = pageText.includes('diabetes') || 
                                 pageText.includes('hba1c') || 
                                 pageText.includes('treatment') ||
                                 pageText.includes('medication') ||
                                 pageText.includes('metformin') ||
                                 pageText.includes('guidance');
        
        return hasAIResponse || hasMedicalContent;
      });
      
      if (!hasResponse) {
        // Log what text we found for debugging
        const pageText = await this.page.evaluate(() => document.body.textContent?.substring(0, 500));
        this.logger.warn('Page text sample:', pageText);
        throw new Error('AI response does not contain expected medical information');
      }
      
      this.logger.success('AI provided medical response');
    });

    // Test 4: Check message history
    await this.runTest('Check message history', async () => {
      // Count messages - look for message containers
      const messageInfo = await this.page.evaluate(() => {
        // Try different selectors for messages
        const selectors = [
          '[role="article"]',
          '[class*="message"]',
          '[class*="Message"]',
          '[class*="chat-message"]',
          'div[class*="bubble"]'
        ];
        
        let messages = [];
        for (const selector of selectors) {
          const elements = document.querySelectorAll(selector);
          if (elements.length > 0) {
            messages = Array.from(elements);
            break;
          }
        }
        
        // If still no messages found, look for text content
        if (messages.length === 0) {
          const pageText = document.body.textContent || '';
          return {
            count: 0,
            hasUserMessage: pageText.includes('45-year-old male'),
            hasAIResponse: pageText.includes('treatment') || pageText.includes('diabetes'),
            pageText: pageText.substring(0, 200)
          };
        }
        
        return {
          count: messages.length,
          hasUserMessage: messages.some(m => m.textContent?.includes('45-year-old male')),
          hasAIResponse: messages.some(m => 
            m.textContent?.toLowerCase().includes('treatment') || 
            m.textContent?.toLowerCase().includes('diabetes')
          )
        };
      });
      
      this.logger.info(`Message info:`, messageInfo);
      
      if (messageInfo.count >= 2) {
        this.logger.success(`Found ${messageInfo.count} messages in conversation`);
      } else if (messageInfo.hasUserMessage && messageInfo.hasAIResponse) {
        this.logger.success('Both user message and AI response are visible');
      } else {
        throw new Error('Expected both user message and AI response to be visible');
      }
    });

    // Test 5: Check thread appears in sidebar with correct type
    await this.runTest('Check thread in sidebar', async () => {
      // Check if the conversation appears in sidebar
      const threadInfo = await this.page.evaluate(() => {
        const sidebar = document.querySelector('aside, [class*="sidebar"], nav, [class*="thread-list"]');
        if (!sidebar) return { found: false, reason: 'No sidebar found' };
        
        const sidebarText = sidebar.textContent || '';
        
        // Look for thread indicators
        const hasThread = sidebarText.includes('45-year-old male') || 
                         sidebarText.includes('Diabetes') ||
                         sidebarText.includes('Type 2') ||
                         sidebarText.includes('Patient Case');
        
        // Check if it shows as Patient Case type
        const hasType = sidebarText.includes('Patient Case') || 
                       sidebarText.includes('Patient Cases');
        
        return {
          found: hasThread,
          hasType: hasType,
          sidebarContent: sidebarText.substring(0, 200)
        };
      });
      
      this.logger.info('Thread info:', threadInfo);
      
      if (threadInfo.found) {
        this.logger.success('Thread appears in sidebar');
        if (threadInfo.hasType) {
          this.logger.success('Thread is marked as Patient Case type');
        }
        await this.utils.screenshot('thread-in-sidebar');
      } else {
        this.logger.warn('Thread not found in sidebar - may be collapsed or dynamically loaded');
      }
    });

    // Test 6: Send follow-up message
    await this.runTest('Send follow-up message', async () => {
      const messageInput = await this.page.$('textarea');
      if (!messageInput) {
        throw new Error('Message input not found for follow-up');
      }
      
      const followUp = 'What about lifestyle modifications for this patient?';
      await messageInput.click();
      await messageInput.type(followUp);
      
      // Send
      await this.page.keyboard.press('Enter');
      
      // Wait for response
      await this.utils.waitForTimeout(3000);
      await this.utils.screenshot('follow-up-conversation');
      
      this.logger.success('Follow-up message sent and responded');
    });

    // Test 7: Test file attachment (if available)
    await this.runTest('Test file attachment', async () => {
      // Look for attachment button
      const attachButton = await this.page.$('button[aria-label*="attach" i], button[title*="attach" i], label[for*="file" i], button[class*="attach"]');
      
      if (attachButton) {
        this.logger.info('File attachment feature available');
        await this.utils.screenshot('file-attachment-available');
        
        // Click to see if file dialog opens
        await attachButton.click();
        await this.utils.waitForTimeout(1000);
        
        // Check for file input
        const fileInput = await this.page.$('input[type="file"]');
        if (fileInput) {
          this.logger.success('File input found');
        }
        
        // Close any dialog that might have opened
        await this.page.keyboard.press('Escape');
      } else {
        this.logger.warn('File attachment feature not found - may not be implemented');
      }
    });
  }
}

module.exports = PatientCaseTestSuite;