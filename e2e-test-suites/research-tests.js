const { TestSuite, CONFIG } = require('../e2e-test-suite');

class ResearchTestSuite extends TestSuite {
  constructor() {
    super('research');
  }

  async execute() {
    // Ensure we're authenticated first
    await this.runTest('Authenticate', async () => {
      const authenticated = await this.utils.ensureAuthenticated();
      if (!authenticated) {
        throw new Error('Failed to authenticate');
      }
    });
    
    // Test 1: Handle any modal if present
    await this.runTest('Handle modal if present', async () => {
      // Check if there's a modal open
      const modalPresent = await this.page.evaluate(() => {
        const modal = document.querySelector('[role="dialog"], .modal, [class*="modal"]');
        return modal && modal.offsetParent !== null;
      });
      
      if (modalPresent) {
        this.logger.info('Modal detected, closing it');
        // Click Cancel or press Escape
        const cancelClicked = await this.page.evaluate(() => {
          const buttons = Array.from(document.querySelectorAll('button'));
          const cancelBtn = buttons.find(btn => 
            btn.textContent?.toLowerCase().includes('cancel')
          );
          if (cancelBtn) {
            cancelBtn.click();
            return true;
          }
          return false;
        });
        
        if (!cancelClicked) {
          await this.page.keyboard.press('Escape');
        }
        
        await this.utils.waitForTimeout(1000);
        this.logger.info('Modal closed');
      } else {
        this.logger.info('No modal to close');
      }
    });

    // Test 2: Ask research question (this creates a Research type thread)
    await this.runTest('Ask research question', async () => {
      const messageInput = await this.page.$('textarea');
      if (!messageInput) {
        throw new Error('Message input not found');
      }
      
      // Use a simpler research-oriented query
      const researchQuery = `Research: Latest treatments for Type 2 Diabetes`;
      
      await messageInput.click();
      await messageInput.type(researchQuery);
      await this.utils.screenshot('research-question-typed');
      
      // Send message
      await this.page.keyboard.press('Enter');
      
      this.logger.info('Research query sent');
    });

    // Test 3: Wait for AI response with citations
    await this.runTest('Check research response', async () => {
      // Wait for thinking indicator
      try {
        await this.page.waitForSelector('[class*="thinking"], [class*="typing"], [class*="loading"]', { 
          timeout: 5000 
        });
        
        // Wait for it to disappear
        await this.page.waitForFunction(() => {
          const indicators = document.querySelectorAll('[class*="thinking"], [class*="typing"], [class*="loading"]');
          return indicators.length === 0;
        }, { timeout: 30000 });
      } catch (e) {
        this.logger.debug('No visible thinking indicator');
      }
      
      // Give time for response to render
      await this.utils.waitForTimeout(3000);
      await this.utils.screenshot('research-response-received');
      
      // Check if response contains research-related content
      const hasResearch = await this.page.evaluate(() => {
        // Get all text content on the page
        const pageText = document.body.textContent?.toLowerCase() || '';
        
        // Look for research/medical content
        return pageText.includes('diabetes') || 
               pageText.includes('treatment') || 
               pageText.includes('research') ||
               pageText.includes('medication') ||
               pageText.includes('clinical') ||
               pageText.includes('study') ||
               pageText.includes('approach');
      });
      
      if (!hasResearch) {
        const pageText = await this.page.evaluate(() => document.body.textContent?.substring(0, 500));
        this.logger.warn('Page text sample:', pageText);
        throw new Error('AI response does not contain expected research information');
      }
      
      this.logger.success('AI provided research response');
    });

    // Test 4: Check for citations/references
    await this.runTest('Check for citations', async () => {
      // Look for citation markers or reference sections
      const citationInfo = await this.page.evaluate(() => {
        const pageText = document.body.textContent || '';
        const messages = Array.from(document.querySelectorAll('[class*="message"], [role="article"], div[class*="Message"]'));
        
        // Check for various citation formats
        const hasNumberedCitations = /\[\d+\]/.test(pageText);
        const hasReferences = pageText.toLowerCase().includes('reference');
        const hasSource = pageText.toLowerCase().includes('source');
        const hasDoi = pageText.includes('doi:') || pageText.includes('DOI:');
        const hasPubMed = pageText.toLowerCase().includes('pubmed');
        const hasJournal = pageText.toLowerCase().includes('journal');
        const hasYear = /\b(19|20)\d{2}\b/.test(pageText); // Years from 1900-2099
        
        return {
          hasNumberedCitations,
          hasReferences,
          hasSource,
          hasDoi,
          hasPubMed,
          hasJournal,
          hasYear,
          citationCount: (pageText.match(/\[\d+\]/g) || []).length
        };
      });
      
      this.logger.info('Citation analysis:', citationInfo);
      
      if (citationInfo.hasNumberedCitations || citationInfo.hasReferences || 
          citationInfo.hasDoi || citationInfo.hasJournal) {
        this.logger.success('Research response includes citations/references');
      } else {
        this.logger.warn('No obvious citations found - AI may have provided general information');
      }
    });

    // Test 5: Ask follow-up research question
    await this.runTest('Ask follow-up research question', async () => {
      const messageInput = await this.page.$('textarea');
      if (!messageInput) {
        throw new Error('Message input not found for follow-up');
      }
      
      const followUp = 'Can you provide specific data on storage duration at different temperatures?';
      await messageInput.click();
      await messageInput.type(followUp);
      
      await this.page.keyboard.press('Enter');
      
      // Wait for response
      await this.utils.waitForTimeout(3000);
      await this.utils.screenshot('research-follow-up-response');
      
      this.logger.success('Follow-up research question answered');
    });

    // Test 6: Check for data tables/visualizations
    await this.runTest('Check for data presentation', async () => {
      // Check for tables or structured data
      const dataElements = await this.page.evaluate(() => {
        const tables = document.querySelectorAll('table');
        const codeBlocks = document.querySelectorAll('pre, code');
        const lists = document.querySelectorAll('ul, ol');
        
        // Check for data patterns in text
        const pageText = document.body.textContent || '';
        const hasNumbers = /\d+\s*(°C|degrees|celsius|fahrenheit|days|hours|months)/i.test(pageText);
        const hasPercentages = /\d+\s*%/.test(pageText);
        
        return {
          tableCount: tables.length,
          codeBlockCount: codeBlocks.length,
          listCount: lists.length,
          hasNumbers,
          hasPercentages
        };
      });
      
      this.logger.info('Data presentation elements:', dataElements);
      
      if (dataElements.tableCount > 0 || dataElements.hasNumbers) {
        this.logger.success('Research includes data presentation');
        await this.utils.screenshot('research-data-visualization');
      }
    });

    // Test 7: Check thread type in sidebar
    await this.runTest('Check research thread in sidebar', async () => {
      const threadInfo = await this.page.evaluate(() => {
        const sidebar = document.querySelector('aside, [class*="sidebar"], nav, [class*="thread-list"]');
        if (!sidebar) return { found: false };
        
        const sidebarText = sidebar.textContent || '';
        
        // Look for research indicators
        const hasThread = sidebarText.includes('diabetes') || 
                         sidebarText.includes('treatment') ||
                         sidebarText.includes('research') ||
                         sidebarText.includes('Research');
        
        return {
          found: hasThread,
          hasResearchType: sidebarText.includes('Research'),
          preview: sidebarText.substring(0, 200)
        };
      });
      
      if (threadInfo.found) {
        this.logger.success('Research thread appears in sidebar');
        if (threadInfo.hasResearchType) {
          this.logger.success('Thread is marked as Research type');
        }
      } else {
        this.logger.warn('Thread not visible in sidebar');
      }
    });
  }
}

module.exports = ResearchTestSuite;