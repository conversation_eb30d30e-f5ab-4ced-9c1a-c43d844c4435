const { TestSuite, CONFIG } = require('../e2e-test-suite');

class ErrorHandlingTestSuite extends TestSuite {
  constructor() {
    super('error-handling');
  }

  async execute() {
    // Test 1: Invalid phone number format
    await this.runTest('Test invalid phone number', async () => {
      await this.page.goto(CONFIG.baseUrl);
      
      // Enter invalid phone number
      await this.utils.waitAndType('input[placeholder*="98193"]', '12345', 'phone input');
      
      // Try to submit
      const submitButton = await this.page.$('button[type="submit"]');
      await submitButton.click();
      
      // Check for error message
      await new Promise(resolve => setTimeout(resolve, 1000));
      const errorMessage = await this.page.evaluate(() => {
        const errors = Array.from(document.querySelectorAll('.error, [role="alert"], .MuiFormHelperText-root'));
        return errors.map(el => el.textContent).join(' ');
      });
      
      if (errorMessage) {
        this.logger.success(`Error message displayed: ${errorMessage}`);
        await this.utils.screenshot('invalid-phone-error');
      } else {
        throw new Error('No error message shown for invalid phone number');
      }
    });

    // Test 2: Empty form submission
    await this.runTest('Test empty form submission', async () => {
      await this.page.reload();
      
      // Clear any existing input
      const phoneInput = await this.page.$('input[placeholder*="98193"]');
      await phoneInput.click({ clickCount: 3 });
      await this.page.keyboard.press('Backspace');
      
      // Try to submit empty form
      const submitButton = await this.page.$('button[type="submit"]');
      const isDisabled = await submitButton.evaluate(btn => btn.disabled);
      
      if (isDisabled) {
        this.logger.success('Submit button correctly disabled for empty input');
      } else {
        await submitButton.click();
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // Check for validation error
        const hasError = await this.page.evaluate(() => {
          return document.querySelector('.error, [role="alert"]') !== null;
        });
        
        if (!hasError) {
          throw new Error('No validation for empty form submission');
        }
      }
    });

    // Test 3: Network error handling
    await this.runTest('Test network error handling', async () => {
      // Intercept network requests to simulate failure
      await this.page.setRequestInterception(true);
      
      this.page.once('request', request => {
        if (request.url().includes('/api/')) {
          request.abort('failed');
        } else {
          request.continue();
        }
      });
      
      // Try to make an API call
      await this.page.goto(CONFIG.baseUrl);
      await this.utils.waitAndType('input[placeholder*="98193"]', CONFIG.testPhone, 'phone input');
      
      const submitButton = await this.page.$('button[type="submit"]');
      await submitButton.click();
      
      // Wait for error handling
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Check for error message
      const errorShown = await this.page.evaluate(() => {
        const errors = Array.from(document.querySelectorAll('*'));
        return errors.some(el => 
          el.textContent.toLowerCase().includes('error') ||
          el.textContent.toLowerCase().includes('failed') ||
          el.textContent.toLowerCase().includes('try again')
        );
      });
      
      if (errorShown) {
        this.logger.success('Network error handled gracefully');
        await this.utils.screenshot('network-error-message');
      } else {
        this.logger.warn('No visible error message for network failure');
      }
      
      // Reset interception
      await this.page.setRequestInterception(false);
    });

    // Test 4: Session timeout handling
    await this.runTest('Test session expiry', async () => {
      // Clear all cookies to simulate session expiry
      const cookies = await this.page.cookies();
      await this.page.deleteCookie(...cookies);
      
      // Try to access protected route
      await this.page.goto(CONFIG.baseUrl + '/dashboard');
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Check if redirected to login
      const currentUrl = this.page.url();
      if (currentUrl === CONFIG.baseUrl || currentUrl.includes('login') || currentUrl === CONFIG.baseUrl + '/') {
        this.logger.success('Session expiry handled - redirected to login');
      } else {
        this.logger.warn('May not have proper session protection');
      }
      
      await this.utils.screenshot('session-expired-redirect');
    });

    // Test 5: Invalid OTP handling
    await this.runTest('Test invalid OTP', async () => {
      // First request OTP
      await this.page.goto(CONFIG.baseUrl);
      await this.utils.waitAndType('input[placeholder*="98193"]', CONFIG.testPhone, 'phone input');
      
      const submitButton = await this.page.$('button[type="submit"]');
      await submitButton.click();
      
      // Wait for OTP field
      await this.page.waitForSelector('input[maxlength="6"]', { timeout: 10000 });
      
      // Enter invalid OTP
      await this.utils.waitAndType('input[maxlength="6"]', '000000', 'OTP input');
      
      // Submit OTP
      await this.page.keyboard.press('Enter');
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Check for error
      const otpError = await this.page.evaluate(() => {
        const errors = Array.from(document.querySelectorAll('*'));
        return errors.some(el => 
          el.textContent.toLowerCase().includes('invalid') ||
          el.textContent.toLowerCase().includes('incorrect') ||
          el.textContent.toLowerCase().includes('wrong')
        );
      });
      
      if (otpError) {
        this.logger.success('Invalid OTP error shown');
        await this.utils.screenshot('invalid-otp-error');
      } else {
        this.logger.warn('No clear error message for invalid OTP');
      }
    });

    // Test 6: Long message handling
    await this.runTest('Test long message handling', async () => {
      // Generate a very long message
      const longMessage = 'A'.repeat(10000);
      
      // Try to send it (assuming we're in a chat interface)
      const messageInput = await this.page.$('textarea[placeholder*="message" i]');
      if (messageInput) {
        await messageInput.type(longMessage);
        
        // Check if input is truncated or limited
        const actualValue = await messageInput.evaluate(el => el.value);
        
        if (actualValue.length < longMessage.length) {
          this.logger.success(`Message length limited to ${actualValue.length} characters`);
        } else {
          this.logger.warn('No apparent message length limit');
        }
        
        await this.utils.screenshot('long-message-test');
      }
    });
  }
}

module.exports = ErrorHandlingTestSuite;