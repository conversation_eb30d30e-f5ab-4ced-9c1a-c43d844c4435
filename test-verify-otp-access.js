const axios = require('axios');

// Test Verify OTP - Check for access field
async function testVerifyOTPAccess() {
  console.log('\n🔍 TEST: Verify OTP - Check for "access" field');
  console.log('=' + '='.repeat(60));
  console.log('Purpose: Verify that the access field is now included in response');
  console.log('Endpoint: POST /auth/practitioner-dashboard/verify-otp');
  console.log('=' + '='.repeat(60));
  
  const url = 'https://gatekeeper-staging.getbeyondhealth.com/auth/practitioner-dashboard/verify-otp';
  
  // Since we can't get a real OTP due to rate limiting, let's try with a test OTP
  // to see the error response structure
  const payload = {
    phone: '+************',
    otp: '123456',  // Invalid OTP to see response structure
    source: 'web'
  };
  
  console.log('\n📤 Request:');
  console.log('URL:', url);
  console.log('Body:', JSON.stringify(payload, null, 2));
  
  console.log('\n📝 Testing with invalid OTP to check response structure...');
  
  try {
    const response = await axios.post(url, payload, {
      headers: { 'Content-Type': 'application/json' }
    });
    
    console.log('\n✅ Unexpected Success!');
    console.log('Status:', response.status, response.statusText);
    console.log('Response:', JSON.stringify(response.data, null, 2));
    
    // Check for access field
    if (response.data.user && 'access' in response.data.user) {
      console.log('\n✅ ACCESS FIELD FOUND!');
      console.log('User access status:', response.data.user.access);
    }
    
  } catch (error) {
    console.log('\n❌ Expected Error (invalid OTP)');
    console.log('Status:', error.response?.status);
    console.log('Error:', error.response?.data || error.message);
    
    // Even in error, let's check the expected response format
    console.log('\n📝 Expected successful response format:');
    console.log(JSON.stringify({
      success: true,
      accessToken: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
      refreshToken: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
      expiresIn: 3600,
      user: {
        id: "user-uuid",
        phone: "+************",
        role: "DOCTOR",
        access: true  // <-- THIS IS THE NEW FIELD
      }
    }, null, 2));
    
    console.log('\n🔍 The "access" field indicates:');
    console.log('- true: User has been approved by admin');
    console.log('- false: User registered but pending approval');
  }
  
  // Let's also try to check with completely wrong format to see validation
  console.log('\n\n📝 Testing with invalid phone format...');
  
  try {
    const response2 = await axios.post(url, {
      phone: '1234567890',  // Invalid format
      otp: '123456',
      source: 'web'
    }, {
      headers: { 'Content-Type': 'application/json' }
    });
    
    console.log('Response:', response2.data);
    
  } catch (error) {
    console.log('Error response:', error.response?.data || error.message);
  }
}

testVerifyOTPAccess();