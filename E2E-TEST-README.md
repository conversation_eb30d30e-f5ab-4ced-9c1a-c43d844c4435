# Doctor Dashboard E2E Test Suite

## Overview

This comprehensive end-to-end test suite validates all major functionality of the Doctor Dashboard application using Puppeteer for browser automation.

## Test Coverage

The test suite includes the following test categories:

### 1. **Authentication Tests** (`authentication`)
- Login page navigation
- Phone number input validation
- OTP request flow
- OTP verification (manual/automated)
- Session management

### 2. **Dashboard Navigation Tests** (`dashboard-navigation`)
- Dashboard layout verification
- Sidebar navigation
- Thread type sections (Patient Cases, Research, Quick Facts)
- New thread creation UI

### 3. **Messaging Tests** (`messaging`)
- Message input functionality
- File attachment capabilities
- Markdown rendering verification
- Message history display

### 4. **Patient Case Tests** (`patient-cases`)
- New patient case creation
- Patient information forms
- Medical query handling
- Case-specific chat functionality
- File attachments in patient context

### 5. **Research Tests** (`research`)
- Research thread creation
- Academic query handling
- Citation/reference checking
- Data visualization (tables, charts)
- Export functionality

### 6. **Quick Facts Tests** (`quick-facts`)
- Drug dosage queries
- Medical calculations (BMI, etc.)
- Lab value interpretation
- Response formatting
- Response time performance

### 7. **Error Handling Tests** (`error-handling`)
- Invalid input validation
- Network error recovery
- Session timeout handling
- Invalid OTP handling
- Input length restrictions
- Graceful error messages

## Installation

```bash
# Ensure Puppeteer is installed
npm install puppeteer

# Make the test runner executable
chmod +x run-all-tests.js
```

## Usage

### Run All Tests
```bash
node run-all-tests.js
```

### Run Specific Test Suite
```bash
node run-all-tests.js authentication
node run-all-tests.js patient-cases
node run-all-tests.js error-handling
```

### Run with Visible Browser (Non-Headless)
```bash
HEADLESS=false node run-all-tests.js
```

### Run with Automated OTP
```bash
TEST_OTP=123456 node run-all-tests.js
```

### Show Help
```bash
node run-all-tests.js --help
```

## Configuration

Edit the `CONFIG` object in `e2e-test-suite.js`:

```javascript
const CONFIG = {
  baseUrl: 'http://localhost:3000',      // Application URL
  testPhone: '+919819304846',             // Test phone number
  headless: true,                         // Run in headless mode
  slowMo: 50,                            // Delay between actions (ms)
  timeout: 30000,                        // Default timeout (ms)
  testOTP: '123456'                      // Test OTP (if available)
};
```

## Test Structure

Each test suite follows this structure:

1. **Setup**: Launch browser, configure page monitoring
2. **Execute**: Run individual test cases
3. **Teardown**: Close browser, save results
4. **Report**: Generate test summary and detailed logs

## Output

### Screenshots
- Location: `test-results/screenshots/`
- Naming: `{suite-name}-{step}-{description}.png`
- Captured at key points and on errors

### Logs
- Location: `test-results/logs/`
- Format: JSON entries with timestamp, level, and data
- One log file per test suite run

### Reports
- Location: `test-results/`
- Format: Comprehensive JSON report
- Includes: Pass/fail stats, error details, timing data

## Test Data

- **Phone Number**: Always use `+919819304846` for testing
- **Test Doctor**: Dr. Test User, Test Hospital
- **License**: TEST123456

## Debugging Failed Tests

1. **Check Screenshots**: Visual evidence of the failure state
2. **Review Logs**: Detailed execution logs with API calls
3. **Run Non-Headless**: `HEADLESS=false` to watch execution
4. **Run Single Suite**: Isolate the failing test suite

## Writing New Tests

To add a new test suite:

1. Create a new file in `e2e-test-suites/`
2. Extend the `TestSuite` base class
3. Implement the `execute()` method
4. Use `this.runTest()` for individual test cases
5. Add the suite to `run-all-tests.js`

Example:
```javascript
const { TestSuite, CONFIG } = require('../e2e-test-suite');

class MyNewTestSuite extends TestSuite {
  constructor() {
    super('my-new-tests');
  }

  async execute() {
    await this.runTest('Test case name', async () => {
      // Test implementation
      await this.page.goto(CONFIG.baseUrl);
      await this.utils.screenshot('test-step');
      // Assertions...
    });
  }
}

module.exports = MyNewTestSuite;
```

## Best Practices

1. **Use Descriptive Names**: Clear test and screenshot names
2. **Wait for Elements**: Use `waitForSelector` before interactions
3. **Capture Screenshots**: At key points for debugging
4. **Handle Async**: Properly await all async operations
5. **Clean State**: Each test should be independent
6. **Meaningful Assertions**: Check actual vs expected behavior

## CI/CD Integration

```yaml
# Example GitHub Actions workflow
name: E2E Tests
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - uses: actions/setup-node@v2
      - run: npm install
      - run: npm run dev &
      - run: sleep 10
      - run: node run-all-tests.js
      - uses: actions/upload-artifact@v2
        if: failure()
        with:
          name: test-results
          path: test-results/
```

## Troubleshooting

### Tests Timing Out
- Increase `CONFIG.timeout`
- Check if selectors have changed
- Verify the application is running

### OTP Verification
- Set `TEST_OTP` environment variable
- Or manually enter OTP when prompted
- Consider using a test SMS service

### Network Errors
- Ensure the application is running
- Check `CONFIG.baseUrl` is correct
- Verify no firewall blocking

## Future Enhancements

- [ ] Parallel test execution
- [ ] Visual regression testing
- [ ] Performance metrics collection
- [ ] Accessibility testing
- [ ] Mobile viewport testing
- [ ] Cross-browser testing
- [ ] API mocking for consistent tests
- [ ] Test data factories
- [ ] Automated OTP retrieval

## Support

For issues or questions:
1. Check test logs in `test-results/logs/`
2. Review screenshots in `test-results/screenshots/`
3. Run specific failing test in non-headless mode
4. Check application logs for server-side errors