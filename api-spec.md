# Doctor Dashboard API Specification

## Base URL
- **Staging**: `https://gatekeeper-staging.getbeyondhealth.com`
- **Tenant**: `practitioner-dashboard`

## Authentication
- **JWT Bearer Token**: For authenticated endpoints (user-specific operations)
- **Static Bearer Token**: For registration endpoint
  - Staging Token: `m}0/m9ZL`k{|Mz:Ca{7k8PF(gJV"Xz/j`

---

## 1. Authentication APIs

### POST `/auth/practitioner-dashboard/request-otp`

Request OTP for phone verification.

**Headers:**
```
Content-Type: application/json
```

**Request Body:**
```json
{
  "phoneNumber": "+************"
}
```

**Response:** ✅ 200 OK
```json
{
  "requestId": "test-request-id"
}
```

**Rate Limiting:**
- 4 requests per 30 minutes (1800 seconds)
- Headers: `Ratelimit-Limit: 4`, `Ratelimit-Remaining: 2`

---

### POST `/auth/practitioner-dashboard/verify-otp`

Verify OTP and receive JWT tokens.

**Headers:**
```
Content-Type: application/json
```

**Request Body:**
```json
{
  "phone": "+************",
  "otp": "123456",
  "source": "web"
}
```

**Expected Response:** ✅ 200 OK
```json
{
  "success": true,
  "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "expiresIn": 3600,
  "user": {
    "id": "user-uuid",
    "phone": "+************",
    "role": "DOCTOR",
    "access": true
  }
}
```

---

### POST `/auth/practitioner-dashboard/refresh-token`

Refresh JWT access token.

**Headers:**
```
Authorization: Bearer <refresh-token>
Content-Type: application/json
```

**Request Body:**
```json
{
  "phone": "+************"
}
```

**Expected Response:**
```json
{
  "success": true,
  "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "expiresIn": 3600
}
```

---

## 2. User Registration API

### POST `/c/practitioner-dashboard/register`

Register a new user after OTP verification. Creates user with `access: false` by default (pending approval).

**Headers:**
```
Authorization: Bearer m}0/m9ZL`k{|Mz:Ca{7k8PF(gJV"Xz/j
Content-Type: application/json
```

**Request Body:**
```json
{
  "source": "WEB",
  "phoneNumber": "+************",
  "user_role": "DOCTOR"
}
```

**Response:** ✅ 200 OK
```json
{
  "success": true,
  "message": "User registered successfully"
}
```

---

## 3. Message APIs

### POST `/c/practitioner-dashboard/webhook`

Send a message (text, single file, or multiple files) to a dialogue.

**Headers:**
```
Authorization: Bearer <JWT> (optional)
Content-Type: application/json
```

### 3.1 Text Message

**Request Body:**
```json
{
  "dialogueId": "2ea2558e-7d45-4bde-b440-5f70086ace03",
  "dialogueType": "patient-case",
  "text": "Patient presents with fever and cough",
  "providerMessageId": "550e8400-e29b-41d4-a716-446655440000",
  "sender": "human",
  "source": "WEB",
  "phoneNumber": "+************",
  "timestamp": 1750816877000,
  "requestId": "660e8400-e29b-41d4-a716-446655440001"
}
```

**Response:** ✅ 200 OK
```json
{
  "success": true,
  "tenantSlug": "practitioner-dashboard"
}
```

### 3.2 Single File Message

**Request Body:**
```json
{
  "dialogueId": "2ea2558e-7d45-4bde-b440-5f70086ace03",
  "dialogueType": "patient-case",
  "text": "Here's the patient's X-ray",
  "providerMessageId": "550e8400-e29b-41d4-a716-446655440000",
  "attachment": "https://azure-blob-url/file.png",
  "fileExtension": ".png",
  "messageType": "image",
  "sender": "human",
  "source": "WEB",
  "phoneNumber": "+************",
  "timestamp": 1750816877000,
  "requestId": "660e8400-e29b-41d4-a716-446655440001"
}
```

**Response:** ✅ 200 OK
```json
{
  "success": true,
  "tenantSlug": "practitioner-dashboard"
}
```

### 3.3 Multiple Files Message

**Request Body:**
```json
{
  "dialogueId": "2ea2558e-7d45-4bde-b440-5f70086ace03",
  "dialogueType": "patient-case",
  "text": "Patient reports and lab results",
  "providerMessageId": "550e8400-e29b-41d4-a716-446655440000",
  "attachment": [
    {
      "url": "https://azure-blob-url/report1.png",
      "fileExtension": ".png",
      "messageType": "image"
    },
    {
      "url": "https://azure-blob-url/report2.pdf",
      "fileExtension": ".pdf",
      "messageType": "pdf"
    }
  ],
  "sender": "human",
  "source": "WEB",
  "phoneNumber": "+************",
  "timestamp": 1750816877000,
  "requestId": "660e8400-e29b-41d4-a716-446655440001"
}
```

**Response:** ✅ 200 OK
```json
{
  "success": true,
  "tenantSlug": "practitioner-dashboard"
}
```

### Important Notes on Message API:
- ⚠️ **NO VALIDATION**: This endpoint accepts ANY payload without validation
- All dialogue types work: `patient-case`, `research`, `quick-fact`
- Accepts invalid phone formats
- Accepts missing required fields
- Always returns success response

---

## 4. Data Retrieval APIs (Require JWT)

### GET `/user/practitioner-dashboard/get-chats-by-dialogueId`

Get all dialogues/threads or messages for a specific dialogue.

**Headers:**
```
Authorization: Bearer <JWT>
Content-Type: application/json
```

### 4.1 Get All Dialogues

**Query Parameters:**
- `limit` (integer, required): Number of dialogues to fetch (recommended: 100+)
- `dialogue_id` (string, required): Pass empty string ""

**Example Request:**
```
GET /user/practitioner-dashboard/get-chats-by-dialogueId?limit=100&dialogue_id=
```

**Expected Response (with valid JWT):**
```json
{
  "success": true,
  "dialogues": [
    {
      "id": "dialogue-uuid-1",
      "type": "patient-case",
      "lastMessage": {
        "text": "Patient case discussion...",
        "timestamp": "2025-06-25T08:00:00Z",
        "sender": "human"
      },
      "createdAt": "2025-06-24T10:00:00Z",
      "updatedAt": "2025-06-25T08:00:00Z"
    },
    {
      "id": "dialogue-uuid-2",
      "type": "research",
      "lastMessage": {
        "text": "Research query about...",
        "timestamp": "2025-06-24T15:00:00Z",
        "sender": "ai"
      },
      "createdAt": "2025-06-24T14:00:00Z",
      "updatedAt": "2025-06-24T15:00:00Z"
    }
  ],
  "total": 2,
  "limit": 100
}
```

### 4.2 Get Messages by Dialogue ID

**Query Parameters:**
- `limit` (integer, optional): Number of messages to fetch (default: 30)
- `dialogue_id` (string, required): The dialogue/thread ID

**Example Request:**
```
GET /user/practitioner-dashboard/get-chats-by-dialogueId?limit=30&dialogue_id=2ea2558e-7d45-4bde-b440-5f70086ace03
```

**Expected Response (with valid JWT):**
```json
{
  "success": true,
  "dialogueId": "dialogue-uuid-1",
  "messages": [
    {
      "id": "msg-1",
      "sender": "human",
      "text": "45 year old male with chest pain",
      "timestamp": "2025-06-25T08:00:00Z",
      "attachments": []
    },
    {
      "id": "msg-2",
      "sender": "ai",
      "text": "I'll help you assess this patient...",
      "timestamp": "2025-06-25T08:00:30Z",
      "attachments": []
    }
  ],
  "hasMore": false,
  "limit": 30
}
```

**Error Response (without valid JWT):** ❌ 401 Unauthorized
```json
{
  "success": false,
  "message": "Invalid or expired JWT token",
  "details": "jwt malformed"
}
```

---

## Additional Information

### Authentication Flow
1. Request OTP via `/auth/practitioner-dashboard/request-otp`
2. Verify OTP via `/auth/practitioner-dashboard/verify-otp` - returns JWT
3. For new users, call registration endpoint after OTP verification
4. Use JWT for authenticated endpoints

### Response Delivery
- Gatekeeper sends responses via Redis Pub/Sub
- Our server subscribes to Redis channels for real-time message delivery
- Message webhook returns immediate success, actual AI response comes via Redis

### File Specifications
- **Supported Types**: Images (PNG, JPG, etc.) and PDF
- **Max Size**: 25MB per file
- **Upload Process**: Upload to Azure Blob Storage first, then send URL
- **Multiple Files**: Supported as array of attachment objects

### Message Types
- `text`: Text-only message
- `image`: Image attachment (PNG, JPG)
- `pdf`: PDF attachment

### Dialogue Types
- `patient-case`: Clinical case discussions
- `research`: Medical research queries
- `quick-fact`: Quick medical information

### Dialogue Creation
- Frontend generates UUID for new dialogue
- Send first message with new UUID
- Backend automatically creates dialogue on receiving new ID

### Timestamp Format
- Always use Unix timestamp in milliseconds

---

## API Test Results Summary (2025-06-25)

### ✅ Working Without JWT (6/8 tests)
1. **Request OTP** - Returns requestId
2. **Register User** - Returns success message
3. **Send Text Message** - Returns success with tenantSlug
4. **Send Single File** - Returns success with tenantSlug
5. **Send Multiple Files** - Returns success with tenantSlug
6. **All Dialogue Types** - All types accepted

### ❌ Require Valid JWT (2/8 tests)
1. **Get All Dialogues** - 401 Unauthorized
2. **Get Messages by ID** - 401 Unauthorized

### Key Findings
1. **Message API has NO validation** - Accepts any payload
2. **GET endpoints require valid JWT** from Gatekeeper auth
3. **Rate limiting on OTP**: 4 requests per 30 minutes
4. **All responses are consistent**: `{ "success": true, "tenantSlug": "practitioner-dashboard" }`

### Implementation Status
- ✅ OTP Request endpoint tested and working
- ✅ User registration endpoint tested and working
- ✅ Message sending via webhook tested for all types
- ✅ All dialogue types supported
- ⚠️ OTP Verify endpoint needs testing with real OTP
- ⚠️ Need real JWT from Gatekeeper for GET endpoints
- ⚠️ Redis Pub/Sub integration pending for receiving AI responses
- ⚠️ WebSocket relay to Gatekeeper pending

### Next Steps
1. Get real JWT through OTP verification flow
2. Test GET endpoints with valid JWT
3. Implement Redis Pub/Sub for receiving AI responses
4. Update custom-server.js to use real Gatekeeper APIs
5. Add proper validation on frontend since backend accepts any payload