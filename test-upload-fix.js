#!/usr/bin/env node

const puppeteer = require('puppeteer');
const fs = require('fs');
const path = require('path');

async function testUploadFix() {
  console.log('🧪 Testing File Upload Fix');
  console.log('==========================\n');

  let browser;
  try {
    // Use the existing test-file.pdf if it exists
    const testFilePath = path.join(__dirname, 'test-file.pdf');
    if (!fs.existsSync(testFilePath)) {
      console.log('❌ test-file.pdf not found. Please ensure the file exists.');
      return;
    }

    browser = await puppeteer.launch({ 
      headless: false,
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });
    
    const page = await browser.newPage();
    
    // Capture console logs
    page.on('console', msg => {
      const text = msg.text();
      console.log(`[CONSOLE.${msg.type()}] ${text}`);
    });
    
    page.on('pageerror', error => {
      console.error('❌ Page Error:', error.message);
    });

    // Navigate to app
    console.log('1️⃣ Navigating to app...');
    await page.goto('http://localhost:3000', { waitUntil: 'networkidle2' });
    
    // Set authentication directly
    console.log('2️⃣ Setting authentication...');
    await page.evaluate(() => {
      localStorage.setItem('authToken', 'test-token');
      localStorage.setItem('access_token', 'test-token');
      localStorage.setItem('user', JSON.stringify({ 
        id: 'test-user-123', 
        name: 'Test User',
        phone: '+************'
      }));
    });
    
    // Reload to apply auth
    await page.reload({ waitUntil: 'networkidle2' });
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Look for attachment button and click it
    console.log('\n3️⃣ Finding and clicking attachment button...');
    const clicked = await page.evaluate(() => {
      const buttons = Array.from(document.querySelectorAll('button'));
      const attachButton = buttons.find(btn => {
        const svg = btn.querySelector('svg');
        return svg && (svg.innerHTML.includes('path') && btn.getAttribute('title')?.includes('Attach'));
      });
      if (attachButton) {
        attachButton.click();
        return true;
      }
      return false;
    });
    
    if (!clicked) {
      console.log('❌ Could not find attachment button');
      return;
    }
    
    console.log('✅ Clicked attachment button');
    await new Promise(resolve => setTimeout(resolve, 500));
    
    // Upload file
    console.log('4️⃣ Uploading file...');
    const fileInput = await page.$('input[type="file"]');
    if (fileInput) {
      await fileInput.uploadFile(testFilePath);
      console.log('✅ File selected');
      
      // Wait for upload
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      // Type a message
      console.log('5️⃣ Typing message...');
      await page.type('textarea', 'Test message with file attachment');
      
      // Send message
      console.log('6️⃣ Sending message...');
      await page.keyboard.press('Enter');
      
      // Wait for any errors
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      console.log('\n✅ Test completed! Check console output for any errors.');
    } else {
      console.log('❌ File input not found');
    }
    
  } catch (error) {
    console.error('\n❌ Test failed:', error.message);
  } finally {
    if (browser) {
      console.log('\n🔄 Keeping browser open. Press Ctrl+C to close.');
      await new Promise(() => {});
    }
  }
}

testUploadFix();