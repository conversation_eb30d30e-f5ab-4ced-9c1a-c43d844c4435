# 🏥 Doctor Dashboard - AI Medical Assistant

A professional dashboard for doctors with AI-powered medical assistance, real SMS authentication, and comprehensive file management capabilities.

![Next.js](https://img.shields.io/badge/Next.js-15.3.4-black?logo=next.js)
![React](https://img.shields.io/badge/React-18-blue?logo=react)
![Material-UI](https://img.shields.io/badge/Material--UI-5-blue?logo=mui)
![PostgreSQL](https://img.shields.io/badge/PostgreSQL-14-blue?logo=postgresql)
![Node.js](https://img.shields.io/badge/Node.js-18+-green?logo=node.js)

## ✨ Features

### 🔐 **Real Authentication System**
- **SMS OTP Verification**: Real SMS delivery via Gatekeeper API
- **Doctor Registration**: Complete signup with medical license verification
- **Admin Approval Workflow**: Manual review and approval of doctor registrations
- **JWT Authentication**: Secure access and refresh token management
- **Role-Based Access**: Doctor and admin user roles

### 🤖 **AI Medical Assistant**
- **Intelligent Chat Interface**: Context-aware medical discussions
- **Multiple Conversation Types**: Patient cases, research, quick facts
- **Markdown Support**: Rich text rendering with tables and formatting
- **Chat History**: Persistent conversation threads

### 📁 **File Management**
- **Medical File Uploads**: Support for up to 5 files per message
- **Multiple Formats**: Images, PDFs, documents (medical-focused MIME types)
- **File Preview**: Visual preview of attached files
- **Secure Storage**: Base64 encoding with future Azure Blob integration

### 🛠️ **Developer Experience**
- **Automated Setup**: One-command local environment setup
- **Comprehensive Testing**: API test suites with real OTP validation
- **Database Management**: PostgreSQL with existing schema compatibility
- **Type Safety**: Structured validation and error handling

## 🚀 Quick Start

### Prerequisites
- **macOS** (setup script is Mac-specific)
- **Node.js 18+**
- **Homebrew** (for PostgreSQL installation)

### One-Command Setup

```bash
# Clone the repository
git clone <repository-url>
cd doctor-dashboard

# Run automated setup (macOS only)
./setup.sh

# Start development server
npm run dev
```

The setup script will:
- ✅ Install PostgreSQL 14 (if needed)
- ✅ Create and configure the database
- ✅ Install all dependencies
- ✅ Set up environment variables
- ✅ Create default admin user
- ✅ Test database connectivity

### Manual Setup

If you prefer manual setup, see [SETUP.md](./SETUP.md) for detailed instructions.

## 🧪 Testing the Application

### 1. **Registration Flow**
```bash
# Go to http://localhost:3000
# 1. Click "Register" tab
# 2. Fill in doctor details
# 3. Use any real phone number
# 4. Check SMS for OTP code
# 5. Complete registration (pending approval)
```

### 2. **Test Login**
```bash
# Use test number with fixed OTP
Phone: +************
OTP: 123456
```

### 3. **Admin Access**
```bash
# Default admin account
Phone: +**********
# Use real OTP from SMS
```

### 4. **API Testing**
```bash
# Run comprehensive API tests
./test-api-curl.sh

# Test with real OTP integration
node test-real-otp.js
```

## 📋 Architecture

### **Tech Stack**
- **Frontend**: Next.js 15, React 18, Material-UI 5
- **Backend**: Next.js API Routes, Node.js
- **Database**: PostgreSQL 14
- **Authentication**: JWT tokens, Gatekeeper OTP API
- **File Storage**: Base64 (transitioning to Azure Blob)
- **Markdown**: react-markdown with remark-gfm

### **Project Structure**
```
doctor-dashboard/
├── src/
│   ├── app/
│   │   ├── api/                 # API routes
│   │   │   ├── auth/           # Authentication endpoints
│   │   │   ├── admin/          # Admin management
│   │   │   └── health/         # System health check
│   │   ├── page.js             # Main application page
│   │   └── layout.js           # Root layout
│   ├── components/
│   │   ├── Auth/               # Login/registration components
│   │   ├── Dashboard/          # Main dashboard UI
│   │   └── FileAttachment/     # File upload components
│   └── lib/
│       ├── api.js              # Frontend API client
│       ├── auth-utils.js       # Authentication utilities
│       ├── auth-middleware.js  # JWT middleware
│       └── database.js         # Database connection
├── database-setup.sql          # Database schema
├── setup.sh                    # Automated setup script
├── SETUP.md                    # Detailed setup guide
└── test-*.js                   # Testing suites
```

## 🔗 API Endpoints

### **Authentication**
| Method | Endpoint | Description |
|--------|----------|-------------|
| `POST` | `/api/auth/request-otp` | Send OTP via SMS |
| `POST` | `/api/auth/verify-and-register` | Register new doctor |
| `POST` | `/api/auth/login` | Request login OTP |
| `POST` | `/api/auth/verify-login` | Verify login & get tokens |
| `GET` | `/api/auth/me` | Get current user info |
| `POST` | `/api/auth/refresh` | Refresh access token |
| `POST` | `/api/auth/logout` | Logout user |

### **Admin**
| Method | Endpoint | Description |
|--------|----------|-------------|
| `GET` | `/api/admin/pending-registrations` | List pending doctors |
| `POST` | `/api/admin/review-registration` | Approve/reject doctors |
| `POST` | `/api/admin/cleanup` | Database cleanup |

### **System**
| Method | Endpoint | Description |
|--------|----------|-------------|
| `GET` | `/api/health` | Health check & system status |

## 🗄️ Database Schema

### **Main Tables**
- **`users`**: Doctor accounts and admin users
- **`verification_token`**: OTP tokens for phone verification  
- **`web_chat_sessions`**: Active JWT sessions
- **`refresh_tokens`**: Refresh token management

### **Key Features**
- Compatible with existing schema
- JSONB fields for flexible metadata
- Proper indexing for performance
- UUID support for session management

## 🔒 Security Features

### **Authentication & Authorization**
- ✅ Real SMS OTP verification via Gatekeeper
- ✅ JWT access tokens (1 hour expiry)
- ✅ Refresh tokens (7 day expiry)
- ✅ Role-based access control (doctor/admin)
- ✅ Session management and cleanup

### **Input Validation**
- ✅ Phone number format validation
- ✅ Medical license number verification
- ✅ File type and size restrictions
- ✅ SQL injection prevention
- ✅ Rate limiting protection

### **Data Protection**
- ✅ Secure token storage
- ✅ Parameterized database queries
- ✅ Error message sanitization
- ✅ CORS and security headers

## 🧪 Testing

### **Test Suites Available**

1. **`test-api-curl.sh`** - Quick bash/curl API testing
2. **`test-api-simple.js`** - Detailed Node.js API testing  
3. **`test-real-otp.js`** - Real OTP integration testing
4. **API_TESTING_GUIDE.md** - Comprehensive testing documentation

### **Test Coverage**
- ✅ Health check validation
- ✅ OTP request/verification
- ✅ Registration flow testing
- ✅ Login flow validation
- ✅ Protected endpoint access
- ✅ Admin functionality
- ✅ Token refresh mechanics
- ✅ Error handling scenarios

## 🚢 Deployment

### **Environment Variables**
```bash
# Database
DATABASE_URL=postgresql://user@localhost:5432/doctor_dashboard
DATABASE_SSL=false

# JWT Secrets
JWT_ACCESS_SECRET=your-secret-key
JWT_REFRESH_SECRET=your-refresh-secret
JWT_ACCESS_EXPIRES_IN=1h
JWT_REFRESH_EXPIRES_IN=7d

# Environment
NODE_ENV=production
```

### **Production Checklist**
- [ ] Update Gatekeeper URLs to production
- [ ] Configure secure JWT secrets
- [ ] Set up SSL/TLS certificates
- [ ] Configure CORS policies
- [ ] Set up monitoring and logging
- [ ] Implement Redis for rate limiting
- [ ] Configure Azure Blob storage
- [ ] Set up backup strategies

## 🔧 Development

### **Available Scripts**
```bash
npm run dev          # Start development server
npm run build        # Build for production
npm run start        # Start production server
npm run lint         # Run ESLint
npm run test         # Run test suite
```

### **Database Operations**
```bash
# Connect to database
psql -d doctor_dashboard

# Run migrations
psql -d doctor_dashboard -f database-setup.sql

# Clear test data
# See database-utils.sql for helper queries
```

### **Useful Commands**
```bash
# View logs in development
npm run dev  # Check console output

# Test API endpoints
./test-api-curl.sh

# Database shell
psql -d doctor_dashboard

# Reset environment
rm .env.local && ./setup.sh
```

## 📚 Documentation

- **[SETUP.md](./SETUP.md)** - Detailed setup instructions
- **[API_TESTING_GUIDE.md](./API_TESTING_GUIDE.md)** - API testing documentation
- **[BACKEND_IMPLEMENTATION_SUMMARY.md](./BACKEND_IMPLEMENTATION_SUMMARY.md)** - Backend architecture details
- **[backend-spec.md](./backend-spec.md)** - Original backend specification

## 🐛 Troubleshooting

### **Common Issues**

**Database Connection Issues**
```bash
# Check PostgreSQL status
brew services list | grep postgresql

# Start PostgreSQL
brew services start postgresql@14

# Test connection
psql -d doctor_dashboard -c "SELECT 'Connected';"
```

**OTP Not Working**
- Verify Gatekeeper API is accessible
- Check rate limits (wait between requests)
- Use test number +************ with OTP 123456
- Check console logs for detailed error messages

**Environment Issues**
```bash
# Verify Node.js version
node --version  # Should be 18+

# Check environment file
cat .env.local

# Recreate environment
rm .env.local && ./setup.sh
```

## 🤝 Contributing

1. **Fork the repository**
2. **Create a feature branch**: `git checkout -b feature/amazing-feature`
3. **Make changes and test thoroughly**
4. **Commit changes**: `git commit -m 'Add amazing feature'`
5. **Push to branch**: `git push origin feature/amazing-feature`
6. **Open a Pull Request**

### **Development Guidelines**
- Follow existing code style and patterns
- Add tests for new features
- Update documentation as needed
- Test with real OTP integration
- Ensure database compatibility

## 📄 License

This project is proprietary and confidential. Unauthorized copying, distribution, or use is strictly prohibited.

## 🆘 Support

For technical support or questions:
1. Check the [troubleshooting section](#-troubleshooting)
2. Review the comprehensive documentation
3. Run the test suites to validate your setup
4. Check console logs for detailed error information

---

**Built with ❤️ for medical professionals**

*The Doctor Dashboard provides secure, efficient tools for modern medical practice with AI assistance and robust authentication.*