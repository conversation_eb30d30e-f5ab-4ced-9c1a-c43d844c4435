# Doctor Dashboard Chat Flow Test Plan

## Overview
This document provides complete context for testing the chat functionality of the Doctor Dashboard application. It includes detailed descriptions of expected behaviors, UI elements, and screenshot verification points.

## Application Architecture
- **URL Structure**: 
  - Login: `http://localhost:3000/`
  - After login: Stays at `/` (not `/dashboard` or `/chat/[uuid]`)
  - The app uses a single-page architecture where the main dashboard is at the root URL
- **Authentication**: Phone number + OTP verification
- **Test Phone**: +************
- **Test OTP**: 123456

## Expected UI Layout After Login

### Main Dashboard Components
1. **Left Sidebar (Thread List)**
   - Width: ~300px
   - Contains threads organized by time periods:
     - "Today"
     - "Yesterday" 
     - "This Week"
     - "Last Week"
     - "Older"
   - Each thread shows the title/first message
   - "New Conversation" button at the top

2. **Main Chat Area (Center)**
   - Welcome message: "Hey <PERSON>, how can I help you today?"
   - Large textarea for message input
   - Placeholder text: "Describe your patient case, symptoms, or clinical question..."

3. **Bottom Action Bar**
   - Three thread type buttons:
     - 🩺 Patient Case (green button)
     - 🔬 Research (gray button)
     - 📋 Quick Facts (gray button)
   - File attachment icon (paperclip)
   - Send button (arrow icon)

4. **Top Header**
   - "Doctor Dashboard" title on the left
   - User avatar/menu on the right (shows "Dr. Raj Jayawant")

## Test Flow: Sending Message and Chat Navigation

### Step 1: Initial State Verification
**What to check:**
- Textarea is empty
- Welcome message is displayed
- Thread type buttons are visible
- No existing messages in the chat area

**Screenshot name**: `chat-initial-state.png`
**Expected elements**:
- Center text: "Hey Doc, how can I help you today?"
- Empty textarea
- Three buttons at bottom

### Step 2: Select Thread Type
**Action**: Click "Patient Case" button
**Expected behavior**:
- Button becomes highlighted (green)
- Textarea remains focused
- No navigation occurs

**Screenshot name**: `thread-type-selected.png`
**Visual verification**:
- Patient Case button should have green background
- Other buttons remain gray

### Step 3: Type Message
**Action**: Type a detailed patient case
**Message**: "45-year-old male presenting with Type 2 Diabetes. HbA1c is 8.2%. Currently on Metformin 1000mg twice daily. Patient reports frequent urination and increased thirst. BP 140/90. What treatment modifications would you recommend?"

**Screenshot name**: `message-typed.png`
**Visual verification**:
- Full message visible in textarea
- Send button should be enabled/visible

### Step 4: Send Message
**Action**: Press Enter or click send button
**Expected behavior**:
- Message appears in chat area as a user message (right-aligned)
- Textarea clears
- Loading indicator appears (spinning dots or "AI is thinking...")
- New thread appears in left sidebar under "Today"

**Screenshot name**: `message-sent.png`
**Visual verification**:
- User message bubble on the right side
- Empty textarea
- Loading indicator visible

### Step 5: AI Response
**Wait time**: 5-10 seconds
**Expected behavior**:
- AI response appears (left-aligned)
- Response includes medical recommendations
- Markdown formatting (bullet points, bold text)
- Loading indicator disappears

**Screenshot name**: `ai-response-received.png`
**Visual verification**:
- Two messages visible (user + AI)
- AI message on left with different styling
- Formatted text with medical information

### Step 6: Thread Creation Verification
**Check sidebar**:
- New thread appears under "Today"
- Thread title is first ~50 characters of the message
- Thread is highlighted as active

**Screenshot name**: `thread-created.png`
**Visual verification**:
- Sidebar shows new thread
- Thread title: "45-year-old male presenting with Type 2..."

### Step 7: Send Follow-up Message
**Action**: Type and send follow-up
**Message**: "What about lifestyle modifications and dietary recommendations?"

**Screenshot name**: `follow-up-sent.png`
**Visual verification**:
- Three messages visible (2 user, 1 AI)
- Second user message at bottom

### Step 8: Navigate Away and Back
**Actions**:
1. Click "New Conversation" button
2. Click on the thread in sidebar

**Expected behavior**:
- Clicking "New Conversation" clears chat area
- Clicking thread restores full conversation

**Screenshot names**: 
- `new-conversation-clicked.png`
- `thread-restored.png`

## Message Types and Expected Responses

### Patient Case Messages
**Input characteristics**:
- Contains patient demographics
- Includes symptoms/conditions
- May include lab values
- Asks for treatment recommendations

**Expected AI response**:
- Structured medical advice
- Differential diagnosis
- Treatment options
- Follow-up recommendations
- Disclaimers about consulting specialists

### Research Messages
**Input characteristics**:
- Questions about studies/trials
- Drug comparisons
- Latest treatment guidelines

**Expected AI response**:
- Evidence-based information
- Citations/references
- Study summaries
- Statistical data

### Quick Facts Messages
**Input characteristics**:
- Short, specific questions
- Normal ranges
- Drug dosages
- Quick references

**Expected AI response**:
- Concise, direct answers
- Bullet points or tables
- Key information highlighted

## Error Scenarios to Test

1. **Empty message**: Should not send
2. **Very long message**: Should handle gracefully
3. **Network interruption**: Should show error message
4. **Rapid message sending**: Should queue properly

## Verification Checklist

- [ ] User can type and send messages
- [ ] Messages appear in correct positions (user right, AI left)
- [ ] AI responds within reasonable time (5-10 seconds)
- [ ] Thread is created in sidebar
- [ ] Thread title reflects message content
- [ ] Can navigate between threads
- [ ] Message history is preserved
- [ ] Markdown formatting renders correctly
- [ ] Thread type selection works
- [ ] New conversation clears the chat

## Success Criteria

The test is successful if:
1. Messages can be sent and received
2. Threads are created and listed in sidebar
3. Navigation between threads preserves history
4. AI responses are relevant and formatted
5. No console errors during interaction
6. UI remains responsive throughout

## Common Issues and Solutions

1. **AI not responding**: Check console for API errors
2. **Thread not appearing**: Verify WebSocket connection
3. **Messages not sending**: Check network tab for failed requests
4. **Formatting issues**: Verify markdown parser is working

## Additional Context for Future Testing

- The app uses Material-UI components
- Real-time updates via WebSocket or polling
- Messages stored in PostgreSQL database
- AI responses come from backend API
- Rate limiting may apply to rapid requests