# End-to-End Testing Framework Documentation

## Overview

This document describes the comprehensive E2E testing framework for the Doctor Dashboard application. The framework is built using Puppeteer for browser automation and provides extensive coverage of all major features.

## Architecture

### Core Components

1. **Base Framework** (`e2e-test-suite.js`)
   - `TestSuite` class: Base class for all test suites
   - `Logger`: Comprehensive logging with color-coded output
   - `TestUtils`: Common utilities for waiting, element interaction, etc.

2. **Test Suites**
   - `authentication-tests.js`: Login, OTP verification, logout flows
   - `dashboard-navigation-tests.js`: Navigation and UI elements
   - `messaging-tests.js`: Thread creation, messaging functionality
   - `patient-cases-tests.js`: Patient case management
   - `research-tests.js`: Research feature testing
   - `quick-facts-tests.js`: Quick facts functionality
   - `error-handling-tests.js`: Error scenarios and edge cases

3. **Test Runner** (`run-all-tests.js`)
   - Orchestrates test execution
   - Generates comprehensive reports
   - Supports selective test suite execution

## Key Features

### 1. Comprehensive Logging
- Color-coded console output (INFO, <PERSON>UCCESS, WARN, ERROR, DEBUG)
- Detailed test execution logs
- Screenshot capture on failures
- Network request/response monitoring

### 2. Smart Wait Mechanisms
- Element visibility waiting
- Network idle detection
- Custom condition waiting
- Configurable timeouts

### 3. Error Handling
- Automatic screenshot capture on failures
- Detailed error messages
- Console error monitoring
- Network error detection

### 4. Reporting
- JSON format test reports
- Test execution metrics
- Success/failure statistics
- Detailed error information

## Test Configuration

### Environment Variables
```bash
# Test phone number (required)
TEST_PHONE="+************"

# Optional configurations
TEST_OTP="123456"              # For automated OTP entry
TEST_HEADLESS="false"          # Run tests in headed mode
TEST_BASE_URL="http://localhost:3000"
TEST_SCREENSHOT_DIR="./test-results/screenshots"
```

### Test Data
- Phone Number: Always use `+************` (as specified by user)
- Test accounts are created/managed automatically
- Mock data for patient cases, research queries, etc.

## Running Tests

### Prerequisites
1. Doctor Dashboard running on `http://localhost:3000`
2. Node.js and npm installed
3. All dependencies installed (`npm install`)

### Full Test Suite
```bash
npm run test:e2e
```

### Individual Test Suites
```bash
# Run specific test suite
node run-all-tests.js authentication

# Run multiple suites
node run-all-tests.js authentication messaging
```

### With Custom Configuration
```bash
TEST_HEADLESS=false TEST_OTP=123456 npm run test:e2e
```

## Test Coverage

### 1. Authentication Flow
- Phone number validation
- OTP request and verification
- Login success/failure scenarios
- Session management
- Logout functionality

### 2. Dashboard Navigation
- Main navigation menu
- Thread list navigation
- Search functionality
- UI responsiveness

### 3. Messaging Features
- Create new threads (all types)
- Send/receive messages
- File attachments
- Message formatting (tables, markdown)
- Thread management (delete, archive)

### 4. Patient Cases
- Create patient case
- Add symptoms and history
- Get AI recommendations
- Case management

### 5. Research Features
- Research query submission
- Evidence-based responses
- Citation handling
- Research history

### 6. Quick Facts
- Quick medical queries
- Fact verification
- Response accuracy

### 7. Error Handling
- Network failures
- Invalid inputs
- Session timeouts
- API errors

## Common Issues and Solutions

### 1. Rate Limiting
**Issue**: "Too many OTP requests" error
**Solution**: Wait 5-10 minutes between test runs or use different phone numbers

### 2. Timing Issues
**Issue**: Tests fail due to slow network/rendering
**Solution**: Increase timeout values in `TestUtils.waitForElement()`

### 3. Authentication Failures
**Issue**: Cannot proceed past login
**Solution**: 
- Verify Gatekeeper service is accessible
- Check API parameter compatibility
- Ensure phone number is whitelisted

### 4. Element Not Found
**Issue**: Puppeteer cannot find elements
**Solution**:
- Update selectors if UI has changed
- Add explicit waits before interactions
- Check if element is in viewport

## Best Practices

1. **Test Isolation**: Each test should be independent
2. **Cleanup**: Always cleanup created data after tests
3. **Waits**: Use smart waits instead of fixed delays
4. **Assertions**: Make specific, meaningful assertions
5. **Logging**: Log important steps for debugging
6. **Screenshots**: Capture state at critical points

## Extending the Framework

### Adding New Test Suites
1. Create new file in test directory
2. Extend `TestSuite` class
3. Implement required methods
4. Register in `run-all-tests.js`

### Adding New Utilities
1. Add methods to `TestUtils` class
2. Follow existing patterns for consistency
3. Document parameters and return values

## Maintenance

### Regular Updates Needed
1. Selectors when UI changes
2. Test data for new features
3. API endpoints if backend changes
4. Timeout values based on performance

### Monthly Review
1. Remove obsolete tests
2. Update documentation
3. Review test coverage
4. Performance optimization

## Debugging Failed Tests

1. **Check Screenshots**: Located in `test-results/screenshots/`
2. **Review Logs**: Detailed execution logs in console
3. **Run Individual Test**: Isolate failing test
4. **Headed Mode**: Run with `TEST_HEADLESS=false`
5. **Network Tab**: Monitor API calls in DevTools

## Integration with CI/CD

The framework is designed to integrate with CI/CD pipelines:

```yaml
# Example GitHub Actions configuration
- name: Run E2E Tests
  run: |
    npm start &
    sleep 10
    npm run test:e2e
  env:
    TEST_PHONE: ${{ secrets.TEST_PHONE }}
    TEST_HEADLESS: true
```

## Performance Considerations

1. **Parallel Execution**: Not recommended due to shared test phone
2. **Resource Usage**: Close browser between test suites
3. **Cleanup**: Remove old screenshots and reports
4. **Optimization**: Use page.$eval() instead of evaluate() when possible

## Future Enhancements

1. **Visual Regression Testing**: Add screenshot comparison
2. **Performance Metrics**: Track page load times
3. **Accessibility Testing**: Add WCAG compliance checks
4. **Mobile Testing**: Add responsive design tests
5. **API Testing**: Integrate API-level tests

## Contact and Support

For issues or questions about the E2E testing framework:
1. Check this documentation first
2. Review existing test examples
3. Check console logs for detailed errors
4. Capture screenshots of failures

Remember: The phone number `+************` should ALWAYS be used for testing unless explicitly instructed otherwise.