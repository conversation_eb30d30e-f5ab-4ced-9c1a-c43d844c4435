# Doctor Dashboard - Local Development Setup

## Quick Setup (macOS)

Run the automated setup script:

```bash
./setup.sh
```

This script will:
- ✅ Install PostgreSQL 14 (if not installed)
- ✅ Create the `doctor_dashboard` database
- ✅ Set up all required tables and schema
- ✅ Create `.env.local` with proper configuration
- ✅ Install Node.js dependencies
- ✅ Create default admin user
- ✅ Test database connection

## Manual Setup

If you prefer manual setup or encounter issues:

### 1. Prerequisites

- **Node.js 18+**: [Download here](https://nodejs.org/)
- **PostgreSQL 14+**: Install via Homebrew
  ```bash
  brew install postgresql@14
  brew services start postgresql@14
  ```

### 2. Database Setup

```bash
# Create database
createdb doctor_dashboard

# Run schema setup
psql -d doctor_dashboard -f database-setup.sql
```

### 3. Environment Configuration

Create `.env.local`:

```bash
# Database
DATABASE_URL=postgresql://your_username@localhost:5432/doctor_dashboard
DATABASE_SSL=false

# JWT Secrets
JWT_ACCESS_SECRET=your-secret-key-here
JWT_REFRESH_SECRET=your-refresh-secret-here
JWT_ACCESS_EXPIRES_IN=1h
JWT_REFRESH_EXPIRES_IN=7d

# Development
NODE_ENV=development
```

### 4. Install Dependencies

```bash
npm install
```

### 5. Start Development Server

```bash
npm run dev
```

## Testing the Application

### Registration Flow
1. Go to http://localhost:3000
2. Click "Register" tab
3. Enter your details and phone number
4. You'll receive a real SMS with OTP
5. Complete registration (account will be pending approval)

### Login Flow  
1. Use test number: `+************`
2. OTP is fixed at: `123456`
3. Or use any approved phone number with real OTP

### Admin Functions
- Default admin: `+**********`
- Approve pending registrations via database or API

## Database Schema

The application uses these main tables:
- `users` - Doctor accounts and admin users
- `verification_token` - OTP tokens for phone verification
- `web_chat_sessions` - Active JWT sessions
- `refresh_tokens` - Refresh token management

## API Endpoints

### Authentication
- `POST /api/auth/request-otp` - Send OTP via SMS
- `POST /api/auth/verify-and-register` - Register new doctor
- `POST /api/auth/login` - Request login OTP  
- `POST /api/auth/verify-login` - Verify login and get tokens
- `GET /api/auth/me` - Get current user info
- `POST /api/auth/refresh` - Refresh access token
- `POST /api/auth/logout` - Logout user

### Admin
- `GET /api/admin/pending-registrations` - List pending doctors
- `POST /api/admin/review-registration` - Approve/reject doctors

### System
- `GET /api/health` - Health check and system status

## Features

### ✅ Implemented
- **Real OTP Integration**: Gatekeeper SMS service
- **Doctor Registration**: Complete signup with approval workflow
- **JWT Authentication**: Access and refresh tokens
- **File Attachments**: Up to 5 medical files per message
- **Markdown Support**: Tables and rich text rendering
- **Admin Approval**: Manual review of doctor registrations

### 🔐 Security
- Phone number verification via SMS
- JWT-based authentication
- Role-based access control
- Rate limiting protection
- Input validation and sanitization

## Troubleshooting

### Database Issues
```bash
# Check if PostgreSQL is running
brew services list | grep postgresql

# Start PostgreSQL
brew services start postgresql@14

# Test connection
psql -d doctor_dashboard -c "SELECT 'Connected' as status;"
```

### Environment Issues
```bash
# Check environment variables
cat .env.local

# Verify Node.js version
node --version  # Should be 18+
```

### OTP Issues
- For testing: Use `+************` with OTP `123456`
- For real numbers: Check SMS for actual OTP code
- Rate limits: Wait a few minutes between requests

## Development Workflow

1. **Start server**: `npm run dev`
2. **Test APIs**: Use provided test scripts
3. **Database shell**: `psql -d doctor_dashboard`
4. **View logs**: Check console output
5. **Reset data**: Clear test users via database

## Production Notes

- Replace Gatekeeper staging URL with production
- Use secure JWT secrets
- Configure proper SSL/TLS
- Set up monitoring and logging
- Implement proper rate limiting
- Use environment-specific configurations