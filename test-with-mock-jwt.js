const axios = require('axios');
const fs = require('fs');
const { v4: uuidv4 } = require('uuid');

// Configuration
const BASE_URL = 'https://gatekeeper-staging.getbeyondhealth.com';
const TENANT = 'practitioner-dashboard';
const AUGUST_TOKEN = 'm}0/m9ZL`k{|Mz:Ca{7k8PF(gJV"Xz/j';
const TEST_PHONE = '+************';

// Mock JWT for testing (since we can't get real OTP in automated tests)
// This is a properly formatted JWT but with test payload
const MOCK_JWT = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c';

// Test state
let testResults = {
  timestamp: new Date().toISOString(),
  baseUrl: BASE_URL,
  tenant: TENANT,
  testPhone: TEST_PHONE,
  apis: {}
};

// Helper to log and save results
function logResult(apiName, endpoint, method, request, response, error = null) {
  const result = {
    endpoint,
    method,
    timestamp: new Date().toISOString(),
    request: {
      headers: request.headers || {},
      params: request.params || {},
      body: request.body || {}
    },
    response: response ? {
      status: response.status,
      statusText: response.statusText,
      data: response.data,
      headers: response.headers
    } : null,
    error: error ? {
      message: error.message,
      code: error.code,
      status: error.response?.status,
      statusText: error.response?.statusText,
      data: error.response?.data
    } : null,
    success: !error
  };

  testResults.apis[apiName] = result;

  console.log(`\n${'='.repeat(70)}`);
  console.log(`API: ${apiName}`);
  console.log(`Method: ${method} ${endpoint}`);
  console.log(`Status: ${!error ? '✅ SUCCESS' : '❌ FAILED'}`);
  
  if (!error) {
    console.log(`Response Status: ${response.status} ${response.statusText}`);
    console.log('Response Data:', JSON.stringify(response.data, null, 2));
  } else {
    console.log('Error:', error.message);
    if (error.response?.data) {
      console.log('Error Response:', JSON.stringify(error.response.data, null, 2));
    }
  }
  console.log('='.repeat(70));
}

// Test 1: Mock OTP Verification Response
async function test1_MockVerifyOTP() {
  console.log('\n📝 MOCK: OTP Verification Response Structure');
  
  // This is what a successful verify-otp response looks like
  const mockResponse = {
    success: true,
    accessToken: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    refreshToken: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    expiresIn: 3600,
    user: {
      id: "user-uuid",
      phone: TEST_PHONE,
      role: "DOCTOR",
      access: true
    }
  };
  
  console.log('Expected verify-otp response:', JSON.stringify(mockResponse, null, 2));
  testResults.apis['Mock Verify OTP Response'] = { 
    note: 'Expected response structure',
    response: mockResponse 
  };
  
  return true;
}

// Test 2: Test All Message Types
async function test2_TestAllMessageTypes() {
  const endpoint = `/c/${TENANT}/webhook`;
  const messageTypes = [
    {
      name: 'Text Only Message',
      body: {
        dialogueId: uuidv4(),
        dialogueType: 'patient-case',
        text: 'Patient presents with fever and headache',
        providerMessageId: uuidv4(),
        sender: 'human',
        source: 'WEB',
        phoneNumber: TEST_PHONE,
        timestamp: Date.now(),
        requestId: uuidv4()
      }
    },
    {
      name: 'Single Image Attachment',
      body: {
        dialogueId: uuidv4(),
        dialogueType: 'patient-case',
        text: 'X-ray image attached',
        providerMessageId: uuidv4(),
        attachment: 'https://example.blob.core.windows.net/container/xray.png',
        fileExtension: '.png',
        messageType: 'image',
        sender: 'human',
        source: 'WEB',
        phoneNumber: TEST_PHONE,
        timestamp: Date.now(),
        requestId: uuidv4()
      }
    },
    {
      name: 'Single PDF Attachment',
      body: {
        dialogueId: uuidv4(),
        dialogueType: 'research',
        text: 'Research paper attached',
        providerMessageId: uuidv4(),
        attachment: 'https://example.blob.core.windows.net/container/paper.pdf',
        fileExtension: '.pdf',
        messageType: 'pdf',
        sender: 'human',
        source: 'WEB',
        phoneNumber: TEST_PHONE,
        timestamp: Date.now(),
        requestId: uuidv4()
      }
    },
    {
      name: 'Multiple Mixed Attachments',
      body: {
        dialogueId: uuidv4(),
        dialogueType: 'research',
        text: 'Multiple files attached',
        providerMessageId: uuidv4(),
        attachment: [
          {
            url: 'https://example.blob.core.windows.net/container/image1.png',
            fileExtension: '.png',
            messageType: 'image'
          },
          {
            url: 'https://example.blob.core.windows.net/container/doc1.pdf',
            fileExtension: '.pdf',
            messageType: 'pdf'
          },
          {
            url: 'https://example.blob.core.windows.net/container/image2.jpg',
            fileExtension: '.jpg',
            messageType: 'image'
          }
        ],
        sender: 'human',
        source: 'WEB',
        phoneNumber: TEST_PHONE,
        timestamp: Date.now(),
        requestId: uuidv4()
      }
    }
  ];

  const results = {};

  for (const msgType of messageTypes) {
    const request = {
      headers: {
        'Content-Type': 'application/json'
      },
      body: msgType.body
    };

    try {
      const response = await axios.post(
        `${BASE_URL}${endpoint}`,
        request.body,
        { headers: request.headers }
      );
      
      results[msgType.name] = {
        success: true,
        request: msgType.body,
        response: response.data
      };
      
      console.log(`\n✅ ${msgType.name}: Success`);
      console.log('Response:', response.data);
    } catch (error) {
      results[msgType.name] = {
        success: false,
        request: msgType.body,
        error: error.response?.data || error.message
      };
      
      console.log(`\n❌ ${msgType.name}: Failed`);
      console.log('Error:', error.response?.data || error.message);
    }
  }

  testResults.apis['All Message Types'] = results;
  return Object.values(results).every(r => r.success);
}

// Test 3: Test Error Cases
async function test3_TestErrorCases() {
  const endpoint = `/c/${TENANT}/webhook`;
  const errorCases = [
    {
      name: 'Missing Required Fields',
      body: {
        dialogueId: uuidv4(),
        // Missing required fields like text, providerMessageId, etc.
      }
    },
    {
      name: 'Invalid Dialogue Type',
      body: {
        dialogueId: uuidv4(),
        dialogueType: 'invalid-type',
        text: 'Test message',
        providerMessageId: uuidv4(),
        sender: 'human',
        source: 'WEB',
        phoneNumber: TEST_PHONE,
        timestamp: Date.now(),
        requestId: uuidv4()
      }
    },
    {
      name: 'Invalid Phone Format',
      body: {
        dialogueId: uuidv4(),
        dialogueType: 'patient-case',
        text: 'Test message',
        providerMessageId: uuidv4(),
        sender: 'human',
        source: 'WEB',
        phoneNumber: '**********', // Missing + and country code
        timestamp: Date.now(),
        requestId: uuidv4()
      }
    }
  ];

  const results = {};

  for (const errorCase of errorCases) {
    const request = {
      headers: {
        'Content-Type': 'application/json'
      },
      body: errorCase.body
    };

    try {
      const response = await axios.post(
        `${BASE_URL}${endpoint}`,
        request.body,
        { headers: request.headers }
      );
      
      results[errorCase.name] = {
        success: true,
        note: 'Expected to fail but succeeded',
        response: response.data
      };
    } catch (error) {
      results[errorCase.name] = {
        success: false,
        expectedError: true,
        status: error.response?.status,
        error: error.response?.data || error.message
      };
    }
  }

  console.log(`\n${'='.repeat(70)}`);
  console.log('Error Case Testing Results:');
  console.log(JSON.stringify(results, null, 2));
  console.log('='.repeat(70));

  testResults.apis['Error Cases'] = results;
  return true;
}

// Test 4: Mock GET Endpoints Response
async function test4_MockGetEndpoints() {
  console.log('\n📝 MOCK: Expected GET Endpoint Response Structures');
  
  // Mock response for get-chats-by-dialogueId with dialogue_id=""
  const mockAllDialogues = {
    success: true,
    dialogues: [
      {
        id: "dialogue-uuid-1",
        type: "patient-case",
        lastMessage: {
          text: "Patient case discussion...",
          timestamp: "2025-06-25T08:00:00Z",
          sender: "human"
        },
        createdAt: "2025-06-24T10:00:00Z",
        updatedAt: "2025-06-25T08:00:00Z"
      },
      {
        id: "dialogue-uuid-2",
        type: "research",
        lastMessage: {
          text: "Research query about...",
          timestamp: "2025-06-24T15:00:00Z",
          sender: "ai"
        },
        createdAt: "2025-06-24T14:00:00Z",
        updatedAt: "2025-06-24T15:00:00Z"
      }
    ],
    total: 2,
    limit: 100
  };
  
  // Mock response for get-chats-by-dialogueId with specific dialogue_id
  const mockDialogueMessages = {
    success: true,
    dialogueId: "dialogue-uuid-1",
    messages: [
      {
        id: "msg-1",
        sender: "human",
        text: "45 year old male with chest pain",
        timestamp: "2025-06-25T08:00:00Z",
        attachments: []
      },
      {
        id: "msg-2",
        sender: "ai",
        text: "I'll help you assess this patient...",
        timestamp: "2025-06-25T08:00:30Z",
        attachments: []
      }
    ],
    hasMore: false,
    limit: 30
  };
  
  console.log('\nExpected response for GET all dialogues:');
  console.log(JSON.stringify(mockAllDialogues, null, 2));
  
  console.log('\nExpected response for GET messages by dialogue ID:');
  console.log(JSON.stringify(mockDialogueMessages, null, 2));
  
  testResults.apis['Mock GET Responses'] = {
    'Get All Dialogues': mockAllDialogues,
    'Get Messages by Dialogue ID': mockDialogueMessages
  };
  
  return true;
}

// Main test runner
async function runAllTests() {
  console.log(`
${'='.repeat(70)}
🧪 GATEKEEPER API COMPREHENSIVE TEST SUITE
${'='.repeat(70)}
Base URL: ${BASE_URL}
Tenant: ${TENANT}
Test Phone: ${TEST_PHONE}
Timestamp: ${new Date().toISOString()}
${'='.repeat(70)}

Note: This test includes mock responses for endpoints that require
real JWT authentication, to document expected response structures.
${'='.repeat(70)}
`);

  const tests = [
    { name: 'Mock Verify OTP Response', fn: test1_MockVerifyOTP },
    { name: 'Test All Message Types', fn: test2_TestAllMessageTypes },
    { name: 'Test Error Cases', fn: test3_TestErrorCases },
    { name: 'Mock GET Endpoints Response', fn: test4_MockGetEndpoints }
  ];

  const summary = {
    total: tests.length,
    passed: 0,
    failed: 0
  };

  for (const test of tests) {
    console.log(`\n🧪 Running: ${test.name}...`);
    const success = await test.fn();
    
    if (success) {
      summary.passed++;
    } else {
      summary.failed++;
    }
    
    // Small delay between tests
    await new Promise(resolve => setTimeout(resolve, 1000));
  }

  // Save results
  const resultsFile = `api-test-comprehensive-${Date.now()}.json`;
  fs.writeFileSync(resultsFile, JSON.stringify(testResults, null, 2));

  console.log(`

${'='.repeat(70)}
📊 TEST SUMMARY
${'='.repeat(70)}
Total Tests: ${summary.total}
Passed: ${summary.passed}
Failed: ${summary.failed}
Results saved to: ${resultsFile}
${'='.repeat(70)}
`);

  return testResults;
}

// Run tests
runAllTests()
  .then(results => {
    console.log('\n✅ Test suite completed');
    process.exit(0);
  })
  .catch(error => {
    console.error('\n❌ Test suite failed:', error);
    process.exit(1);
  });