# Multi-stage build for optimized production image
FROM python:3.11-slim as builder

# Install system dependencies
RUN apt-get update && apt-get install -y \
    build-essential \
    && rm -rf /var/lib/apt/lists/*

# Set working directory
WORKDIR /app

# Copy requirements and install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir --user -r requirements.txt

# Production stage
FROM python:3.11-slim

# Install runtime dependencies
RUN apt-get update && apt-get install -y \
    procps \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Create non-root user
RUN useradd --create-home --shell /bin/bash app

# Set working directory
WORKDIR /app

# Copy Python packages from builder stage
COPY --from=builder /root/.local /home/<USER>/.local

# Copy application code
COPY main.py .
COPY download_model.py .

# Create models directory
RUN mkdir -p models

# Change ownership to app user
RUN chown -R app:app /app

# Add local Python packages to PATH
ENV PATH=/home/<USER>/.local/bin:$PATH

# Switch to non-root user
USER app

COPY ./models/cross_encoder_model /app/models/cross_encoder_model

# Download model during build (optional - can be done at runtime)
# RUN python download_model.py

# Environment variables
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1
ENV USE_LOCAL_MODEL=true
ENV LOCAL_MODEL_PATH=/app/models/cross_encoder_model

# Expose port
EXPOSE 8002

# # Health check
# HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
#     CMD curl -f http://localhost:8002/health || exit 1

# Run the application
CMD ["python", "main.py"]
