#!/bin/bash

# Optimized startup script for 3GB RAM utilization on m8g.medium
echo "============================================================"
echo "🚀 Starting Cross-Encoder Service (3GB RAM Optimization)"
echo "============================================================"

# Set aggressive memory optimization environment variables
export PYTHONUNBUFFERED=1
export TOKENIZERS_PARALLELISM=true
export OMP_NUM_THREADS=8
export MKL_NUM_THREADS=8
export PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:512

# Memory management settings
export PYTHONMALLOC=malloc
export MALLOC_ARENA_MAX=4

# PyTorch optimizations
export TORCH_SHOW_CPP_STACKTRACES=1
export PYTORCH_JIT=1

# Custom configuration for 3GB target
export CACHE_SIZE=5000
export MAX_BATCH_SIZE=256
export MAX_TEXT_LENGTH=512
export NUM_WORKERS=6
export 
export MEMORY_POOL_SIZE=2048
export ENABLE_MEMORY_MAPPING=true
export PRECOMPUTE_EMBEDDINGS=true

# Check system resources
echo "📊 System Resources:"
echo "  Total Memory: $(free -h | awk '/^Mem:/ {print $2}' 2>/dev/null || echo 'N/A')"
echo "  Available Memory: $(free -h | awk '/^Mem:/ {print $7}' 2>/dev/null || echo 'N/A')"
echo "  CPU Cores: $(nproc 2>/dev/null || sysctl -n hw.ncpu 2>/dev/null || echo 'N/A')"
echo "  Target Memory Usage: 3GB"

echo ""
echo "⚙️  Performance Configuration:"
echo "  Cache Size: $CACHE_SIZE entries"
echo "  Max Batch Size: $MAX_BATCH_SIZE documents"
echo "  Max Text Length: $MAX_TEXT_LENGTH chars"
echo "  Worker Threads: $NUM_WORKERS"
echo "  PyTorch Threads: $OMP_NUM_THREADS"
echo "  Memory Pool: ${MEMORY_POOL_SIZE}MB"

# Download model if needed
if [ ! -d "models" ] || [ -z "$(ls -A models 2>/dev/null)" ]; then
    echo ""
    echo "📥 Downloading model (first run)..."
    python download_model.py
fi

# Pre-warm the system
echo ""
echo "🔥 Pre-warming system..."
python -c "
import torch
import gc
torch.set_num_threads($OMP_NUM_THREADS)
torch.set_num_interop_threads(4)
print('PyTorch warmed up')
gc.collect()
print('Memory pre-optimized')
"

echo ""
echo "🏃 Starting optimized service on port 8002..."
echo "   Health Check: http://localhost:8002/health"
echo "   Performance: http://localhost:8002/admin/performance-metrics"
echo "   Documentation: http://localhost:8002/docs"

# Start with memory monitoring
gunicorn -k uvicorn.workers.UvicornWorker main:app --bind 0.0.0.0:8002 --workers $NUM_WORKERS --threads $OMP_NUM_THREADS --timeout 120 &
SERVICE_PID=$!

# Wait a moment for service to start
sleep 3

# Check if service started successfully
if kill -0 $SERVICE_PID 2>/dev/null; then
    echo ""
    echo "✅ Service started successfully (PID: $SERVICE_PID)"
    echo ""
    echo "💡 Performance Monitoring Commands:"
    echo "   curl http://localhost:8002/admin/performance-metrics"
    echo "   curl http://localhost:8002/admin/cache-stats"
    echo "   curl -X POST http://localhost:8002/admin/optimize-memory"
    
    # Keep the service running
    wait $SERVICE_PID
else
    echo ""
    echo "❌ Failed to start service"
    exit 1
fi
