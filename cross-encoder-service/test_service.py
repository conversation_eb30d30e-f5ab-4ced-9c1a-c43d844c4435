from flashrank import Ranker, RerankRequest

ranker = Ranker(model_name="ms-marco-MiniLM-L-12-v2")

# Metadata is optional, Id can be your DB ids from your retrieval stage or simple numeric indices.

import json

def load_json_input(path):
    with open(path, "r") as f:
        return json.load(f)

def convert_to_queries_and_passages(data):
    query = data["queries"][0] if data.get("queries") else ""
    passages = []
    for idx, doc in enumerate(data.get("documents", []), 1):
        passages.append({
            "id": idx,
            "text": doc,
            "meta": {}
        })
    return query, passages

input_data = load_json_input("/Users/<USER>/Workspace/doctor-dashboard/python-research-service/debug_payloads/rank_batch_payload_20250712_205832_707177_3b195582.json")
query, passages = convert_to_queries_and_passages(input_data)

rerankrequest = RerankRequest(query=query, passages=passages)
results = ranker.rerank(rerankrequest)
print(results)
