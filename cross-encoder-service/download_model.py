#!/usr/bin/env python3
"""
<PERSON>ript to download the cross-encoder model locally
"""
import os
import sys
from sentence_transformers import CrossEncoder

def download_model():
    """Download the cross-encoder model to local directory"""
    model_name = os.getenv("CROSS_ENCODER_MODEL", "cross-encoder/ms-marco-MiniLM-L-12-v2")
    local_model_path = "./models/cross_encoder_model"
    
    print(f"Downloading model: {model_name}")
    print(f"Local path: {local_model_path}")
    
    # Create models directory if it doesn't exist
    os.makedirs(os.path.dirname(local_model_path), exist_ok=True)
    
    try:
        # Download and save model
        model = CrossEncoder(model_name)
        model.save(local_model_path)
        print(f"Model successfully downloaded to {local_model_path}")
        
        # Test the model
        test_score = model.predict([("test query", "test document")])
        print(f"Model test successful. Test score: {test_score}")
        
    except Exception as e:
        print(f"Error downloading model: {e}")
        sys.exit(1)

if __name__ == "__main__":
    download_model()
