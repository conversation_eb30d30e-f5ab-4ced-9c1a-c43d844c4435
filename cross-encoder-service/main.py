import os
import gc
import time
import hashlib
from datetime import datetime
from typing import List, Dict, Any, Optional, Tuple
from fastapi import Fast<PERSON><PERSON>, HTTPException, status
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field
import uvicorn
from sentence_transformers import CrossEncoder
import torch
import numpy as np
from functools import lru_cache
import logging
import asyncio
import concurrent.futures
from multiprocessing import Pool, cpu_count
import math
from tqdm import tqdm
import threading
from collections import defaultdict

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Pydantic models
class RankingRequest(BaseModel):
    query: str = Field(..., description="Query text")
    documents: List[str] = Field(..., description="List of document texts to rank")
    top_k: int = Field(10, description="Number of top documents to return")

class BatchRankingRequest(BaseModel):
    queries: Optional[List[str]] = Field(None, description="List of queries")
    subqueries: Optional[List[str]] = Field(None, description="List of subqueries (alternative to queries)")
    documents: List[str] = Field(..., description="List of document texts to rank")
    top_k: int = Field(10, description="Number of top documents to return per query")
    # Optimization parameters
    deduplicate_documents: bool = Field(True, description="Remove duplicate documents before processing")
    max_text_length: Optional[int] = Field(None, description="Override default text truncation length")
    batch_size: Optional[int] = Field(None, description="Override default batch size for processing")
    
    def get_query_list(self) -> List[str]:
        """Get the list of queries from either queries or subqueries field"""
        if self.queries:
            return self.queries
        elif self.subqueries:
            return self.subqueries
        else:
            raise ValueError("Either 'queries' or 'subqueries' must be provided")

class RankingResponse(BaseModel):
    scores: List[float] = Field(..., description="Ranking scores for documents")
    ranked_indices: List[int] = Field(..., description="Indices of documents sorted by score")
    processing_time: float = Field(..., description="Processing time in seconds")
    # NEW: Performance metrics
    performance_metrics: Dict[str, Any] = Field(default_factory=dict, description="Performance optimization metrics")

class BatchRankingResponse(BaseModel):
    query_results: Dict[str, Dict[str, Any]] = Field(..., description="Results per query")
    processing_time: float = Field(..., description="Processing time in seconds")
    optimization_stats: Dict[str, Any] = Field(default_factory=dict, description="Performance optimization statistics")

class HealthResponse(BaseModel):
    status: str = Field(..., description="Service status")
    model_loaded: bool = Field(..., description="Whether model is loaded")
    memory_usage: Dict[str, Any] = Field(..., description="Memory usage info")
    timestamp: str = Field(..., description="Response timestamp")
    model_warmup_status: str = Field(..., description="Model warmup status")

# Configuration optimized for m8g.medium (8 vCPUs, 32GB RAM)
class Config:
    CROSS_ENCODER_MODEL = os.getenv("CROSS_ENCODER_MODEL", "cross-encoder/ms-marco-MiniLM-L-12-v2")
    LOCAL_MODEL_PATH = os.getenv("LOCAL_MODEL_PATH", "./models/cross_encoder_model")
    USE_LOCAL_MODEL = os.getenv("USE_LOCAL_MODEL", "true").lower() == "true"
    USE_FP16 = os.getenv("USE_FP16", "false").lower() == "true"  # Disable FP16 for CPU
    
    # Optimized cache sizes for m8g.medium
    CACHE_SIZE = int(os.getenv("CACHE_SIZE", "2000"))  # Larger cache
    MAX_BATCH_SIZE = int(os.getenv("MAX_BATCH_SIZE", "64"))  # Optimized for single queries
    MAX_TEXT_LENGTH = int(os.getenv("MAX_TEXT_LENGTH", "256"))
    ENABLE_MEMORY_CLEANUP = os.getenv("ENABLE_MEMORY_CLEANUP", "false").lower() == "true"  # Reduce cleanup overhead
    
    # CPU optimization for m8g.medium
    NUM_THREADS = int(os.getenv("NUM_THREADS", "8"))  # Match vCPU count
    USE_PARALLEL_PROCESSING = os.getenv("USE_PARALLEL_PROCESSING", "false").lower() == "true"  # Disable for single queries
    PARALLEL_THRESHOLD = int(os.getenv("PARALLEL_THRESHOLD", "100"))
    
    # Advanced optimization settings
    ENABLE_DOCUMENT_DEDUPLICATION = os.getenv("ENABLE_DOCUMENT_DEDUPLICATION", "true").lower() == "true"
    ENABLE_QUERY_CACHING = os.getenv("ENABLE_QUERY_CACHING", "true").lower() == "true"
    AGGRESSIVE_BATCH_PROCESSING = os.getenv("AGGRESSIVE_BATCH_PROCESSING", "true").lower() == "true"
    
    # NEW: Single query optimizations
    ENABLE_MODEL_WARMUP = os.getenv("ENABLE_MODEL_WARMUP", "true").lower() == "true"
    WARMUP_QUERIES = int(os.getenv("WARMUP_QUERIES", "5"))
    ENABLE_FAST_TOKENIZATION = os.getenv("ENABLE_FAST_TOKENIZATION", "true").lower() == "true"
    SINGLE_QUERY_BATCH_SIZE = int(os.getenv("SINGLE_QUERY_BATCH_SIZE", "32"))  # Smaller batch for single queries
    PRECOMPILE_MODEL = os.getenv("PRECOMPILE_MODEL", "true").lower() == "true"

config = Config()

# Global model instance and performance state
model = None
model_warmed_up = False
warmup_lock = threading.Lock()
performance_stats = defaultdict(list)

# Caches
document_hash_cache = {}
query_cache = {}
preprocessed_query_cache = {}

def get_memory_usage():
    """Get current memory usage statistics"""
    try:
        import psutil
        process = psutil.Process(os.getpid())
        memory_info = process.memory_info()
        return {
            "rss_mb": round(memory_info.rss / 1024 / 1024, 2),
            "vms_mb": round(memory_info.vms / 1024 / 1024, 2),
            "percent": round(process.memory_percent(), 2)
        }
    except ImportError:
        return {"error": "psutil not available"}

def cleanup_memory():
    """Selective memory cleanup to avoid performance impact"""
    if config.ENABLE_MEMORY_CLEANUP:
        # Only do minimal cleanup
        if torch.cuda.is_available():
            torch.cuda.empty_cache()

@lru_cache(maxsize=500)  # Smaller cache for preprocessing
def preprocess_query_cached(query: str, max_length: int) -> str:
    """Cached query preprocessing for better single query performance"""
    if not query:
        return ""
    
    # Strip whitespace and normalize
    query = query.strip()
    query = ' '.join(query.split())
    
    # Smart truncation at word boundary
    if len(query) > max_length:
        words = query[:max_length].rsplit(' ', 1)
        if len(words) > 1 and len(words[0]) > max_length * 0.8:
            query = words[0]
        else:
            query = query[:max_length]
    
    return query

@lru_cache(maxsize=1000)  # Larger cache for documents
def preprocess_document_cached(document: str, max_length: int) -> str:
    """Cached document preprocessing"""
    if not document:
        return ""
    
    # Strip whitespace and normalize
    document = document.strip()
    document = ' '.join(document.split())
    
    # Smart truncation at sentence boundary
    if len(document) > max_length:
        sentences = document[:max_length].split('. ')
        if len(sentences) > 1 and len('. '.join(sentences[:-1])) > max_length * 0.8:
            document = '. '.join(sentences[:-1]) + '.'
        else:
            document = document[:max_length]
    
    return document

def deduplicate_documents_fast(documents: List[str]) -> Tuple[List[str], Dict[int, int]]:
    """Faster document deduplication using cached hashes"""
    if not config.ENABLE_DOCUMENT_DEDUPLICATION:
        return documents, {i: i for i in range(len(documents))}
    
    seen_hashes = {}
    dedup_docs = []
    original_to_dedup = {}
    
    for i, doc in enumerate(documents):
        doc_clean = doc.strip()
        
        # Use cached hash if available
        if doc_clean in document_hash_cache:
            doc_hash = document_hash_cache[doc_clean]
        else:
            doc_hash = hashlib.md5(doc_clean.encode()).hexdigest()
            document_hash_cache[doc_clean] = doc_hash
        
        if doc_hash in seen_hashes:
            original_to_dedup[i] = seen_hashes[doc_hash]
        else:
            dedup_idx = len(dedup_docs)
            seen_hashes[doc_hash] = dedup_idx
            original_to_dedup[i] = dedup_idx
            dedup_docs.append(doc)
    
    return dedup_docs, original_to_dedup

@lru_cache(maxsize=config.CACHE_SIZE)
def cached_score_pairs(query: str, doc_tuple: tuple) -> tuple:
    """Highly optimized cached scoring for single queries"""
    global model
    if model is None:
        raise RuntimeError("Model not loaded")
    
    try:
        documents = list(doc_tuple)
        
        # Create pairs efficiently
        pairs = [[query, doc] for doc in documents]
        
        # Use optimized batch size for single queries
        batch_size = min(config.SINGLE_QUERY_BATCH_SIZE, len(pairs))
        
        if len(pairs) <= batch_size:
            # Process all at once for small batches
            with torch.no_grad():  # Ensure no gradients for inference
                scores = model.predict(pairs)
        else:
            # Process in optimized batches
            scores = []
            for i in range(0, len(pairs), batch_size):
                batch_pairs = pairs[i:i + batch_size]
                with torch.no_grad():
                    batch_scores = model.predict(batch_pairs)
                if hasattr(batch_scores, 'tolist'):
                    scores.extend(batch_scores.tolist())
                else:
                    scores.extend([float(batch_scores)] if not isinstance(batch_scores, list) else batch_scores)
        
        # Convert to tuple for caching
        if hasattr(scores, 'tolist'):
            scores = scores.tolist()
        elif not isinstance(scores, list):
            scores = [float(scores)]
        
        return tuple(scores)
        
    except Exception as e:
        logger.error(f"Error in cached scoring: {e}")
        return tuple([0.0] * len(doc_tuple))

def warmup_model():
    """Warm up the model with sample queries to achieve optimal performance state"""
    global model, model_warmed_up
    
    if not config.ENABLE_MODEL_WARMUP or model_warmed_up:
        return
    
    with warmup_lock:
        if model_warmed_up:  # Double-check after acquiring lock
            return
        
        logger.info("Starting model warmup for optimal single query performance...")
        
        # Sample queries for warmup
        warmup_queries = [
            "what is machine learning",
            "how does natural language processing work",
            "explain artificial intelligence concepts",
            "document ranking and retrieval systems",
            "cross encoder models for text similarity"
        ][:config.WARMUP_QUERIES]
        
        # Sample documents for warmup
        warmup_docs = [
            "Machine learning is a subset of artificial intelligence that enables computers to learn automatically.",
            "Natural language processing involves computational techniques for analyzing human language.",
            "Artificial intelligence systems can perform tasks that typically require human intelligence.",
            "Information retrieval systems help users find relevant documents from large collections.",
            "Cross-encoder models directly encode query-document pairs for relevance scoring."
        ]
        
        start_time = time.time()
        
        try:
            # Perform warmup scoring to get model into optimal state
            for query in warmup_queries:
                processed_query = preprocess_query_cached(query, config.MAX_TEXT_LENGTH)
                processed_docs = [preprocess_document_cached(doc, config.MAX_TEXT_LENGTH) for doc in warmup_docs]
                
                # Score with different batch sizes to warm up all code paths
                pairs = [[processed_query, doc] for doc in processed_docs]
                
                with torch.no_grad():
                    _ = model.predict(pairs)
            
            warmup_time = time.time() - start_time
            logger.info(f"Model warmup completed in {warmup_time:.3f}s")
            model_warmed_up = True
            
        except Exception as e:
            logger.error(f"Model warmup failed: {e}")

def load_model():
    """Load and optimize the cross-encoder model for m8g.medium instance"""
    global model

    # Determine model path
    if config.USE_LOCAL_MODEL and os.path.exists(config.LOCAL_MODEL_PATH):
        model_path = config.LOCAL_MODEL_PATH
        logger.info(f"Loading local cross-encoder model from: {model_path}")
    else:
        model_path = config.CROSS_ENCODER_MODEL
        logger.info(f"Loading cross-encoder model from HuggingFace: {model_path}")

    try:
        # Load model on CPU with optimizations for m8g.medium
        model = CrossEncoder(model_path, device='cpu')

        # CPU optimizations
        model.model.eval()  # Set to evaluation mode
        
        # Optimize PyTorch for CPU inference on m8g.medium
        torch.set_num_threads(config.NUM_THREADS)
        torch.set_num_interop_threads(2)  # Reduce context switching
        
        # Enable CPU optimizations
        if hasattr(torch.backends.mkldnn, 'enabled'):
            torch.backends.mkldnn.enabled = True
        
        # Precompile model if supported
        if config.PRECOMPILE_MODEL:
            try:
                # JIT compile for better performance
                sample_input = [["sample query", "sample document"]]
                with torch.no_grad():
                    _ = model.predict(sample_input)
                logger.info("Model precompilation completed")
            except Exception as e:
                logger.warning(f"Model precompilation failed: {e}")

        logger.info(f"Model loaded successfully on CPU with {config.NUM_THREADS} threads")
        logger.info(f"Memory usage after loading: {get_memory_usage()}")

        # Warm up the model for optimal performance
        if config.ENABLE_MODEL_WARMUP:
            warmup_model()

    except Exception as e:
        logger.error(f"Failed to load model: {e}")
        raise e

def rank_documents_single_optimized(query: str, documents: List[str], top_k: int = 10) -> tuple:
    """Ultra-optimized single query document ranking"""
    if not documents:
        return [], [], {}
    
    start_time = time.time()
    stats = {
        "original_doc_count": len(documents),
        "model_warmed_up": model_warmed_up,
        "optimization_path": "single_query_optimized"
    }
    
    # Step 1: Fast query preprocessing with caching
    processed_query = preprocess_query_cached(query, config.MAX_TEXT_LENGTH)
    
    # Step 2: Fast document preprocessing and deduplication
    processed_docs = [preprocess_document_cached(doc, config.MAX_TEXT_LENGTH) for doc in documents]
    dedup_docs, original_to_dedup = deduplicate_documents_fast(processed_docs)
    
    stats["deduplicated_doc_count"] = len(dedup_docs)
    stats["deduplication_ratio"] = len(dedup_docs) / len(documents) if documents else 0
    
    # Step 3: Ultra-fast cached scoring
    doc_tuple = tuple(dedup_docs)
    
    try:
        if config.ENABLE_QUERY_CACHING:
            cached_scores = cached_score_pairs(processed_query, doc_tuple)
            dedup_scores = list(cached_scores)
            stats["cache_hit"] = True
        else:
            raise KeyError("Caching disabled")
    except:
        stats["cache_hit"] = False
        
        # Direct scoring without extra batching overhead for single queries
        pairs = [[processed_query, doc] for doc in dedup_docs]
        
        with torch.no_grad():
            scores = model.predict(pairs)
            
        if hasattr(scores, 'tolist'):
            dedup_scores = scores.tolist()
        else:
            dedup_scores = [float(scores)] if not isinstance(scores, list) else scores
    
    # Step 4: Map scores back to original indices (vectorized)
    original_scores = [dedup_scores[original_to_dedup[i]] for i in range(len(documents))]
    
    # Step 5: Fast top-k selection using numpy for better performance
    scores_array = np.array(original_scores)
    if len(scores_array) <= top_k:
        # If we have fewer docs than top_k, return all
        sorted_indices = np.argsort(scores_array)[::-1]
        top_scores = [original_scores[i] for i in sorted_indices]
        top_indices = sorted_indices.tolist()
    else:
        # Use argpartition for O(n) top-k selection
        top_k_indices = np.argpartition(scores_array, -top_k)[-top_k:]
        top_k_indices = top_k_indices[np.argsort(scores_array[top_k_indices])[::-1]]
        top_scores = [original_scores[i] for i in top_k_indices]
        top_indices = top_k_indices.tolist()
    
    stats["processing_time"] = time.time() - start_time
    
    return top_scores, top_indices, stats

# Create FastAPI app
app = FastAPI(
    title="Ultra-Optimized Cross-Encoder Service",
    description="High-performance microservice for document re-ranking optimized for single queries on m8g.medium",
    version="3.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["GET", "POST"],
    allow_headers=["*"],
)

@app.on_event("startup")
async def startup_event():
    """Initialize model on startup"""
    load_model()

@app.get("/health", response_model=HealthResponse)
async def health_check():
    """Health check endpoint with warmup status"""
    return HealthResponse(
        status="healthy" if model is not None else "unhealthy",
        model_loaded=model is not None,
        memory_usage=get_memory_usage(),
        timestamp=datetime.utcnow().isoformat(),
        model_warmup_status="complete" if model_warmed_up else "pending"
    )

@app.post("/rank/single", response_model=RankingResponse)
async def rank_single(request: RankingRequest):
    """Ultra-optimized single query document ranking"""
    if model is None:
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Model not loaded"
        )
    
    if not request.query or not request.query.strip():
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Query cannot be empty"
        )
    
    if not request.documents:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Documents list cannot be empty"
        )
    
    start_time = time.time()
    
    try:
        scores, indices, stats = rank_documents_single_optimized(
            request.query, 
            request.documents, 
            request.top_k
        )
        
        processing_time = time.time() - start_time
        
        # Track performance for monitoring
        performance_stats["single_query_latency"].append(processing_time)
        if len(performance_stats["single_query_latency"]) > 1000:
            performance_stats["single_query_latency"] = performance_stats["single_query_latency"][-500:]
        
        # Calculate throughput
        throughput = len(request.documents) / processing_time if processing_time > 0 else 0
        performance_stats["single_query_throughput"].append(throughput)
        if len(performance_stats["single_query_throughput"]) > 1000:
            performance_stats["single_query_throughput"] = performance_stats["single_query_throughput"][-500:]
        
        stats["throughput_docs_per_sec"] = throughput
        
        return RankingResponse(
            scores=scores,
            ranked_indices=indices,
            processing_time=processing_time,
            performance_metrics=stats
        )
        
    except Exception as e:
        logger.error(f"Error in single ranking: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Ranking failed: {str(e)}"
        )

@app.post("/rank/batch", response_model=BatchRankingResponse)
async def rank_batch(request: BatchRankingRequest):
    """Batch ranking endpoint (keeps existing optimizations)"""
    if model is None:
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Model not loaded"
        )
    
    queries = request.get_query_list()
    
    if not queries:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Queries list cannot be empty"
        )
    
    if not request.documents:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Documents list cannot be empty"
        )
    
    # Use the optimized single query function for each query in batch
    start_time = time.time()
    query_results = {}
    all_stats = {"total_queries": len(queries), "total_documents": len(request.documents)}
    
    try:
        for query in queries:
            if not query or not query.strip():
                continue
            
            scores, indices, query_stats = rank_documents_single_optimized(
                query=query,
                documents=request.documents,
                top_k=request.top_k
            )
            
            query_results[query] = {
                "scores": scores,
                "ranked_indices": indices,
                "top_k": len(scores)
            }
        
        processing_time = time.time() - start_time
        all_stats["total_processing_time"] = processing_time
        
        return BatchRankingResponse(
            query_results=query_results,
            processing_time=processing_time,
            optimization_stats=all_stats
        )
        
    except Exception as e:
        logger.error(f"Error in batch ranking: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Batch ranking failed: {str(e)}"
        )

@app.get("/")
async def root():
    """Root endpoint with optimization info"""
    avg_latency = np.mean(performance_stats["single_query_latency"]) if performance_stats["single_query_latency"] else 0
    avg_throughput = np.mean(performance_stats["single_query_throughput"]) if performance_stats["single_query_throughput"] else 0
    
    return {
        "service": "Ultra-Optimized Cross-Encoder Service",
        "version": "3.0.0",
        "model": config.CROSS_ENCODER_MODEL,
        "status": "running" if model is not None else "loading",
        "target_instance": "m8g.medium (8 vCPUs, 32GB RAM)",
        "model_warmup_status": "complete" if model_warmed_up else "pending",
        "recent_performance": {
            "avg_single_query_latency_ms": round(avg_latency * 1000, 2),
            "avg_throughput_docs_per_sec": round(avg_throughput, 1),
            "samples": len(performance_stats["single_query_latency"])
        },
        "optimizations": {
            "model_warmup": config.ENABLE_MODEL_WARMUP,
            "cached_preprocessing": True,
            "fast_document_deduplication": True,
            "cached_scoring": config.ENABLE_QUERY_CACHING,
            "numpy_topk_selection": True,
            "torch_no_grad": True,
            "cpu_thread_optimization": True,
            "single_query_batch_size": config.SINGLE_QUERY_BATCH_SIZE
        },
        "configuration": {
            "cpu_threads": config.NUM_THREADS,
            "cache_size": config.CACHE_SIZE,
            "max_text_length": config.MAX_TEXT_LENGTH,
            "single_query_batch_size": config.SINGLE_QUERY_BATCH_SIZE
        },
        "endpoints": {
            "health": "/health",
            "single_ranking": "/rank/single", 
            "batch_ranking": "/rank/batch",
            "docs": "/docs",
            "performance_stats": "/admin/performance-stats"
        }
    }

# Performance monitoring endpoints
@app.get("/admin/performance-stats")
async def performance_statistics():
    """Get detailed performance statistics"""
    if not performance_stats["single_query_latency"]:
        return {"message": "No performance data available yet"}
    
    latencies = performance_stats["single_query_latency"]
    throughputs = performance_stats["single_query_throughput"]
    
    return {
        "latency_stats": {
            "count": len(latencies),
            "mean_ms": round(np.mean(latencies) * 1000, 2),
            "median_ms": round(np.median(latencies) * 1000, 2),
            "p95_ms": round(np.percentile(latencies, 95) * 1000, 2),
            "p99_ms": round(np.percentile(latencies, 99) * 1000, 2),
            "min_ms": round(np.min(latencies) * 1000, 2),
            "max_ms": round(np.max(latencies) * 1000, 2)
        },
        "throughput_stats": {
            "mean_docs_per_sec": round(np.mean(throughputs), 1),
            "median_docs_per_sec": round(np.median(throughputs), 1),
            "max_docs_per_sec": round(np.max(throughputs), 1)
        },
        "cache_stats": {
            "query_preprocessing": preprocess_query_cached.cache_info()._asdict(),
            "document_preprocessing": preprocess_document_cached.cache_info()._asdict(),
            "score_caching": cached_score_pairs.cache_info()._asdict()
        },
        "model_status": {
            "warmed_up": model_warmed_up,
            "memory_usage": get_memory_usage()
        }
    }

@app.post("/admin/clear-cache")
async def clear_cache():
    """Clear all caches"""
    cached_score_pairs.cache_clear()
    preprocess_query_cached.cache_clear()
    preprocess_document_cached.cache_clear()
    document_hash_cache.clear()
    query_cache.clear()
    preprocessed_query_cache.clear()
    
    # Reset performance stats
    performance_stats.clear()
    
    cleanup_memory()
    return {"message": "All caches and performance stats cleared", "timestamp": datetime.utcnow().isoformat()}

if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8002,
        reload=False,
        log_level="info",
        workers=1  # Single worker for optimal model sharing
    )
