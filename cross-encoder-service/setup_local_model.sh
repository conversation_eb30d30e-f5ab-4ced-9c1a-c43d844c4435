#!/bin/bash
# Setup script for cross-encoder service local model

set -e

echo "Setting up local cross-encoder model..."

# Create virtual environment if it doesn't exist
if [ ! -d "venv" ]; then
    echo "Creating virtual environment..."
    python3 -m venv venv
fi

# Activate virtual environment
source venv/bin/activate

# Install requirements
echo "Installing requirements..."
pip install -r requirements.txt

# Download model
echo "Downloading cross-encoder model..."
python download_model.py

echo "Local model setup complete!"
echo "Model saved to: ./models/cross_encoder_model"
echo "To use local model, set USE_LOCAL_MODEL=true in environment"
