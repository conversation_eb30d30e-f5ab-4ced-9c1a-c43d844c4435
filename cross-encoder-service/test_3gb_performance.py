#!/usr/bin/env python3

"""
Performance testing script specifically for 3GB RAM optimization
Tests memory utilization, throughput, and latency improvements
"""

import asyncio
import aiohttp
import time
import json
import statistics
from typing import List, Dict

# Test configuration for realistic medical scenarios
TEST_QUERIES = [
    "What are the early symptoms of diabetes mellitus type 2?",
    "How to diagnose and treat acute myocardial infarction?",
    "Clinical management of hypertension in elderly patients",
    "Differential diagnosis of chest pain in emergency department",
    "Treatment protocols for pneumonia in immunocompromised patients",
    "Signs and symptoms of stroke and immediate management",
    "Medication interactions with anticoagulants warfarin",
    "Post-operative care guidelines for cardiac surgery patients",
    "Diagnostic criteria for chronic kidney disease stages",
    "Management of acute diabetic ketoacidosis in adults"
]

# Realistic medical documents (varying sizes)
TEST_DOCUMENTS = [
    "Diabetes mellitus type 2 is characterized by insulin resistance and relative insulin deficiency. Early symptoms include polyuria, polydipsia, unexplained weight loss, fatigue, and blurred vision. Risk factors include obesity, family history, sedentary lifestyle, and ethnic background.",
    
    "Acute myocardial infarction presents with chest pain, shortness of breath, nausea, and diaphoresis. Immediate management includes oxygen, aspirin, nitroglycerin, and urgent revascularization. ECG changes and cardiac enzymes confirm diagnosis.",
    
    "Hypertension management in elderly patients requires careful consideration of comorbidities, drug interactions, and target blood pressure goals. First-line treatments include ACE inhibitors, ARBs, calcium channel blockers, and thiazide diuretics.",
    
    "Chest pain differential diagnosis includes cardiac causes (MI, angina, aortic dissection), pulmonary causes (PE, pneumothorax, pneumonia), GI causes (GERD, esophageal spasm), and musculoskeletal causes (costochondritis).",
    
    "Pneumonia in immunocompromised patients requires broader antibiotic coverage, consideration of atypical pathogens, and aggressive supportive care. Common organisms include Pneumocystis, CMV, and gram-negative bacteria.",
    
    "Stroke symptoms include sudden onset weakness, speech difficulties, visual changes, and coordination problems. Time-sensitive treatments include thrombolytics within 4.5 hours and mechanical thrombectomy within 6 hours.",
    
    "Warfarin interactions occur with many medications including antibiotics, antifungals, NSAIDs, and herbal supplements. Regular INR monitoring and dose adjustments are essential to prevent bleeding or thrombotic complications.",
    
    "Post-operative cardiac surgery care involves hemodynamic monitoring, pain management, respiratory support, anticoagulation, and early mobilization. Common complications include bleeding, arrhythmias, and infection.",
    
    "Chronic kidney disease staging is based on GFR and albuminuria levels. Stage 1: GFR >90, Stage 2: 60-89, Stage 3a: 45-59, Stage 3b: 30-44, Stage 4: 15-29, Stage 5: <15 or dialysis.",
    
    "Diabetic ketoacidosis management includes fluid resuscitation, insulin therapy, electrolyte replacement, and treatment of precipitating factors. Monitor glucose, ketones, pH, and electrolytes closely.",
] * 6  # 60 documents total for larger batch testing

# BASE_URL = "http://localhost:8002"
BASE_URL = "https://442c552ea81f.ngrok-free.app"

async def test_service_health():
    """Test if service is running and get configuration"""
    async with aiohttp.ClientSession() as session:
        try:
            async with session.get(f"{BASE_URL}/health") as response:
                if response.status == 200:
                    return await response.json()
                else:
                    return {"error": f"Health check failed: {response.status}"}
        except Exception as e:
            return {"error": f"Connection failed: {e}"}

async def test_performance_metrics():
    """Get detailed performance metrics from service"""
    async with aiohttp.ClientSession() as session:
        try:
            async with session.get(f"{BASE_URL}/admin/performance-metrics") as response:
                if response.status == 200:
                    return await response.json()
                else:
                    return {"error": f"Metrics failed: {response.status}"}
        except Exception as e:
            return {"error": f"Metrics connection failed: {e}"}

async def test_single_query_performance(session: aiohttp.ClientSession, 
                                      query: str, documents: List[str]) -> Dict:
    """Test single query performance with detailed timing"""
    start_time = time.time()
    
    payload = {
        "query": query,
        "documents": documents,
        "top_k": 10
    }
    
    try:
        async with session.post(f"{BASE_URL}/rank/single", json=payload) as response:
            if response.status == 200:
                result = await response.json()
                latency = time.time() - start_time
                
                return {
                    "success": True,
                    "latency": latency,
                    "documents_processed": len(documents),
                    "results_returned": len(result.get("scores", [])),
                    "throughput_docs_per_sec": len(documents) / latency if latency > 0 else 0,
                    "processing_time": result.get("processing_time", latency)
                }
            else:
                return {"success": False, "error": f"HTTP {response.status}"}
                
    except Exception as e:
        return {"success": False, "error": str(e), "latency": time.time() - start_time}

async def test_batch_performance(session: aiohttp.ClientSession, 
                               queries: List[str], documents: List[str]) -> Dict:
    """Test batch query performance"""
    start_time = time.time()
    
    payload = {
        "queries": queries,
        "documents": documents,
        "top_k": 10,
        "deduplicate_documents": True,
        "batch_size": 256  # Use optimized batch size
    }
    
    try:
        async with session.post(f"{BASE_URL}/rank/batch", json=payload) as response:
            if response.status == 200:
                result = await response.json()
                latency = time.time() - start_time
                total_pairs = len(queries) * len(documents)
                
                return {
                    "success": True,
                    "latency": latency,
                    "queries_processed": len(queries),
                    "documents_processed": len(documents),
                    "total_pairs": total_pairs,
                    "pairs_per_second": total_pairs / latency if latency > 0 else 0,
                    "processing_time": result.get("processing_time", latency),
                    "optimization_stats": result.get("optimization_stats", {}),
                    "results_count": len(result.get("query_results", {}))
                }
            else:
                error_text = await response.text()
                return {"success": False, "error": f"HTTP {response.status}: {error_text}"}
                
    except Exception as e:
        return {"success": False, "error": str(e), "latency": time.time() - start_time}

async def test_concurrent_load(num_concurrent: int = 8) -> Dict:
    """Test concurrent request handling"""
    print(f"🔄 Testing {num_concurrent} concurrent requests...")
    
    async with aiohttp.ClientSession() as session:
        tasks = []
        for i in range(num_concurrent):
            query = TEST_QUERIES[i % len(TEST_QUERIES)]
            docs = TEST_DOCUMENTS[:30]  # Use subset for concurrent test
            task = test_single_query_performance(session, query, docs)
            tasks.append(task)
        
        start_time = time.time()
        results = await asyncio.gather(*tasks)
        total_time = time.time() - start_time
        
        successful = [r for r in results if r.get("success", False)]
        failed = [r for r in results if not r.get("success", False)]
        
        if successful:
            latencies = [r["latency"] for r in successful]
            throughputs = [r.get("throughput_docs_per_sec", 0) for r in successful]
            
            return {
                "total_requests": num_concurrent,
                "successful": len(successful),
                "failed": len(failed),
                "total_time": total_time,
                "avg_latency": statistics.mean(latencies),
                "median_latency": statistics.median(latencies),
                "min_latency": min(latencies),
                "max_latency": max(latencies),
                "avg_throughput": statistics.mean(throughputs),
                "requests_per_second": len(successful) / total_time if total_time > 0 else 0,
                "concurrent_efficiency": (len(successful) / total_time) / (1 / statistics.mean(latencies)) if latencies else 0
            }
        else:
            return {
                "total_requests": num_concurrent,
                "successful": 0,
                "failed": len(failed),
                "errors": [r.get("error", "Unknown") for r in failed[:3]]  # Show first 3 errors
            }

async def test_memory_scaling():
    """Test performance with increasing document counts"""
    print("📈 Testing memory scaling with different document counts...")
    
    document_counts = [10, 25, 50, 100, 150]  # Increased for 3GB testing
    results = {}
    
    async with aiohttp.ClientSession() as session:
        for doc_count in document_counts:
            print(f"   Testing with {doc_count} documents...")
            
            docs = TEST_DOCUMENTS[:doc_count]
            query = TEST_QUERIES[0]
            
            # Test multiple times for consistency
            test_results = []
            for _ in range(3):
                result = await test_single_query_performance(session, query, docs)
                if result.get("success", False):
                    test_results.append(result)
                
                # Brief pause between tests
                await asyncio.sleep(0.5)
            
            if test_results:
                latencies = [r["latency"] for r in test_results]
                throughputs = [r["throughput_docs_per_sec"] for r in test_results]
                
                results[doc_count] = {
                    "avg_latency": statistics.mean(latencies),
                    "min_latency": min(latencies),
                    "max_latency": max(latencies),
                    "avg_throughput": statistics.mean(throughputs),
                    "scaling_efficiency": doc_count / statistics.mean(latencies),
                    "test_runs": len(test_results)
                }
            else:
                results[doc_count] = {"error": "All tests failed"}
    
    return results

async def comprehensive_performance_test():
    print("🚀 3GB RAM Optimization Performance Test")
    print("=" * 60)
    
    # Test 1: Service Health
    print("\n1️⃣  Checking service health...")
    health = await test_service_health()
    if "error" in health:
        print(f"❌ Service not available: {health['error']}")
        return
    
    print(f"✅ Service healthy - Model loaded: {health.get('model_loaded', False)}")
    
    # Test 2: Get performance metrics
    print("\n2️⃣  Getting performance metrics...")
    metrics = await test_performance_metrics()
    if "error" not in metrics:
        opt_status = metrics.get('optimization_status', {})
        print(f"   Current Memory: {opt_status.get('current_memory_gb', 'N/A')}GB")
        print(f"   Memory Utilization: {opt_status.get('memory_utilization', 'N/A')}%")
        print(f"   Performance Level: {opt_status.get('performance_level', 'N/A')}")
    
    # Test 3: Single Query Performance
    print("\n3️⃣  Testing single query performance...")
    async with aiohttp.ClientSession() as session:
        single_result = await test_single_query_performance(
            session, TEST_QUERIES[0], TEST_DOCUMENTS[:50]
        )
        
        if single_result.get("success", False):
            print(f"✅ Single query completed")
            print(f"   Latency: {single_result['latency']:.3f}s")
            print(f"   Throughput: {single_result['throughput_docs_per_sec']:.1f} docs/sec")
            print(f"   Documents processed: {single_result['documents_processed']}")
        else:
            print(f"❌ Single query failed: {single_result.get('error', 'Unknown error')}")
    
    # Test 4: Batch Performance
    print("\n4️⃣  Testing batch query performance...")
    async with aiohttp.ClientSession() as session:
        batch_result = await test_batch_performance(
            session, TEST_QUERIES[:5], TEST_DOCUMENTS[:40]
        )
        
        if batch_result.get("success", False):
            print(f"✅ Batch processing completed")
            print(f"   Latency: {batch_result['latency']:.3f}s")
            print(f"   Pairs per second: {batch_result['pairs_per_second']:.1f}")
            print(f"   Total pairs processed: {batch_result['total_pairs']}")
            
            opt_stats = batch_result.get('optimization_stats', {})
            if opt_stats:
                agg_stats = opt_stats.get('aggregate_stats', {})
                if agg_stats:
                    print(f"   Cache hit rate: {agg_stats.get('cache_hit_rate', 0)*100:.1f}%")
                    print(f"   Deduplication ratio: {agg_stats.get('average_deduplication_ratio', 1)*100:.1f}%")
        else:
            print(f"❌ Batch processing failed: {batch_result.get('error', 'Unknown error')}")
    
    # Test 5: Concurrent Load
    print("\n5️⃣  Testing concurrent load handling...")
    concurrent_result = await test_concurrent_load(8)  # 8 concurrent requests
    
    if concurrent_result.get("successful", 0) > 0:
        print(f"✅ Concurrent test completed")
        print(f"   Successful: {concurrent_result['successful']}/{concurrent_result['total_requests']}")
        print(f"   Average latency: {concurrent_result['avg_latency']:.3f}s")
        print(f"   Requests per second: {concurrent_result['requests_per_second']:.2f}")
        print(f"   Concurrent efficiency: {concurrent_result['concurrent_efficiency']:.2f}")
    else:
        print(f"❌ Concurrent test failed")
        if "errors" in concurrent_result:
            print(f"   Sample errors: {concurrent_result['errors'][:2]}")
    
    # Test 6: Memory Scaling
    print("\n6️⃣  Testing memory scaling...")
    scaling_results = await test_memory_scaling()
    
    print("   Document count -> Latency -> Throughput")
    for doc_count, result in scaling_results.items():
        if "error" not in result:
            print(f"   {doc_count:3d} docs -> {result['avg_latency']:.3f}s -> {result['avg_throughput']:.1f} docs/sec")
        else:
            print(f"   {doc_count:3d} docs -> ERROR")
    
    # Final Performance Summary
    print("\n" + "=" * 60)
    print("📊 PERFORMANCE SUMMARY")
    print("=" * 60)
    
    # Get final system metrics
    final_metrics = await test_performance_metrics()
    if "error" not in final_metrics:
        opt_status = final_metrics.get('optimization_status', {})
        system_metrics = final_metrics.get('system_metrics', {})
        
        print(f"🎯 Memory Utilization:")
        print(f"   Target: 3.0GB")
        print(f"   Current: {opt_status.get('current_memory_gb', 'N/A')}GB")
        print(f"   Utilization: {opt_status.get('memory_utilization', 'N/A')}%")
        print(f"   Performance Level: {opt_status.get('performance_level', 'N/A').upper()}")
        
        print(f"\n🖥️  System Resources:")
        print(f"   Total Memory: {system_metrics.get('total_memory_gb', 'N/A')}GB")
        print(f"   Available: {system_metrics.get('available_memory_gb', 'N/A')}GB")
        print(f"   System Usage: {system_metrics.get('memory_usage_percent', 'N/A')}%")
    
    print(f"\n💡 OPTIMIZATION RECOMMENDATIONS:")
    
    if single_result.get("success", False):
        throughput = single_result.get("throughput_docs_per_sec", 0)
        if throughput > 100:
            print("   ✅ Excellent single-query performance!")
        elif throughput > 50:
            print("   ✅ Good performance - consider increasing batch size for heavy loads")
        else:
            print("   ⚠️  Consider optimizing batch size or checking system resources")
    
    if batch_result.get("success", False):
        pairs_per_sec = batch_result.get("pairs_per_second", 0)
        if pairs_per_sec > 500:
            print("   ✅ Excellent batch performance!")
        elif pairs_per_sec > 200:
            print("   ✅ Good batch performance")
        else:
            print("   ⚠️  Batch performance could be improved")
    
    if concurrent_result.get("successful", 0) >= 6:
        print("   ✅ Good concurrent handling capacity")
    else:
        print("   ⚠️  Consider reducing concurrent load or increasing resources")
    
    print("\n🎉 Performance test completed!")

if __name__ == "__main__":
    asyncio.run(comprehensive_performance_test())
