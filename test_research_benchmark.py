#!/usr/bin/env python3
"""
Benchmark Research API with 10 queries - Detailed timing analysis
"""

import requests
import time
from pathlib import Path
from dotenv import load_dotenv
import os
import json
from statistics import mean, stdev
from tabulate import tabulate

# Load .env file
env_path = Path(__file__).parent / '.env'
load_dotenv(env_path)

# 17 diverse medical queries (first one is warmup)
BENCHMARK_QUERIES = [
    # Warmup query (will be excluded from results)
    "basic medical query for model initialization and caching warmup",
    # Actual benchmark queries (16 queries)
    "dosage adjustments of ceftriaxone in patients with severe renal impairment",
    "management of diabetic ketoacidosis in pediatric patients emergency protocols",
    "drug interactions between warfarin and common antibiotics INR monitoring",
    "treatment resistant depression electroconvulsive therapy vs ketamine infusion",
    "prophylactic antibiotics for preventing surgical site infections in cardiac surgery",
    "acute management of ischemic stroke tissue plasminogen activator time window",
    "metformin contraindications in patients with chronic kidney disease GFR thresholds",
    "ventilator weaning protocols for COVID-19 patients prone positioning strategies",
    "immunosuppressive therapy in kidney transplant recipients tacrolimus monitoring",
    "management of anaphylaxis epinephrine dosing pediatric vs adult protocols",
    "hypertensive crisis management labetalol vs nicardipine emergency department",
    "perioperative beta blocker therapy cardiovascular risk reduction guidelines",
    "contrast induced nephropathy prevention strategies high risk patients",
    "antibiotic stewardship programs reducing C difficile infection rates",
    "deep vein thrombosis prophylaxis orthopedic surgery enoxaparin dosing"
    # "pain management sickle cell crisis opioid vs non-opioid strategies"
]

class TimingStats:
    def __init__(self):
        self.query_expansion_times = []
        self.vector_search_times = []
        self.similarity_filter_times = []
        self.cross_encoder_times = []
        self.llm_processing_times = []
        self.total_api_times = []
        self.source_counts = []
        
    def add_timing(self, query_exp_time, vector_time, sim_filter_time, cross_enc_time, llm_time, total_time, source_count):
        self.query_expansion_times.append(query_exp_time)
        self.vector_search_times.append(vector_time)
        self.similarity_filter_times.append(sim_filter_time)
        self.cross_encoder_times.append(cross_enc_time)
        self.llm_processing_times.append(llm_time)
        self.total_api_times.append(total_time)
        self.source_counts.append(source_count)
    
    def get_stats(self):
        def calc_stats(times):
            if not times:
                return {'mean': 0, 'std': 0, 'min': 0, 'max': 0}
            return {
                'mean': mean(times),
                'std': stdev(times) if len(times) > 1 else 0,
                'min': min(times),
                'max': max(times)
            }
        
        return {
            'query_expansion': calc_stats(self.query_expansion_times),
            'vector_search': calc_stats(self.vector_search_times),
            'similarity_filter': calc_stats(self.similarity_filter_times),
            'cross_encoder': calc_stats(self.cross_encoder_times),
            'llm_processing': calc_stats(self.llm_processing_times),
            'total_api': calc_stats(self.total_api_times),
            'sources': {
                'mean': mean(self.source_counts) if self.source_counts else 0,
                'min': min(self.source_counts) if self.source_counts else 0,
                'max': max(self.source_counts) if self.source_counts else 0
            }
        }

def run_single_query(query, query_num):
    """Run a single query and return timing details"""
    print(f"\n{'='*80}")
    print(f"Query {query_num}: {query[:60]}...")
    print(f"{'='*80}")
    
    # Make research request
    start_time = time.time()
    
    try:
        response = requests.post(
            "http://localhost:8000/api/research",
            json={
                "query": query,
                "top_k": 10,
                "similarity_threshold": 0.7
            },
            timeout=120
        )
        
        total_api_time = time.time() - start_time
        
        if response.status_code == 200:
            data = response.json()
            
            # Extract timing information
            query_expansion_time = data.get('query_expansion_time', 0)
            vector_search_time = data.get('vector_search_time', 0)
            similarity_filter_time = data.get('similarity_filter_time', 0)
            cross_encoder_time = data.get('cross_encoder_time', 0)
            llm_processing_time = data.get('llm_processing_time', 0)
            processing_time = data.get('processing_time', 0)
            source_count = len(data.get('papers', []))
            
            print(f"✅ Success!")
            print(f"  - Query Expansion: {query_expansion_time:.3f}s")
            print(f"  - Vector Search: {vector_search_time:.3f}s")
            print(f"  - Similarity Filter: {similarity_filter_time:.3f}s")
            print(f"  - Cross Encoder: {cross_encoder_time:.3f}s")
            print(f"  - LLM Processing: {llm_processing_time:.3f}s")
            print(f"  - Total API Time: {total_api_time:.3f}s")
            print(f"  - Papers Found: {source_count}")
            
            # Show first paper
            if data.get('papers'):
                print(f"  - Top Paper: {data['papers'][0].get('title', 'No title')[:50]}...")
            
            return {
                'success': True,
                'query_expansion_time': query_expansion_time,
                'vector_search_time': vector_search_time,
                'similarity_filter_time': similarity_filter_time,
                'cross_encoder_time': cross_encoder_time,
                'llm_processing_time': llm_processing_time,
                'total_api_time': total_api_time,
                'source_count': source_count,
                'summary': data.get('summary', ''),
                'subquestions': data.get('subquestions', []),
                'papers': data.get('papers', [])
            }
            
        else:
            print(f"❌ Request failed with status {response.status_code}")
            return {'success': False}
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return {'success': False}

def benchmark_research_api():
    """Run benchmark with 16 queries (plus 1 warmup)"""
    
    print("🏥 Research API Benchmark")
    print("=" * 80)
    print(f"Running {len(BENCHMARK_QUERIES)-1} queries (plus 1 warmup)")
    print(f"Using VectorX Index: {os.getenv('VECTORX_INDEX_NAME')}")
    
    # Check if service is running
    try:
        health = requests.get("http://localhost:8000/health", timeout=2)
        if health.status_code != 200:
            print("❌ Service returned non-200 status")
            return
    except:
        print("❌ Service not running! Start it with:")
        print("   cd /Users/<USER>/doctor-dashboard/doctor-dashboard")
        print("   bash run_research_api.sh")
        return
    
    print("✅ Service is running")
    
    # Initialize timing stats
    stats = TimingStats()
    successful_queries = 0
    llm_responses = []  # Collect LLM responses
    
    # Run warmup query first
    print("\n🔥 Running warmup query...")
    warmup_result = run_single_query(BENCHMARK_QUERIES[0], 0)
    if warmup_result['success']:
        print("✅ Warmup complete - models loaded and cached\n")
    else:
        print("⚠️  Warmup query failed, continuing anyway...\n")
    
    time.sleep(1)  # Brief pause after warmup
    
    # Run actual benchmark queries
    overall_start = time.time()
    
    for i, query in enumerate(BENCHMARK_QUERIES[1:], 1):  # Skip warmup query
        result = run_single_query(query, i)
        
        if result['success']:
            successful_queries += 1
            stats.add_timing(
                result['query_expansion_time'],
                result['vector_search_time'],
                result['similarity_filter_time'],
                result['cross_encoder_time'],
                result['llm_processing_time'],
                result['total_api_time'],
                result['source_count']
            )
            
            # Collect LLM response data
            llm_responses.append({
                'query_num': i,
                'query': query,
                'summary': result['summary'],
                'subquestions': result['subquestions'],
                'papers_count': result['source_count'],
                'papers': [{
                    'title': p.get('title', 'No title'),
                    'similarity_score': p.get('similarity_score', 0),
                    'cross_score': p.get('cross_score', 0)
                } for p in result['papers'][:3]]  # Top 3 papers
            })
        
        # Small delay between queries
        if i < len(BENCHMARK_QUERIES) - 1:
            time.sleep(0.5)
    
    overall_time = time.time() - overall_start
    
    # Calculate and display statistics
    print(f"\n{'='*80}")
    print("📊 BENCHMARK RESULTS")
    print(f"{'='*80}")
    
    print(f"\nSuccessful Queries: {successful_queries}/{len(BENCHMARK_QUERIES)-1} (excluding warmup)")
    print(f"Total Benchmark Time: {overall_time:.2f}s (excluding warmup)")
    
    if successful_queries > 0:
        stats_data = stats.get_stats()
        
        # Create summary table
        table_data = [
            ["Pipeline Step", "Mean (s)", "Std Dev (s)", "Min (s)", "Max (s)"],
            ["1. Query Expansion", 
             f"{stats_data['query_expansion']['mean']:.3f}",
             f"{stats_data['query_expansion']['std']:.3f}",
             f"{stats_data['query_expansion']['min']:.3f}",
             f"{stats_data['query_expansion']['max']:.3f}"],
            ["2. Vector Search", 
             f"{stats_data['vector_search']['mean']:.3f}",
             f"{stats_data['vector_search']['std']:.3f}",
             f"{stats_data['vector_search']['min']:.3f}",
             f"{stats_data['vector_search']['max']:.3f}"],
            ["3. Similarity Filter",
             f"{stats_data['similarity_filter']['mean']:.3f}",
             f"{stats_data['similarity_filter']['std']:.3f}",
             f"{stats_data['similarity_filter']['min']:.3f}",
             f"{stats_data['similarity_filter']['max']:.3f}"],
            ["4. Cross Encoder",
             f"{stats_data['cross_encoder']['mean']:.3f}",
             f"{stats_data['cross_encoder']['std']:.3f}",
             f"{stats_data['cross_encoder']['min']:.3f}",
             f"{stats_data['cross_encoder']['max']:.3f}"],
            ["5. LLM Processing",
             f"{stats_data['llm_processing']['mean']:.3f}",
             f"{stats_data['llm_processing']['std']:.3f}",
             f"{stats_data['llm_processing']['min']:.3f}",
             f"{stats_data['llm_processing']['max']:.3f}"],
            ["-" * 15, "-" * 10, "-" * 12, "-" * 8, "-" * 8],
            ["Total API Time",
             f"{stats_data['total_api']['mean']:.3f}",
             f"{stats_data['total_api']['std']:.3f}",
             f"{stats_data['total_api']['min']:.3f}",
             f"{stats_data['total_api']['max']:.3f}"]
        ]
        
        print("\n📈 Timing Statistics:")
        print(tabulate(table_data, headers="firstrow", tablefmt="grid"))
        
        print(f"\n📚 Paper Statistics:")
        print(f"  - Average papers per query: {stats_data['sources']['mean']:.1f}")
        print(f"  - Min papers: {stats_data['sources']['min']}")
        print(f"  - Max papers: {stats_data['sources']['max']}")
        
        # Performance breakdown
        total_mean = stats_data['total_api']['mean']
        if total_mean > 0:
            print(f"\n⚡ Performance Breakdown (% of total time):")
            print(f"  - Query Expansion: {(stats_data['query_expansion']['mean'] / total_mean) * 100:.1f}%")
            print(f"  - Vector Search: {(stats_data['vector_search']['mean'] / total_mean) * 100:.1f}%")
            print(f"  - Similarity Filter: {(stats_data['similarity_filter']['mean'] / total_mean) * 100:.1f}%")
            print(f"  - Cross Encoder: {(stats_data['cross_encoder']['mean'] / total_mean) * 100:.1f}%")
            print(f"  - LLM Processing: {(stats_data['llm_processing']['mean'] / total_mean) * 100:.1f}%")
        
        # Save detailed results
        results = {
            'timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
            'successful_queries': successful_queries,
            'total_queries': len(BENCHMARK_QUERIES) - 1,  # Exclude warmup
            'warmup_excluded': True,
            'total_time': overall_time,
            'statistics': stats_data,
            'individual_timings': {
                'query_expansion': stats.query_expansion_times,
                'vector_search': stats.vector_search_times,
                'similarity_filter': stats.similarity_filter_times,
                'cross_encoder': stats.cross_encoder_times,
                'llm_processing': stats.llm_processing_times,
                'total_api': stats.total_api_times,
                'source_counts': stats.source_counts
            }
        }
        
        with open('benchmark_results.json', 'w') as f:
            json.dump(results, f, indent=2)
        print(f"\n💾 Detailed results saved to benchmark_results.json")
        
        # Save LLM responses separately
        llm_results = {
            'timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
            'total_queries': len(llm_responses),
            'responses': llm_responses
        }
        
        with open('benchmark_llm_responses.json', 'w') as f:
            json.dump(llm_results, f, indent=2)
        print(f"📝 LLM responses saved to benchmark_llm_responses.json")

if __name__ == "__main__":
    benchmark_research_api()