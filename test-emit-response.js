#!/usr/bin/env node

const axios = require('axios');
const { io } = require('socket.io-client');

async function testEmitResponse() {
  console.log('🧪 Testing Gatekeeper Emit-Message Response');
  console.log('==========================================\n');

  let socket;
  
  try {
    // Connect WebSocket
    socket = io('http://localhost:3000', {
      transports: ['websocket']
    });

    await new Promise((resolve) => {
      socket.on('connect', () => {
        console.log('✅ Connected:', socket.id);
        resolve();
      });
    });

    // Authenticate
    socket.emit('authenticate', {
      token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJlbmNyeXB0ZWQiOnRydWUsImRhdGEiOiJVMkZzZEdWa1gxL2NSeHFSdFRwcm9JSWRjNnJoemZJUjU1ZDYzZzA3Y3Jyd29UR2l2UXk0RWdqTm1iT25leXU2SlVhVE5aNTV3WGoxa0I1a3R0Qk9JVDhod2JGclBtNnBSeFZxS2trYUwxQUU2UlpQM1pDWlNYSVhxWG5mUDhyU2F0S0ZUaDlLVEdIUHgraDRIeHJyb2VzdVVhZWtFVXlzRlZTRTdhQXM1QU09IiwiaWF0IjoxNzUwOTEyNjQ3LCJleHAiOjE3NTA5MTQ0NDd9.lHvRqsLKlclyZ4WPy3-SPAwOvFpk4tjdcSxVKkuR2xE',
      user: {
        id: '898a9e7b-6873-4c4c-b21c-f4786ee281ad',
        userId: '898a9e7b-6873-4c4c-b21c-f4786ee281ad',
        phone: '+919819304846',
        name: 'Test User'
      }
    });

    await new Promise((resolve) => {
      socket.on('authenticated', () => {
        console.log('✅ Authenticated');
        resolve();
      });
    });

    // Send a message
    const testThreadId = 'test-dialogue-' + Date.now();
    console.log('\n📤 Sending message with threadId:', testThreadId);
    
    socket.emit('send_message', {
      threadId: testThreadId,
      content: 'Test message to check emit-message response',
      conversationType: 'patient-case',
      attachments: []
    });

    socket.on('message_sent', (data) => {
      console.log('✅ Message sent:', data);
    });

    // Listen for responses
    socket.on('message_response', (data) => {
      console.log('\n📥 WebSocket response received:', JSON.stringify(data, null, 2));
    });

    // Wait for Gatekeeper to respond
    console.log('\n⏳ Waiting for Gatekeeper to call emit-message...');
    console.log('📋 Check server.log for emit-message payload');
    
    // Keep connection open for 10 seconds
    await new Promise(resolve => setTimeout(resolve, 10000));

  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    if (socket) socket.disconnect();
    
    // Show the last emit-message from server log
    console.log('\n📋 Checking server.log for emit-message calls...\n');
    const { execSync } = require('child_process');
    try {
      const logs = execSync('grep -A 10 "Received emit-message" server.log | tail -50', { encoding: 'utf8' });
      console.log(logs);
    } catch (e) {
      console.log('No emit-message calls found in logs');
    }
  }
}

testEmitResponse();