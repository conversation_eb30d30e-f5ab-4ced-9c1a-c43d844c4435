# E2E Test Issues - Suites Below 100% Coverage

## 1. Messaging Test Suite (66.7% - 2/3 passing)

### Failed Test:
- **Test message input**
  - **Error**: "Message input not found"
  - **Root Cause**: Test is navigating to base URL which shows a blank page or 404
  - **Issue**: The test doesn't navigate to the chat page and isn't logged in
  - **Fix Needed**: Test needs to either:
    1. Have a pre-authenticated session setup
    2. Go through login flow first
    3. Navigate to the correct `/chat` URL

### Passing Tests:
- ✅ Test file attachment (warns but passes)
- ✅ Test markdown rendering

---

## 2. Patient Cases Test Suite (50% - 3/6 passing)

### Failed Tests:
1. **Send patient case message**
   - **Error**: "Message input not found"
   - **Root Cause**: Getting 404 page when navigating to `/chat`
   - **Issue**: Not authenticated, redirected to login or 404
   
2. **Check AI response**
   - **Error**: "AI response does not contain expected medical information"
   - **Root Cause**: No message was sent (previous test failed), so no response exists
   - **Issue**: Cascading failure from previous test
   
3. **Check message history**
   - **Error**: "Expected both user message and AI response to be visible"
   - **Root Cause**: No messages exist because sending failed
   - **Issue**: Cascading failure from first test

### Passing Tests:
- ✅ Create new patient case (warns but passes)
- ✅ Check thread in sidebar (warns but passes)
- ✅ Test file attachment (warns but passes)

---

## 3. Research Test Suite (83.3% - 5/6 passing)

### Failed Test:
- **Navigate to Research section**
  - **Error**: "Research section link not found"
  - **Root Cause**: The page shows 404 error
  - **Issue**: Either:
    1. "Research" is not a separate section in current UI
    2. Test is looking in wrong location
    3. Not authenticated to see the section

### Passing Tests:
- ✅ Create research thread
- ✅ Ask research question
- ✅ Check for citations
- ✅ Check for data tables/visualizations
- ✅ Test research export options

---

## 4. Quick Facts Test Suite (83.3% - 5/6 passing)

### Failed Test:
- **Navigate to Quick Facts section**
  - **Error**: "Quick Facts section link not found"
  - **Root Cause**: The page shows 404 error
  - **Issue**: Similar to Research - either:
    1. "Quick Facts" is not a separate section
    2. Test is looking in wrong location
    3. Not authenticated

### Passing Tests:
- ✅ Test drug dosage query
- ✅ Test medical calculation
- ✅ Test lab value interpretation
- ✅ Check response formatting
- ✅ Test response speed

---

## Common Issues Across All Failing Tests

### 1. Authentication Issue
- Tests are not logged in before trying to access protected routes
- `/chat` returns 404 when not authenticated
- Need to either:
  - Set up authentication in test setup
  - Mock authentication
  - Run login flow before each test suite

### 2. Navigation Problems
- Tests navigate to `/chat` but get 404 or blank page
- Possible causes:
  - Route requires authentication
  - Route doesn't exist in current build
  - Server not properly running

### 3. UI Assumptions
- Tests assume "Patient Cases", "Research", "Quick Facts" are clickable sections
- These might be:
  - Thread types selected during conversation creation
  - Not visible UI elements
  - Part of a different UI flow

### 4. Cascading Failures
- When message sending fails, all subsequent tests fail
- Tests should be more isolated

## Recommendations

1. **Add Authentication Helper**
   ```javascript
   async function loginTestUser(page) {
     await page.goto(CONFIG.baseUrl);
     await page.type('input[placeholder*="98193"]', CONFIG.testPhone);
     await page.click('button[type="submit"]');
     await page.waitForSelector('input[maxlength="6"]');
     await page.type('input[maxlength="6"]', CONFIG.testOTP);
     await page.keyboard.press('Enter');
     await page.waitForNavigation();
   }
   ```

2. **Update Test Structure**
   - Each test suite should handle its own authentication
   - Tests should check current page state before proceeding
   - Add better error messages indicating why elements weren't found

3. **Verify UI Assumptions**
   - Check if "Research" and "Quick Facts" are actual UI elements
   - Update tests to match current UI structure
   - Remove tests for features that don't exist

4. **Fix Navigation**
   - Ensure tests navigate to correct URLs
   - Add waits after navigation
   - Check for redirects