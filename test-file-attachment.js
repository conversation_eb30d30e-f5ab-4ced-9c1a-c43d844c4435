#!/usr/bin/env node

// Load environment variables
require('dotenv').config({ path: '.env.local' });

const fs = require('fs');
const path = require('path');
const { v4: uuidv4 } = require('uuid');

// Create a simple test PDF content (minimal valid PDF)
const createTestPDF = () => {
  const pdfContent = `%PDF-1.4
1 0 obj
<<
/Type /Catalog
/Pages 2 0 R
>>
endobj

2 0 obj
<<
/Type /Pages
/Kids [3 0 R]
/Count 1
>>
endobj

3 0 obj
<<
/Type /Page
/Parent 2 0 R
/MediaBox [0 0 612 792]
/Contents 4 0 R
>>
endobj

4 0 obj
<<
/Length 44
>>
stream
BT
/F1 12 Tf
100 700 Td
(Test Medical Report) Tj
ET
endstream
endobj

xref
0 5
0000000000 65535 f 
0000000010 00000 n 
0000000053 00000 n 
0000000109 00000 n 
0000000181 00000 n 
trailer
<<
/Size 5
/Root 1 0 R
>>
startxref
276
%%EOF`;

  return Buffer.from(pdfContent, 'utf8');
};

// Test the Azure Blob Storage upload functionality
async function testFileUpload() {
  console.log('🧪 Testing File Attachment Functionality');
  console.log('=====================================\n');

  // Create test PDF
  const testPDFBuffer = createTestPDF();
  const fileName = 'test-medical-report.pdf';
  
  console.log('📄 Created test PDF:', {
    fileName,
    size: testPDFBuffer.length,
    type: 'application/pdf'
  });

  // Test the upload function
  try {
    // Import the Azure blob functions from custom-server.js
    const { BlobServiceClient, StorageSharedKeyCredential } = require('@azure/storage-blob');
    
    const accountName = process.env.AZURE_STORAGE_ACCOUNT_NAME || 'augustbuckets';
    const accountKey = process.env.AZURE_STORAGE_ACCOUNT_KEY;
    
    if (!accountName || !accountKey) {
      console.log('❌ Azure Storage credentials not configured');
      console.log('Environment variables needed:');
      console.log('- AZURE_STORAGE_ACCOUNT_NAME');
      console.log('- AZURE_STORAGE_ACCOUNT_KEY');
      return;
    }

    console.log('🔑 Azure credentials found');
    console.log('Account:', accountName);
    
    // Create blob service client
    const credential = new StorageSharedKeyCredential(accountName, accountKey);
    const blobServiceClient = new BlobServiceClient(
      `https://${accountName}.blob.core.windows.net`,
      credential
    );
    
    // Upload test
    const containerName = process.env.REPORTS_BUCKET || 'doctor-attachments';
    const userId = 'test-user-123';
    const threadId = uuidv4();
    const blobName = `${userId}/${threadId}/${uuidv4()}_${fileName}`;
    
    console.log('📤 Uploading to Azure Blob Storage...');
    console.log('Container:', containerName);
    console.log('Blob path:', blobName);
    
    const containerClient = blobServiceClient.getContainerClient(containerName);
    const blockBlobClient = containerClient.getBlockBlobClient(blobName);
    
    const uploadResult = await blockBlobClient.upload(testPDFBuffer, testPDFBuffer.length, {
      blobHTTPHeaders: {
        blobContentType: 'application/pdf'
      }
    });
    
    const fileUrl = blockBlobClient.url;
    
    console.log('✅ Upload successful!');
    console.log('File URL:', fileUrl);
    console.log('Upload result:', {
      requestId: uploadResult.requestId,
      etag: uploadResult.etag
    });
    
    // Test the WebSocket payload format
    const attachmentPayload = {
      url: fileUrl,
      name: fileName,
      size: testPDFBuffer.length,
      type: 'application/pdf'
    };
    
    console.log('\n📋 Attachment payload for WebSocket:');
    console.log(JSON.stringify(attachmentPayload, null, 2));
    
    // Test webhook payload with attachment
    const webhookPayload = {
      dialogueId: threadId,
      dialogueType: 'patient-case',
      text: 'Patient case with medical report attachment',
      providerMessageId: uuidv4(),
      sender: 'human',
      source: 'WEB',
      phoneNumber: '+919819304846',
      timestamp: Date.now(),
      requestId: uuidv4(),
      attachments: [attachmentPayload]
    };
    
    console.log('\n📬 Webhook payload with attachment:');
    console.log(JSON.stringify(webhookPayload, null, 2));
    
    return {
      success: true,
      fileUrl,
      attachment: attachmentPayload,
      webhookPayload
    };
    
  } catch (error) {
    console.error('❌ Upload failed:', error.message);
    console.error('Stack:', error.stack);
    return { success: false, error: error.message };
  }
}

// Run the test
if (require.main === module) {
  testFileUpload()
    .then(result => {
      if (result && result.success) {
        console.log('\n🎉 File attachment test completed successfully!');
      } else {
        console.log('\n💥 File attachment test failed');
        process.exit(1);
      }
    })
    .catch(error => {
      console.error('💥 Test failed with error:', error);
      process.exit(1);
    });
}

module.exports = { testFileUpload, createTestPDF };