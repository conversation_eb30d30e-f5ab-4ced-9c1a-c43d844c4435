# WebSocket Testing Guide

## Quick Start

1. **Start the server**:
   ```bash
   npm run dev
   ```

2. **Open your browser** and go to http://localhost:3000

3. **Open Browser Console** (F12 or Cmd+Option+I) to see WebSocket logs

4. **Login** with test phone: `+************`

5. **Send a test message** and watch the logs

## What to Look For

### In the Browser Console:
- 🔌 "Initializing WebSocket connection..."
- ✅ "WebSocket connected, authenticating with token..."
- ✅ "WebSocket authenticated"
- 📤 "Attempting to send message via WebSocket"
- 🚀 "Emitting send_message event with data"
- ✅ "Message sent successfully"

### In the Terminal (Server):
- 🔌 "New WebSocket connection established"
- 🔐 "Authentication attempt received"
- ✅ "User authenticated successfully"
- 💬 "Message received from frontend"
- 🧪 "TEST MODE: Simulating Gatekeeper response"
- 🤖 "Sending simulated AI response"

## Testing Steps

1. **Test Connection**:
   - Login and check if WebSocket connects automatically
   - Look for authentication success messages

2. **Test Message Send**:
   - Type a message and send it
   - Check browser console for send logs
   - Check terminal for receive logs

3. **Test Response**:
   - After sending, wait 2 seconds
   - You should see a test response appear in the chat
   - The response will echo your message

## Troubleshooting

- **No WebSocket logs**: Check if access_token exists in localStorage
- **Authentication failed**: JWT secret might not match between frontend and backend
- **Port already in use**: Kill existing process with `lsof -ti:3000 | xargs kill -9`
- **No response**: Check if WebSocket events are properly subscribed in ChatInterface

## Environment Variables Needed

Add to `.env.local`:
```
JWT_SECRET=your_jwt_secret_here
GATEKEEPER_URL=https://gatekeeper-staging.getbeyondhealth.com
BEARER_TOKEN=your_bearer_token_here
```

## Next Steps

Once WebSocket is confirmed working:
1. Update `custom-server.js` to use real Gatekeeper API
2. Configure Gatekeeper to send responses to webhook endpoint
3. Add proper error handling and reconnection logic