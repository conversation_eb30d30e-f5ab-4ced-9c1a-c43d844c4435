const puppeteer = require('puppeteer');

(async () => {
  const browser = await puppeteer.launch({ headless: false });
  const page = await browser.newPage();

  try {
    // Navigate to the app
    await page.goto('http://localhost:3000');
    await page.waitForSelector('[data-test="phone-input"], input[type="tel"]', { timeout: 10000 });

    console.log('📱 Step 1: Entering phone number...');
    const phoneInput = await page.$('[data-test="phone-input"], input[type="tel"]');
    await phoneInput.click({ clickCount: 3 });
    await phoneInput.type('+919819304846');

    // Click send OTP button
    console.log('📤 Step 2: Clicking Send OTP...');
    await page.click('button[type="submit"]');

    // Wait for OTP input
    await page.waitForSelector('[data-test="otp-input"], input[placeholder*="OTP"], input[placeholder*="code"]', { timeout: 30000 });
    console.log('✅ OTP input appeared');

    // Enter OTP
    console.log('🔑 Step 3: Entering OTP...');
    const otpInput = await page.$('[data-test="otp-input"], input[placeholder*="OTP"], input[placeholder*="code"]');
    await otpInput.type('123456');

    // Click verify button
    console.log('✅ Step 4: Clicking Verify...');
    await page.click('button[type="submit"]');

    // Wait for dashboard to load
    await page.waitForSelector('.MuiDrawer-root', { timeout: 10000 });
    console.log('✅ Login successful! Dashboard loaded');

    // Check localStorage for user data
    const userData = await page.evaluate(() => {
      return {
        user: JSON.parse(localStorage.getItem('user') || '{}'),
        accessToken: localStorage.getItem('access_token')?.substring(0, 50) + '...',
        userPhone: localStorage.getItem('userPhone')
      };
    });
    
    console.log('🔍 User data from localStorage:', JSON.stringify(userData, null, 2));

    // Monitor WebSocket logs
    page.on('console', msg => {
      const text = msg.text();
      if (text.includes('WebSocket') || text.includes('authenticated')) {
        console.log('🔌 WebSocket log:', text);
      }
    });

    // Wait a bit to see WebSocket authentication
    await page.waitForTimeout(2000);

    // Check server logs
    console.log('\n📋 Checking server logs for authentication...');
    const fs = require('fs');
    const serverLog = fs.readFileSync('/Users/<USER>/projects/doctor-dashboard/server.log', 'utf8');
    const lines = serverLog.split('\n');
    const recentLines = lines.slice(-30);
    
    console.log('Recent authentication logs:');
    recentLines.forEach(line => {
      if (line.includes('authenticated') || line.includes('userId')) {
        console.log(line);
      }
    });

  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    await browser.close();
  }
})();