const axios = require('axios');
const readline = require('readline');

// Configuration
const GATEKEEPER_BASE_URL = 'https://gatekeeper-staging.getbeyondhealth.com/auth/august';
const TEST_PHONE = '+919819304846';

// Create readline interface for user input
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// Helper to get user input
function askQuestion(question) {
  return new Promise(resolve => {
    rl.question(question, answer => {
      resolve(answer);
    });
  });
}

// Test OTP Request
async function requestOTP() {
  console.log('\n🔐 Requesting OTP for:', TEST_PHONE);
  
  try {
    const response = await axios.post(
      `${GATEKEEPER_BASE_URL}/request-otp`,
      {
        phoneNumber: TEST_PHONE
      },
      {
        headers: {
          'Content-Type': 'application/json'
        }
      }
    );
    
    console.log('✅ OTP Request Success!');
    console.log('Response:', JSON.stringify(response.data, null, 2));
    return true;
  } catch (error) {
    console.error('❌ OTP Request Failed!');
    console.error('Error:', error.response?.data || error.message);
    return false;
  }
}

// Test OTP Verification
async function verifyOTP(otp) {
  console.log('\n🔍 Verifying OTP...');
  
  try {
    const response = await axios.post(
      `${GATEKEEPER_BASE_URL}/verify-otp`,
      {
        phone: TEST_PHONE,
        otp: otp,
        source: 'web'
      },
      {
        headers: {
          'Content-Type': 'application/json'
        }
      }
    );
    
    console.log('✅ OTP Verification Success!');
    console.log('Response:', JSON.stringify(response.data, null, 2));
    
    if (response.data.accessToken) {
      console.log('\n🎉 JWT Token Received!');
      console.log('Access Token:', response.data.accessToken);
      console.log('Refresh Token:', response.data.refreshToken);
      
      // Decode JWT to show contents
      const parts = response.data.accessToken.split('.');
      if (parts.length === 3) {
        const payload = JSON.parse(Buffer.from(parts[1], 'base64').toString());
        console.log('\nJWT Payload:', JSON.stringify(payload, null, 2));
      }
    }
    
    return response.data;
  } catch (error) {
    console.error('❌ OTP Verification Failed!');
    console.error('Error:', error.response?.data || error.message);
    return null;
  }
}

// Test authenticated API call
async function testAuthenticatedAPI(accessToken) {
  console.log('\n🧪 Testing authenticated API with real JWT...');
  
  try {
    const response = await axios.get(
      'https://gatekeeper-staging.getbeyondhealth.com/user/practitioner-dashboard/get-chats-by-dialogueId',
      {
        params: {
          limit: 10,
          dialogue_id: ''
        },
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/json'
        }
      }
    );
    
    console.log('✅ Authenticated API Call Success!');
    console.log('Response:', JSON.stringify(response.data, null, 2));
    return response.data;
  } catch (error) {
    console.error('❌ Authenticated API Call Failed!');
    console.error('Error:', error.response?.data || error.message);
    return null;
  }
}

// Main test flow
async function runAuthTest() {
  console.log(`
${'='.repeat(60)}
🔐 GATEKEEPER AUTHENTICATION TEST
${'='.repeat(60)}
This test will:
1. Request an OTP for ${TEST_PHONE}
2. Ask you to enter the OTP you receive
3. Verify the OTP and get a JWT
4. Test an authenticated API call
${'='.repeat(60)}
`);

  // Step 1: Request OTP
  const otpRequested = await requestOTP();
  if (!otpRequested) {
    console.log('\n❌ Failed to request OTP. Exiting...');
    rl.close();
    return;
  }

  // Step 2: Get OTP from user
  console.log('\n📱 Please check your phone for the OTP');
  const otp = await askQuestion('Enter the 6-digit OTP: ');
  
  if (!otp || otp.length !== 6) {
    console.log('\n❌ Invalid OTP format. Exiting...');
    rl.close();
    return;
  }

  // Step 3: Verify OTP
  const authData = await verifyOTP(otp);
  if (!authData || !authData.accessToken) {
    console.log('\n❌ Failed to verify OTP. Exiting...');
    rl.close();
    return;
  }

  // Step 4: Test authenticated API
  await testAuthenticatedAPI(authData.accessToken);

  // Save the tokens for future use
  const fs = require('fs');
  const tokenData = {
    timestamp: new Date().toISOString(),
    phone: TEST_PHONE,
    accessToken: authData.accessToken,
    refreshToken: authData.refreshToken,
    expiresIn: authData.expiresIn || 3600
  };
  
  fs.writeFileSync('gatekeeper-tokens.json', JSON.stringify(tokenData, null, 2));
  console.log('\n💾 Tokens saved to gatekeeper-tokens.json');

  console.log(`\n
${'='.repeat(60)}
✅ AUTHENTICATION TEST COMPLETE!
${'='.repeat(60)}
You can now use the access token for API calls:
${authData.accessToken}
${'='.repeat(60)}
`);

  rl.close();
}

// Run the test
runAuthTest().catch(error => {
  console.error('Unexpected error:', error);
  rl.close();
});