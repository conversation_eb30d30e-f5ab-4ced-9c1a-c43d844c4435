const axios = require('axios');
const { v4: uuidv4 } = require('uuid');

async function testWebhook() {
  console.log('🔐 STEP 1: Getting fresh JWT token...');
  console.log('=====================================\n');
  
  // Step 1: Request OTP
  try {
    const otpResponse = await axios.post(
      'https://gatekeeper-staging.getbeyondhealth.com/auth/practitioner-dashboard/request-otp',
      { phoneNumber: '+************' },
      { headers: { 'Content-Type': 'application/json' } }
    );
    console.log('✅ OTP requested successfully\n');
  } catch (error) {
    console.error('❌ OTP request failed:', error.message);
    return;
  }

  // Step 2: Verify OTP and get JWT
  let jwt;
  try {
    const verifyResponse = await axios.post(
      'https://gatekeeper-staging.getbeyondhealth.com/auth/practitioner-dashboard/verify-otp',
      {
        phone: '+************',
        otp: '123456',
        source: 'web'
      },
      { headers: { 'Content-Type': 'application/json' } }
    );
    
    jwt = verifyResponse.data.accessToken;
    console.log('✅ JWT obtained successfully');
    console.log(`JWT: ${jwt}\n`);
  } catch (error) {
    console.error('❌ OTP verification failed:', error.message);
    return;
  }

  // Step 3: Prepare webhook payload
  const webhookPayload = {
    dialogueId: uuidv4(),
    dialogueType: 'patient-case',
    text: 'Test message from Node.js script',
    providerMessageId: uuidv4(),
    sender: 'human',
    source: 'WEB',
    phoneNumber: '+************',
    timestamp: Date.now(),
    requestId: uuidv4()
  };

  console.log('📤 STEP 2: Sending webhook request...');
  console.log('=====================================\n');
  console.log('Payload:', JSON.stringify(webhookPayload, null, 2));
  console.log('\n');

  // Generate curl command
  const curlCommand = `curl -X POST "https://gatekeeper-staging.getbeyondhealth.com/c/practitioner-dashboard/webhook" \\
    -H "Content-Type: application/json" \\
    -H "Authorization: Bearer ${jwt}" \\
    -d '${JSON.stringify(webhookPayload, null, 2).replace(/'/g, "'\"'\"'")}'`;
  
  console.log('🔧 Equivalent CURL command:');
  console.log('============================');
  console.log(curlCommand);
  console.log('\n');

  // Step 4: Send webhook request
  try {
    const response = await axios.post(
      'https://gatekeeper-staging.getbeyondhealth.com/c/practitioner-dashboard/webhook',
      webhookPayload,
      {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${jwt}`
        }
      }
    );
    
    console.log('✅ Webhook response:');
    console.log('Status:', response.status);
    console.log('Data:', JSON.stringify(response.data, null, 2));
    console.log('\n');
    
    console.log('📝 Key IDs for database check:');
    console.log('==============================');
    console.log(`dialogueId: ${webhookPayload.dialogueId}`);
    console.log(`providerMessageId: ${webhookPayload.providerMessageId}`);
    console.log(`requestId: ${webhookPayload.requestId}`);
    console.log(`timestamp: ${webhookPayload.timestamp}`);
    
  } catch (error) {
    console.error('❌ Webhook request failed:');
    console.error('Status:', error.response?.status);
    console.error('Data:', error.response?.data);
    console.error('Message:', error.message);
  }
}

// Run the test
testWebhook();