const axios = require('axios');
const { v4: uuidv4 } = require('uuid');

// Test All Dialogue Types
async function testDialogueTypes() {
  console.log('\n🏷️ TEST 7: Test All Dialogue Types');
  console.log('=' + '='.repeat(60));
  console.log('Purpose: Verify all 3 dialogue types are accepted');
  console.log('Endpoint: POST /c/practitioner-dashboard/webhook');
  console.log('Types: patient-case, research, quick-fact');
  console.log('=' + '='.repeat(60));
  
  const url = 'https://gatekeeper-staging.getbeyondhealth.com/c/practitioner-dashboard/webhook';
  
  const dialogueTypes = [
    {
      type: 'patient-case',
      text: '65-year-old female with diabetes and hypertension presenting with dizziness',
      description: 'Clinical case discussion'
    },
    {
      type: 'research', 
      text: 'What are the latest guidelines for treating resistant hypertension?',
      description: 'Medical research query'
    },
    {
      type: 'quick-fact',
      text: 'What is the normal range for HbA1c?',
      description: 'Quick medical fact'
    }
  ];
  
  console.log('\n🧪 Testing each dialogue type...\n');
  
  for (const dialogue of dialogueTypes) {
    const payload = {
      dialogueId: uuidv4(),
      dialogueType: dialogue.type,
      text: dialogue.text,
      providerMessageId: uuidv4(),
      sender: 'human',
      source: 'WEB',
      phoneNumber: '+************',
      timestamp: Date.now(),
      requestId: uuidv4()
    };
    
    console.log(`📝 Testing: ${dialogue.type} (${dialogue.description})`);
    console.log(`Message: "${dialogue.text}"`);
    
    try {
      const response = await axios.post(url, payload, {
        headers: { 'Content-Type': 'application/json' }
      });
      
      console.log(`✅ Success! Response:`, JSON.stringify(response.data));
      console.log('');
      
    } catch (error) {
      console.log(`❌ Failed!`);
      console.log('Error:', error.response?.data || error.message);
      console.log('');
    }
    
    // Small delay between requests
    await new Promise(resolve => setTimeout(resolve, 500));
  }
  
  console.log('📊 Summary:');
  console.log('- All dialogue types are accepted by the webhook');
  console.log('- Each type will trigger different AI response patterns:');
  console.log('  • patient-case: Clinical analysis and recommendations');
  console.log('  • research: Evidence-based medical information');
  console.log('  • quick-fact: Concise medical facts and values');
}

testDialogueTypes();