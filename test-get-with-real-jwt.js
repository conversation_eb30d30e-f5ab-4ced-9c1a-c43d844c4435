const axios = require('axios');

// Test GET endpoint with real JWT
async function testWithRealJWT() {
  console.log('\n🔑 TEST: GET Endpoint with Real JWT');
  console.log('=' + '='.repeat(60));
  console.log('Purpose: Test GET endpoint with real JWT from verify-otp');
  console.log('=' + '='.repeat(60));
  
  // Use the JWT we just received
  const realJWT = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************************************************************************************************************************************************.AOgGpcYIesLPQ1Q85xpYDiyCPv6HoW3VvrlqQWbHMYo';
  
  const url = 'https://gatekeeper-staging.getbeyondhealth.com/user/practitioner-dashboard/get-chats-by-dialogueId';
  
  console.log('\n📤 Request:');
  console.log('URL:', url);
  console.log('Headers: Authorization: Bearer [JWT]');
  console.log('Params: limit=10, dialogue_id=""');
  
  try {
    const response = await axios.get(url, {
      params: { limit: 10, dialogue_id: '' },
      headers: {
        'Authorization': `Bearer ${realJWT}`,
        'Content-Type': 'application/json'
      }
    });
    
    console.log('\n✅ SUCCESS! Real response from GET endpoint:');
    console.log('Status:', response.status, response.statusText);
    console.log('Response:', JSON.stringify(response.data, null, 2));
    
    console.log('\n🎉 This proves:');
    console.log('- JWT authentication is working');
    console.log('- GET endpoints are accessible with proper JWT');
    console.log('- The access field change is deployed');
    
  } catch (error) {
    console.log('\n❌ Error:');
    console.log('Status:', error.response?.status);
    console.log('Error:', error.response?.data || error.message);
    
    if (error.response?.status === 401) {
      console.log('\n🤔 Possible reasons:');
      console.log('- JWT expired (30 min expiry)');
      console.log('- User access: false (not approved yet)');
      console.log('- JWT signature validation');
    }
  }
}

testWithRealJWT();