#!/usr/bin/env node

// Test script to verify WebSocket functionality
const { spawn } = require('child_process');
const axios = require('axios');

console.log('🚀 Starting WebSocket test...\n');

// Start the server
console.log('📦 Starting custom server...');
const server = spawn('node', ['custom-server.js'], {
  stdio: 'inherit',
  env: { ...process.env, NODE_ENV: 'development' }
});

// Wait for server to start
setTimeout(async () => {
  try {
    console.log('\n✅ Server should be running. Please test the WebSocket connection by:');
    console.log('1. Open http://localhost:3000 in your browser');
    console.log('2. Login with test phone number: +919819304846');
    console.log('3. Send a message in the chat');
    console.log('4. Watch the terminal for WebSocket logs\n');
    console.log('Look for these emoji indicators:');
    console.log('🔌 = WebSocket connection');
    console.log('🔐 = Authentication attempt');
    console.log('✅ = Success');
    console.log('❌ = Error');
    console.log('💬 = Message received');
    console.log('🤖 = AI response sent\n');
    console.log('Press Ctrl+C to stop the server\n');
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}, 3000);

// Handle cleanup
process.on('SIGINT', () => {
  console.log('\n🛑 Stopping server...');
  server.kill();
  process.exit(0);
});