-- Dialogue Migration - Add title and type columns
-- This migration adds title and type fields to the dialogues table

-- Create enum type for dialogue types
CREATE TYPE dialogue_type AS ENUM ('patient-case', 'research', 'quick-fact');

-- Add title and type columns to dialogues table
ALTER TABLE dialogues 
ADD COLUMN title text,
ADD COLUMN type dialogue_type;

-- Add comment for documentation
COMMENT ON COLUMN dialogues.title IS 'Human-readable title for the dialogue/conversation';
COMMENT ON COLUMN dialogues.type IS 'Type of dialogue: patient-case, research, or quick-fact';