// Test 2: Verify OTP - Mock/Expected Response

console.log('\n🔍 TEST 2: Verify OTP (Expected Structure)');
console.log('=' + '='.repeat(60));
console.log('Purpose: Verify OTP and receive JWT tokens for authentication');
console.log('Endpoint: POST /auth/practitioner-dashboard/verify-otp');
console.log('Note: Cannot test live due to rate limiting, showing expected format');
console.log('=' + '='.repeat(60));

console.log('\n📤 Expected Request:');
console.log('URL: https://gatekeeper-staging.getbeyondhealth.com/auth/practitioner-dashboard/verify-otp');
console.log('Body:');
console.log(JSON.stringify({
  phone: '+************',
  otp: '123456',  // 6-digit OTP received via SMS
  source: 'web'
}, null, 2));

console.log('\n✅ Expected Success Response:');
console.log(JSON.stringify({
  success: true,
  accessToken: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',  // JWT for API calls
  refreshToken: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...', // JWT for token refresh
  expiresIn: 3600,  // Access token expires in 1 hour
  user: {
    id: 'user-uuid',
    phone: '+************',
    role: 'DOCTOR',
    access: true  // Whether user has been approved
  }
}, null, 2));

console.log('\n📝 What this endpoint does:');
console.log('1. Validates the OTP against what was sent to the phone');
console.log('2. Creates a session for the user');
console.log('3. Returns JWT tokens for authentication');
console.log('4. The accessToken is used for all authenticated API calls');

console.log('\n🔑 JWT Token Usage:');
console.log('- Add to headers: Authorization: Bearer <accessToken>');
console.log('- Required for GET endpoints (dialogues, messages)');
console.log('- Optional for webhook endpoint');

console.log('\n❌ Possible Error Responses:');
console.log('- 400: Invalid OTP format');
console.log('- 401: Incorrect or expired OTP');
console.log('- 429: Too many attempts');