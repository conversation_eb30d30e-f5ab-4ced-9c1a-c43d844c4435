const puppeteer = require('puppeteer');
const fs = require('fs');
const path = require('path');

// Configuration
const BASE_URL = process.env.BASE_URL || 'http://localhost:3000';
const HEADLESS = process.env.HEADLESS !== 'false';

// Create logs directory
const logsDir = path.join(__dirname, 'test-logs');
if (!fs.existsSync(logsDir)) {
  fs.mkdirSync(logsDir);
}

// Create log file with timestamp
const logFile = path.join(logsDir, `test-run-${new Date().toISOString().replace(/:/g, '-')}.log`);
const logStream = fs.createWriteStream(logFile, { flags: 'a' });

// Enhanced logging utility with file output
const log = {
  write: (level, msg, data) => {
    const timestamp = new Date().toISOString();
    const logEntry = {
      timestamp,
      level,
      message: msg,
      data: data || null
    };
    
    // Console output
    console.log(`[${level}] ${timestamp} - ${msg}`, data || '');
    
    // File output
    logStream.write(JSON.stringify(logEntry) + '\n');
  },
  info: (msg, data) => log.write('INFO', msg, data),
  error: (msg, data) => log.write('ERROR', msg, data),
  debug: (msg, data) => log.write('DEBUG', msg, data),
  warn: (msg, data) => log.write('WARN', msg, data),
  json: (msg, data) => log.write('JSON_ERROR', msg, data)
};

// Store all network requests and responses
const networkLog = {
  requests: [],
  responses: [],
  failures: [],
  jsonErrors: []
};

async function setupComprehensiveLogging(page) {
  // Intercept all console messages
  page.on('console', async msg => {
    const type = msg.type();
    const text = msg.text();
    const args = msg.args();
    
    // Try to get detailed error information
    let detailedArgs = [];
    for (const arg of args) {
      try {
        const jsonValue = await arg.jsonValue();
        detailedArgs.push(jsonValue);
      } catch (e) {
        detailedArgs.push(arg.toString());
      }
    }
    
    const logEntry = {
      type,
      text,
      args: detailedArgs,
      location: msg.location(),
      timestamp: new Date().toISOString()
    };
    
    // Special handling for errors and JSON issues
    if (type === 'error' || text.toLowerCase().includes('json') || text.toLowerCase().includes('parse')) {
      log.json('Console JSON/Parse Error Detected', logEntry);
      networkLog.jsonErrors.push(logEntry);
    } else {
      log.debug(`Console [${type}]`, logEntry);
    }
  });

  // Intercept all network requests
  page.on('request', request => {
    const requestData = {
      url: request.url(),
      method: request.method(),
      headers: request.headers(),
      postData: request.postData(),
      timestamp: new Date().toISOString()
    };
    
    networkLog.requests.push(requestData);
    
    // Log API requests with body
    if (requestData.url.includes('/api/') || requestData.method === 'POST') {
      log.debug('API Request', {
        ...requestData,
        postDataParsed: tryParseJSON(requestData.postData)
      });
    }
  });

  // Intercept all network responses
  page.on('response', async response => {
    const url = response.url();
    const status = response.status();
    const headers = response.headers();
    
    const responseData = {
      url,
      status,
      statusText: response.statusText(),
      headers,
      timestamp: new Date().toISOString()
    };
    
    // Always try to get response body for API calls
    if (url.includes('/api/') || headers['content-type']?.includes('json') || status >= 400) {
      try {
        const text = await response.text();
        responseData.body = text;
        
        // Try to parse as JSON
        if (headers['content-type']?.includes('json')) {
          const parsed = tryParseJSON(text);
          if (parsed.error) {
            log.json('Invalid JSON in API Response', {
              url,
              status,
              text: text.substring(0, 1000),
              error: parsed.error
            });
            networkLog.jsonErrors.push({
              type: 'response',
              url,
              status,
              text,
              error: parsed.error,
              timestamp: new Date().toISOString()
            });
          } else {
            responseData.bodyParsed = parsed.data;
          }
        }
      } catch (e) {
        log.warn('Could not read response body', { url, error: e.message });
      }
    }
    
    networkLog.responses.push(responseData);
    
    // Log important responses
    if (url.includes('/api/') || status >= 400) {
      log.debug('API Response', responseData);
    }
  });

  // Page errors
  page.on('error', error => {
    const errorData = {
      message: error.message,
      stack: error.stack,
      timestamp: new Date().toISOString()
    };
    log.error('Page Crashed', errorData);
    networkLog.jsonErrors.push({ type: 'page_crash', ...errorData });
  });

  // JavaScript errors on the page
  page.on('pageerror', error => {
    const errorData = {
      message: error.message,
      stack: error.stack,
      timestamp: new Date().toISOString()
    };
    
    if (error.message.toLowerCase().includes('json') || error.message.toLowerCase().includes('parse')) {
      log.json('Page JavaScript JSON Error', errorData);
      networkLog.jsonErrors.push({ type: 'page_error', ...errorData });
    } else {
      log.error('Page JavaScript Error', errorData);
    }
  });

  // Failed requests
  page.on('requestfailed', request => {
    const failureData = {
      url: request.url(),
      method: request.method(),
      failure: request.failure(),
      timestamp: new Date().toISOString()
    };
    networkLog.failures.push(failureData);
    log.error('Request Failed', failureData);
  });

  // Monitor for specific JSON parsing in evaluate calls
  const originalEvaluate = page.evaluate.bind(page);
  page.evaluate = async function(...args) {
    try {
      const result = await originalEvaluate(...args);
      return result;
    } catch (error) {
      if (error.message.includes('JSON')) {
        log.json('JSON Error in page.evaluate', {
          error: error.message,
          stack: error.stack
        });
      }
      throw error;
    }
  };
}

function tryParseJSON(text) {
  if (!text) return { data: null, error: null };
  
  try {
    const parsed = JSON.parse(text);
    return { data: parsed, error: null };
  } catch (e) {
    return { 
      data: null, 
      error: {
        message: e.message,
        position: e.message.match(/position (\d+)/) ? parseInt(e.message.match(/position (\d+)/)[1]) : null,
        text: text.substring(0, 200)
      }
    };
  }
}

async function checkForJSONIssuesInPage(page) {
  return await page.evaluate(() => {
    const issues = [];
    
    // Check all script tags with JSON
    document.querySelectorAll('script[type="application/json"]').forEach((script, index) => {
      try {
        JSON.parse(script.textContent);
      } catch (e) {
        issues.push({
          type: 'script_tag',
          index,
          error: e.message,
          content: script.textContent.substring(0, 200)
        });
      }
    });
    
    // Check __NEXT_DATA__ if it exists
    const nextDataScript = document.getElementById('__NEXT_DATA__');
    if (nextDataScript) {
      try {
        JSON.parse(nextDataScript.textContent);
      } catch (e) {
        issues.push({
          type: '__NEXT_DATA__',
          error: e.message,
          content: nextDataScript.textContent.substring(0, 200)
        });
      }
    }
    
    // Check for any data attributes that might contain JSON
    document.querySelectorAll('[data-json]').forEach((element, index) => {
      const jsonData = element.getAttribute('data-json');
      if (jsonData) {
        try {
          JSON.parse(jsonData);
        } catch (e) {
          issues.push({
            type: 'data_attribute',
            element: element.tagName,
            index,
            error: e.message,
            content: jsonData.substring(0, 200)
          });
        }
      }
    });
    
    // Check localStorage for JSON issues
    const localStorageIssues = [];
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      const value = localStorage.getItem(key);
      try {
        // Only try to parse if it looks like JSON
        if (value && (value.startsWith('{') || value.startsWith('['))) {
          JSON.parse(value);
        }
      } catch (e) {
        localStorageIssues.push({
          key,
          error: e.message,
          value: value.substring(0, 100)
        });
      }
    }
    if (localStorageIssues.length > 0) {
      issues.push({
        type: 'localStorage',
        issues: localStorageIssues
      });
    }
    
    return issues;
  });
}

async function runDoctorDashboardTest() {
  log.info('Starting Enhanced Doctor Dashboard Test with JSON Debugging');
  log.info('Log file', { path: logFile });
  
  const browser = await puppeteer.launch({
    headless: HEADLESS,
    args: ['--no-sandbox', '--disable-setuid-sandbox'],
    devtools: !HEADLESS
  });
  
  try {
    const page = await browser.newPage();
    await page.setViewport({ width: 1440, height: 900 });
    
    // Enable request interception
    await page.setRequestInterception(true);
    page.on('request', request => request.continue());
    
    // Set up comprehensive logging
    await setupComprehensiveLogging(page);
    
    // Navigate to the application
    log.info('Navigating to application', { url: BASE_URL });
    
    try {
      const response = await page.goto(BASE_URL, {
        waitUntil: ['networkidle0', 'domcontentloaded'],
        timeout: 30000
      });
      
      log.info('Initial navigation complete', {
        status: response.status(),
        url: response.url()
      });
      
      // Wait a bit for any async operations
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Check for JSON issues in the page
      const pageJSONIssues = await checkForJSONIssuesInPage(page);
      if (pageJSONIssues.length > 0) {
        log.json('JSON Issues Found in Page', pageJSONIssues);
      }
      
      // Take screenshot
      await page.screenshot({ 
        path: path.join('test-screenshots', 'initial-load.png'),
        fullPage: true 
      });
      
      // Try to interact with the page
      log.info('Checking page state');
      
      // Check if we're on login page or dashboard
      const isLoginPage = await page.$('input[type="tel"], input[placeholder*="phone"]')
        .then(el => !!el)
        .catch(() => false);
      
      if (isLoginPage) {
        log.info('Login page detected');
        
        // Try to fill the form
        const phoneInput = await page.$('input[type="tel"], input[placeholder*="phone"]');
        if (phoneInput) {
          await phoneInput.type('+919999999999');
          await page.screenshot({ 
            path: path.join('test-screenshots', 'phone-filled.png')
          });
          
          // Look for submit button
          const submitButton = await page.$('button[type="submit"]');
          if (submitButton) {
            log.info('Clicking submit button');
            
            // Monitor the network for the API call
            const responsePromise = page.waitForResponse(
              response => response.url().includes('/api/') && response.request().method() === 'POST',
              { timeout: 10000 }
            ).catch(() => null);
            
            await submitButton.click();
            
            const apiResponse = await responsePromise;
            if (apiResponse) {
              log.info('API Response received', {
                url: apiResponse.url(),
                status: apiResponse.status()
              });
            }
          }
        }
      } else {
        log.info('Not on login page, checking for dashboard elements');
        const dashboardContent = await page.content();
        log.debug('Page content preview', dashboardContent.substring(0, 500));
      }
      
      // Final JSON error check
      await new Promise(resolve => setTimeout(resolve, 3000));
      const finalJSONIssues = await checkForJSONIssuesInPage(page);
      if (finalJSONIssues.length > 0) {
        log.json('Final JSON Issues in Page', finalJSONIssues);
      }
      
    } catch (error) {
      log.error('Navigation/Interaction Error', {
        message: error.message,
        stack: error.stack
      });
      
      await page.screenshot({ 
        path: path.join('test-screenshots', 'error-state.png'),
        fullPage: true 
      });
    }
    
    // Generate summary report
    const summary = {
      totalRequests: networkLog.requests.length,
      totalResponses: networkLog.responses.length,
      failedRequests: networkLog.failures.length,
      jsonErrors: networkLog.jsonErrors.length,
      apiRequests: networkLog.requests.filter(r => r.url.includes('/api/')).length,
      errorResponses: networkLog.responses.filter(r => r.status >= 400).length
    };
    
    log.info('Test Summary', summary);
    
    // Save detailed network log
    fs.writeFileSync(
      path.join(logsDir, 'network-log.json'),
      JSON.stringify(networkLog, null, 2)
    );
    
    // If there were JSON errors, provide detailed report
    if (networkLog.jsonErrors.length > 0) {
      log.error('JSON ERRORS DETECTED - Detailed Report:');
      networkLog.jsonErrors.forEach((error, index) => {
        log.json(`JSON Error ${index + 1}/${networkLog.jsonErrors.length}`, error);
      });
    }
    
  } finally {
    await browser.close();
    logStream.end();
  }
}

// Create directories
['test-screenshots', 'test-logs'].forEach(dir => {
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir);
  }
});

// Run the test
runDoctorDashboardTest()
  .then(() => {
    log.info('Test completed. Check the logs for details.');
    log.info('Log files saved to:', { 
      mainLog: logFile,
      networkLog: path.join(logsDir, 'network-log.json')
    });
    process.exit(0);
  })
  .catch(error => {
    log.error('Test failed', error);
    process.exit(1);
  });