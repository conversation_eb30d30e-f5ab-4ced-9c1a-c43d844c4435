const puppeteer = require('puppeteer');

// Test all features accessible without authentication
(async () => {
  console.log('🚀 Testing Public Features of Doctor Dashboard\n');
  
  const browser = await puppeteer.launch({
    headless: process.env.TEST_HEADLESS !== 'false',
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });
  
  const page = await browser.newPage();
  await page.setViewport({ width: 1280, height: 800 });
  
  const tests = {
    passed: 0,
    failed: 0
  };
  
  const runTest = async (name, testFn) => {
    console.log(`\n📋 ${name}`);
    try {
      await testFn();
      console.log('   ✅ Passed');
      tests.passed++;
    } catch (error) {
      console.log(`   ❌ Failed: ${error.message}`);
      tests.failed++;
    }
  };
  
  try {
    // Test 1: Page accessibility
    await runTest('Page loads successfully', async () => {
      await page.goto('http://localhost:3000');
      const title = await page.title();
      if (!title.includes('Doctor Dashboard')) {
        throw new Error('Invalid page title');
      }
    });
    
    // Test 2: Form validation
    await runTest('Phone validation works', async () => {
      const phoneInput = await page.$('input[placeholder*="98193"]');
      const submitBtn = await page.$('button[type="submit"]');
      
      // Test empty
      const initiallyDisabled = await page.evaluate(btn => btn.disabled, submitBtn);
      console.log(`   • Initially disabled: ${initiallyDisabled}`);
      
      // Test invalid
      await phoneInput.type('123');
      await new Promise(resolve => setTimeout(resolve, 500));
      const disabledAfterInvalid = await page.evaluate(btn => btn.disabled, submitBtn);
      console.log(`   • Disabled with "123": ${disabledAfterInvalid}`);
      
      // Test valid
      await phoneInput.click({ clickCount: 3 });
      await phoneInput.type('+919819304846');
      await new Promise(resolve => setTimeout(resolve, 500));
      const enabledAfterValid = await page.evaluate(btn => !btn.disabled, submitBtn);
      console.log(`   • Enabled with valid phone: ${enabledAfterValid}`);
      
      if (!enabledAfterValid) {
        throw new Error('Button not enabled for valid phone');
      }
    });
    
    // Test 3: UI elements
    await runTest('All UI elements present', async () => {
      const elements = {
        logo: await page.$('h1'),
        phoneInput: await page.$('input[placeholder*="phone" i]'),
        submitButton: await page.$('button[type="submit"]'),
        loginTab: await page.evaluate(() => 
          Array.from(document.querySelectorAll('button')).find(b => b.textContent === 'Login')
        ),
        registerTab: await page.evaluate(() => 
          Array.from(document.querySelectorAll('button')).find(b => b.textContent === 'Register')
        )
      };
      
      for (const [name, element] of Object.entries(elements)) {
        if (!element) throw new Error(`${name} not found`);
        console.log(`   • ${name} ✓`);
      }
    });
    
    // Test 4: Tab switching
    await runTest('Login/Register tab switching', async () => {
      // Click Register
      await page.evaluate(() => {
        const btn = Array.from(document.querySelectorAll('button')).find(b => b.textContent === 'Register');
        if (btn) btn.click();
      });
      
      await new Promise(resolve => setTimeout(resolve, 500));
      
      // Check for register-specific elements
      const hasRegisterElements = await page.evaluate(() => {
        const inputs = Array.from(document.querySelectorAll('input'));
        return inputs.some(input => 
          input.placeholder?.toLowerCase().includes('name') ||
          input.placeholder?.toLowerCase().includes('organization')
        );
      });
      
      console.log(`   • Register form shown: ${hasRegisterElements}`);
      
      // Switch back to Login
      await page.evaluate(() => {
        const btn = Array.from(document.querySelectorAll('button')).find(b => b.textContent === 'Login');
        if (btn) btn.click();
      });
    });
    
    // Test 5: Responsive design
    await runTest('Responsive design', async () => {
      const viewports = [
        { name: 'Mobile', width: 375, height: 667 },
        { name: 'Tablet', width: 768, height: 1024 },
        { name: 'Desktop', width: 1280, height: 800 }
      ];
      
      for (const viewport of viewports) {
        await page.setViewport({ width: viewport.width, height: viewport.height });
        
        // Check if elements are still visible
        const phoneInput = await page.$('input[placeholder*="98193"]');
        const isVisible = await page.evaluate(el => {
          const rect = el.getBoundingClientRect();
          return rect.width > 0 && rect.height > 0;
        }, phoneInput);
        
        console.log(`   • ${viewport.name} (${viewport.width}px): ${isVisible ? '✓' : '✗'}`);
      }
    });
    
    // Test 6: Performance
    await runTest('Performance metrics', async () => {
      const metrics = await page.metrics();
      const performance = await page.evaluate(() => ({
        loadTime: performance.timing.loadEventEnd - performance.timing.navigationStart,
        domReady: performance.timing.domContentLoadedEventEnd - performance.timing.navigationStart
      }));
      
      console.log(`   • Page load: ${performance.loadTime}ms`);
      console.log(`   • DOM ready: ${performance.domReady}ms`);
      console.log(`   • JS Heap: ${(metrics.JSHeapUsedSize / 1048576).toFixed(2)}MB`);
      console.log(`   • DOM Nodes: ${metrics.Nodes}`);
      
      if (performance.loadTime > 3000) {
        throw new Error('Page load too slow (>3s)');
      }
    });
    
    // Test 7: Security headers
    await runTest('Security checks', async () => {
      const response = await page.goto('http://localhost:3000');
      const headers = response.headers();
      
      // Check for security headers
      const securityChecks = {
        'X-Frame-Options': headers['x-frame-options'],
        'X-Content-Type-Options': headers['x-content-type-options'],
        'Content-Type': headers['content-type']?.includes('text/html')
      };
      
      for (const [header, value] of Object.entries(securityChecks)) {
        console.log(`   • ${header}: ${value || 'Not set'}`);
      }
    });
    
    // Test 8: Error handling
    await runTest('Error message display', async () => {
      // Try to submit with valid phone to trigger OTP
      const phoneInput = await page.$('input[placeholder*="98193"]');
      await phoneInput.click({ clickCount: 3 });
      await phoneInput.type('+919819304846');
      
      await page.click('button[type="submit"]');
      
      // Wait for either OTP field or error
      await page.waitForSelector('input[maxlength="6"], [class*="error"]', { timeout: 10000 });
      
      const errorElement = await page.$('[class*="error"]');
      if (errorElement) {
        const errorText = await page.evaluate(el => el.textContent, errorElement);
        console.log(`   • Error displayed: "${errorText}"`);
      } else {
        console.log('   • OTP screen displayed (no error)');
      }
    });
    
  } catch (error) {
    console.error('\n💥 Critical error:', error.message);
  } finally {
    // Summary
    console.log('\n' + '='.repeat(60));
    console.log('📊 TEST SUMMARY');
    console.log('='.repeat(60));
    console.log(`Total Tests: ${tests.passed + tests.failed}`);
    console.log(`✅ Passed: ${tests.passed}`);
    console.log(`❌ Failed: ${tests.failed}`);
    console.log(`📈 Success Rate: ${(tests.passed / (tests.passed + tests.failed) * 100).toFixed(1)}%`);
    
    console.log('\n📋 FEATURES TESTED:');
    console.log('• Page loading and accessibility');
    console.log('• Form validation (phone number)');
    console.log('• UI element presence');
    console.log('• Tab switching (Login/Register)');
    console.log('• Responsive design (Mobile/Tablet/Desktop)');
    console.log('• Performance metrics');
    console.log('• Security headers');
    console.log('• Error handling');
    
    console.log('\n🔒 FEATURES REQUIRING AUTHENTICATION:');
    console.log('• Dashboard access');
    console.log('• Thread management');
    console.log('• Messaging');
    console.log('• File attachments');
    console.log('• User profile');
    
    console.log('\n✨ Public features test completed!');
    
    await browser.close();
  }
})();