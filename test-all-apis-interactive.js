const axios = require('axios');
const readline = require('readline');
const fs = require('fs');
const { v4: uuidv4 } = require('uuid');

// Configuration
const BASE_URL = 'https://gatekeeper-staging.getbeyondhealth.com';
const AUGUST_TOKEN = 'm}0/m9ZL`k{|Mz:Ca{7k8PF(gJV"Xz/j';
const TEST_PHONE = '+************';

// Test state
let authData = {};
let testDialogueId = null;

// Create readline interface
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// Helper functions
function askQuestion(question) {
  return new Promise(resolve => {
    rl.question(question, answer => {
      resolve(answer);
    });
  });
}

function logResponse(apiName, response, error = null) {
  const timestamp = new Date().toISOString();
  const logData = {
    timestamp,
    api: apiName,
    success: !error,
    response: response?.data || null,
    error: error ? {
      message: error.message,
      status: error.response?.status,
      data: error.response?.data
    } : null
  };

  // Append to log file
  fs.appendFileSync('api-test-results.json', JSON.stringify(logData) + '\n');

  // Print to console
  console.log(`\n${'='.repeat(60)}`);
  console.log(`API: ${apiName}`);
  console.log(`Status: ${!error ? '✅ SUCCESS' : '❌ FAILED'}`);
  console.log(`Timestamp: ${timestamp}`);
  if (!error) {
    console.log('Response:', JSON.stringify(response.data, null, 2));
  } else {
    console.log('Error:', error.message);
    if (error.response?.data) {
      console.log('Error Details:', JSON.stringify(error.response.data, null, 2));
    }
  }
  console.log('='.repeat(60));
}

// Test functions
async function test1_RequestOTP() {
  console.log('\n🔐 TEST 1: Request OTP');
  console.log('Endpoint: POST /auth/august/request-otp');
  
  try {
    const response = await axios.post(
      `${BASE_URL}/auth/august/request-otp`,
      { phoneNumber: TEST_PHONE },
      { headers: { 'Content-Type': 'application/json' } }
    );
    
    logResponse('Request OTP', response);
    return true;
  } catch (error) {
    logResponse('Request OTP', null, error);
    return false;
  }
}

async function test2_VerifyOTP() {
  console.log('\n🔍 TEST 2: Verify OTP');
  console.log('Endpoint: POST /auth/august/verify-otp');
  
  const otp = await askQuestion('Enter the 6-digit OTP you received: ');
  
  if (!otp || otp.length !== 6) {
    console.log('❌ Invalid OTP format');
    return false;
  }

  try {
    const response = await axios.post(
      `${BASE_URL}/auth/august/verify-otp`,
      { 
        phone: TEST_PHONE,
        otp: otp,
        source: 'web'
      },
      { headers: { 'Content-Type': 'application/json' } }
    );
    
    logResponse('Verify OTP', response);
    
    // Store auth data
    if (response.data.accessToken) {
      authData = {
        accessToken: response.data.accessToken,
        refreshToken: response.data.refreshToken,
        user: response.data.user
      };
      
      // Save tokens to file
      fs.writeFileSync('gatekeeper-auth.json', JSON.stringify(authData, null, 2));
      console.log('💾 Auth tokens saved to gatekeeper-auth.json');
    }
    
    return true;
  } catch (error) {
    logResponse('Verify OTP', null, error);
    return false;
  }
}

async function test3_RegisterUser() {
  console.log('\n👤 TEST 3: Register User (Skip if already registered)');
  console.log('Endpoint: POST /c/august/register');
  
  const proceed = await askQuestion('Test user registration? (y/n): ');
  if (proceed.toLowerCase() !== 'y') {
    console.log('⏭️  Skipping registration test');
    return true;
  }

  try {
    const response = await axios.post(
      `${BASE_URL}/c/august/register`,
      {
        source: 'WEB',
        phoneNumber: TEST_PHONE,
        user_role: 'DOCTOR'
      },
      {
        headers: {
          'Authorization': `Bearer ${AUGUST_TOKEN}`,
          'Content-Type': 'application/json'
        }
      }
    );
    
    logResponse('Register User', response);
    return true;
  } catch (error) {
    logResponse('Register User', null, error);
    return error.response?.status === 409; // Already exists is ok
  }
}

async function test4_GetAllDialogues() {
  console.log('\n📋 TEST 4: Get All Dialogues');
  console.log('Endpoint: GET /user/practitioner-dashboard/get-chats-by-dialogueId');
  
  if (!authData.accessToken) {
    console.log('❌ No auth token available. Please complete OTP verification first.');
    return false;
  }

  try {
    const response = await axios.get(
      `${BASE_URL}/user/practitioner-dashboard/get-chats-by-dialogueId`,
      {
        params: {
          limit: 100,
          dialogue_id: ''
        },
        headers: {
          'Authorization': `Bearer ${authData.accessToken}`,
          'Content-Type': 'application/json'
        }
      }
    );
    
    logResponse('Get All Dialogues', response);
    
    // Store first dialogue ID if available
    if (response.data && response.data.length > 0) {
      testDialogueId = response.data[0].id || response.data[0].dialogueId;
      console.log(`📌 Stored dialogue ID for testing: ${testDialogueId}`);
    }
    
    return true;
  } catch (error) {
    logResponse('Get All Dialogues', null, error);
    return false;
  }
}

async function test5_SendTextMessage() {
  console.log('\n💬 TEST 5: Send Text Message');
  console.log('Endpoint: POST /c/august/webhook');
  
  const dialogueId = uuidv4();
  const providerMessageId = uuidv4();
  const requestId = uuidv4();
  
  console.log(`📝 New dialogue ID: ${dialogueId}`);
  
  const messageText = await askQuestion('Enter message text (or press Enter for default): ');
  const text = messageText || 'Test message from API testing script';

  try {
    const response = await axios.post(
      `${BASE_URL}/c/august/webhook`,
      {
        dialogueId: dialogueId,
        dialogueType: 'patient-case',
        text: text,
        providerMessageId: providerMessageId,
        sender: 'human',
        source: 'WEB',
        phoneNumber: TEST_PHONE,
        timestamp: Date.now(),
        requestId: requestId
      },
      {
        headers: {
          'Authorization': authData.accessToken ? `Bearer ${authData.accessToken}` : undefined,
          'Content-Type': 'application/json'
        }
      }
    );
    
    logResponse('Send Text Message', response);
    testDialogueId = dialogueId; // Store for later tests
    return true;
  } catch (error) {
    logResponse('Send Text Message', null, error);
    return false;
  }
}

async function test6_GetMessagesByDialogueId() {
  console.log('\n📨 TEST 6: Get Messages by Dialogue ID');
  console.log('Endpoint: GET /user/practitioner-dashboard/get-chats-by-dialogueId');
  
  if (!authData.accessToken) {
    console.log('❌ No auth token available');
    return false;
  }
  
  const dialogueId = await askQuestion(`Enter dialogue ID (or press Enter for ${testDialogueId}): `);
  const idToUse = dialogueId || testDialogueId;
  
  if (!idToUse) {
    console.log('❌ No dialogue ID available');
    return false;
  }

  console.log(`🔍 Fetching messages for dialogue: ${idToUse}`);

  try {
    const response = await axios.get(
      `${BASE_URL}/user/practitioner-dashboard/get-chats-by-dialogueId`,
      {
        params: {
          limit: 30,
          dialogue_id: idToUse
        },
        headers: {
          'Authorization': `Bearer ${authData.accessToken}`,
          'Content-Type': 'application/json'
        }
      }
    );
    
    logResponse('Get Messages by Dialogue ID', response);
    return true;
  } catch (error) {
    logResponse('Get Messages by Dialogue ID', null, error);
    return false;
  }
}

async function test7_SendMessageWithFile() {
  console.log('\n📎 TEST 7: Send Message with File');
  console.log('Endpoint: POST /c/august/webhook');
  
  const proceed = await askQuestion('Test file upload? (y/n): ');
  if (proceed.toLowerCase() !== 'y') {
    console.log('⏭️  Skipping file upload test');
    return true;
  }

  const dialogueId = uuidv4();
  const providerMessageId = uuidv4();
  const requestId = uuidv4();
  
  // In real implementation, you'd upload to Azure first
  const mockFileUrl = 'https://example-blob.blob.core.windows.net/container/test-file.pdf';

  try {
    const response = await axios.post(
      `${BASE_URL}/c/august/webhook`,
      {
        dialogueId: dialogueId,
        dialogueType: 'patient-case',
        text: 'Patient report attached',
        providerMessageId: providerMessageId,
        attachment: mockFileUrl,
        fileExtension: '.pdf',
        messageType: 'pdf',
        sender: 'human',
        source: 'WEB',
        phoneNumber: TEST_PHONE,
        timestamp: Date.now(),
        requestId: requestId
      },
      {
        headers: {
          'Authorization': authData.accessToken ? `Bearer ${authData.accessToken}` : undefined,
          'Content-Type': 'application/json'
        }
      }
    );
    
    logResponse('Send Message with File', response);
    return true;
  } catch (error) {
    logResponse('Send Message with File', null, error);
    return false;
  }
}

async function test8_SendMessageMultipleFiles() {
  console.log('\n📎📎 TEST 8: Send Message with Multiple Files');
  console.log('Endpoint: POST /c/august/webhook');
  
  const proceed = await askQuestion('Test multiple file upload? (y/n): ');
  if (proceed.toLowerCase() !== 'y') {
    console.log('⏭️  Skipping multiple file upload test');
    return true;
  }

  const dialogueId = uuidv4();
  const providerMessageId = uuidv4();
  const requestId = uuidv4();

  try {
    const response = await axios.post(
      `${BASE_URL}/c/august/webhook`,
      {
        dialogueId: dialogueId,
        dialogueType: 'research',
        text: 'Multiple patient reports',
        providerMessageId: providerMessageId,
        attachment: [
          {
            url: 'https://example-blob.blob.core.windows.net/container/report1.png',
            fileExtension: '.png',
            messageType: 'image'
          },
          {
            url: 'https://example-blob.blob.core.windows.net/container/report2.pdf',
            fileExtension: '.pdf',
            messageType: 'pdf'
          }
        ],
        sender: 'human',
        source: 'WEB',
        phoneNumber: TEST_PHONE,
        timestamp: Date.now(),
        requestId: requestId
      },
      {
        headers: {
          'Authorization': authData.accessToken ? `Bearer ${authData.accessToken}` : undefined,
          'Content-Type': 'application/json'
        }
      }
    );
    
    logResponse('Send Message Multiple Files', response);
    return true;
  } catch (error) {
    logResponse('Send Message Multiple Files', null, error);
    return false;
  }
}

async function test9_RefreshToken() {
  console.log('\n🔄 TEST 9: Refresh Token');
  console.log('Endpoint: POST /auth/august/refresh-token');
  
  if (!authData.refreshToken) {
    console.log('❌ No refresh token available');
    return false;
  }

  const proceed = await askQuestion('Test token refresh? (y/n): ');
  if (proceed.toLowerCase() !== 'y') {
    console.log('⏭️  Skipping token refresh test');
    return true;
  }

  try {
    const response = await axios.post(
      `${BASE_URL}/auth/august/refresh-token`,
      { phone: TEST_PHONE },
      {
        headers: {
          'Authorization': `Bearer ${authData.refreshToken}`,
          'Content-Type': 'application/json'
        }
      }
    );
    
    logResponse('Refresh Token', response);
    
    // Update auth data with new token
    if (response.data.accessToken) {
      authData.accessToken = response.data.accessToken;
      fs.writeFileSync('gatekeeper-auth.json', JSON.stringify(authData, null, 2));
      console.log('💾 Updated auth tokens saved');
    }
    
    return true;
  } catch (error) {
    logResponse('Refresh Token', null, error);
    return false;
  }
}

// Main test runner
async function runTests() {
  console.log(`
${'='.repeat(60)}
🧪 GATEKEEPER API INTERACTIVE TEST SUITE
${'='.repeat(60)}
Base URL: ${BASE_URL}
Test Phone: ${TEST_PHONE}
Timestamp: ${new Date().toISOString()}
${'='.repeat(60)}

This will test all APIs interactively.
Results will be saved to: api-test-results.json
`);

  // Check for existing auth
  if (fs.existsSync('gatekeeper-auth.json')) {
    const savedAuth = JSON.parse(fs.readFileSync('gatekeeper-auth.json', 'utf8'));
    const useExisting = await askQuestion('Found existing auth tokens. Use them? (y/n): ');
    if (useExisting.toLowerCase() === 'y') {
      authData = savedAuth;
      console.log('✅ Loaded existing auth tokens');
    }
  }

  const tests = [
    { name: 'Request OTP', fn: test1_RequestOTP },
    { name: 'Verify OTP', fn: test2_VerifyOTP },
    { name: 'Register User', fn: test3_RegisterUser },
    { name: 'Get All Dialogues', fn: test4_GetAllDialogues },
    { name: 'Send Text Message', fn: test5_SendTextMessage },
    { name: 'Get Messages by Dialogue ID', fn: test6_GetMessagesByDialogueId },
    { name: 'Send Message with File', fn: test7_SendMessageWithFile },
    { name: 'Send Message Multiple Files', fn: test8_SendMessageMultipleFiles },
    { name: 'Refresh Token', fn: test9_RefreshToken }
  ];

  const results = [];

  for (const test of tests) {
    const cont = await askQuestion(`\n▶️  Run test: ${test.name}? (y/n/quit): `);
    
    if (cont.toLowerCase() === 'quit') {
      console.log('👋 Exiting test suite');
      break;
    }
    
    if (cont.toLowerCase() === 'y') {
      const success = await test.fn();
      results.push({ test: test.name, success });
      
      if (!success && test.name === 'Verify OTP') {
        console.log('⚠️  Auth failed. Some tests may not work without valid JWT.');
      }
    } else {
      console.log(`⏭️  Skipping ${test.name}`);
      results.push({ test: test.name, skipped: true });
    }
  }

  // Summary
  console.log(`\n
${'='.repeat(60)}
📊 TEST SUMMARY
${'='.repeat(60)}
Total: ${results.length}
Passed: ${results.filter(r => r.success).length}
Failed: ${results.filter(r => r.success === false).length}
Skipped: ${results.filter(r => r.skipped).length}
${'='.repeat(60)}
`);

  rl.close();
}

// Run tests
runTests().catch(error => {
  console.error('Unexpected error:', error);
  rl.close();
});