#!/usr/bin/env node

const axios = require('axios');
const { io } = require('socket.io-client');

async function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

async function testFullFlow() {
  console.log('🧪 Testing Full Gatekeeper Integration Flow');
  console.log('==========================================\n');

  let socket;
  const userId = '898a9e7b-6873-4c4c-b21c-f4786ee281ad';
  const phone = '+919819304846';
  const testThreadId = 'test-thread-' + Date.now();
  
  try {
    // 1. Connect and authenticate WebSocket
    console.log('1️⃣ Connecting WebSocket...');
    socket = io('http://localhost:3000', {
      transports: ['websocket']
    });

    await new Promise((resolve, reject) => {
      socket.on('connect', () => {
        console.log('✅ Connected:', socket.id);
        resolve();
      });
      socket.on('connect_error', reject);
      setTimeout(() => reject(new Error('Connection timeout')), 5000);
    });

    // 2. Authenticate with proper user data
    console.log('\n2️⃣ Authenticating...');
    socket.emit('authenticate', {
      token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJlbmNyeXB0ZWQiOnRydWUsImRhdGEiOiJVMkZzZEdWa1gxL2NSeHFSdFRwcm9JSWRjNnJoemZJUjU1ZDYzZzA3Y3Jyd29UR2l2UXk0RWdqTm1iT25leXU2SlVhVE5aNTV3WGoxa0I1a3R0Qk9JVDhod2JGclBtNnBSeFZxS2trYUwxQUU2UlpQM1pDWlNYSVhxWG5mUDhyU2F0S0ZUaDlLVEdIUHgraDRIeHJyb2VzdVVhZWtFVXlzRlZTRTdhQXM1QU09IiwiaWF0IjoxNzUwOTEyNjQ3LCJleHAiOjE3NTA5MTQ0NDd9.lHvRqsLKlclyZ4WPy3-SPAwOvFpk4tjdcSxVKkuR2xE',
      user: {
        id: userId,
        userId: userId,
        phone: phone,
        name: 'Test User'
      }
    });

    await new Promise((resolve) => {
      socket.on('authenticated', (data) => {
        console.log('✅ Authenticated:', data);
        resolve();
      });
    });

    // 3. Set up response listener
    const responses = [];
    socket.on('message_response', (data) => {
      console.log('\n📥 Received WebSocket response:');
      console.log('Thread ID:', data.threadId);
      console.log('Message:', data.response?.content?.substring(0, 100) + '...');
      responses.push(data);
    });

    // 4. Send a message to establish thread mapping
    console.log('\n3️⃣ Sending message to establish thread mapping...');
    console.log(`Thread ID: ${testThreadId}`);
    
    socket.emit('send_message', {
      threadId: testThreadId,
      content: 'Hello, I need help with a patient case',
      conversationType: 'patient-case',
      attachments: []
    });

    socket.on('message_sent', (data) => {
      console.log('✅ Message sent:', data);
    });

    // Wait for message to be processed
    await sleep(2000);

    // 5. Test emit-message WITHOUT threadId (simulating Gatekeeper)
    console.log('\n4️⃣ Testing emit-message WITHOUT threadId...');
    const gatekeeperPayload1 = {
      userId: userId,
      messageText: encodeURIComponent("Thank you for your message. I'm analyzing the patient case now."),
      phone: phone
      // No dialogueId - testing thread mapping
    };

    const response1 = await axios.post('http://localhost:3000/api/emit-message', gatekeeperPayload1);
    console.log('✅ Emit response:', response1.data);

    await sleep(1000);

    // Check if we received the response
    console.log(`\n📊 Received ${responses.length} responses via WebSocket`);
    if (responses.length > 0) {
      const lastResponse = responses[responses.length - 1];
      console.log('✅ Thread ID in response:', lastResponse.threadId);
      console.log('✅ Matches our thread?', lastResponse.threadId === testThreadId);
    }

    // 6. Send another message to a different thread
    console.log('\n5️⃣ Sending message to a different thread...');
    const newThreadId = 'new-thread-' + Date.now();
    
    socket.emit('send_message', {
      threadId: newThreadId,
      content: 'This is a different conversation',
      conversationType: 'research',
      attachments: []
    });

    await sleep(2000);

    // 7. Test emit-message again - should use new thread
    console.log('\n6️⃣ Testing emit-message with new thread context...');
    const gatekeeperPayload2 = {
      userId: userId,
      messageText: "Here's information about the research topic you asked about.",
      phone: phone
    };

    const response2 = await axios.post('http://localhost:3000/api/emit-message', gatekeeperPayload2);
    console.log('✅ Second emit response:', response2.data);

    await sleep(1000);

    // Final check
    console.log(`\n📊 Total responses received: ${responses.length}`);
    if (responses.length >= 2) {
      console.log('✅ First response thread:', responses[0].threadId);
      console.log('✅ Second response thread:', responses[1].threadId);
    }

  } catch (error) {
    console.error('\n❌ Test failed:', error.message);
    if (error.response) {
      console.error('Response:', error.response.data);
    }
  } finally {
    if (socket) {
      console.log('\n🔌 Disconnecting...');
      socket.disconnect();
    }
    console.log('\n✅ Test complete');
    process.exit(0);
  }
}

// Run the test
testFullFlow();