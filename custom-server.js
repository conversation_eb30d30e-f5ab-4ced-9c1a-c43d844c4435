require('dotenv').config({ path: '.env' });

const express = require('express');
const next = require('next');
const http = require('http');
const { Server } = require("socket.io");
const axios = require('axios');
const { v4: uuidv4 } = require('uuid');
const bodyParser = require('body-parser');
const { createClient } = require('redis');
const { createAdapter } = require('@socket.io/redis-adapter');
const jwt = require('jsonwebtoken');
const { BlobServiceClient, StorageSharedKeyCredential } = require('@azure/storage-blob');
const { getFileCategory } = require('./src/lib/fileValidation');
const transcriptionService = require('./src/lib/transcription-service');
const transcriptionCleanup = require('./src/lib/transcription-cleanup');

const GATEKEEPER_URL = process.env.GATEKEEPER_URL;
if (!GATEKEEPER_URL) {
  throw new Error('GATEKEEPER_URL is not set');
}

// Helper function to get simplified file type for attachments
function getSimplifiedFileType(mimeType) {
  if (mimeType === 'application/pdf') return 'pdf';
  if (mimeType.startsWith('image/')) return 'image';
  return getFileCategory(mimeType); // fallback to existing logic
}

const dev = process.env.NODE_ENV !== 'production';
const app = next({ dev });
const handle = app.getRequestHandler();
const port = process.env.PORT || 3000;

// Simple console logger for now (we'll create a proper logger later)
const logger = {
  info: (...args) => console.log('[INFO]', new Date().toISOString(), ...args),
  error: (...args) => console.error('[ERROR]', new Date().toISOString(), ...args),
  warn: (...args) => console.warn('[WARN]', new Date().toISOString(), ...args)
};

// Request logging utility
const fs = require('fs').promises;
const path = require('path');
const { start } = require('repl');

const REQUEST_LOG_FILE = path.join(__dirname, 'requests.log');

async function logRequest(type, data) {
  try {
    const timestamp = new Date().toISOString();
    let logEntry = `\n${'='.repeat(80)}\n`;
    logEntry += `[${timestamp}] TYPE: ${type}\n`;
    logEntry += `${'='.repeat(80)}\n`;

    // Format as curl command based on type
    if (type === 'INCOMING_HTTP') {
      logEntry += formatIncomingHttpAsCurl(data);
    } else if (type === 'OUTGOING_HTTP') {
      logEntry += formatOutgoingHttpAsCurl(data);
    } else if (type === 'WEBSOCKET_IN') {
      logEntry += formatWebSocketEvent(data, 'INCOMING');
    } else if (type === 'WEBSOCKET_OUT') {
      logEntry += formatWebSocketEvent(data, 'OUTGOING');
    }

    logEntry += '\n';

    await fs.appendFile(REQUEST_LOG_FILE, logEntry);
  } catch (error) {
    console.error('Logging error:', error);
  }
}

// Format incoming HTTP requests as curl
function formatIncomingHttpAsCurl(data) {
  let curl = `curl -X ${data.method} "http://localhost:3000${data.url}"`;

  // Add headers
  if (data.headers.authorization) {
    curl += ` \\\n    -H "Authorization: ${data.headers.authorization}"`;
  }
  if (data.headers['content-type']) {
    curl += ` \\\n    -H "Content-Type: ${data.headers['content-type']}"`;
  }

  // Add body
  if (data.body && Object.keys(data.body).length > 0) {
    curl += ` \\\n    -d '${JSON.stringify(data.body, null, 2)}'`;
  }

  // Add response info
  curl += `\n\nRESPONSE:\nStatus: ${data.responseStatus}\nBody: ${JSON.stringify(data.responseBody, null, 2)}`;
  curl += `\nDuration: ${data.duration}ms`;

  return curl;
}

// Format outgoing HTTP requests as curl
function formatOutgoingHttpAsCurl(data) {
  let curl = `curl -X ${data.method} "${data.url}"`;

  // Add authorization header
  if (data.headers?.authorization) {
    curl += ` \\\n    -H "Authorization: ${data.headers.authorization}"`;
  }

  // Add content-type
  if (data.headers?.['content-type']) {
    curl += ` \\\n    -H "Content-Type: ${data.headers['content-type']}"`;
  }

  // Add body
  if (data.data) {
    curl += ` \\\n    -d '${JSON.stringify(data.data, null, 2)}'`;
  }

  // Add response
  if (data.response) {
    curl += `\n\nRESPONSE:\nStatus: ${data.response.status}\nBody: ${JSON.stringify(data.response.data, null, 2)}`;
  }

  if (data.error) {
    curl += `\n\nERROR: ${data.error}`;
  }

  if (data.duration !== undefined) {
    curl += `\nDuration: ${data.duration}ms`;
  }

  return curl;
}

// Format WebSocket events
function formatWebSocketEvent(data, direction) {
  let output = `WebSocket ${direction}\n`;
  output += `Socket ID: ${data.socketId}\n`;
  output += `Event: ${data.event}\n`;
  output += `Data: ${JSON.stringify(data.data, null, 2)}`;
  return output;
}

// Wrapper for axios requests
async function makeRequest(config) {
  const startTime = Date.now();

  try {
    const response = await axios(config);

    await logRequest('OUTGOING_HTTP', {
      method: config.method || 'GET',
      url: config.url,
      headers: config.headers,
      data: config.data,
      response: {
        status: response.status,
        data: response.data
      },
      duration: Date.now() - startTime
    });

    return response;
  } catch (error) {
    await logRequest('OUTGOING_HTTP', {
      method: config.method || 'GET',
      url: config.url,
      headers: config.headers,
      data: config.data,
      error: error.message,
      response: error.response ? {
        status: error.response.status,
        data: error.response.data
      } : null,
      duration: Date.now() - startTime
    });

    throw error;
  }
}

// Azure Blob Storage utilities
const uploadBytesToBlob = async (containerName, blobName, body) => {
  const accountName = process.env.AZURE_STORAGE_ACCOUNT_NAME || 'augustbuckets';
  const accountKey = process.env.AZURE_STORAGE_ACCOUNT_KEY;

  if (!accountName || !accountKey) {
    throw new Error('Azure Storage credentials are not properly configured');
  }

  const credential = new StorageSharedKeyCredential(accountName, accountKey);
  const blobServiceClient = new BlobServiceClient(
    `https://${accountName}.blob.core.windows.net`,
    credential
  );

  const containerClient = blobServiceClient.getContainerClient(containerName);
  const blockBlobClient = containerClient.getBlockBlobClient(blobName);

  await blockBlobClient.upload(body, body.length);
  return blockBlobClient.url;
};

const uploadFileToBlobFromBuffer = async (file, containerName, blobName) => {
  try {
    logger.info('Uploading file to blob:', { containerName, blobName });
    const blobUrl = await uploadBytesToBlob(containerName, blobName, file);
    logger.info('File uploaded successfully:', { blobUrl });
    return blobUrl;
  } catch (error) {
    logger.error('Blob upload error:', error);
    throw error;
  }
};

// Redis setup for Socket.io adapter and Gatekeeper Pub/Sub
const setupRedis = async () => {
  // Skip Redis if not configured or in development
  if (!process.env.REDIS_URL || !process.env.REDIS_PASSWORD) {
    logger.warn('Redis not configured (missing REDIS_URL or REDIS_PASSWORD), using in-memory adapter');
    return null;
  }

  try {
    const clientConfig = {
      url: `rediss://${process.env.REDIS_URL}:6380`,
      password: process.env.REDIS_PASSWORD,
      socket: {
        tls: true,
        rejectUnauthorized: false,
        connectTimeout: 30000, // Reduced from 60s to 30s
        commandTimeout: 10000,  // Increased from 5s to 10s
        reconnectDelay: 2000,   // Increased from 1s to 2s
        retryDelayOnFailover: 2000,
        maxRetriesPerRequest: 5, // Increased from 3 to 5
        keepAlive: true,
        noDelay: true,
        // Add ping interval to keep connection alive
        pingInterval: 30000, // 30 seconds
        lazyConnect: true, // Don't auto-connect, connect manually
      }
    };

    const pubClient = createClient(clientConfig);
    const subClient = pubClient.duplicate();
    const gatekeeperSubClient = pubClient.duplicate();

    // Enhanced error handling with graceful degradation and retry logic
    const setupClientHandlers = (client, clientName) => {
      client.on('error', (err) => {
        logger.error(`Redis ${clientName} Client Error:`, err.message);
        // Don't crash the server on Redis errors

        // Attempt reconnection after a delay
        setTimeout(() => {
          if (!client.isOpen) {
            logger.info(`Attempting to reconnect Redis ${clientName} client...`);
            client.connect().catch(reconnectErr => {
              logger.error(`Failed to reconnect Redis ${clientName} client:`, reconnectErr.message);
            });
          }
        }, 5000);
      });

      client.on('reconnecting', () => {
        logger.info(`Redis ${clientName} Client reconnecting...`);
      });

      client.on('ready', () => {
        logger.info(`Redis ${clientName} Client ready`);
      });

      client.on('end', () => {
        logger.warn(`Redis ${clientName} Client connection ended`);
      });

      client.on('connect', () => {
        logger.info(`Redis ${clientName} Client connected`);
      });
    };

    setupClientHandlers(pubClient, 'Pub');
    setupClientHandlers(subClient, 'Sub');
    setupClientHandlers(gatekeeperSubClient, 'Gatekeeper Sub');

    // Connect with timeout and retry logic
    const connectWithRetry = async (client, clientName, maxRetries = 3) => {
      for (let i = 0; i < maxRetries; i++) {
        try {
          await client.connect();
          logger.info(`${clientName} connected successfully`);
          return;
        } catch (error) {
          logger.error(`${clientName} connection attempt ${i + 1} failed:`, error.message);
          if (i === maxRetries - 1) throw error;
          await new Promise(resolve => setTimeout(resolve, 2000 * (i + 1))); // Progressive delay
        }
      }
    };

    // Connect all clients with retry logic
    await Promise.all([
      connectWithRetry(pubClient, 'Redis Pub Client'),
      connectWithRetry(subClient, 'Redis Sub Client'),
      connectWithRetry(gatekeeperSubClient, 'Redis Gatekeeper Sub Client')
    ]);

    logger.info('All Redis clients connected successfully');
    return { pubClient, subClient, gatekeeperSubClient };
  } catch (error) {
    logger.error('Redis connection failed:', error.message);
    logger.warn('Continuing without Redis - using in-memory adapter');
    return null;
  }
};

// Setup Gatekeeper Redis Pub/Sub subscription
const setupGatekeeperSubscription = async (gatekeeperSubClient, io) => {
  if (!gatekeeperSubClient) {
    logger.warn('No Gatekeeper Redis client - AI responses will be mocked');
    return;
  }

  try {
    // Subscribe to Gatekeeper response pattern
    const channelPattern = 'gatekeeper:response:*';

    // Add connection state checks before operations
    const ensureConnection = async (client, clientName) => {
      if (!client.isOpen) {
        logger.warn(`${clientName} not connected, attempting to reconnect...`);
        try {
          await client.connect();
          logger.info(`${clientName} reconnected successfully`);
        } catch (error) {
          logger.error(`Failed to reconnect ${clientName}:`, error.message);
          throw error;
        }
      }
    };

    await ensureConnection(gatekeeperSubClient, 'Gatekeeper Sub Client');

    await gatekeeperSubClient.pSubscribe(channelPattern, (message, channel) => {
      try {
        logger.info('Received Gatekeeper response:', { channel, messageLength: message.length });

        // Parse the channel to extract phone number
        // Expected format: gatekeeper:response:+************:dialogue-id
        const channelParts = channel.split(':');
        if (channelParts.length < 4) {
          logger.warn('Invalid channel format:', channel);
          return;
        }

        const phoneNumber = channelParts[2];
        const dialogueId = channelParts[3];

        // Parse the message
        let responseData;
        try {
          responseData = JSON.parse(message);
        } catch (parseError) {
          logger.error('Failed to parse Gatekeeper message:', parseError);
          return;
        }

        // Find connected users with this phone number
        const connectedSockets = Array.from(io.sockets.sockets.values())
          .filter(socket => socket.phone === phoneNumber);

        if (connectedSockets.length === 0) {
          logger.info('No connected sockets for phone:', phoneNumber);
          return;
        }

        // Send response to all connected sockets for this user
        connectedSockets.forEach(socket => {
          socket.emit('message_response', {
            threadId: dialogueId,
            messageId: responseData.messageId || uuidv4(),
            response: {
              content: responseData.text || responseData.content,
              attachments: responseData.attachments || []
            },
            timestamp: new Date().toISOString(),
            source: 'gatekeeper'
          });
        });

        logger.info('AI response delivered to client:', {
          phoneNumber,
          dialogueId,
          socketsCount: connectedSockets.length
        });

      } catch (error) {
        logger.error('Error processing Gatekeeper message:', error);
      }
    });

    logger.info('Subscribed to Gatekeeper responses:', channelPattern);

  } catch (error) {
    logger.error('Failed to setup Gatekeeper subscription:', error);
    // Don't throw the error, just log it and continue
  }
};

// Send message to Gatekeeper
async function sendMessageToGatekeeper(message) {
  const { threadId, content, userId, conversationType, attachments } = message;

  logger.info("Sending message to Gatekeeper:", {
    threadId,
    userId,
    conversationType,
    hasAttachments: attachments && attachments.length > 0
  });

  try {
    const gatekeeperMessage = {
      messageId: uuidv4(),
      threadId,
      userId,
      content,
      conversationType,
      attachments: attachments || [],
      timestamp: new Date().toISOString(),
      source: 'doctor-dashboard'
    };

    const response = await makeRequest({
      method: 'post',
      url: `${GATEKEEPER_URL}/api/messages`,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${process.env.BEARER_TOKEN}`
      },
      data: gatekeeperMessage
    });

    logger.info('Gatekeeper response:', {
      status: response.status,
      messageId: gatekeeperMessage.messageId
    });

    return response.data;
  } catch (error) {
    logger.error('Gatekeeper error:', {
      error: error.message,
      response: error.response?.data
    });
    throw error;
  }
}

// JWT verification for Socket.io
const verifySocketJWT = (token) => {
  try {
    if (!token) {
      logger.error('No token provided');
      return null;
    }

    // Decode the JWT token without verification for now
    // In production, you would verify with the proper secret
    const decoded = jwt.decode(token);

    if (!decoded) {
      logger.error('Failed to decode JWT token');
      return null;
    }

    console.log('🔐 Decoded JWT payload:', decoded);

    // Extract user information from the token
    // Gatekeeper tokens have the user data in a 'data' field that needs decryption
    // For now, we'll return minimal info and rely on the client-provided user data

    // Don't generate fake IDs - let the client provide the actual user data
    const userInfo = {
      id: null, // Will be provided by client
      phone: null, // Will be provided by client
      role: "doctor",
      jwt: token // Store the original JWT for API calls
    };

    console.log('✅ JWT token decoded successfully');

    return userInfo;

  } catch (error) {
    logger.error('JWT verification failed:', error);
    return null;
  }
};

// Global maps to track last thread ID per phone number and user ID
const phoneToLastThread = new Map();
const userToLastThread = new Map();

app.prepare().then(async () => {
  const server = express();

  // Remove manual static file serving - let Next.js handle everything
  // Next.js will serve its own static files through the handle function

  // Only use body parser for specific routes, not for Next.js API routes
  server.use('/api/webhook', bodyParser.json({ limit: '50mb' }));
  server.use('/api/webhook', bodyParser.urlencoded({ extended: true, limit: '50mb' }));
  server.use('/api/emit-message', bodyParser.json({ limit: '50mb' }));
  server.use('/api/emit-message', bodyParser.urlencoded({ extended: true, limit: '50mb' }));
  server.use('/api/transcription-callback', bodyParser.json({ limit: '50mb' }));
  server.use('/api/transcription-callback', bodyParser.urlencoded({ extended: true, limit: '50mb' }));

  // Request logging middleware
  server.use(async (req, res, next) => {
    const startTime = Date.now();
    const originalSend = res.send;
    const originalJson = res.json;

    let responseBody;

    // Capture response
    res.send = function (data) {
      responseBody = data;
      originalSend.call(this, data);
    };

    res.json = function (data) {
      responseBody = data;
      originalJson.call(this, data);
    };

    // Log on response finish
    res.on('finish', async () => {
      // Only log API routes and emit-message
      if (req.url.startsWith('/api/') || req.url.includes('emit-message')) {
        await logRequest('INCOMING_HTTP', {
          method: req.method,
          url: req.url,
          headers: req.headers,
          body: req.body,
          responseStatus: res.statusCode,
          responseBody: responseBody,
          duration: Date.now() - startTime
        });
      }
    });

    next();
  });

  // Setup Redis if available
  const redisConfig = await setupRedis();

  const httpServer = http.createServer(server);
  const io = new Server(httpServer, {
    maxHttpBufferSize: 1e8, // 100MB for file uploads
    cors: {
      origin: (origin, callback) => {
        // Allow all origins in development
        if (dev) {
          callback(null, true);
          return;
        }

        // Allow requests with no origin (same-origin)
        if (!origin) {
          callback(null, true);
          return;
        }

        // In production, only allow your domain
        const allowedOrigins = [
          'http://localhost:3000',
          'https://doc.getbeyondhealth.com',
          'https://doc.meetaugust.ai',
          'https://doctor-dashboard.vercel.app',
          'https://august-doctor-dashboard.getbeyondhealth.com',
          'http://ec2-3-111-56-167.ap-south-1.compute.amazonaws.com:3000',
          'https://ec2-3-111-56-167.ap-south-1.compute.amazonaws.com:3000'
        ];

        if (allowedOrigins.includes(origin)) {
          callback(null, true);
        } else {
          callback(new Error('Not allowed by CORS'));
        }
      },
      methods: ["GET", "POST"],
      credentials: true
    }
  });

  // Use Redis adapter if available
  if (redisConfig) {
    io.adapter(createAdapter(redisConfig.pubClient, redisConfig.subClient));

    // Setup Gatekeeper Pub/Sub subscription for AI responses
    await setupGatekeeperSubscription(redisConfig.gatekeeperSubClient, io);
  }

  // Middleware to verify webhook requests from Gatekeeper
  const verifyWebhookToken = (req, res, next) => {
    const bearerHeader = req.headers['authorization'];
    if (bearerHeader) {
      const bearer = bearerHeader.split(' ');
      const bearerToken = bearer[1];

      if (bearerToken === process.env.BEARER_TOKEN) {
        next();
      } else {
        res.status(403).json({ error: 'Invalid token' });
      }
    } else {
      res.status(401).json({ error: 'No authorization header' });
    }
  };

  // Webhook endpoint for Gatekeeper to send responses
  server.post('/api/webhook/message-response', verifyWebhookToken, async (req, res) => {
    logger.info('Webhook received from Gatekeeper:', req.body);

    try {
      const { threadId, userId, response, messageId } = req.body;

      // Emit the response to the specific user
      io.to(userId).emit("message_response", {
        threadId,
        messageId,
        response,
        timestamp: new Date().toISOString()
      });

      res.status(200).json({ success: true, message: 'Response delivered' });
    } catch (error) {
      logger.error('Webhook processing error:', error);
      res.status(500).json({ error: 'Failed to process webhook' });
    }
  });

  // IMPORTANT: Define Express routes BEFORE Next.js routes
  // This ensures Express handles these specific routes instead of Next.js

  // Emit message endpoint - Gatekeeper calls this to send AI responses
  server.post('/api/emit-message', async (req, res) => {
    logger.info('📥 Received emit-message from Gatekeeper:', JSON.stringify(req.body, null, 2));

    try {
      // Gatekeeper sends different message types:
      // AI responses: { userId, messageText, phone, dialogueId, ... }
      // Chunk confirmations: { userId, messageType, chunkId, ... }
      const {
        userId,
        messageType,
        messageText,
        phone,
        dialogueId: incomingDialogueId,
        text,
        content,
        providerMessageId,
        messageId,
        sender,
        attachments,
        timestamp,
        phoneNumber,
        // Chunk upload specific fields
        chunkId,
        isStream,
        isComplete,
        sequenceNumber,
        sessionId,
        error,
        retryable,
        retryAfter,
        // Transcription fields
        transcription,
        isFinalChunk,
        requestId,
        threadId,
        // SOAP notes field
        soapNotes,
        // Transcription complete fields
        transcriptionSessionId,
        totalChunks,
        totalDuration,
        referenceChunkId,
        status,
      } = req.body;

      // Use phone from the request (Gatekeeper format) or phoneNumber (old format)
      const userPhone = phone || phoneNumber;

      // Decode the messageText if it's URL encoded
      const decodedMessage = messageText ? decodeURIComponent(messageText) : (text || content || '');

      // Find connected sockets for this user by userId (more reliable than phone)
      const connectedSockets = Array.from(io.sockets.sockets.values())
        .filter(socket => socket.userId === userId);

      if (connectedSockets.length === 0) {
        logger.warn('No connected sockets found for userId:', userId);
        // Still return success to Gatekeeper
        return res.status(200).json({ success: true, message: 'No active connections' });
      }

      // Route based on message type
      if (messageType) {
        switch (messageType) {
          case 'chunk_upload_confirmation':
            logger.info('📤 Processing chunk upload confirmation:', { userId, chunkId, sequenceNumber });
            connectedSockets.forEach(socket => {
              socket.emit('chunk_upload_success', {
                chunkId: chunkId,
                sequenceNumber: sequenceNumber,
                sessionId: sessionId,
                timestamp: timestamp || new Date().toISOString()
              });
            });

            return res.status(200).json({
              success: true,
              message: 'Chunk upload confirmation delivered',
              deliveredTo: connectedSockets.length
            });

          case 'chunk_upload_failure':
            logger.info('📤 Processing chunk upload failure:', { userId, chunkId, error });
            connectedSockets.forEach(socket => {
              socket.emit('chunk_upload_failed', {
                chunkId: chunkId,
                sequenceNumber: sequenceNumber,
                sessionId: sessionId,
                error: error,
                retryable: retryable !== false, // Default to true
                retryAfter: retryAfter || 1000,
                timestamp: timestamp || new Date().toISOString()
              });
            });

            return res.status(200).json({
              success: true,
              message: 'Chunk upload failure delivered',
              deliveredTo: connectedSockets.length
            });

          case 'chunk_transcription':
            logger.info('📝 Processing chunk transcription:', {
              userId,
              chunkId,
              sequenceNumber,
              isFinalChunk,
              requestId,
              transcriptionLength: transcription?.length
            });

            connectedSockets.forEach(socket => {
              socket.emit('chunk_transcription', {
                chunkId: chunkId,
                sequenceNumber: sequenceNumber,
                sessionId: requestId, // For consistency with existing handlers
                requestId: requestId,
                transcription: transcription,
                isFinalChunk: isFinalChunk || false,
                timestamp: timestamp || new Date().toISOString()
              });
            });

            return res.status(200).json({
              success: true,
              message: 'Chunk transcription delivered',
              deliveredTo: connectedSockets.length
            });

          case 'transcription_complete':
            logger.info('✅ Processing transcription complete:', {
              userId,
              requestId,
              threadId,
              totalChunks,
              totalDuration
            });

            connectedSockets.forEach(socket => {
              socket.emit('transcription_complete', {
                requestId: requestId,
                sessionId: transcriptionSessionId,
                threadId: threadId,
                totalChunks: totalChunks,
                totalDuration: totalDuration,
                status: status || 'completed',
                timestamp: timestamp || new Date().toISOString()
              });
            });

            return res.status(200).json({
              success: true,
              message: 'Transcription complete notification delivered',
              deliveredTo: connectedSockets.length
            });

          case 'soap_notes_generated':
            logger.info('📋 Processing SOAP notes:', {
              userId,
              requestId,
              threadId,
              soapNotesLength: soapNotes?.length
            });

            connectedSockets.forEach(socket => {
              socket.emit('soap_notes_generated', {
                requestId: requestId,
                sessionId: requestId, // For consistency
                threadId: threadId,
                soapNotes: soapNotes,
                timestamp: timestamp || new Date().toISOString()
              });
            });

            return res.status(200).json({
              success: true,
              message: 'SOAP notes delivered',
              deliveredTo: connectedSockets.length
            });

          default:
            logger.warn('Unknown message type received:', messageType);
            // Fall through to handle as regular AI message
            break;
        }
      }

      // Use dialogueId from Gatekeeper directly - it's the authoritative source
      // If no dialogueId provided, try to find the last active dialogue for this user
      let dialogueId = incomingDialogueId;
      let finalDialogueId = dialogueId;
      if (!dialogueId) {
        // First try to get dialogue ID by user ID (most accurate)
        finalDialogueId = userToLastThread.get(userId);
        if (finalDialogueId) {
          logger.info(`Using last dialogue ID for user ${userId}: ${finalDialogueId}`);
        } else {
          // Find the dialogue ID from any connected socket for this specific user
          const userSocket = connectedSockets.find(socket => socket.userId === userId && socket.lastThreadId);
          if (userSocket && userSocket.lastThreadId) {
            finalDialogueId = userSocket.lastThreadId;
            logger.info(`Found dialogue ID from user's socket: ${finalDialogueId}`);
            // Store it for future use
            userToLastThread.set(userId, finalDialogueId);
          } else {
            logger.warn(`⚠️  No dialogueId available for user ${userId} - cannot route message correctly`);
            // Don't set dialogueId to 'unknown' - let the client handle missing dialogueId
            finalDialogueId = null;
          }
        }
      }

      // Prepare the message to emit
      // IMPORTANT: Validate sender to prevent user messages appearing as AI
      // Gatekeeper should only send AI responses through emit-message
      const validatedSender = (sender === 'user') ? 'user' : 'ai'; // Default to 'ai' for safety

      const responseMessage = {
        threadId: finalDialogueId, // Frontend expects threadId, but it's actually dialogueId
        dialogueId: finalDialogueId, // Include both for compatibility
        messageId: messageId || providerMessageId || uuidv4(),
        response: {
          content: decodedMessage,
          attachments: attachments || []
        },
        sender: validatedSender,
        timestamp: timestamp || new Date().toISOString(),
        source: 'gatekeeper',
        userId: userId, // Include userId in the response,
        isStream: isStream || false,
        isComplete: isComplete || false,
        chunkId: chunkId || null
      };

      // Send to all connected sockets for this user
      connectedSockets.forEach(socket => {
        socket.emit('message_response', responseMessage);
        logger.info(`📤 Emitted response to socket ${socket.id}`, {
          userId: userId,
          dialogueId: finalDialogueId,
          sender: responseMessage.sender,
          messagePreview: decodedMessage.substring(0, 50),
          isStream: isStream,
          isComplete: isComplete,
          chunkId: chunkId
        });
      });

      res.status(200).json({
        success: true,
        message: 'Response delivered',
        deliveredTo: connectedSockets.length
      });

    } catch (error) {
      logger.error('❌ Error processing emit-message:', error);
      res.status(500).json({ error: 'Failed to process message' });
    }
  });

  // GET handler for emit-message endpoint (for debugging)
  server.get('/api/emit-message', (req, res) => {
    res.status(200).json({
      status: 'ok',
      endpoint: '/api/emit-message',
      purpose: 'Receives messages from Gatekeeper and forwards to WebSocket clients',
      method: 'POST required'
    });
  });

  // Transcription callback endpoint - receives results from transcription service
  server.post('/api/transcription-callback', async (req, res) => {
    logger.info('🎤 Received transcription callback:', JSON.stringify(req.body, null, 2));

    try {
      const {
        chunk_id,
        session_id,
        status,
        transcription,
        speakers,
        timestamp,
        error,
        metadata
      } = req.body;

      // Extract user info from metadata
      const userId = metadata?.userId;
      const threadId = metadata?.threadId;

      if (!userId) {
        logger.warn('No userId provided in transcription callback metadata');
        return res.status(400).json({ error: 'Missing userId in metadata' });
      }

      // Update chunk in database with transcription result
      let sessionInfo = null;
      try {
        const updatedChunk = await transcriptionService.updateChunkWithTranscription({
          chunkId: chunk_id,
          transcriptionText: transcription,
          speakers: speakers || [],
          status: status
        });

        if (updatedChunk) {
          logger.info('Updated chunk in database:', {
            chunkId: chunk_id,
            dbChunkId: updatedChunk.id,
            status: status
          });

          // Check if this session is stopping and all chunks are done
          if (status === 'success') {
            sessionInfo = await transcriptionService.checkAndCreateSummaryIfReady(
              updatedChunk.transcription_session_id
            );

            if (sessionInfo && sessionInfo.summaryCreated) {
              logger.info('✅ All chunks processed, summary created:', {
                sessionId: session_id,
                summaryId: sessionInfo.summary.id,
                fullTranscriptLength: sessionInfo.summary.full_transcript?.length || 0
              });

              // Emit completion event to all connected sockets for this user
              const completionSockets = Array.from(io.sockets.sockets.values())
                .filter(socket => socket.userId === userId);

              completionSockets.forEach(socket => {
                socket.emit('transcription_completed', {
                  sessionId: session_id,
                  summary: sessionInfo.summary,
                  fullTranscript: sessionInfo.summary.full_transcript
                });
              });
            }
          }
        }
      } catch (dbError) {
        logger.error('Error updating chunk in database:', dbError);
        // Don't fail the whole callback if database update fails
      }

      // Find connected sockets for this user
      const connectedSockets = Array.from(io.sockets.sockets.values())
        .filter(socket => socket.userId === userId);

      if (connectedSockets.length === 0) {
        logger.warn('No connected sockets found for userId:', userId);
        return res.status(200).json({
          success: true,
          message: 'No active connections for user'
        });
      }

      // Prepare transcription result message
      const transcriptionResult = {
        chunkId: chunk_id,
        sessionId: session_id,
        status: status,
        transcription: transcription,
        speakers: speakers,
        timestamp: timestamp || new Date().toISOString(),
        error: error,
        threadId: threadId,
        userId: userId
      };

      // Send to all connected sockets for this user
      connectedSockets.forEach(socket => {
        socket.emit('transcription_result', transcriptionResult);
        logger.info(`📤 Emitted transcription result to socket ${socket.id}`, {
          userId: userId,
          chunkId: chunk_id,
          sessionId: session_id,
          status: status,
          transcriptionPreview: transcription ? transcription.substring(0, 50) + '...' : null
        });
      });

      res.status(200).json({
        success: true,
        message: 'Transcription result delivered',
        deliveredTo: connectedSockets.length,
        chunkId: chunk_id,
        sessionId: session_id
      });

    } catch (error) {
      logger.error('❌ Error processing transcription callback:', error);
      res.status(500).json({ error: 'Failed to process transcription callback' });
    }
  });

  // GET handler for transcription-callback endpoint (for debugging)
  server.get('/api/transcription-callback', (req, res) => {
    res.status(200).json({
      status: 'ok',
      endpoint: '/api/transcription-callback',
      purpose: 'Receives transcription results from audio transcription service',
      method: 'POST required'
    });
  });

  // Transcription callback endpoint - receives results from transcription service
  server.post('/api/transcription-callback', async (req, res) => {
    logger.info('🎤 Received transcription callback:', JSON.stringify(req.body, null, 2));

    try {
      const {
        chunk_id,
        session_id,
        status,
        transcription,
        speakers,
        timestamp,
        error,
        metadata
      } = req.body;

      // Extract user info from metadata
      const userId = metadata?.userId;
      const threadId = metadata?.threadId;

      if (!userId) {
        logger.warn('No userId provided in transcription callback metadata');
        return res.status(400).json({ error: 'Missing userId in metadata' });
      }

      // Update chunk in database with transcription result
      let sessionInfo = null;
      try {
        const updatedChunk = await transcriptionService.updateChunkWithTranscription({
          chunkId: chunk_id,
          transcriptionText: transcription,
          speakers: speakers || [],
          status: status
        });

        if (updatedChunk) {
          logger.info('Updated chunk in database:', {
            chunkId: chunk_id,
            dbChunkId: updatedChunk.id,
            status: status
          });

          // Check if this session is stopping and all chunks are done
          if (status === 'success') {
            sessionInfo = await transcriptionService.checkAndCreateSummaryIfReady(
              updatedChunk.transcription_session_id
            );

            if (sessionInfo && sessionInfo.summaryCreated) {
              logger.info('✅ All chunks processed, summary created:', {
                sessionId: session_id,
                summaryId: sessionInfo.summary.id,
                fullTranscriptLength: sessionInfo.summary.full_transcript?.length || 0
              });

              // Emit completion event to all connected sockets for this user
              const completionSockets = Array.from(io.sockets.sockets.values())
                .filter(socket => socket.userId === userId);

              completionSockets.forEach(socket => {
                socket.emit('transcription_completed', {
                  sessionId: session_id,
                  summary: sessionInfo.summary,
                  fullTranscript: sessionInfo.summary.full_transcript
                });
              });
            }
          }
        }
      } catch (dbError) {
        logger.error('Error updating chunk in database:', dbError);
        // Don't fail the whole callback if database update fails
      }

      // Find connected sockets for this user
      const connectedSockets = Array.from(io.sockets.sockets.values())
        .filter(socket => socket.userId === userId);

      if (connectedSockets.length === 0) {
        logger.warn('No connected sockets found for userId:', userId);
        return res.status(200).json({
          success: true,
          message: 'No active connections for user'
        });
      }

      // Prepare transcription result message
      const transcriptionResult = {
        chunkId: chunk_id,
        sessionId: session_id,
        status: status,
        transcription: transcription,
        speakers: speakers,
        timestamp: timestamp || new Date().toISOString(),
        error: error,
        threadId: threadId,
        userId: userId
      };

      // Send to all connected sockets for this user
      connectedSockets.forEach(socket => {
        socket.emit('transcription_result', transcriptionResult);
        logger.info(`📤 Emitted transcription result to socket ${socket.id}`, {
          userId: userId,
          chunkId: chunk_id,
          sessionId: session_id,
          status: status,
          transcriptionPreview: transcription ? transcription.substring(0, 50) + '...' : null
        });
      });

      res.status(200).json({
        success: true,
        message: 'Transcription result delivered',
        deliveredTo: connectedSockets.length,
        chunkId: chunk_id,
        sessionId: session_id
      });

    } catch (error) {
      logger.error('❌ Error processing transcription callback:', error);
      res.status(500).json({ error: 'Failed to process transcription callback' });
    }
  });

  // GET handler for transcription-callback endpoint (for debugging)
  server.get('/api/transcription-callback', (req, res) => {
    res.status(200).json({
      status: 'ok',
      endpoint: '/api/transcription-callback',
      purpose: 'Receives transcription results from audio transcription service',
      method: 'POST required'
    });
  });

  // Health check endpoint with Redis status
  server.get('/health', async (req, res) => {
    let redisStatus = 'not_configured';

    if (redisConfig) {
      try {
        // Check all Redis clients
        const pubStatus = redisConfig.pubClient.isOpen ? 'connected' : 'disconnected';
        const subStatus = redisConfig.subClient.isOpen ? 'connected' : 'disconnected';
        const gatekeeperStatus = redisConfig.gatekeeperSubClient.isOpen ? 'connected' : 'disconnected';

        redisStatus = {
          pub: pubStatus,
          sub: subStatus,
          gatekeeper: gatekeeperStatus,
          overall: (pubStatus === 'connected' && subStatus === 'connected' && gatekeeperStatus === 'connected') ? 'healthy' : 'degraded'
        };
      } catch (error) {
        redisStatus = { error: error.message };
      }
    }

    res.status(200).json({
      status: 'healthy',
      timestamp: new Date().toISOString(),
      websocket: io.engine.clientsCount,
      redis: redisStatus
    });
  });

  // Transcription cleanup endpoint
  server.get('/api/transcription-cleanup', async (req, res) => {
    try {
      const results = await transcriptionCleanup.runCleanupJob();
      res.status(200).json({
        success: true,
        message: 'Cleanup job completed',
        results: results
      });
    } catch (error) {
      logger.error('Cleanup endpoint error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to run cleanup job',
        details: error.message
      });
    }
  });

  // Transcription stats endpoint
  server.get('/api/transcription-stats', async (req, res) => {
    try {
      const stats = await transcriptionCleanup.getCleanupStats();
      res.status(200).json({
        success: true,
        stats: stats
      });
    } catch (error) {
      logger.error('Stats endpoint error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to get stats',
        details: error.message
      });
    }
  });


  // Redis health check endpoint
  server.get('/health/redis', async (req, res) => {
    if (!redisConfig) {
      return res.status(200).json({
        status: 'not_configured',
        message: 'Redis not configured'
      });
    }

    try {
      const pubPing = redisConfig.pubClient.isOpen ? await redisConfig.pubClient.ping() : 'DISCONNECTED';
      const subPing = redisConfig.subClient.isOpen ? await redisConfig.subClient.ping() : 'DISCONNECTED';
      const gatekeeperPing = redisConfig.gatekeeperSubClient.isOpen ? await redisConfig.gatekeeperSubClient.ping() : 'DISCONNECTED';

      res.status(200).json({
        status: 'healthy',
        clients: {
          pub: { connected: redisConfig.pubClient.isOpen, ping: pubPing },
          sub: { connected: redisConfig.subClient.isOpen, ping: subPing },
          gatekeeper: { connected: redisConfig.gatekeeperSubClient.isOpen, ping: gatekeeperPing }
        }
      });
    } catch (error) {
      res.status(500).json({
        status: 'error',
        error: error.message
      });
    }
  });

  // Socket.io connection handling
  io.on("connection", (socket) => {
    logger.info("New socket connection:", socket.id);
    console.log("🔌 New WebSocket connection established:", socket.id);

    // Wrap emit for outgoing events
    const originalEmit = socket.emit.bind(socket);
    socket.emit = async function (event, ...args) {
      await logRequest('WEBSOCKET_OUT', {
        socketId: socket.id,
        event: event,
        data: args
      });
      return originalEmit(event, ...args);
    };

    // Authenticate socket connection
    socket.on("authenticate", async (authData) => {
      console.log("🔐 Authentication attempt received");
      await logRequest('WEBSOCKET_IN', {
        socketId: socket.id,
        event: 'authenticate',
        data: [authData]
      });

      // Handle both old format (string token) and new format (object with token and user)
      let token, userData;
      if (typeof authData === 'string') {
        token = authData;
        userData = null;
      } else {
        token = authData.token;
        userData = authData.user;
      }

      const decoded = verifySocketJWT(token);

      if (!decoded) {
        console.log("❌ Authentication failed - invalid token");
        socket.emit("authentication_failed", { error: "Invalid token" });
        socket.disconnect();
        return;
      }

      // Debug: Log what we received
      console.log("🔍 Auth data received:", {
        userData: userData,
        decodedId: decoded?.id
      });

      // Use user data from client if available, otherwise use decoded token data
      const userId = userData?.id || userData?.userId || decoded.id || 'unknown-user';
      const userPhone = userData?.phone || decoded.phone;

      // Store user info and token in socket
      socket.userId = userId;
      socket.phone = userPhone;
      socket.authToken = token; // Store the actual JWT token string for later use

      // Join user-specific room using userId instead of phone
      socket.join(userId);

      socket.emit("authenticated", { userId: userId });
      console.log("✅ User authenticated successfully:", userId);
      logger.info(`User authenticated and joined room:`, { userId: userId });
    });

    // Handle audio transcription start
    socket.on("start_transcription", async (data) => {
      await logRequest('WEBSOCKET_IN', {
        socketId: socket.id,
        event: 'start_transcription',
        data: [data]
      });

      console.log("🎤 Starting transcription session:", {
        userId: socket.userId,
        threadId: data.threadId
      });

      if (!socket.userId) {
        console.log("❌ Transcription rejected - user not authenticated");
        socket.emit("transcription_error", { error: "Not authenticated" });
        return;
      }

      try {
        // Generate request_id on backend
        const requestId = uuidv4();

        // Create transcription session in database
        const dbSession = await transcriptionService.createTranscriptionSession({
          requestId: requestId,
          threadId: data.threadId,
          userId: socket.userId
        });

        // Store transcription session info on socket
        socket.transcriptionSession = {
          requestId: requestId,
          dbSessionId: dbSession.id, // Store our database ID
          threadId: data.threadId,
          startTime: new Date().toISOString()
        };

        // Acknowledge transcription start with request_id
        socket.emit("transcription_started", {
          requestId: requestId,
          threadId: data.threadId,
          userId: socket.userId,
          startTime: socket.transcriptionSession.startTime
        });

        logger.info('Transcription session started and stored in database:', {
          userId: socket.userId,
          requestId: requestId,
          dbSessionId: dbSession.id,
          threadId: data.threadId
        });
      } catch (error) {
        logger.error('Error creating transcription session:', error);
        socket.emit("transcription_error", {
          error: "Failed to start transcription session: " + error.message
        });
      }
    });

    // Handle audio transcription stop
    socket.on("stop_transcription", async (data) => {
      await logRequest('WEBSOCKET_IN', {
        socketId: socket.id,
        event: 'stop_transcription',
        data: [data]
      });

      console.log("🎤 Stopping transcription session:", {
        userId: socket.userId,
        requestId: data.requestId
      });

      try {
        // Find session by requestId
        const session = await transcriptionService.getSessionByRequestId(data.requestId);

        if (!session) {
          socket.emit("transcription_error", { error: "Session not found" });
          return;
        }

        const endTime = new Date();
        // Parse started_at as UTC timestamp
        const startTime = new Date(session.started_at);

        const duration = Math.round((endTime.getTime() - startTime.getTime()) / 1000)

        // Update session status to 'stopping' instead of creating summary immediately
        await transcriptionService.updateSessionStatus(
          session.id,
          'stopping',
          endTime.toISOString(),
          duration
        );

        // Send acknowledgment without summary (summary will be created when chunks are done)
        socket.emit("transcription_stopped", {
          requestId: data.requestId,
          threadId: session.thread_id,
          startTime: startTime.toISOString(), // Use the parsed startTime which is already in UTC
          endTime: endTime.toISOString(),
          duration: Math.round((endTime.getTime() - startTime.getTime()) / 1000), // Convert to seconds to match database
          status: 'stopping' // Let client know we're waiting for chunks
        });

        logger.info('Transcription session marked as stopping, waiting for chunks to complete:', {
          userId: socket.userId,
          requestId: data.requestId,
          dbSessionId: session.id,
          duration: duration
        });

        // Clear transcription session from socket
        socket.transcriptionSession = null;


      } catch (error) {
        logger.error('Error updating transcription session:', error);
        socket.emit("transcription_error", {
          error: "Failed to stop transcription session: " + error.message
        });
      }
    });


    // Handle sending messages
    socket.on("send_message", async (data) => {
      await logRequest('WEBSOCKET_IN', {
        socketId: socket.id,
        event: 'send_message',
        data: [data]
      });

      console.log("💬 Message received from frontend:", {
        userId: socket.userId,
        threadId: data.threadId,
        content: data.content?.substring(0, 50) + '...',
        conversationType: data.conversationType
      });

      if (!socket.userId) {
        console.log("❌ Message rejected - user not authenticated");
        socket.emit("error", { message: "Not authenticated" });
        return;
      }

      logger.info("Message received from client:", {
        userId: socket.userId,
        threadId: data.threadId,
        hasAttachments: data.attachments && data.attachments.length > 0
      });

      // Track the last thread primarily by user ID (more reliable)
      if (socket.userId) {
        userToLastThread.set(socket.userId, data.threadId);
        logger.info(`Stored thread ID ${data.threadId} for user ${socket.userId}`);
      }

      // Keep phone tracking as fallback only
      if (socket.phone) {
        phoneToLastThread.set(socket.phone, data.threadId);
      }

      // Also store on the socket itself for quick access
      socket.lastThreadId = data.threadId;

      try {
        // Handle file uploads if present
        let uploadedAttachments = [];
        if (data.attachments && data.attachments.length > 0) {
          for (const attachment of data.attachments) {
            const filePath = `${socket.userId}/${data.threadId}/${uuidv4()}_${attachment.name}`;
            const fileUrl = await uploadFileToBlobFromBuffer(
              attachment.data,
              process.env.REPORTS_BUCKET || 'doctor-attachments',
              filePath
            );

            uploadedAttachments.push({
              url: fileUrl,
              name: attachment.name,
              size: attachment.size,
              type: getSimplifiedFileType(attachment.type)
            });

          }

        }

        // Prepare message for Gatekeeper
        const message = {
          threadId: data.threadId,
          content: data.content,
          userId: socket.userId,
          conversationType: data.conversationType,
          attachments: uploadedAttachments
        };

        // Always use real Gatekeeper - no more mocks
        console.log("🚀 PRODUCTION MODE: Sending to real Gatekeeper");

        try {
          // Send via real webhook endpoint
          const webhookPayload = {
            dialogueId: data.threadId,
            dialogueType: data.conversationType.replace('_', '-'), // Convert patient_case to patient-case
            text: data.content,
            providerMessageId: uuidv4(),
            sender: 'human',
            source: 'WEB',
            phoneNumber: socket.phone || '+************',
            timestamp: Date.now(),
            requestId: uuidv4()
          };

          // Only add attachments if there are any (to match working format)
          if (uploadedAttachments && uploadedAttachments.length > 0) {
            webhookPayload.attachments = uploadedAttachments.map(attachment => ({
              url: attachment.url,
              name: attachment.name,
              size: attachment.size,
              messageType: attachment.type
            }));
          }

          logger.info('📤 Sending webhook payload to Gatekeeper:', JSON.stringify(webhookPayload, null, 2));

          // Get the user's JWT token from their session
          const userJWT = socket.authToken || socket.accessToken;

          // Log the equivalent curl command
          const curlCommand = `curl -X POST "${GATEKEEPER_URL}/c/practitioner-dashboard/webhook" \\
    -H "Content-Type: application/json" \\
    -H "Authorization: Bearer ${userJWT}" \\
    -d '${JSON.stringify(webhookPayload, null, 2)}'`;

          logger.info('🔧 Equivalent CURL command:\n' + curlCommand);

          if (!userJWT) {
            logger.error('No JWT token available for user');
            throw new Error('Authentication required');
          }

          const response = await makeRequest({
            method: 'post',
            url: `${GATEKEEPER_URL}/c/practitioner-dashboard/webhook`,
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${userJWT}`
            },
            data: webhookPayload
          });

          logger.info('📥 Gatekeeper webhook response:', {
            status: response.status,
            data: response.data
          });

          // Emit message_sent with full user message details for display
          socket.emit("message_sent", {
            success: true,
            messageId: webhookPayload.providerMessageId,
            threadId: data.threadId,
            dialogueId: data.threadId,
            userMessage: {
              id: webhookPayload.providerMessageId,
              threadId: data.threadId,
              sender: 'user',
              content: data.content,
              timestamp: new Date(webhookPayload.timestamp).toISOString(),
              attachments: uploadedAttachments || []
            },
            isFirstMessage: !socket.lastThreadId || socket.lastThreadId !== data.threadId
          });

          logger.info('Message sent to Gatekeeper successfully:', {
            dialogueId: data.threadId,
            messageId: webhookPayload.providerMessageId,
            hasRedis: !!redisConfig
          });

          // If no Redis, inform user that responses will come via webhook
          if (!redisConfig) {
            logger.warn('No Redis configured - AI responses will come via webhook endpoint only');
          }

        } catch (gatekeeperError) {
          logger.error('Gatekeeper webhook failed:', gatekeeperError.message);

          // Send error to client
          socket.emit("message_error", {
            error: "Failed to send message to AI service: " + gatekeeperError.message,
            threadId: data.threadId
          });
        }

      } catch (error) {
        logger.error("Error processing message:", error);
        socket.emit("message_error", {
          error: error.message,
          threadId: data.threadId
        });
      }
    });

    // Handle disconnection
    socket.on("disconnect", async () => {
      await logRequest('WEBSOCKET_IN', {
        socketId: socket.id,
        event: 'disconnect',
        data: []
      });

      logger.info("Socket disconnected:", {
        socketId: socket.id,
        userId: socket.userId
      });
    });
  });

  // Let Next.js handle all other routes
  server.all('*', (req, res) => {
    return handle(req, res);
  });

  httpServer.listen(port, (err) => {
    if (err) {
      logger.error("Server failed to start:", err);
      throw err;
    }
    logger.info(`Server ready on http://localhost:${port}`);

    // Start periodic cleanup job (every 30 minutes)
    setInterval(async () => {
      try {
        logger.info('🧹 Running scheduled transcription cleanup...');
        await transcriptionCleanup.runCleanupJob();
      } catch (error) {
        logger.error('Scheduled cleanup failed:', error);
      }
    }, 30 * 60 * 1000); // 30 minutes in milliseconds

    logger.info('🕐 Transcription cleanup job scheduled every 30 minutes');
  });
});
