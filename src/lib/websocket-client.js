import { io } from 'socket.io-client';

class WebSocketClient {
  constructor() {
    this.socket = null;
    this.connected = false;
    this.authenticated = false;
    this.messageHandlers = new Map();
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
    // Transcription state
    this.transcriptionSession = null;
    this.transcriptionHandlers = new Map();
    // Chunk upload state
    this.chunkUploadHandlers = new Map();
  }

  connect() {
    const token = localStorage.getItem('access_token');
    const userStr = localStorage.getItem('user');
    
    if (!token) {
      console.error('❌ No access token found');
      return false;
    }

    console.log('🔌 Initializing WebSocket connection...');

    // Initialize socket connection
    this.socket = io('/', {
      transports: ['websocket'],
      upgrade: false,
      reconnection: true,
      reconnectionDelay: 1000,
      reconnectionDelayMax: 5000,
      reconnectionAttempts: this.maxReconnectAttempts
    });

    this.setupEventHandlers();
    
    // Authenticate immediately after connection
    this.socket.on('connect', () => {
      console.log('✅ WebSocket connected, authenticating with token...');
      
      // Send authentication with user data if available
      const authData = {
        token: token,
        user: userStr ? JSON.parse(userStr) : null
      };
      
      this.socket.emit('authenticate', authData);
    });

    return true;
  }

  setupEventHandlers() {
    // Authentication events
    this.socket.on('authenticated', (data) => {
      console.log('WebSocket authenticated:', data);
      this.authenticated = true;
      this.connected = true;
      this.reconnectAttempts = 0;
      
      // Notify UI of connection status
      window.dispatchEvent(new CustomEvent('websocket_connected', { 
        detail: { userId: data.userId } 
      }));
    });

    this.socket.on('authentication_failed', (error) => {
      console.error('WebSocket authentication failed:', error);
      this.authenticated = false;
      this.disconnect();
      
      // Notify UI to re-authenticate
      window.dispatchEvent(new CustomEvent('websocket_auth_failed'));
    });

    // Message events
    this.socket.on('message_response', (data) => {
      console.log('🔔 WebSocket received message_response:', data);
      
      // Use threadId or dialogueId for routing
      const threadId = data.threadId || data.dialogueId;
      
      // Trigger handlers for this thread
      const handlers = this.messageHandlers.get(threadId) || [];
      handlers.forEach(handler => handler(data));
      
      // Also dispatch a global event
      console.log('🔔 Dispatching message_response event to window');
      window.dispatchEvent(new CustomEvent('message_response', { 
        detail: data 
      }));
    });

    this.socket.on('message_sent', (data) => {
      console.log('Message sent confirmation:', data);
      window.dispatchEvent(new CustomEvent('message_sent', { 
        detail: data 
      }));
    });

    this.socket.on('message_error', (error) => {
      console.error('Message error:', error);
      window.dispatchEvent(new CustomEvent('message_error', { 
        detail: error 
      }));
    });

    // Connection events
    this.socket.on('connect_error', (error) => {
      console.error('WebSocket connection error:', error);
      this.connected = false;
      this.reconnectAttempts++;
      
      if (this.reconnectAttempts >= this.maxReconnectAttempts) {
        window.dispatchEvent(new CustomEvent('websocket_max_reconnect'));
      }
    });

    this.socket.on('disconnect', (reason) => {
      console.log('WebSocket disconnected:', reason);
      this.connected = false;
      this.authenticated = false;
      
      window.dispatchEvent(new CustomEvent('websocket_disconnected', { 
        detail: { reason } 
      }));
    });

    this.socket.on('error', (error) => {
      console.error('WebSocket error:', error);
      window.dispatchEvent(new CustomEvent('websocket_error', { 
        detail: error 
      }));
    });

    // Transcription events
    this.socket.on('transcription_started', (data) => {
      console.log('Transcription started:', data);
      this.transcriptionSession = data;
      window.dispatchEvent(new CustomEvent('transcription_started', { 
        detail: data 
      }));
    });

    this.socket.on('transcription_stopped', (data) => {
      console.log('Transcription stopped event received:', data);
      console.log('Current transcription session:', this.transcriptionSession);
      this.transcriptionSession = null;
      window.dispatchEvent(new CustomEvent('transcription_stopped', { 
        detail: data 
      }));
    });

    this.socket.on('transcription_error', (data) => {
      console.error('Transcription error:', data);
      window.dispatchEvent(new CustomEvent('transcription_error', { 
        detail: data 
      }));
    });

    this.socket.on('transcription_result', (data) => {
      console.log('Transcription result:', data);
      
      // Trigger handlers for this session
      const handlers = this.transcriptionHandlers.get(data.sessionId) || [];
      handlers.forEach(handler => handler(data));
      
      // Also dispatch a global event
      window.dispatchEvent(new CustomEvent('transcription_result', { 
        detail: data 
      }));
    });

    // Chunk upload events
    this.socket.on('chunk_upload_success', (data) => {
      console.log('Chunk upload success:', data);
      
      // Trigger handlers for this session
      const handlers = this.chunkUploadHandlers.get(data.sessionId) || [];
      handlers.forEach(handler => handler({ ...data, status: 'success' }));
      
      // Also dispatch a global event
      window.dispatchEvent(new CustomEvent('chunk_upload_success', { 
        detail: data 
      }));
    });

    this.socket.on('chunk_upload_failed', (data) => {
      console.error('Chunk upload failed:', data);
      // Trigger handlers for this session
      const handlers = this.chunkUploadHandlers.get(data.sessionId) || [];
      handlers.forEach(handler => handler({ ...data, status: 'failed' }));
      
      // Also dispatch a global event
      window.dispatchEvent(new CustomEvent('chunk_upload_failed', { 
        detail: data 
      }));
    });

    this.socket.on('chunk_transcription', (data) => {
      console.log('Chunk transcription received:', {
        chunkId: data.chunkId,
        sessionId: data.sessionId,
        requestId: data.requestId,
        isFinalChunk: data.isFinalChunk,
        transcriptionLength: data.transcription?.length
      });
      
      // Trigger handlers for this session (use sessionId for consistency)
      const handlers = this.transcriptionHandlers.get(data.sessionId) || [];
      handlers.forEach(handler => handler(data));
      
      // Also dispatch a window event for components
      window.dispatchEvent(new CustomEvent('chunk_transcription', { 
        detail: data 
      }));
    });

    // // Handle chunk transcription events
    // this.socket.on('chunk-transcription', (data) => {
    //   console.log('Chunk transcription received:', data);
      
    //   // Dispatch event for components to listen to
    //   window.dispatchEvent(new CustomEvent('chunk_transcription', { 
    //     detail: data 
    //   }));
    // });

    // Handle transcription complete event
    this.socket.on('transcription_complete', (data) => {
      console.log('Transcription complete:', data);
      
      // Dispatch event for components to listen to
      window.dispatchEvent(new CustomEvent('transcription_complete', { 
        detail: data 
      }));
    });

    // Handle SOAP notes generated event
    this.socket.on('soap_notes_generated', (data) => {
      console.log('SOAP notes generated:', data);
      
      // Dispatch event for components to listen to
      window.dispatchEvent(new CustomEvent('soap_notes_generated', { 
        detail: data 
      }));
    });
  }

  sendMessage(threadId, content, conversationType, attachments = []) {
    console.log('📤 Attempting to send message via WebSocket:', {
      threadId,
      content: content?.substring(0, 50) + '...',
      conversationType,
      authenticated: this.authenticated
    });

    if (!this.authenticated) {
      console.error('❌ WebSocket not authenticated');
      return Promise.reject(new Error('WebSocket not authenticated'));
    }

    return new Promise((resolve, reject) => {
      // Prepare attachments for upload
      const prepareAttachments = async () => {
        const prepared = [];
        
        for (const file of attachments) {
          const buffer = await file.arrayBuffer();
          prepared.push({
            name: file.name,
            size: file.size,
            type: file.type,
            data: buffer
          });
        }
        
        return prepared;
      };

      prepareAttachments().then(preparedAttachments => {
        const messageData = {
          threadId,
          content,
          conversationType,
          attachments: preparedAttachments,
          timestamp: new Date().toISOString()
        };

        console.log('🚀 Emitting send_message event with data:', messageData);

        // Set up one-time listeners for this specific message
        const handleSuccess = (data) => {
          console.log('✅ Message sent successfully:', data);
          if (data.threadId === threadId) {
            this.socket.off('message_sent', handleSuccess);
            this.socket.off('message_error', handleError);
            resolve(data);
          }
        };

        const handleError = (error) => {
          console.error('❌ Message send error:', error);
          if (error.threadId === threadId) {
            this.socket.off('message_sent', handleSuccess);
            this.socket.off('message_error', handleError);
            reject(new Error(error.error || 'Failed to send message'));
          }
        };

        // Listen for response
        this.socket.on('message_sent', handleSuccess);
        this.socket.on('message_error', handleError);

        // Send the message
        this.socket.emit('send_message', messageData);

        // Timeout after 30 seconds
        setTimeout(() => {
          this.socket.off('message_sent', handleSuccess);
          this.socket.off('message_error', handleError);
          reject(new Error('Message send timeout'));
        }, 30000);
      }).catch(reject);
    });
  }

  // Subscribe to messages for a specific thread
  subscribeToThread(threadId, handler) {
    if (!this.messageHandlers.has(threadId)) {
      this.messageHandlers.set(threadId, []);
    }
    
    this.messageHandlers.get(threadId).push(handler);
    
    // Return unsubscribe function
    return () => {
      const handlers = this.messageHandlers.get(threadId) || [];
      const index = handlers.indexOf(handler);
      if (index > -1) {
        handlers.splice(index, 1);
      }
      
      if (handlers.length === 0) {
        this.messageHandlers.delete(threadId);
      }
    };
  }

  // Transcription methods
  startTranscription(threadId) {
    if (!this.authenticated) {
      console.error('❌ WebSocket not authenticated');
      return Promise.reject(new Error('WebSocket not authenticated'));
    }

    return new Promise((resolve, reject) => {
      const data = { threadId };
      
      const handleStarted = (response) => {
        this.socket.off('transcription_started', handleStarted);
        this.socket.off('transcription_error', handleError);
        resolve(response);
      };

      const handleError = (error) => {
        this.socket.off('transcription_started', handleStarted);
        this.socket.off('transcription_error', handleError);
        reject(new Error(error.error || 'Failed to start transcription'));
      };

      this.socket.on('transcription_started', handleStarted);
      this.socket.on('transcription_error', handleError);

      this.socket.emit('start_transcription', data);

      setTimeout(() => {
        this.socket.off('transcription_started', handleStarted);
        this.socket.off('transcription_error', handleError);
        reject(new Error('Start transcription timeout'));
      }, 10000);
    });
  }

  stopTranscription(requestId) {
    if (!this.authenticated) {
      console.error('❌ WebSocket not authenticated');
      return Promise.reject(new Error('WebSocket not authenticated'));
    }

    return new Promise((resolve, reject) => {
      const data = { requestId };
      
      let timeoutId;
      let isResolved = false;
      
      const cleanup = () => {
        if (timeoutId) clearTimeout(timeoutId);
        this.socket.off('transcription_stopped', handleStopped);
        this.socket.off('transcription_error', handleError);
      };

      const handleStopped = (response) => {
        console.log('stopTranscription handleStopped called with:', response);
        console.log('Comparing requestIds:', { expected: requestId, received: response.requestId });
        if (response.requestId === requestId && !isResolved) {
          isResolved = true;
          cleanup();
          resolve(response);
        }
      };

      const handleError = (error) => {
        if (!isResolved) {
          isResolved = true;
          cleanup();
          reject(new Error(error.error || 'Failed to stop transcription'));
        }
      };

      // Set up listeners first
      this.socket.on('transcription_stopped', handleStopped);
      this.socket.on('transcription_error', handleError);

      // Small delay to ensure listeners are ready
      setTimeout(() => {
        console.log('🛑 Emitting stop_transcription event:', data);
        this.socket.emit('stop_transcription', data);
      }, 50);

      timeoutId = setTimeout(() => {
        if (!isResolved) {
          console.log('stopTranscription timeout reached after 10 seconds');
          isResolved = true;
          cleanup();
          reject(new Error('Stop transcription timeout'));
        }
      }, 10000);
    });
  }


  // Subscribe to transcription results for a specific session
  subscribeToTranscription(sessionId, handler) {
    if (!this.transcriptionHandlers.has(sessionId)) {
      this.transcriptionHandlers.set(sessionId, []);
    }
    
    this.transcriptionHandlers.get(sessionId).push(handler);
    
    // Return unsubscribe function
    return () => {
      const handlers = this.transcriptionHandlers.get(sessionId) || [];
      const index = handlers.indexOf(handler);
      if (index > -1) {
        handlers.splice(index, 1);
      }
      
      if (handlers.length === 0) {
        this.transcriptionHandlers.delete(sessionId);
      }
    };
  }

  // Subscribe to chunk upload events for a specific session
  subscribeToChunkUploads(sessionId, handler) {
    if (!this.chunkUploadHandlers.has(sessionId)) {
      this.chunkUploadHandlers.set(sessionId, []);
    }
    
    this.chunkUploadHandlers.get(sessionId).push(handler);
    
    // Return unsubscribe function
    return () => {
      const handlers = this.chunkUploadHandlers.get(sessionId) || [];
      const index = handlers.indexOf(handler);
      if (index > -1) {
        handlers.splice(index, 1);
      }
      
      if (handlers.length === 0) {
        this.chunkUploadHandlers.delete(sessionId);
      }
    };
  }

  disconnect() {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }
    
    this.connected = false;
    this.authenticated = false;
    this.messageHandlers.clear();
    this.transcriptionHandlers.clear();
    this.chunkUploadHandlers.clear();
    this.transcriptionSession = null;
  }

  isConnected() {
    return this.connected && this.authenticated;
  }

  reconnect() {
    this.disconnect();
    return this.connect();
  }
}

// Create singleton instance
const wsClient = new WebSocketClient();

// Auto-connect when access token is available
if (typeof window !== 'undefined') {
  // Listen for auth changes
  window.addEventListener('auth_login', () => {
    console.log('🔑 Auth login event received, connecting WebSocket...');
    wsClient.connect();
  });

  window.addEventListener('auth_logout', () => {
    console.log('🔒 Auth logout event received, disconnecting WebSocket...');
    wsClient.disconnect();
  });

  // Auto-connect if token exists
  setTimeout(() => {
    if (localStorage.getItem('access_token')) {
      console.log('🔑 Access token found, auto-connecting WebSocket...');
      wsClient.connect();
    }
  }, 100);
}

export default wsClient;