// Authentication utilities
import { v4 as uuidv4 } from 'uuid';
import fetch from 'node-fetch';

// Gatekeeper API configuration
const GATEKEEPER_BASE_URL = process.env.GATEKEEPER_URL
  ? `${process.env.GATEKEEPER_URL}/auth/practitioner-dashboard`
  : 'https://gatekeeper-staging.getbeyondhealth.com/auth/practitioner-dashboard';

// Request OTP via Gatekeeper
export async function requestOTPViaGatekeeper(phoneNumber, isLogin) {
  try {
    console.log(`📱 Requesting OTP via Gatekeeper for ${phoneNumber}`);
    
    const response = await fetch(`${GATEKEEPER_BASE_URL}/request-otp`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ phoneNumber, isLogin }),
    });
    
    console.log(`📡 Response status: ${response.status}`);
    
    let data;
    try {
      data = await response.json();
    } catch (e) {
      console.error('Failed to parse response as JSON:', e);
      throw new Error(`Invalid JSON response from Gatekeeper`);
    }
    
    console.log(`📡 Response data:`, JSON.stringify(data, null, 2));
    
    if (!response.ok) {
      console.error('Gatekeeper response error:', { status: response.status, data });
      throw new Error(data.message || data.error || `Failed to request OTP: ${response.status}`);
    }
    
    console.log(`✅ OTP requested successfully via Gatekeeper for ${phoneNumber}`);
    return { success: true, data, requestId: data.requestId };
  } catch (error) {
    console.error('❌ Gatekeeper OTP request failed:', error);
    return { success: false, error: error.message };
  }
}

// Verify OTP via Gatekeeper with optional registration fields
export async function verifyOTPViaGatekeeper(phone, otp, registrationData = {}, requestId) {
  try {
    console.log(`🔍 Attempting to verify OTP via Gatekeeper for ${phone} with OTP: ${otp}`);
    
    // Build request body with mandatory fields
    const requestBody = { 
      phone, 
      otp, 
      source: 'web',
      requestId
    };
    
    // Add optional registration fields if provided
    requestBody.name = registrationData.name || null;
    requestBody.organization = registrationData.organization || null;
    requestBody.license_number = registrationData.license_number || null;
    requestBody.speciality = registrationData.speciality || null;
    
    console.log(`📡 Making request to: ${GATEKEEPER_BASE_URL}/verify-otp`);
    console.log(`📡 Request body:`, JSON.stringify(requestBody, null, 2));
    const response = await fetch(`${GATEKEEPER_BASE_URL}/verify-otp`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestBody),
    });
    
    console.log(`📡 Response status: ${response.status}`);
    
    let data;
    try {
      data = await response.json();
    } catch (e) {
      console.error('Failed to parse response as JSON:', e);
      throw new Error(`Invalid JSON response from Gatekeeper`);
    }
    
    console.log(`📡 Response data:`, JSON.stringify(data, null, 2));
    
    if (!response.ok) {
      console.error('Gatekeeper response error:', { status: response.status, data });
      return { 
        success: false, 
        error: data.message || data.error || 'OTP verification failed',
        data: null 
      };
    }
    
    console.log(`✅ OTP verified via Gatekeeper for ${phone}`);
    
    // Check if we have tokens in the response
    if (data.accessToken && data.refreshToken) {
      console.log(`🔑 External tokens received and stored locally`);
    }
    
    return { 
      success: true, 
      data,
      externalTokens: data.accessToken ? {
        accessToken: data.accessToken,
        refreshToken: data.refreshToken
      } : null
    };
  } catch (error) {
    console.error('❌ Gatekeeper OTP verification failed:', error);
    return { success: false, error: error.message, data: null };
  }
}

// Refresh token via Gatekeeper
export async function refreshTokenViaGatekeeper(phone, refreshToken) {
  try {
    console.log(`🔄 Attempting to refresh token for ${phone}`);
    
    const response = await fetch(`${GATEKEEPER_BASE_URL}/refresh-token`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${refreshToken}`
      },
      body: JSON.stringify({ phone }),
    });
    
    console.log(`📡 Refresh response status: ${response.status}`);
    
    const data = await response.json();
    console.log(`📡 Refresh response data:`, JSON.stringify(data, null, 2));
    
    if (!response.ok) {
      throw new Error(data.message || 'Token refresh failed');
    }
    
    return { 
      success: true, 
      data,
      accessToken: data.accessToken,
      expiresIn: data.expiresIn || 3600
    };
  } catch (error) {
    console.error('❌ Gatekeeper token refresh error:', error);
    return { success: false, error: error.message };
  }
}

// Generate OTP - Fallback for local testing
export function generateOTP() {
  return Math.floor(100000 + Math.random() * 900000).toString();
}

// Mock SMS sender - Fallback for local testing
export async function sendSMS(phone, message) {
  console.log(`📱 Mock SMS to ${phone}: ${message}`);
  return { success: true, message: 'SMS sent successfully' };
}

// Rate limiting check - Simple in-memory implementation
export async function checkRateLimit(identifier, action, maxAttempts = 3) {
  // Simple in-memory rate limiting for now
  // In production, use Redis or database-based rate limiting
  return { allowed: true, remainingAttempts: maxAttempts };
}

// Create user ID
export function createUserId() {
  return `dr-${uuidv4()}`;
}
