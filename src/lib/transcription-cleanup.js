// Transcription Cleanup Service
// This module handles cleanup of orphaned transcription sessions

const transcriptionService = require('./transcription-service');

/**
 * Process orphaned transcription sessions
 * @param {number} maxAgeMinutes - Maximum age in minutes before considering a session orphaned
 * @returns {Promise<Object>} - Summary of cleanup results
 */
const processOrphanedSessions = async (maxAgeMinutes = 30) => {
  const results = {
    found: 0,
    processed: 0,
    failed: 0,
    errors: []
  };

  try {
    console.log(`🧹 Starting transcription cleanup for sessions older than ${maxAgeMinutes} minutes...`);
    
    // Get orphaned sessions
    const orphanedSessions = await transcriptionService.getOrphanedSessions(maxAgeMinutes);
    results.found = orphanedSessions.length;
    
    if (orphanedSessions.length === 0) {
      console.log('✅ No orphaned transcription sessions found');
      return results;
    }
    
    console.log(`📋 Found ${orphanedSessions.length} orphaned sessions to process`);
    
    // Process each orphaned session
    for (const session of orphanedSessions) {
      try {
        console.log(`🔄 Processing orphaned session: ${session.id} (started: ${session.started_at})`);
        
        // Create summary with timeout reason
        const summary = await transcriptionService.createTranscriptionSummary(
          session.id,
          'timeout' // Completion reason
        );
        
        console.log(`✅ Created summary for orphaned session ${session.id}: ${summary.id}`);
        results.processed++;
        
      } catch (error) {
        console.error(`❌ Failed to process orphaned session ${session.id}:`, error);
        results.failed++;
        results.errors.push({
          sessionId: session.id,
          error: error.message
        });
      }
    }
    
    console.log(`🏁 Cleanup completed: ${results.processed} processed, ${results.failed} failed`);
    return results;
    
  } catch (error) {
    console.error('❌ Error during transcription cleanup:', error);
    results.errors.push({
      error: error.message,
      type: 'cleanup_error'
    });
    return results;
  }
};

/**
 * Get cleanup statistics
 * @returns {Promise<Object>} - Current database statistics
 */
const getCleanupStats = async () => {
  try {
    const { query } = require('./transcription-db');
    const stats = await query(`
      SELECT 
        COUNT(*) as total_sessions,
        COUNT(CASE WHEN status = 'active' THEN 1 END) as active_sessions,
        COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_sessions,
        COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed_sessions,
        COUNT(CASE WHEN status = 'timed_out' THEN 1 END) as timed_out_sessions,
        COUNT(CASE WHEN status = 'active' AND last_activity_at < CURRENT_TIMESTAMP - INTERVAL '30 minutes' THEN 1 END) as orphaned_sessions
      FROM transcription_sessions
    `);
    
    return stats.rows[0];
  } catch (error) {
    console.error('Error getting cleanup stats:', error);
    return null;
  }
};

/**
 * Run cleanup job (can be called from a cron job or scheduled task)
 */
const runCleanupJob = async () => {
  console.log('🚀 Starting scheduled transcription cleanup job...');
  
  try {
    const stats = await getCleanupStats();
    if (stats) {
      console.log('📊 Current stats:', {
        total_sessions: stats.total_sessions,
        active_sessions: stats.active_sessions,
        completed_sessions: stats.completed_sessions,
        orphaned_sessions: stats.orphaned_sessions
      });
    }
    
    const results = await processOrphanedSessions();
    
    console.log('📈 Cleanup results:', {
      found: results.found,
      processed: results.processed,
      failed: results.failed,
      errors: results.errors.length
    });
    
    return results;
    
  } catch (error) {
    console.error('❌ Cleanup job failed:', error);
    return {
      found: 0,
      processed: 0,
      failed: 1,
      errors: [{ error: error.message, type: 'job_error' }]
    };
  }
};

module.exports = {
  processOrphanedSessions,
  getCleanupStats,
  runCleanupJob
};