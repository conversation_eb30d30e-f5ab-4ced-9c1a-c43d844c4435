// API services for doctor dashboard
import { checkAndRefreshAuth, redirectToLogin } from './client-auth';

const GATEKEEPER_URL = process.env.NEXT_PUBLIC_GATEKEEPER_URL;
console.log('GK: ', GATEKEEPER_URL);

if (!GATEKEEPER_URL) {
  throw new Error('GATEKEEPER_URL is not set');
}

// const API_BASE_URL = 'http://localhost:3000/api'; // Local backend URL

// Helper function to handle API requests with auth refresh
async function makeAuthenticatedRequest(url, options = {}) {
  try {
    const response = await fetch(url, options);
    console.log('API response:', { status: response.status });
    // If we get a 401, try to refresh the token
    if (response.status === 401) {
      console.log('Got 401, attempting to refresh token...');
      const { isAuthenticated, needsLogin } = await checkAndRefreshAuth();
      
      if (needsLogin) {
        redirectToLogin();
        throw new Error('Authentication required');
      }
      
      if (isAuthenticated) {
        // Retry the request with the new token
        const newToken = localStorage.getItem('access_token');
        if (newToken && options.headers) {
          options.headers['Authorization'] = `Bearer ${newToken}`;
        }
        return fetch(url, options);
      }
    }
    
    return response;
  } catch (error) {
    console.error('Request error:', error);
    throw error;
  }
}

// Thread types
export const THREAD_TYPES = {
  PATIENT_CASE: 'patient-case',
  RESEARCH: 'research', 
  QUICK_FACTS: 'chat'
};

// Utility function to generate UUIDs
function generateUUID() {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    var r = Math.random() * 16 | 0, v = c == 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}

// Mock data
const mockThreads = [
  {
    id: generateUUID(),
    type: THREAD_TYPES.RESEARCH,
    title: 'Diabetes Medication Comparison Table',
    lastMessage: 'Here\'s a comprehensive table of diabetes medications...',
    lastUpdated: new Date().toISOString(), // Today
    messageCount: 2
  },
  {
    id: generateUUID(),
    type: THREAD_TYPES.PATIENT_CASE,
    title: '45yo Male - Chest Pain',
    lastMessage: 'What are the differential diagnoses?',
    lastUpdated: new Date(Date.now() - 300000).toISOString(), // 5 minutes ago
    messageCount: 5
  },
  {
    id: generateUUID(), 
    type: THREAD_TYPES.RESEARCH,
    title: 'Latest COVID-19 Treatments',
    lastMessage: 'Research on Paxlovid effectiveness',
    lastUpdated: new Date(Date.now() - 86400000).toISOString(), // Yesterday
    messageCount: 3
  },
  {
    id: generateUUID(),
    type: THREAD_TYPES.QUICK_FACTS,
    title: 'Medication Dosing Chat',
    lastMessage: 'What is the standard starting dose for metformin?',
    lastUpdated: new Date(Date.now() - 172800000).toISOString(), // 2 days ago (This Week)
    messageCount: 2
  },
  {
    id: generateUUID(),
    type: THREAD_TYPES.PATIENT_CASE,
    title: '32yo Female - Chronic Migraine with Medication Overuse Headache',
    lastMessage: 'Prophylaxis options?',
    lastUpdated: new Date(Date.now() - 345600000).toISOString(), // 4 days ago (This Week)
    messageCount: 7
  },
  {
    id: generateUUID(),
    type: THREAD_TYPES.RESEARCH,
    title: 'Comprehensive Diabetes Management Guidelines and Evidence-Based Treatment Protocols',
    lastMessage: 'New ADA recommendations',
    lastUpdated: new Date(Date.now() - 864000000).toISOString(), // 10 days ago (Last Week)
    messageCount: 4
  },
  {
    id: generateUUID(),
    type: THREAD_TYPES.QUICK_FACTS,
    title: 'Blood Pressure Medications Discussion',
    lastMessage: 'What are the contraindications for ACE inhibitors?',
    lastUpdated: new Date(Date.now() - **********).toISOString(), // 20 days ago (Older)
    messageCount: 3
  }
];

// Build mockMessages dynamically based on thread IDs
const mockMessages = {};

// Add messages for the first thread (Diabetes Medication)
if (mockThreads[0]) {
  mockMessages[mockThreads[0].id] = [
    {
      id: generateUUID(),
      threadId: mockThreads[0].id,
      sender: 'user',
      content: 'Can you provide a comprehensive comparison table of diabetes medications including dosing, side effects, and contraindications?',
      timestamp: new Date(Date.now() - 180000).toISOString(), // 3 minutes ago
      attachments: []
    },
    {
      id: generateUUID(),
      threadId: mockThreads[0].id,
      sender: 'ai',
      content: `Here's a comprehensive comparison table of common diabetes medications:

| **Medication Class** | **Generic Name** | **Brand Name** | **Starting Dose** | **Max Daily Dose** | **Common Side Effects** | **Contraindications** | **Mechanism of Action** |
|---------------------|------------------|----------------|-------------------|-------------------|------------------------|----------------------|------------------------|
| **Metformin** | Metformin | Glucophage | 500mg BID | 2000mg | GI upset, lactic acidosis (rare), B12 deficiency | eGFR <30, acute kidney injury, metabolic acidosis | Decreases hepatic glucose production, improves insulin sensitivity |
| **Sulfonylureas** | Glipizide | Glucotrol | 5mg daily | 40mg | Hypoglycemia, weight gain, skin reactions | Severe kidney/liver disease, pregnancy | Stimulates pancreatic insulin release |
| **Sulfonylureas** | Glyburide | DiaBeta | 2.5mg daily | 20mg | Hypoglycemia, weight gain, prolonged duration | Severe kidney/liver disease, elderly patients | Stimulates pancreatic insulin release |
| **DPP-4 Inhibitors** | Sitagliptin | Januvia | 100mg daily | 100mg | Upper respiratory infections, headache, pancreatitis (rare) | End-stage renal disease | Inhibits DPP-4 enzyme, increases incretin levels |
| **DPP-4 Inhibitors** | Linagliptin | Tradjenta | 5mg daily | 5mg | Nasopharyngitis, hypersensitivity reactions | History of pancreatitis | Inhibits DPP-4 enzyme, increases incretin levels |
| **GLP-1 Agonists** | Semaglutide | Ozempic | 0.25mg weekly | 2mg weekly | Nausea, vomiting, diarrhea, pancreatitis risk | Personal/family history of MTC, MEN 2 | GLP-1 receptor agonist, slows gastric emptying |
| **GLP-1 Agonists** | Liraglutide | Victoza | 0.6mg daily | 1.8mg daily | Nausea, vomiting, injection site reactions | Personal/family history of MTC, MEN 2 | GLP-1 receptor agonist, slows gastric emptying |
| **SGLT-2 Inhibitors** | Empagliflozin | Jardiance | 10mg daily | 25mg daily | UTIs, genital mycotic infections, DKA risk | eGFR <30, dialysis, severe hepatic impairment | Inhibits SGLT-2 in kidneys, increases glucose excretion |
| **SGLT-2 Inhibitors** | Canagliflozin | Invokana | 100mg daily | 300mg daily | UTIs, genital infections, amputation risk, fractures | eGFR <30, dialysis, severe hepatic impairment | Inhibits SGLT-2 in kidneys, increases glucose excretion |
| **Thiazolidinediones** | Pioglitazone | Actos | 15mg daily | 45mg daily | Weight gain, fluid retention, bone fractures, bladder cancer risk | Heart failure, active liver disease | PPARγ agonist, improves insulin sensitivity |
| **Alpha-glucosidase Inhibitors** | Acarbose | Precose | 25mg TID | 100mg TID | GI upset, flatulence, diarrhea, hepatotoxicity | IBD, cirrhosis, creatinine >2mg/dL | Inhibits intestinal alpha-glucosidases, delays carb absorption |
| **Insulin** | Regular Insulin | Humulin R | 4-6 units SC | Variable | Hypoglycemia, weight gain, injection site reactions | Hypoglycemia, allergy to insulin | Replaces endogenous insulin, lowers blood glucose |
| **Insulin** | NPH Insulin | Humulin N | 10 units SC | Variable | Hypoglycemia, weight gain, injection site reactions | Hypoglycemia, allergy to insulin | Intermediate-acting insulin replacement |
| **Insulin** | Insulin Aspart | NovoLog | 4-6 units SC | Variable | Hypoglycemia, weight gain, injection site reactions | Hypoglycemia, allergy to insulin | Rapid-acting insulin replacement |

## **Key Clinical Considerations:**

### **First-line Therapy:**
- **Metformin** is typically first-line unless contraindicated
- Well-tolerated, weight-neutral, low hypoglycemia risk
- Cardiovascular benefits demonstrated

### **Combination Therapy:**
- Often required for optimal glycemic control
- Consider patient-specific factors: weight, hypoglycemia risk, cardiovascular disease
- **GLP-1 agonists** and **SGLT-2 inhibitors** have cardiovascular and renal benefits

### **Special Populations:**
- **Elderly:** Avoid glyburide, prefer DPP-4 inhibitors
- **CKD:** Dose adjustments needed, SGLT-2 inhibitors contraindicated if eGFR <30
- **Heart Failure:** Avoid thiazolidinediones, prefer SGLT-2 inhibitors

### **Monitoring Requirements:**
- **All patients:** Regular HbA1c, glucose monitoring
- **Metformin:** Annual B12 levels, kidney function
- **SGLT-2 inhibitors:** Kidney function, foot exams
- **Insulin:** More frequent glucose monitoring`,
      timestamp: new Date(Date.now() - 120000).toISOString(), // 2 minutes ago
      attachments: []
    }
  ];
}

// Add messages for other threads if needed
if (mockThreads[1]) {
  mockMessages[mockThreads[1].id] = [
    {
      id: generateUUID(),
      threadId: mockThreads[1].id,
      sender: 'user',
      content: 'I have a 45-year-old male patient presenting with chest pain. What should I consider?',
      timestamp: new Date(Date.now() - 300000).toISOString(),
      attachments: []
    },
    {
      id: generateUUID(),
      threadId: mockThreads[1].id, 
      sender: 'ai',
      content: 'For a 45-year-old male with chest pain, consider these key differential diagnoses:\n\n**Cardiac causes:**\n- Acute coronary syndrome (STEMI/NSTEMI)\n- Unstable angina\n- Aortic dissection\n\n**Pulmonary causes:**\n- Pulmonary embolism\n- Pneumothorax\n- Pneumonia\n\n**Other causes:**\n- GERD\n- Musculoskeletal pain\n- Anxiety/panic disorder\n\nImmediate priorities: ECG, troponins, chest X-ray, and risk stratification using HEART score.',
      timestamp: new Date(Date.now() - 240000).toISOString(),
      attachments: []
    }
  ];
}

// Remove all the old hardcoded mock messages - keeping code commented for reference
/*
const oldMockMessages = {
  '2': [
    {
      id: 'm3',
      threadId: '2',
      sender: 'user', 
      content: 'What are the latest updates on COVID-19 treatment protocols?',
      timestamp: new Date(Date.now() - 86400000).toISOString(),
      attachments: []
    }
  ],
  '3': [
    {
      id: 'm4',
      threadId: '3',
      sender: 'user',
      content: 'What is the standard starting dose for metformin?',
      timestamp: new Date(Date.now() - 172800000).toISOString(),
      attachments: []
    }
  ],
  '4': [
    {
      id: 'm5',
      threadId: '4',
      sender: 'user',
      content: 'I have a 32-year-old female patient with recurring migraines. What prophylaxis options should I consider?',
      timestamp: new Date(Date.now() - 345600000).toISOString()
    }
  ],
  '5': [
    {
      id: 'm6',
      threadId: '5',
      sender: 'user',
      content: 'What are the new ADA recommendations for diabetes management?',
      timestamp: new Date(Date.now() - 864000000).toISOString()
    }
  ],
  '6': [
    {
      id: 'm7',
      threadId: '6',
      sender: 'user',
      content: 'What are the contraindications for ACE inhibitors in blood pressure management?',
      timestamp: new Date(Date.now() - **********).toISOString()
    }
  ]
};
*/

// Auth API - Real Gatekeeper integration
export const authAPI = {
  async sendOTP(phoneNumber, isLogin = false) {
    try {
      const response = await fetch('/api/auth/request-otp', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ phoneNumber, isLogin }),
      });

      const data = await response.json();
      console.log("DATA IN authApi call: ", data)

      if (!response.ok) {
        if (response.status === 429) {
          throw new Error('Too many OTP requests. Please wait 30 minutes before trying again.');
        }
        throw new Error(data.error || 'Failed to send OTP');
      }

      return { 
        success: true, 
        requestId: data.requestId,
        provider: data.provider || 'SMS' 
      };
    } catch (error) {
      console.error('OTP request failed:', error);
      throw new Error(error.message || 'Failed to send OTP');
    }
  },

  async verifyOTP(phoneNumber, otp, isRegistration = false, userData, isLogin = false, requestId) {
    try {
      // If this is registration mode, use registration endpoint
      console.log('verifyOTP args:', {
        phoneNumber,
        otp,
        isRegistration,
        userData,
        isLogin,
        requestId
      });
      if (isRegistration && userData) {
        const registerResponse = await fetch('/api/auth/verify-and-register', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ 
            phone: phoneNumber, 
            otp,
            name: userData.name,
            specialty: userData.specialization,
            license: userData.license_number,
            hospital: userData.organization,
            idFileData: userData.id_file_data,
            requestId : requestId
          }),
        });

        const registerData = await registerResponse.json();

        if (!registerResponse.ok) {
          throw new Error(registerData.error || 'Registration failed');
        }

        return {
          success: true,
          message: registerData.message || 'Registration successful! Your account is pending approval.',
          isRegistration: true
        };
      }

      // Use login verification endpoint
      const verifyResponse = await fetch('/api/auth/verify-login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ 
          phone: phoneNumber, 
          otp,
          requestId
        }),
      });

      const verifyData = await verifyResponse.json();

      if (!verifyResponse.ok) {
        if (verifyData.error && verifyData.error.includes('Invalid or expired OTP')) {
          throw new Error('Invalid or expired OTP. Please request a new one.');
        }
        if (verifyData.status === 'pending_approval') {
          throw new Error('Account pending approval. Please contact admin for access.');
        }
        throw new Error(verifyData.error || 'OTP verification failed');
      }

      // Return login tokens and user data
      return {
        success: true,
        access_token: verifyData.access_token,
        refresh_token: verifyData.refresh_token,
        expires_in: verifyData.expires_in || 3600,
        user: {
          id: verifyData.user.id,
          name: verifyData.user.name || 'Doctor',
          phone: verifyData.user.phone,
          role: verifyData.user.role || 'DOCTOR',
          access: true, // If we got here, user has access
          meta: verifyData.user.meta
        }
      };
    } catch (error) {
      console.error('OTP verification failed:', error);
      throw new Error(error.message || 'Verification failed');
    }
  },

  async registerUser(phoneNumber) {
    try {
      const response = await fetch('/api/auth/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          source: 'WEB',
          phoneNumber: phoneNumber,
          user_role: 'DOCTOR'
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Registration failed');
      }

      return {
        success: true,
        message: data.message
      };
    } catch (error) {
      console.error('User registration failed:', error);
      throw error;
    }
  },

  async refreshToken() {
    try {
      const response = await fetch('/api/auth/refresh-token', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include', // Include cookies
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Token refresh failed');
      }

      // Store the new access token
      if (data.access_token) {
        localStorage.setItem('access_token', data.access_token);
        localStorage.setItem('authToken', data.access_token);
      }

      return {
        success: true,
        access_token: data.access_token,
        expires_in: data.expires_in || 3600
      };
    } catch (error) {
      console.error('Token refresh failed:', error);
      throw new Error(error.message || 'Token refresh failed');
    }
  },

  async logout() {
    try {
      // Clear local storage
      localStorage.removeItem('access_token');
      localStorage.removeItem('authToken');
      // Refresh token is now in HTTP-only cookie, cleared by server
      localStorage.removeItem('user');
      localStorage.removeItem('userPhone');
      
      // Dispatch logout event for WebSocket
      window.dispatchEvent(new Event('auth_logout'));
      
      return { success: true };
    } catch (error) {
      console.error('Logout failed:', error);
      throw new Error(error.message || 'Logout failed');
    }
  },

  async getCurrentUser() {
    try {
      // For now, return user from localStorage since Gatekeeper doesn't have a user profile endpoint
      const userStr = localStorage.getItem('user');
      if (!userStr) {
        throw new Error('No user data found');
      }
      
      const user = JSON.parse(userStr);
      return {
        success: true,
        user
      };
    } catch (error) {
      console.error('Get user info failed:', error);
      throw new Error(error.message || 'Failed to get user info');
    }
  }
};

// Threads API
export const threadsAPI = {
  async getThreads() {
    try {
      const accessToken = localStorage.getItem('access_token') || localStorage.getItem('token');
      if (!accessToken) {
        console.warn('No access token available');
        return [];
      }

      const response = await makeAuthenticatedRequest('/api/threads?limit=100', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/json',
        },
      });

      const data = await response.json();

      if (!response.ok) {
        console.error('Failed to load threads from server:', data);
        return [];
      }

      // Transform Gatekeeper threads to our format
      const rawThreads = data.dialogues || data.chats || data || [];
      console.log('Raw threads from API:', rawThreads);
      
      const threads = rawThreads
        .filter(dialogue => {
          // Additional frontend filtering for safety
          return dialogue && 
                 dialogue.id && 
                 dialogue.status !== 'failed' && 
                 dialogue.status !== 'discarded' &&
                 dialogue.title &&
                 dialogue.type;
        })
        .map(dialogue => {
          // Get the timestamp - could be Unix timestamp or ISO string
          let timestamp = dialogue.lastUpdated || dialogue.updatedAt || dialogue.lastMessage?.timestamp || dialogue.createdAt || new Date().toISOString();
          
          // Convert Unix timestamp to ISO string if needed
          if (typeof timestamp === 'number') {
            // If it's a Unix timestamp (milliseconds), convert to ISO string
            timestamp = new Date(timestamp).toISOString();
          }

          console.log("Processing dialogue from Gatekeeper: ", dialogue);
          
          return {
            id: dialogue.id,
            type: dialogue.type || 'patient-case',
            title: dialogue.title || 'Untitled Conversation',
            lastMessage: dialogue.lastMessage?.text || dialogue.lastMessage || dialogue.title || '',
            lastUpdated: timestamp,
            messageCount: dialogue.messageCount || 1 // Default to 1 if not provided
          };
        });

      // Sort by last updated, most recent first
      threads.sort((a, b) => new Date(b.lastUpdated) - new Date(a.lastUpdated));

      return threads;
    } catch (error) {
      console.error('Failed to load threads:', error);
      return [];
    }
  },

  async createThread(type, title, initialMessage = '') {
    // For now, create a local thread since Gatekeeper doesn't have a create thread endpoint
    // The thread will be created when the first message is sent
    const newThread = {
      id: generateUUID(),
      type,
      title: title || `New ${type.replace('-', ' ')} Thread`,
      lastMessage: initialMessage,
      lastUpdated: new Date().toISOString(),
      messageCount: initialMessage ? 1 : 0
    };
    
    return newThread;
  },

  async deleteThread(threadId) {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 400));
    
    const index = mockThreads.findIndex(t => t.id === threadId);
    if (index > -1) {
      mockThreads.splice(index, 1);
      delete mockMessages[threadId];
    }
    
    return { success: true };
  }
};

// Messages API
export const messagesAPI = {
  async getMessages(threadId) {
    try {
      const accessToken = localStorage.getItem('access_token');
      if (!accessToken) {
        throw new Error('No access token available');
      }

      const response = await makeAuthenticatedRequest(`/api/messages?threadId=${threadId}&limit=50`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/json',
        },
      });

      const data = await response.json();
      
      // Log the full API response for debugging
      console.log('Messages API Response for thread', threadId, ':', JSON.stringify(data, null, 2));

      if (!response.ok) {
        console.warn('Failed to load messages from server:', data);
        return [];
      }

      // Handle multiple possible response structures from Gatekeeper
      // Check for messages, chats, data, or direct array response
      let rawMessages = data.messages || data.chats || data.data || [];
      
      // If the response is directly an array, use it
      if (Array.isArray(data) && data.length > 0 && data[0].sender) {
        rawMessages = data;
      }
      
      console.log('Raw messages found:', rawMessages.length);
      
      // Log first message to debug sender field
      if (rawMessages.length > 0) {
        console.log('First message raw data:', JSON.stringify(rawMessages[0], null, 2));
        console.log('Sender field:', rawMessages[0].sender);
      }

      // Transform Gatekeeper messages to our format
      const messages = rawMessages.map(msg => ({
        id: msg.id || msg._id || generateUUID(),
        threadId: threadId,
        sender: (msg.sender?.toLowerCase() === 'human' || msg.sender?.toLowerCase() === 'user' || msg.role?.toLowerCase() === 'human' || msg.role?.toLowerCase() === 'user' || msg.senderType?.toLowerCase() === 'human' || msg.senderType?.toLowerCase() === 'user') ? 'user' : 'ai',
        content: msg.text || msg.content || msg.message || '',
        timestamp: msg.timestamp || msg.createdAt || new Date().toISOString(),
        attachments: msg.attachments || [],
        meta: msg.meta || {}
      }));

      console.log('Transformed messages:', messages.length);
      
      // Sort messages by timestamp in ascending order (oldest first)
      messages.sort((a, b) => new Date(a.timestamp) - new Date(b.timestamp));
      
      return messages;
    } catch (error) {
      console.error('Failed to load messages:', error);
      return [];
    }
  },

  async sendMessage(threadId, content, attachments = [], threadType = THREAD_TYPES.PATIENT_CASE) {
    try {
      const userPhone = localStorage.getItem('userPhone');
      const accessToken = localStorage.getItem('access_token');
      
      if (!userPhone) {
        throw new Error('User phone not found');
      }

      // Create user message for immediate display
      const userMessage = {
        id: 'msg-' + Date.now(),
        threadId,
        sender: 'user',
        content,
        timestamp: new Date().toISOString(),
        attachments: attachments || []
      };

      // Send via local API
      const response = await fetch('/api/messages/send', {
        method: 'POST',
        headers: {
          ...(accessToken && { 'Authorization': `Bearer ${accessToken}` }),
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          threadId,
          content,
          attachments,
          threadType,
          userPhone
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to send message');
      }

      console.log('Message sent via API:', data);

      // Update mock data for immediate UI response
      if (!mockMessages[threadId]) {
        mockMessages[threadId] = [];
      }
      mockMessages[threadId].push(userMessage);

      // Update thread last message
      const thread = mockThreads.find(t => t.id === threadId);
      if (thread) {
        thread.lastMessage = content;
        thread.lastUpdated = new Date().toISOString();
        thread.messageCount = mockMessages[threadId].length;
      }

      // Note: AI response will come via WebSocket
      return { 
        userMessage, 
        success: data.success,
        gatekeeperResponse: data.gatekeeperResponse,
        messageId: data.messageId,
        timestamp: data.timestamp
      };
    } catch (error) {
      console.error('Send message failed:', error);
      throw new Error(error.message || 'Failed to send message');
    }
  },

  async generateAIResponse(threadId) {
    // Simulate AI processing time
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    const thread = mockThreads.find(t => t.id === threadId);
    const threadType = thread?.type || THREAD_TYPES.QUICK_FACTS;
    
    let response = '';
    
    if (threadType === THREAD_TYPES.PATIENT_CASE) {
      response = 'Based on the clinical presentation, I recommend considering the following diagnostic workup and treatment approach. Please provide additional history, physical exam findings, or specific concerns for more targeted guidance.';
    } else if (threadType === THREAD_TYPES.RESEARCH) {
      response = 'Here are the latest evidence-based findings on this topic. I can provide specific studies, guidelines, or recommendations if you need more detailed information on any particular aspect.';
    } else {
      response = 'I\'m here to help with your medical questions. Feel free to ask about diagnoses, treatments, medications, or any other clinical topics you\'d like to discuss.';
    }
    
    return {
      id: 'ai-' + Date.now(),
      threadId,
      sender: 'ai',
      content: response,
      timestamp: new Date().toISOString(),
      attachments: []
    };
  },

  async uploadAudioChunk(formData) {
    const startTime = Date.now();
    let chunkId = 'unknown';
    
    try {
      // Extract chunk ID for logging
      if (formData.has('referenceChunkId')) {
        chunkId = formData.get('referenceChunkId');
      }
      
      console.log('📤 Starting chunk upload:', { 
        chunkId,
        sequenceNumber: formData.get('sequenceNumber'),
        sessionId: formData.get('requestId')
      });
      
      const accessToken = localStorage.getItem('access_token');
      
      if (!accessToken) {
        throw new Error('Authentication required');
      }

      // Use local API proxy route
      const response = await makeAuthenticatedRequest(
        `/api/audio/upload-chunk`,
        {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${accessToken}`
          },
          body: formData
        }
      );

      const duration = Date.now() - startTime;

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        const error = new Error(errorData.message || `Upload failed: ${response.statusText}`);
        error.status = response.status;
        error.retryable = response.status >= 500 || response.status === 429; // Server errors and rate limits are retryable
        error.chunkId = chunkId;
        throw error;
      }

      const data = await response.json();
      console.log('✅ Audio chunk uploaded successfully:', { 
        chunkId,
        duration: `${duration}ms`,
        response: data
      });
      
      return {
        ...data,
        chunkId: chunkId,
        uploadDuration: duration,
        success: true
      };
    } catch (error) {
      const duration = Date.now() - startTime;
      console.error('❌ Failed to upload audio chunk:', { 
        chunkId,
        duration: `${duration}ms`,
        error: error.message,
        status: error.status,
        retryable: error.retryable
      });
      
      // Enhance error with additional context
      error.chunkId = chunkId;
      error.uploadDuration = duration;
      if (error.retryable === undefined) {
        // Network errors and timeouts are generally retryable
        error.retryable = error.name === 'TypeError' || error.message.includes('fetch');
      }
      
      throw error;
    }
  }
};

// Helper to resolve audio slug
const getAudioSlug = () => {
  try {
    // Next.js exposes NEXT_PUBLIC_* at build time
    const envSlug = typeof process !== 'undefined' && process.env && process.env.NEXT_PUBLIC_AUDIO_SLUG;
    return (envSlug && envSlug.trim()) || 'practitioner-dashboard';
  } catch (_) {
    return 'practitioner-dashboard';
  }
};

// Audio (Deepgram/Gemini) API
export const audioAPI = {
  async startSession(slug, { sessionId, userId, callbackUrl } = {}) {
    const accessToken = localStorage.getItem('access_token');
    if (!accessToken) throw new Error('Authentication required');
    const resolvedSlug = slug || getAudioSlug();
    const payload = {
      sessionId: sessionId || (typeof crypto !== 'undefined' && crypto.randomUUID ? crypto.randomUUID() : 'sess-' + Date.now()),
      userId: userId || localStorage.getItem('userPhone') || 'web-user',
      ...(callbackUrl ? { callbackUrl } : {}),
      slug: resolvedSlug
    };
    const res = await makeAuthenticatedRequest(`/api/audio/session/start`, {
      method: 'POST',
      headers: { 'Authorization': `Bearer ${accessToken}`, 'Content-Type': 'application/json' },
      body: JSON.stringify(payload),
    });
    const data = await res.json();
    if (!res.ok || !data.success) throw new Error(data.error || 'Failed to start session');
    return data;
  },

  async sendChunk(slug, sessionId, { audioData, mimeType = 'audio/pcm16', sequenceNumber } = {}) {
    const accessToken = localStorage.getItem('access_token');
    if (!accessToken) throw new Error('Authentication required');
    const resolvedSlug = slug || getAudioSlug();
    const body = { audioData, mimeType, ...(sequenceNumber !== undefined ? { sequenceNumber } : {}) };
    const res = await makeAuthenticatedRequest(`/api/audio/session/${resolvedSlug}/${sessionId}/chunk`, {
      method: 'POST',
      headers: { 'Authorization': `Bearer ${accessToken}`, 'Content-Type': 'application/json' },
      body: JSON.stringify(body)
    });
    const data = await res.json();
    if (!res.ok || !data.success) throw new Error(data.error || 'Failed to upload chunk');
    return data;
  },

  async pollTranscripts(slug, sessionId, sinceTs) {
    const accessToken = localStorage.getItem('access_token');
    if (!accessToken) throw new Error('Authentication required');
    const resolvedSlug = slug || getAudioSlug();
    const url = new URL(`/api/audio/session/${resolvedSlug}/${sessionId}/transcripts`, window.location.origin);
    if (sinceTs) url.searchParams.set('since', String(sinceTs));
    const res = await makeAuthenticatedRequest(url.toString(), {
      method: 'GET',
      headers: { 'Authorization': `Bearer ${accessToken}` }
    });
    const data = await res.json();
    if (!res.ok || !data.success) throw new Error(data.error || 'Failed to fetch transcripts');
    return data;
  },

  async endSession(slug, sessionId) {
    const accessToken = localStorage.getItem('access_token');
    if (!accessToken) throw new Error('Authentication required');
    const resolvedSlug = slug || getAudioSlug();
    const res = await makeAuthenticatedRequest(`/api/audio/session/${resolvedSlug}/${sessionId}/end`, {
      method: 'POST',
      headers: { 'Authorization': `Bearer ${accessToken}` }
    });
    const data = await res.json();
    if (!res.ok || !data.success) throw new Error(data.error || 'Failed to end session');
    return data;
  }
};

// SOAP Notes API
export const generateSoapNotes = async (dialogueId, currentEditedNotes = null) => {
  try {
    const accessToken = localStorage.getItem('access_token') || localStorage.getItem('token');
    if (!accessToken) {
      throw new Error('No access token available');
    }

    const body = {};
    if (currentEditedNotes) {
      body.currentEditedNotes = currentEditedNotes;
    }

    const response = await makeAuthenticatedRequest(`/api/dialogues/${dialogueId}/generate-soap-notes`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(body),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to generate SOAP notes');
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Generate SOAP notes error:', error);
    throw error;
  }
};

// Title and Summary Generation API
export const generateTitleAndSummary = async (dialogueId) => {
  try {
    const accessToken = localStorage.getItem('access_token') || localStorage.getItem('token');
    if (!accessToken) {
      throw new Error('No access token available');
    }

    console.log(`Generating title and summary for dialogue: ${dialogueId}`);

    // Call local API route (server-side proxy to Gatekeeper)
    const response = await makeAuthenticatedRequest(`/api/dialogues/${dialogueId}/generate-title-and-summary`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to generate title and summary');
    }

    const data = await response.json();
    console.log(`Successfully generated title and summary for dialogue: ${dialogueId}`, data);

    return data;
  } catch (error) {
    console.error('Generate title and summary error:', error);
    throw error;
  }
};

export const saveSOAPNotes = async (threadId, content, threadType, attachments = []) => {
    if (!threadId) return;

    try {
      const accessToken = localStorage.getItem('access_token');
      const userPhone = localStorage.getItem('userPhone');

      // Prepare the payload
      const payload = {
        text: content,
        sender: "human",
        source: "WEB",
        requestId: generateUUID(),
        timestamp: Date.now(),
        dialogueId: threadId,
        phoneNumber: userPhone,
        dialogueType: threadType
      };

      // Add attachments if any (following the same format as chat)
      if (attachments && attachments.length > 0) {
        payload.attachments = attachments;
      }

      const response = await fetch('/api/soapnotes/save', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...(accessToken && { 'Authorization': `Bearer ${accessToken}` }),
        },
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        const errorData = await response.json();
        console.error('Failed to save SOAP notes:', errorData);
        throw new Error(errorData.error || 'Failed to save SOAP notes');
      }

      return { success: true };
    } catch (error) {
      console.error('Error saving SOAP notes:', error);
      throw error;
    }
  };

// Feedback API - send thumbs up/down or free-text feedback
export const feedbackAPI = {
  async sendFeedback({ feedback, providerMessageId }) {
    const accessToken = localStorage.getItem('access_token');
    if (!accessToken) {
      throw new Error('Not authenticated');
    }

    // Determine userId as phone number
    let userId = localStorage.getItem('userPhone');
    if (!userId) {
      try {
        const userStr = localStorage.getItem('user');
        if (userStr) {
          const userObj = JSON.parse(userStr);
          userId = userObj?.phone || userObj?.id; // fall back to id if needed
        }
      } catch (_) {}
    }

    if (!userId) {
      throw new Error('User ID (phone) not found');
    }

    const res = await fetch('/api/feedback', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${accessToken}`,
      },
      body: JSON.stringify({ feedback, providerMessageId, userId }),
    });

    const data = await res.json().catch(() => ({}));
    if (!res.ok) {
      throw new Error(data.error || 'Failed to submit feedback');
    }
    return data;
  }
};
