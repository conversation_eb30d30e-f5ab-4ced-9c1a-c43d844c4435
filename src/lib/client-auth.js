// Client-side authentication utilities
import { authAPI } from './api';

// Check if JWT is expired
export function isTokenExpired(token) {
  if (!token) return true;
  
  try {
    // Decode JWT without verification (base64 decode)
    const parts = token.split('.');
    if (parts.length !== 3) return true;
    
    const payload = JSON.parse(atob(parts[1]));
    const exp = payload.exp;
    
    if (!exp) return false; // No expiration
    
    // Check if expired (exp is in seconds, Date.now() is in milliseconds)
    const now = Math.floor(Date.now() / 1000);
    return now >= exp;
  } catch (error) {
    console.error('Error checking token expiration:', error);
    return true; // Assume expired if we can't parse
  }
}

// Get stored tokens
export function getStoredTokens() {
  return {
    accessToken: localStorage.getItem('access_token') || localStorage.getItem('authToken'),
    // Refresh token is now in HTTP-only cookie
    userPhone: localStorage.getItem('userPhone')
  };
}

// Store new tokens
export function storeTokens(accessToken) {
  if (accessToken) {
    localStorage.setItem('access_token', accessToken);
    localStorage.setItem('authToken', accessToken);
  }
  // Refresh token is now stored in HTTP-only cookie by server
  // Clear logout flag on successful login
  localStorage.removeItem('isLoggedOut');
}

// Clear all auth data
export function clearAuthData() {
  localStorage.removeItem('access_token');
  localStorage.removeItem('authToken');
  // Refresh token is now in HTTP-only cookie, cleared by server
  localStorage.removeItem('user');
  localStorage.removeItem('userPhone');
}

// Logout function
export async function logout() {
  try {
    await fetch('/api/auth/logout', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
    });
  } catch (error) {
    console.error('Logout error:', error);
  } finally {
    // Clear all auth data
    clearAuthData();
    
    // Set logout flag to prevent automatic token refresh
    localStorage.setItem('isLoggedOut', 'true');
  }
}

// Attempt to refresh the access token
export async function refreshAccessToken() {
  // Check if user has explicitly logged out
  if (localStorage.getItem('isLoggedOut') === 'true') {
    return false;
  }
  
  try {
    const result = await authAPI.refreshToken();
    
    if (result.success && result.access_token) {
      // Store the new access token
      storeTokens(result.access_token);
      // Clear logout flag on successful refresh
      localStorage.removeItem('isLoggedOut');
      console.log('✅ Token refreshed successfully');
      return true;
    }
    
    console.error('Token refresh failed:', result);
    return false;
  } catch (error) {
    console.error('Error refreshing token:', error);
    return false;
  }
}

// Check auth status and refresh if needed
export async function checkAndRefreshAuth() {
  // Check if user has explicitly logged out
  if (localStorage.getItem('isLoggedOut') === 'true') {
    return { isAuthenticated: false, needsLogin: true };
  }
  
  const { accessToken } = getStoredTokens();
  
  // No access token - user needs to login
  if (!accessToken) {
    console.log('No auth token found');
    return { isAuthenticated: false, needsLogin: true };
  }
  
  // Check if access token is expired
  if (isTokenExpired(accessToken)) {
    console.log('Access token expired, attempting refresh...');
    
    // Try to refresh
    const refreshed = await refreshAccessToken();
    
    if (refreshed) {
      return { isAuthenticated: true, needsLogin: false };
    } else {
      // Refresh failed - user needs to login again
      clearAuthData();
      return { isAuthenticated: false, needsLogin: true };
    }
  }
  
  // Token is valid
  return { isAuthenticated: true, needsLogin: false };
}

// Redirect to login
export function redirectToLogin() {
  // Clear any stale auth data
  clearAuthData();
  
  // Redirect to home/login page
  if (window.location.pathname !== '/') {
    window.location.href = '/';
  }
}

// Calculate time until token expires (in milliseconds)
function getTimeUntilExpiry(token) {
  if (!token) return 0;
  
  try {
    const parts = token.split('.');
    if (parts.length !== 3) return 0;
    
    const payload = JSON.parse(atob(parts[1]));
    const exp = payload.exp;
    
    if (!exp) return 0;
    
    // Calculate time until expiry (exp is in seconds, Date.now() is in milliseconds)
    const now = Math.floor(Date.now() / 1000);
    const timeUntilExpiry = (exp - now) * 1000;
    
    return timeUntilExpiry > 0 ? timeUntilExpiry : 0;
  } catch (error) {
    console.error('Error calculating token expiry:', error);
    return 0;
  }
}

// Setup auth check with proactive refresh
export function setupAuthCheck(intervalMs = 60000) { // Check every minute
  let refreshTimeout = null;
  
  // Function to schedule next refresh
  const scheduleProactiveRefresh = () => {
    const { accessToken } = getStoredTokens();
    if (!accessToken) return;
    
    const timeUntilExpiry = getTimeUntilExpiry(accessToken);
    
    // Clear existing timeout
    if (refreshTimeout) {
      clearTimeout(refreshTimeout);
    }
    
    // If token expires in less than 5 minutes, refresh now
    const fiveMinutes = 5 * 60 * 1000;
    if (timeUntilExpiry > 0 && timeUntilExpiry < fiveMinutes) {
      console.log('Token expiring soon, refreshing now...');
      refreshAccessToken().then(success => {
        if (success) {
          // Schedule next refresh based on new token
          scheduleProactiveRefresh();
        }
      });
    } else if (timeUntilExpiry > fiveMinutes) {
      // Schedule refresh for 5 minutes before expiry
      const refreshIn = timeUntilExpiry - fiveMinutes;
      console.log(`Scheduling token refresh in ${Math.round(refreshIn / 60000)} minutes`);
      
      refreshTimeout = setTimeout(() => {
        refreshAccessToken().then(success => {
          if (success) {
            scheduleProactiveRefresh();
          }
        });
      }, refreshIn);
    }
  };
  
  // Check immediately
  checkAndRefreshAuth().then(({ needsLogin, isAuthenticated }) => {
    if (needsLogin) {
      redirectToLogin();
    } else if (isAuthenticated) {
      // Schedule proactive refresh
      scheduleProactiveRefresh();
    }
  });
  
  // Also check periodically as a fallback
  const interval = setInterval(async () => {
    const { needsLogin } = await checkAndRefreshAuth();
    if (needsLogin) {
      clearInterval(interval);
      if (refreshTimeout) clearTimeout(refreshTimeout);
      redirectToLogin();
    }
  }, intervalMs);
  
  // Return cleanup function
  return () => {
    clearInterval(interval);
    if (refreshTimeout) clearTimeout(refreshTimeout);
  };
}
