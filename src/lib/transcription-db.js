// Transcription Database Connection Utility
// This utility handles database connections for the transcription service

const { Pool } = require('pg');

// Database configuration using local environment variables
const transcriptionDbConfig = {
  host: process.env.PG_HOST,
  port: process.env.PG_PORT,
  database: process.env.PG_DATABASE,
  user: process.env.PG_USER,
  password: process.env.PG_PASSWORD,
  // Connection pool settings
  max: 20, // Maximum number of connections
  idleTimeoutMillis: 30000, // Close idle connections after 30 seconds
  connectionTimeoutMillis: 2000, // Return error after 2 seconds if connection could not be established
  ssl: true,
  // Force UTC timezone to prevent conversion issues
  timezone: 'UTC'
};
console.log("Database Configuration:", transcriptionDbConfig);
// Create connection pool
const transcriptionPool = new Pool(transcriptionDbConfig);

// Handle pool errors
transcriptionPool.on('error', (err) => {
  console.error('Unexpected error on idle transcription database client', err);
});

// Test connection on startup
transcriptionPool.query('SELECT NOW()', (err, res) => {
  if (err) {
    console.error('❌ Failed to connect to transcription database:', err.message);
  } else {
    console.log('✅ Connected to transcription database at:', res.rows[0].now);
  }
});

/**
 * Execute a query with parameters
 * @param {string} text - SQL query
 * @param {Array} params - Query parameters
 * @returns {Promise} - Query result
 */
const query = async (text, params = []) => {
  const client = await transcriptionPool.connect();
  try {
    // Ensure UTC timezone for this connection
    await client.query("SET timezone = 'UTC'");
    const result = await client.query(text, params);
    return result;
  } catch (error) {
    console.error('Database query error:', error);
    throw error;
  } finally {
    client.release();
  }
};

/**
 * Execute a transaction with multiple queries
 * @param {Function} callback - Function that receives client and executes queries
 * @returns {Promise} - Transaction result
 */
const transaction = async (callback) => {
  const client = await transcriptionPool.connect();
  try {
    // Ensure UTC timezone for this transaction
    await client.query("SET timezone = 'UTC'");
    await client.query('BEGIN');
    const result = await callback(client);
    await client.query('COMMIT');
    return result;
  } catch (error) {
    await client.query('ROLLBACK');
    console.error('Transaction error:', error);
    throw error;
  } finally {
    client.release();
  }
};

/**
 * Close the database connection pool
 */
const close = async () => {
  await transcriptionPool.end();
};

module.exports = {
  query,
  transaction,
  close,
  pool: transcriptionPool
};