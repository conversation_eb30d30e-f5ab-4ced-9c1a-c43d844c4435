// Thread Service - Database operations for thread management
const { Pool } = require('pg');

// Database connection
const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
  ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false
});

class ThreadService {
  // Create or update thread in database
  async createOrUpdateThread(threadId, userId, title, type, status = 'active') {
    const client = await pool.connect();
    try {
      const query = `
        INSERT INTO threads (id, user_id, title, type, status, created_at, updated_at)
        VALUES ($1, $2, $3, $4, $5, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
        ON CONFLICT (id) DO UPDATE SET
          title = EXCLUDED.title,
          type = EXCLUDED.type,
          status = EXCLUDED.status,
          updated_at = CURRENT_TIMESTAMP
        RETURNING *
      `;
      
      const result = await client.query(query, [threadId, userId, title, type, status]);
      return result.rows[0];
    } catch (error) {
      console.error('Error creating/updating thread:', error);
      throw error;
    } finally {
      client.release();
    }
  }

  // Get thread by ID
  async getThread(threadId) {
    const client = await pool.connect();
    try {
      const query = 'SELECT * FROM threads WHERE id = $1';
      const result = await client.query(query, [threadId]);
      return result.rows[0] || null;
    } catch (error) {
      console.error('Error getting thread:', error);
      throw error;
    } finally {
      client.release();
    }
  }

  // Get threads for a user
  async getUserThreads(userId, limit = 100) {
    const client = await pool.connect();
    try {
      const query = `
        SELECT * FROM threads 
        WHERE user_id = $1 
        ORDER BY last_message_at DESC NULLS LAST, updated_at DESC 
        LIMIT $2
      `;
      const result = await client.query(query, [userId, limit]);
      return result.rows;
    } catch (error) {
      console.error('Error getting user threads:', error);
      throw error;
    } finally {
      client.release();
    }
  }

  // Get thread type by ID (fast lookup)
  async getThreadType(threadId) {
    const client = await pool.connect();
    try {
      const query = 'SELECT type FROM threads WHERE id = $1';
      const result = await client.query(query, [threadId]);
      return result.rows[0]?.type || null;
    } catch (error) {
      console.error('Error getting thread type:', error);
      throw error;
    } finally {
      client.release();
    }
  }

  // Update thread last activity
  async updateThreadActivity(threadId) {
    const client = await pool.connect();
    try {
      const query = `
        UPDATE threads 
        SET last_message_at = CURRENT_TIMESTAMP, updated_at = CURRENT_TIMESTAMP 
        WHERE id = $1
      `;
      await client.query(query, [threadId]);
    } catch (error) {
      console.error('Error updating thread activity:', error);
      throw error;
    } finally {
      client.release();
    }
  }

  // Store message in database
  async storeMessage(messageId, threadId, sender, content, attachments = []) {
    const client = await pool.connect();
    try {
      const query = `
        INSERT INTO messages (id, thread_id, sender, content, attachments, created_at)
        VALUES ($1, $2, $3, $4, $5, CURRENT_TIMESTAMP)
        RETURNING *
      `;
      
      const result = await client.query(query, [
        messageId, 
        threadId, 
        sender, 
        content, 
        JSON.stringify(attachments)
      ]);
      
      return result.rows[0];
    } catch (error) {
      console.error('Error storing message:', error);
      throw error;
    } finally {
      client.release();
    }
  }

  // Get messages for a thread
  async getThreadMessages(threadId, limit = 100) {
    const client = await pool.connect();
    try {
      const query = `
        SELECT * FROM messages 
        WHERE thread_id = $1 
        ORDER BY created_at ASC 
        LIMIT $2
      `;
      const result = await client.query(query, [threadId, limit]);
      return result.rows.map(row => ({
        ...row,
        attachments: row.attachments || []
      }));
    } catch (error) {
      console.error('Error getting thread messages:', error);
      throw error;
    } finally {
      client.release();
    }
  }

  // Sync threads from Gatekeeper response
  async syncThreadsFromGatekeeper(userId, gatekeeperThreads) {
    const client = await pool.connect();
    try {
      await client.query('BEGIN');
      
      for (const thread of gatekeeperThreads) {
        if (thread.id && thread.type && thread.title) {
          await this.createOrUpdateThread(
            thread.id,
            userId,
            thread.title,
            thread.type,
            thread.status || 'active'
          );
        }
      }
      
      await client.query('COMMIT');
    } catch (error) {
      await client.query('ROLLBACK');
      console.error('Error syncing threads from Gatekeeper:', error);
      throw error;
    } finally {
      client.release();
    }
  }

  // Delete thread
  async deleteThread(threadId) {
    const client = await pool.connect();
    try {
      const query = 'DELETE FROM threads WHERE id = $1';
      await client.query(query, [threadId]);
    } catch (error) {
      console.error('Error deleting thread:', error);
      throw error;
    } finally {
      client.release();
    }
  }

  // Health check
  async healthCheck() {
    const client = await pool.connect();
    try {
      await client.query('SELECT 1');
      return true;
    } catch (error) {
      console.error('Thread service health check failed:', error);
      return false;
    } finally {
      client.release();
    }
  }
}

module.exports = new ThreadService();
