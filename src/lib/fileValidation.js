// File validation system for medical file attachments

// Allowed file types for medical use cases
export const ALLOWED_FILE_TYPES = {
  // Medical Images
  'image/jpeg': ['.jpg', '.jpeg'],
  'image/png': ['.png'], 
  'image/gif': ['.gif'],
  'image/webp': ['.webp'],
  'application/dicom': ['.dcm', '.dicom'],
  
  // Documents
  'application/pdf': ['.pdf'],
  'text/plain': ['.txt'],
  'application/msword': ['.doc'],
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx'],
  
  // Medical Data
  'text/csv': ['.csv'],
  'application/json': ['.json'],
  
  // Spreadsheets
  'application/vnd.ms-excel': ['.xls'],
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['.xlsx']
};

// File size limits
export const FILE_SIZE_LIMITS = {
  MAX_FILE_SIZE: 10 * 1024 * 1024, // 10MB per file
  MAX_TOTAL_SIZE: 50 * 1024 * 1024, // 50MB total per message
  MAX_FILES_PER_MESSAGE: 5
};

// File type categories for icons/display
export const FILE_CATEGORIES = {
  image: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
  medical: ['application/dicom'],
  document: ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'],
  data: ['text/csv', 'application/json', 'application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'],
  text: ['text/plain']
};

export class FileValidationError extends Error {
  constructor(message, code, file = null) {
    super(message);
    this.name = 'FileValidationError';
    this.code = code;
    this.file = file;
  }
}

/**
 * Validates a single file
 * @param {File} file - The file to validate
 * @returns {Object} Validation result
 */
export function validateFile(file) {
  const errors = [];
  
  // Check file size
  if (file.size > FILE_SIZE_LIMITS.MAX_FILE_SIZE) {
    errors.push({
      code: 'FILE_TOO_LARGE',
      message: `File "${file.name}" is too large. Maximum size is ${formatFileSize(FILE_SIZE_LIMITS.MAX_FILE_SIZE)}.`,
      severity: 'error'
    });
  }
  
  // Check file type
  const isValidType = Object.keys(ALLOWED_FILE_TYPES).includes(file.type);
  const hasValidExtension = getFileExtension(file.name) && 
    Object.values(ALLOWED_FILE_TYPES).flat().includes(getFileExtension(file.name).toLowerCase());
  
  if (!isValidType && !hasValidExtension) {
    errors.push({
      code: 'INVALID_FILE_TYPE',
      message: `File type "${file.type || 'unknown'}" is not allowed. Please upload medical images, documents, or data files.`,
      severity: 'error'
    });
  }
  
  // Check filename safety
  if (!isSafeFilename(file.name)) {
    errors.push({
      code: 'UNSAFE_FILENAME',
      message: `Filename "${file.name}" contains invalid characters. Please rename the file.`,
      severity: 'error'
    });
  }
  
  return {
    isValid: errors.length === 0,
    errors,
    warnings: []
  };
}

/**
 * Validates a list of files
 * @param {File[]} files - Array of files to validate
 * @param {File[]} existingFiles - Already attached files
 * @returns {Object} Validation result
 */
export function validateFiles(files, existingFiles = []) {
  const allFiles = [...existingFiles, ...files];
  const errors = [];
  const warnings = [];
  
  // Check total number of files
  if (allFiles.length > FILE_SIZE_LIMITS.MAX_FILES_PER_MESSAGE) {
    errors.push({
      code: 'TOO_MANY_FILES',
      message: `Maximum ${FILE_SIZE_LIMITS.MAX_FILES_PER_MESSAGE} files allowed per message. You have ${allFiles.length} files.`,
      severity: 'error'
    });
  }
  
  // Check total size
  const totalSize = allFiles.reduce((sum, file) => sum + (file.size || 0), 0);
  if (totalSize > FILE_SIZE_LIMITS.MAX_TOTAL_SIZE) {
    errors.push({
      code: 'TOTAL_SIZE_TOO_LARGE',
      message: `Total file size is too large. Maximum is ${formatFileSize(FILE_SIZE_LIMITS.MAX_TOTAL_SIZE)}, you have ${formatFileSize(totalSize)}.`,
      severity: 'error'
    });
  }
  
  // Check for duplicate filenames
  const filenames = allFiles.map(f => f.name);
  const duplicates = filenames.filter((name, index) => filenames.indexOf(name) !== index);
  if (duplicates.length > 0) {
    warnings.push({
      code: 'DUPLICATE_FILENAMES',
      message: `Duplicate filenames detected: ${[...new Set(duplicates)].join(', ')}`,
      severity: 'warning'
    });
  }
  
  // Validate individual files
  const fileValidations = files.map(file => ({
    file,
    ...validateFile(file)
  }));
  
  // Collect all individual file errors
  fileValidations.forEach(validation => {
    errors.push(...validation.errors);
    warnings.push(...validation.warnings);
  });
  
  return {
    isValid: errors.length === 0,
    errors,
    warnings,
    fileValidations,
    totalSize,
    totalFiles: allFiles.length
  };
}

/**
 * Get file extension from filename
 * @param {string} filename
 * @returns {string|null}
 */
export function getFileExtension(filename) {
  const match = filename.match(/\.[^.]+$/);
  return match ? match[0] : null;
}

/**
 * Check if filename is safe (no dangerous characters)
 * @param {string} filename
 * @returns {boolean}
 */
export function isSafeFilename(filename) {
  // Allow alphanumeric, spaces, dots, hyphens, underscores
  const safePattern = /^[a-zA-Z0-9\s._-]+$/;
  return safePattern.test(filename) && filename.length <= 255;
}

/**
 * Format file size for display
 * @param {number} bytes
 * @returns {string}
 */
export function formatFileSize(bytes) {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * Get file category for display purposes
 * @param {string} mimeType
 * @returns {string}
 */
export function getFileCategory(mimeType) {
  for (const [category, types] of Object.entries(FILE_CATEGORIES)) {
    if (types.includes(mimeType)) {
      return category;
    }
  }
  return 'unknown';
}

/**
 * Get appropriate icon for file type
 * @param {string} mimeType
 * @returns {string} Icon name/emoji
 */
export function getFileIcon(mimeType) {
  const category = getFileCategory(mimeType);
  
  const iconMap = {
    image: '🖼️',
    medical: '🩻',
    document: '📄',
    data: '📊',
    text: '📝',
    unknown: '📎'
  };
  
  return iconMap[category] || iconMap.unknown;
}

/**
 * Create file preview URL (for images)
 * @param {File} file
 * @returns {Promise<string>} Preview URL
 */
export function createFilePreview(file) {
  return new Promise((resolve, reject) => {
    if (getFileCategory(file.type) !== 'image') {
      resolve(null);
      return;
    }
    
    const reader = new FileReader();
    reader.onload = () => resolve(reader.result);
    reader.onerror = reject;
    reader.readAsDataURL(file);
  });
}