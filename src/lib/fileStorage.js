// Mock file storage system using base64 encoding
// In production, this would upload to Azure Blob Storage

import { formatFileSize } from './fileValidation';

// In-memory storage for demo purposes
let fileStorage = new Map();
let fileCounter = 1;

/**
 * Generate unique file ID
 * @returns {string}
 */
function generateFileId() {
  return `file-${Date.now()}-${fileCounter++}`;
}

/**
 * Convert File to base64 data URL
 * @param {File} file
 * @returns {Promise<string>}
 */
function fileToBase64(file) {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = () => resolve(reader.result);
    reader.onerror = reject;
    reader.readAsDataURL(file);
  });
}

/**
 * Create thumbnail for image files
 * @param {File} file
 * @returns {Promise<string|null>}
 */
function createThumbnail(file) {
  return new Promise((resolve) => {
    if (!file.type.startsWith('image/')) {
      resolve(null);
      return;
    }

    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    const img = new Image();
    
    img.onload = () => {
      // Create 150x150 thumbnail
      const size = 150;
      canvas.width = size;
      canvas.height = size;
      
      // Calculate scaling to fit in square while maintaining aspect ratio
      const scale = Math.min(size / img.width, size / img.height);
      const scaledWidth = img.width * scale;
      const scaledHeight = img.height * scale;
      const x = (size - scaledWidth) / 2;
      const y = (size - scaledHeight) / 2;
      
      ctx.fillStyle = '#f0f0f0';
      ctx.fillRect(0, 0, size, size);
      ctx.drawImage(img, x, y, scaledWidth, scaledHeight);
      
      resolve(canvas.toDataURL('image/jpeg', 0.8));
    };
    
    img.onerror = () => resolve(null);
    
    // Convert file to data URL for the image
    fileToBase64(file).then(dataUrl => {
      img.src = dataUrl;
    }).catch(() => resolve(null));
  });
}

/**
 * Simulate file upload with progress
 * @param {File} file
 * @param {Function} onProgress - Progress callback (percent)
 * @returns {Promise<Object>} File metadata
 */
export async function uploadFile(file, onProgress = () => {}) {
  const fileId = generateFileId();
  
  try {
    // Simulate upload progress
    const progressSteps = [10, 25, 50, 75, 90, 100];
    for (const progress of progressSteps) {
      await new Promise(resolve => setTimeout(resolve, 100 + Math.random() * 200));
      onProgress(progress);
    }
    
    // Convert file to base64
    const dataUrl = await fileToBase64(file);
    const thumbnail = await createThumbnail(file);
    
    // Create file metadata
    const fileMetadata = {
      id: fileId,
      filename: file.name,
      originalName: file.name,
      mimeType: file.type,
      size: file.size,
      uploadDate: new Date().toISOString(),
      url: `/api/files/${fileId}`,
      thumbnailUrl: thumbnail ? `/api/files/${fileId}/thumbnail` : null,
      status: 'completed'
    };
    
    // Store file data
    fileStorage.set(fileId, {
      ...fileMetadata,
      data: dataUrl,
      thumbnailData: thumbnail
    });
    
    return fileMetadata;
  } catch (error) {
    console.error('File upload failed:', error);
    throw new Error(`Upload failed for ${file.name}: ${error.message}`);
  }
}

/**
 * Upload multiple files with progress tracking
 * @param {File[]} files
 * @param {Function} onProgress - Progress callback with file details
 * @returns {Promise<Object[]>} Array of file metadata
 */
export async function uploadFiles(files, onProgress = () => {}) {
  const uploadPromises = files.map(async (file, index) => {
    try {
      const fileMetadata = await uploadFile(file, (progress) => {
        onProgress({
          fileIndex: index,
          filename: file.name,
          progress,
          status: progress === 100 ? 'completed' : 'uploading'
        });
      });
      
      onProgress({
        fileIndex: index,
        filename: file.name,
        progress: 100,
        status: 'completed',
        fileId: fileMetadata.id
      });
      
      return fileMetadata;
    } catch (error) {
      onProgress({
        fileIndex: index,
        filename: file.name,
        progress: 0,
        status: 'failed',
        error: error.message
      });
      throw error;
    }
  });
  
  return Promise.all(uploadPromises);
}

/**
 * Get file metadata by ID
 * @param {string} fileId
 * @returns {Object|null}
 */
export function getFile(fileId) {
  return fileStorage.get(fileId) || null;
}

/**
 * Get file data URL by ID
 * @param {string} fileId
 * @returns {string|null}
 */
export function getFileData(fileId) {
  const file = fileStorage.get(fileId);
  return file ? file.data : null;
}

/**
 * Get file thumbnail by ID
 * @param {string} fileId
 * @returns {string|null}
 */
export function getFileThumbnail(fileId) {
  const file = fileStorage.get(fileId);
  return file ? file.thumbnailData : null;
}

/**
 * Delete file by ID
 * @param {string} fileId
 * @returns {boolean} Success status
 */
export function deleteFile(fileId) {
  return fileStorage.delete(fileId);
}

/**
 * Get storage statistics
 * @returns {Object}
 */
export function getStorageStats() {
  const files = Array.from(fileStorage.values());
  const totalSize = files.reduce((sum, file) => sum + file.size, 0);
  const filesByType = files.reduce((acc, file) => {
    acc[file.mimeType] = (acc[file.mimeType] || 0) + 1;
    return acc;
  }, {});
  
  return {
    totalFiles: files.length,
    totalSize,
    formattedSize: formatFileSize(totalSize),
    filesByType
  };
}

/**
 * Clear all files (for testing)
 */
export function clearStorage() {
  fileStorage.clear();
  fileCounter = 1;
}

/**
 * Simulate Azure Blob Storage URL patterns
 * @param {string} fileId
 * @returns {string}
 */
export function generateAzureBlobUrl(fileId) {
  // This would be the actual Azure Blob Storage URL in production
  return `https://medicaldashboard.blob.core.windows.net/attachments/${fileId}`;
}

/**
 * Simulate file download
 * @param {string} fileId
 * @param {string} filename
 */
export function downloadFile(fileId, filename) {
  const fileData = getFileData(fileId);
  if (!fileData) {
    throw new Error('File not found');
  }
  
  // Create download link
  const link = document.createElement('a');
  link.href = fileData;
  link.download = filename;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
}

// Mock API endpoints for Next.js API routes
export const fileAPI = {
  /**
   * Upload files endpoint
   */
  async upload(files, options = {}) {
    const { onProgress } = options;
    return uploadFiles(files, onProgress);
  },
  
  /**
   * Get file endpoint
   */
  async get(fileId) {
    const file = getFile(fileId);
    if (!file) {
      throw new Error('File not found');
    }
    return file;
  },
  
  /**
   * Delete file endpoint
   */
  async delete(fileId) {
    const success = deleteFile(fileId);
    if (!success) {
      throw new Error('File not found');
    }
    return { success: true };
  },
  
  /**
   * Get storage stats endpoint
   */
  async stats() {
    return getStorageStats();
  }
};