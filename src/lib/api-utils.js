import { NextResponse } from 'next/server';

export function with<PERSON><PERSON>r<PERSON><PERSON><PERSON>(handler) {
  return async (request, context) => {
    try {
      return await handler(request, context);
    } catch (err) {
      console.error('[API Error]', err);
      
      // Check for specific error types
      if (err.message?.includes('Invalid token')) {
        return NextResponse.json(
          { error: 'Authentication failed' },
          { status: 401 }
        );
      }
      
      if (err.message?.includes('Rate limit')) {
        return NextResponse.json(
          { error: 'Rate limit exceeded. Please try again later.' },
          { status: 429 }
        );
      }
      
      // Generic error response
      return NextResponse.json(
        { error: 'Internal server error' },
        { status: 500 }
      );
    }
  };
}

export function validateRequest(schema) {
  return async (request) => {
    try {
      const body = await request.json();
      const validation = schema.safeParse(body);
      
      if (!validation.success) {
        return {
          error: true,
          message: validation.error.errors[0].message,
          status: 400
        };
      }
      
      return {
        error: false,
        data: validation.data
      };
    } catch {
      return {
        error: true,
        message: 'Invalid request body',
        status: 400
      };
    }
  };
}

export function corsHeaders() {
  return {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
  };
}
