// Transcription Service Functions
// This module handles all database operations for the transcription system

const { query, transaction } = require('./transcription-db');

/**
 * Create a new transcription session
 * @param {Object} sessionData - Session information
 * @param {string} sessionData.requestId - Request ID for the transcription
 * @param {string} sessionData.threadId - Thread ID from the conversation
 * @param {string} sessionData.userId - User ID who started the recording
 * @returns {Promise<Object>} - Created session object
 */
const createTranscriptionSession = async (sessionData) => {
  const { requestId, threadId, userId } = sessionData;
  
  // Use CURRENT_TIMESTAMP in PostgreSQL to ensure server-side UTC time
  // This avoids any timezone conversion issues between Node.js and PostgreSQL
  const query_text = `
    INSERT INTO transcription_sessions (
      request_id, 
      thread_id, 
      user_id, 
      started_at, 
      last_activity_at,
      status
    ) VALUES ($1, $2, $3, CURRENT_TIMESTAMP AT TIME ZONE 'UTC', CURRENT_TIMESTAMP AT TIME ZONE 'UTC', 'active')
    RETURNING *, 
            started_at::text as started_at_text,
            started_at AT TIME ZONE 'UTC' as started_at_utc
  `;
  
  try {
    const result = await query(query_text, [requestId, threadId, userId]);
    console.log('✅ Created transcription session:', result.rows[0].id);
    console.log('🕐 Session timestamp verification:', { 
      dbStoredTime: result.rows[0].started_at,
      dbStoredTimeText: result.rows[0].started_at_text,
      dbStoredTimeUTC: result.rows[0].started_at_utc,
      systemTimezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
      nodeCurrentTime: new Date().toISOString()
    });
    return result.rows[0];
  } catch (error) {
    console.error('❌ Error creating transcription session:', error);
    throw error;
  }
};

/**
 * Add a new transcription chunk
 * @param {Object} chunkData - Chunk information
 * @param {string} chunkData.transcriptionSessionId - Session ID from our database
 * @param {string} chunkData.referenceChunkId - Chunk ID from transcription service
 * @param {number} chunkData.sequenceNumber - Order of this chunk
 * @param {string} chunkData.audioFileUrl - URL to audio file (optional)
 * @returns {Promise<Object>} - Created chunk object
 */
const addTranscriptionChunk = async (chunkData) => {
  const { transcriptionSessionId, referenceChunkId, sequenceNumber, audioFileUrl } = chunkData;
  
  const query_text = `
    INSERT INTO transcription_chunks (
      transcription_session_id,
      reference_chunk_id,
      sequence_number,
      audio_file_url,
      status
    ) VALUES ($1, $2, $3, $4, 'pending')
    RETURNING *
  `;
  
  try {
    const result = await query(query_text, [
      transcriptionSessionId, 
      referenceChunkId, 
      sequenceNumber, 
      audioFileUrl
    ]);
    
    // Update session chunk count and activity
    await updateSessionActivity(transcriptionSessionId);
    await query(
      'UPDATE transcription_sessions SET total_chunks = total_chunks + 1 WHERE id = $1',
      [transcriptionSessionId]
    );
    
    console.log('✅ Added transcription chunk:', result.rows[0].id);
    return result.rows[0];
  } catch (error) {
    console.error('❌ Error adding transcription chunk:', error);
    throw error;
  }
};

/**
 * Update a chunk with transcription results
 * @param {Object} updateData - Update information
 * @param {string} updateData.chunkId - Chunk ID to update
 * @param {string} updateData.transcriptionText - Transcribed text
 * @param {Array} updateData.speakers - Array of speakers
 * @param {string} updateData.status - Status (success/failed)
 * @returns {Promise<Object>} - Updated chunk object
 */
const updateChunkWithTranscription = async (updateData) => {
  const { chunkId, transcriptionText, speakers, status } = updateData;
  
  const query_text = `
    UPDATE transcription_chunks 
    SET transcription_text = $1, 
        speakers = $2, 
        status = $3
    WHERE reference_chunk_id = $4
    RETURNING *
  `;
  
  try {
    const result = await query(query_text, [
      transcriptionText, 
      JSON.stringify(speakers), 
      status, 
      chunkId
    ]);
    
    if (result.rows.length > 0) {
      // Update session activity
      await updateSessionActivity(result.rows[0].transcription_session_id);
      console.log('✅ Updated transcription chunk:', result.rows[0].id);
      return result.rows[0];
    } else {
      console.warn('⚠️ No chunk found to update with ID:', chunkId);
      return null;
    }
  } catch (error) {
    console.error('❌ Error updating transcription chunk:', error);
    throw error;
  }
};

/**
 * Update session activity timestamp
 * @param {string} sessionId - Session ID to update
 * @returns {Promise<void>}
 */
const updateSessionActivity = async (sessionId) => {
  const query_text = `
    UPDATE transcription_sessions 
    SET last_activity_at = CURRENT_TIMESTAMP AT TIME ZONE 'UTC'
    WHERE id = $1
  `;
  
  try {
    await query(query_text, [sessionId]);
  } catch (error) {
    console.error('❌ Error updating session activity:', error);
    throw error;
  }
};

/**
 * Update session status and end time
 * @param {string} sessionId - Session ID to update
 * @param {string} status - New status
 * @param {string} endTime - End time ISO string
 * @param {number} duration - Duration in seconds
 * @returns {Promise<void>}
 */
const updateSessionStatus = async (sessionId, status, endTime, duration) => {
  const query_text = `
    UPDATE transcription_sessions 
    SET status = $1,
        ended_at = $2::timestamptz,
        duration_seconds = $3,
        last_activity_at = $2::timestamptz
    WHERE id = $4
  `;
  
  try {
    // Duration is already in seconds, no conversion needed
    await query(query_text, [status, endTime, duration, sessionId]);
    console.log('✅ Updated session status:', { sessionId, status, durationSeconds: duration, endTime });
  } catch (error) {
    console.error('❌ Error updating session status:', error);
    throw error;
  }
};

/**
 * Create transcription summary from session chunks
 * @param {string} sessionId - Session ID to summarize
 * @param {string} completionReason - Reason for completion (manual/timeout/recovered)
 * @returns {Promise<Object>} - Created summary object
 */
const createTranscriptionSummary = async (sessionId, completionReason = 'manual') => {
  try {
    const result = await transaction(async (client) => {
      // Get session info
      const sessionResult = await client.query(
        'SELECT * FROM transcription_sessions WHERE id = $1',
        [sessionId]
      );
      
      if (sessionResult.rows.length === 0) {
        throw new Error('Session not found');
      }
      
      const session = sessionResult.rows[0];
      
      // Get all chunks for this session, ordered by sequence
      const chunksResult = await client.query(
        `SELECT * FROM transcription_chunks 
         WHERE transcription_session_id = $1 
         ORDER BY sequence_number ASC`,
        [sessionId]
      );
      
      const chunks = chunksResult.rows;
      
      // Combine transcription text from all chunks
      const fullTranscript = chunks
        .filter(chunk => chunk.transcription_text)
        .map(chunk => chunk.transcription_text)
        .join(' ');
      
      // Get unique speakers across all chunks
      const allSpeakers = new Set();
      chunks.forEach(chunk => {
        if (chunk.speakers) {
          const speakers = typeof chunk.speakers === 'string' 
            ? JSON.parse(chunk.speakers) 
            : chunk.speakers;
          speakers.forEach(speaker => allSpeakers.add(speaker));
        }
      });
      
      // Create summary
      const summaryResult = await client.query(
        `INSERT INTO transcription_summaries (
          transcription_session_id,
          thread_id,
          full_transcript,
          total_speakers,
          completion_reason,
          is_complete
        ) VALUES ($1, $2, $3, $4, $5, $6)
        RETURNING *`,
        [
          sessionId,
          session.thread_id,
          fullTranscript,
          allSpeakers.size,
          completionReason,
          completionReason === 'manual'
        ]
      );
      
      // Update session status
      await client.query(
        `UPDATE transcription_sessions 
         SET status = 'completed', 
             ended_at = CURRENT_TIMESTAMP,
             duration_seconds = EXTRACT(EPOCH FROM (CURRENT_TIMESTAMP - started_at))
         WHERE id = $1`,
        [sessionId]
      );
      
      return summaryResult.rows[0];
    });
    
    console.log('✅ Created transcription summary:', result.id);
    return result;
  } catch (error) {
    console.error('❌ Error creating transcription summary:', error);
    throw error;
  }
};

/**
 * Get all chunks for a session
 * @param {string} sessionId - Session ID
 * @returns {Promise<Array>} - Array of chunk objects
 */
const getSessionChunks = async (sessionId) => {
  const query_text = `
    SELECT * FROM transcription_chunks 
    WHERE transcription_session_id = $1 
    ORDER BY sequence_number ASC
  `;
  
  try {
    const result = await query(query_text, [sessionId]);
    return result.rows;
  } catch (error) {
    console.error('❌ Error getting session chunks:', error);
    throw error;
  }
};

/**
 * Get session by reference session ID (now uses request_id)
 * @param {string} referenceSessionId - Reference session ID from transcription service
 * @returns {Promise<Object|null>} - Session object or null
 */
const getSessionByReferenceId = async (referenceSessionId) => {
  const query_text = `
    SELECT *, 
           started_at AT TIME ZONE 'UTC' as started_at_utc,
           ended_at AT TIME ZONE 'UTC' as ended_at_utc
    FROM transcription_sessions 
    WHERE request_id = $1::uuid
  `;
  
  try {
    const result = await query(query_text, [referenceSessionId]);
    if (result.rows.length > 0) {
      // Ensure timestamps are in UTC
      const session = result.rows[0];
      if (session.started_at_utc) {
        session.started_at = session.started_at_utc;
      }
      if (session.ended_at_utc) {
        session.ended_at = session.ended_at_utc;
      }
      return session;
    }
    return null;
  } catch (error) {
    console.error('❌ Error getting session by reference ID:', error);
    throw error;
  }
};

/**
 * Get session by request ID
 * @param {string} requestId - Request ID for the transcription
 * @returns {Promise<Object|null>} - Session object or null
 */
const getSessionByRequestId = async (requestId) => {
  const query_text = `
    SELECT *, 
           started_at AT TIME ZONE 'UTC' as started_at_utc,
           ended_at AT TIME ZONE 'UTC' as ended_at_utc
    FROM transcription_sessions 
    WHERE request_id = $1
    ORDER BY started_at DESC
    LIMIT 1
  `;
  
  try {
    const result = await query(query_text, [requestId]);
    if (result.rows.length > 0) {
      // Ensure timestamps are in UTC
      const session = result.rows[0];
      if (session.started_at_utc) {
        session.started_at = session.started_at_utc;
      }
      if (session.ended_at_utc) {
        session.ended_at = session.ended_at_utc;
      }
      return session;
    }
    return null;
  } catch (error) {
    console.error('❌ Error getting session by request ID:', error);
    throw error;
  }
};

/**
 * Get orphaned sessions (active sessions older than specified minutes)
 * @param {number} maxAgeMinutes - Maximum age in minutes (default: 30)
 * @returns {Promise<Array>} - Array of orphaned session objects
 */
const getOrphanedSessions = async (maxAgeMinutes = 30) => {
  const query_text = `
    SELECT * FROM transcription_sessions 
    WHERE status = 'active' 
    AND last_activity_at < CURRENT_TIMESTAMP - INTERVAL '${maxAgeMinutes} minutes'
    ORDER BY last_activity_at ASC
  `;
  
  try {
    const result = await query(query_text);
    return result.rows;
  } catch (error) {
    console.error('❌ Error getting orphaned sessions:', error);
    throw error;
  }
};

/**
 * Get sessions for a specific thread
 * @param {string} threadId - Thread ID
 * @returns {Promise<Array>} - Array of session objects
 */
const getSessionsByThread = async (threadId) => {
  const query_text = `
    SELECT s.*, su.full_transcript, su.total_speakers, su.completion_reason
    FROM transcription_sessions s
    LEFT JOIN transcription_summaries su ON s.id = su.transcription_session_id
    WHERE s.thread_id = $1
    ORDER BY s.started_at DESC
  `;
  
  try {
    const result = await query(query_text, [threadId]);
    return result.rows;
  } catch (error) {
    console.error('❌ Error getting sessions by thread:', error);
    throw error;
  }
};

/**
 * Check if all chunks are processed and create summary if ready
 * @param {string} sessionId - Session ID to check
 * @returns {Promise<Object|null>} - Returns session info with summary if created
 */
const checkAndCreateSummaryIfReady = async (sessionId) => {
  try {
    return await transaction(async (client) => {
      // Get session info
      const sessionResult = await client.query(
        'SELECT * FROM transcription_sessions WHERE id = $1',
        [sessionId]
      );
      
      if (sessionResult.rows.length === 0) {
        return null;
      }
      
      const session = sessionResult.rows[0];
      
      // Only proceed if session is in 'stopping' status
      if (session.status !== 'stopping') {
        return { session, summaryCreated: false };
      }
      
      // Check if all chunks are processed AND have transcription results
      const chunksStatusResult = await client.query(
        `SELECT 
          COUNT(*) as total_count,
          COUNT(CASE WHEN status IN ('pending', 'processing') THEN 1 END) as pending_count,
          COUNT(CASE WHEN status = 'success' AND transcription_text IS NOT NULL THEN 1 END) as completed_count
         FROM transcription_chunks 
         WHERE transcription_session_id = $1`,
        [sessionId]
      );
      
      const { total_count, pending_count, completed_count } = chunksStatusResult.rows[0];
      const totalChunks = parseInt(total_count);
      const pendingChunks = parseInt(pending_count);
      const completedChunks = parseInt(completed_count);
      
      console.log('📊 Checking session chunks:', { 
        sessionId, 
        status: session.status, 
        totalChunks,
        pendingChunks,
        completedChunks,
        allChunksHaveTranscripts: completedChunks === totalChunks
      });
      
      // If there are still pending chunks OR not all chunks have transcripts, don't create summary yet
      if (pendingChunks > 0 || completedChunks < totalChunks) {
        return { 
          session, 
          summaryCreated: false, 
          pendingChunks, 
          waitingForTranscripts: totalChunks - completedChunks 
        };
      }
      
      // All chunks are done, create summary
      console.log('✅ All chunks processed, creating summary for session:', sessionId);
      
      // Get all chunks for summary
      const chunksResult = await client.query(
        `SELECT * FROM transcription_chunks 
         WHERE transcription_session_id = $1 
         ORDER BY sequence_number ASC`,
        [sessionId]
      );
      
      const chunks = chunksResult.rows;
      
      // Combine transcription text from all chunks
      const fullTranscript = chunks
        .filter(chunk => chunk.transcription_text)
        .map(chunk => chunk.transcription_text)
        .join(' ');
      
      // Get unique speakers across all chunks
      const allSpeakers = new Set();
      chunks.forEach(chunk => {
        if (chunk.speakers) {
          const speakers = typeof chunk.speakers === 'string' 
            ? JSON.parse(chunk.speakers) 
            : chunk.speakers;
          speakers.forEach(speaker => allSpeakers.add(speaker));
        }
      });
      
      // Create summary
      const summaryResult = await client.query(
        `INSERT INTO transcription_summaries (
          transcription_session_id,
          thread_id,
          full_transcript,
          total_speakers,
          completion_reason,
          is_complete
        ) VALUES ($1, $2, $3, $4, $5, $6)
        RETURNING *`,
        [
          sessionId,
          session.thread_id,
          fullTranscript,
          allSpeakers.size,
          'manual',
          true
        ]
      );
      
      // Update session status to completed
      await client.query(
        `UPDATE transcription_sessions 
         SET status = 'completed'
         WHERE id = $1`,
        [sessionId]
      );
      
      const summary = summaryResult.rows[0];
      console.log('✅ Created transcription summary:', summary.id);
      
      return { 
        session, 
        summary, 
        summaryCreated: true,
        fullTranscriptLength: fullTranscript.length
      };
    });
  } catch (error) {
    console.error('❌ Error checking/creating summary:', error);
    throw error;
  }
};

module.exports = {
  createTranscriptionSession,
  addTranscriptionChunk,
  updateChunkWithTranscription,
  updateSessionActivity,
  updateSessionStatus,
  createTranscriptionSummary,
  checkAndCreateSummaryIfReady,
  getSessionChunks,
  getSessionByReferenceId,
  getSessionByRequestId,
  getOrphanedSessions,
  getSessionsByThread
};