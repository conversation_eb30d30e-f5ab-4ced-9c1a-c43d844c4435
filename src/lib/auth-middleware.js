// Authentication middleware
import { NextResponse } from 'next/server';
import jwt from 'jsonwebtoken';

// Decode JWT token from Gatekeeper (encrypted payload)
function decodeGatekeeperToken(token) {
  try {
    // Gatekeeper tokens have encrypted payloads
    const decoded = jwt.decode(token);
    
    if (!decoded) {
      throw new Error('Failed to decode token');
    }
    
    // For Gatekeeper tokens, we trust them as-is since verification happens on their end
    // The token contains encrypted data that we can't decrypt, but we can use it
    return {
      success: true,
      decoded,
      // Mock user data since we can't decrypt the actual payload
      user: {
        id: 'gatekeeper-user',
        role: 'doctor',
        access: true
      }
    };
  } catch (error) {
    console.error('Token decode error:', error);
    return {
      success: false,
      error: 'Invalid token format'
    };
  }
}

export async function authenticateRequest(request) {
  try {
    const authHeader = request.headers.get('authorization');
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return {
        success: false,
        error: 'Missing or invalid authorization header',
        status: 401
      };
    }
    
    const token = authHeader.substring(7);
    
    // Decode the Gatekeeper token
    const result = decodeGatekeeperToken(token);
    
    if (!result.success) {
      return {
        success: false,
        error: result.error,
        status: 401
      };
    }
    
    // Since we're using Gatekeeper, we trust their token validation
    // In a production app, you might want to verify the token with Gatekeeper's API
    return {
      success: true,
      user: result.user,
      decoded: result.decoded
    };
  } catch (error) {
    console.error('Authentication error:', error);
    return {
      success: false,
      error: 'Invalid or expired token',
      status: 401
    };
  }
}

export async function requireAuth(request) {
  const auth = await authenticateRequest(request);
  
  if (!auth.success) {
    return NextResponse.json(
      { success: false, error: auth.error },
      { status: auth.status }
    );
  }
  
  return auth;
}

export async function requireAdmin(request) {
  const auth = await authenticateRequest(request);
  
  if (!auth.success) {
    return NextResponse.json(
      { success: false, error: auth.error },
      { status: auth.status }
    );
  }
  
  // Since we don't have user roles from Gatekeeper, admin check is disabled
  // In production, you'd check against Gatekeeper's user data
  return NextResponse.json(
    { success: false, error: 'Admin functionality not available' },
    { status: 403 }
  );
}