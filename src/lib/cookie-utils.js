import { cookies } from 'next/headers';

export const REFRESH_TOKEN_COOKIE_NAME = 'august_refresh_token';

export function setRefreshTokenCookie(refreshToken) {
  const cookieStore = cookies();
  
  cookieStore.set(REFRESH_TOKEN_COOKIE_NAME, refreshToken, {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'strict',
    maxAge: 30 * 24 * 60 * 60 * 1000, // 30 days
    path: '/'
  });
}

export function getRefreshTokenCookie() {
  const cookieStore = cookies();
  return cookieStore.get(REFRESH_TOKEN_COOKIE_NAME)?.value;
}

export function clearRefreshTokenCookie() {
  const cookieStore = cookies();
  
  cookieStore.set(REFRESH_TOKEN_COOKIE_NAME, '', {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'strict',
    maxAge: 0, // Expire immediately
    path: '/'
  });
}