'use client';

import { useState, useRef, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  TextField,
  Button,
  Typography,
  Alert,
  CircularProgress,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Fade,
  Collapse,
  Modal,
  Divider,
  LinearProgress
} from '@mui/material';
import { Phone, VpnKey, LocalHospital, CheckCircle, Email, CloudUpload, AttachFile, Login as LoginIcon } from '@mui/icons-material';
import { authAPI } from '@/lib/api';
import Image from 'next/image';
import AugustLogo from '@/app/august-logo.svg';
import PhoneInput from 'react-phone-input-2';
import 'react-phone-input-2/lib/style.css';




// Top 15 medical specialities in India
const SPECIALITIES = [
  'General Medicine',
  'Pediatrics',
  'Orthopedics',
  'Obstetrics & Gynecology',
  'General Surgery',
  'Dermatology',
  'Cardiology',
  'Psychiatry',
  'Radiology',
  'Anesthesiology',
  'Ophthalmology',
  'ENT (Otolaryngology)',
  'Dentistry',
  'Ayurveda',
  'Homeopathy',
  'Other'
];

export default function LoginForm({ onLoginSuccess }) {
  const [step, setStep] = useState('phone'); // 'phone', 'otp', or 'register'
  const [mode, setMode] = useState('register'); // 'login' or 'register'
  const [carouselIndex, setCarouselIndex] = useState(0); // Carousel slide index
  const [phoneNumber, setPhoneNumber] = useState(' ');
  const [otp, setOtp] = useState(['', '', '', '', '', '']);
  const otpRefs = useRef([]);
  const [detectedCountry, setDetectedCountry] = useState('in'); // Default to India
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  // Registration fields
  const [name, setName] = useState('');
  const [organization, setOrganization] = useState('');
  const [licenseNumber, setLicenseNumber] = useState('');
  const [specialization, setSpecialization] = useState('');
  const [customSpecialization, setCustomSpecialization] = useState('');
  const [mciHelpModalOpen, setMciHelpModalOpen] = useState(false);
  const [registrationSuccess, setRegistrationSuccess] = useState(false);
  
  // ID upload fields
  const [idFile, setIdFile] = useState(null);
  const [uploadingId, setUploadingId] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [uploadError, setUploadError] = useState('');
  const [requestId, setRequestId] = useState(null);

  // Carousel content
  const carouselSlides = [
    {
      title: "Free Access",
      description: "Exclusively for registered doctors, medical students, and nurses in India. No fees, no hidden costs - just advanced AI assistance for healthcare professionals."
    },
    {
      title: "Clinical Companion",
      description: "Discuss cases like you would with a colleague. Get information on differentials, treatment options, and clinical reasoning - enhancing, not replacing your expertise."
    },
    {
      title: "Evidence Based",
      description: "Access relevant studies, guidelines, and medical literature instantly. Every research finding comes with proper citations for verification."
    }
  ];

  // Auto-focus first OTP input when step changes to 'otp'
  useEffect(() => {
    if (step === 'otp' && otpRefs.current[0]) {
      // Small delay to ensure the input is rendered
      setTimeout(() => {
        otpRefs.current[0]?.focus();
      }, 100);
    }
  }, [step]);

  // Auto-rotate carousel
  useEffect(() => {
    const interval = setInterval(() => {
      setCarouselIndex((prev) => (prev + 1) % carouselSlides.length);
    }, 5000); // Change slide every 5 seconds

    return () => clearInterval(interval);
  }, [carouselSlides.length]);

  // Detect user's country via IP geolocation
  useEffect(() => {
    fetch('https://ipapi.co/json/')
      .then(res => res.json())
      .then(data => {
        if (data.country_code) {
          const countryCode = data.country_code.toLowerCase();
          setDetectedCountry(countryCode);
        }
      })
      .catch(err => {
        console.log('Failed to detect country, using default');
        // Keep default 'in' if detection fails
      });
  }, []);


  // Phone number handling functions like august-web-app
  const handlePhoneChange = (value, data) => {
    setPhoneNumber(value);
    
    // Extract country code from data object
    if (data && data.countryCode) {
      setDetectedCountry(data.countryCode);
    }
  };

  const handleKeyPress = (e) => {
    if (e.key === 'Enter') {
      handleSendOTP(e);
    }
  };

  const handleOtpChange = (index, value) => {
    if (value.length > 1) return; // Only allow single digits

    const newOtp = [...otp];
    newOtp[index] = value;
    setOtp(newOtp);

    // Auto-focus next input
    if (value && index < 5) {
      otpRefs.current[index + 1]?.focus();
    }
  };

  const handleOtpKeyDown = (index, e) => {
    // Handle backspace
    if (e.key === 'Backspace' && !otp[index] && index > 0) {
      otpRefs.current[index - 1]?.focus();
    }
    // Handle arrow keys
    if (e.key === 'ArrowLeft' && index > 0) {
      otpRefs.current[index - 1]?.focus();
    }
    if (e.key === 'ArrowRight' && index < 5) {
      otpRefs.current[index + 1]?.focus();
    }
  };

  const handleOtpPaste = (e) => {
    e.preventDefault();
    const pastedData = e.clipboardData.getData('text').slice(0, 6);
    const newOtp = [...otp];

    for (let i = 0; i < pastedData.length && i < 6; i++) {
      if (/\d/.test(pastedData[i])) {
        newOtp[i] = pastedData[i];
      }
    }
    setOtp(newOtp);

    // Focus the next empty field or the last field
    const nextEmptyIndex = newOtp.findIndex(digit => digit === '');
    if (nextEmptyIndex !== -1) {
      otpRefs.current[nextEmptyIndex]?.focus();
    } else {
      otpRefs.current[5]?.focus();
    }
  };

  const handleIdFileChange = async (e) => {
    const file = e.target.files[0];
    if (!file) return;

    // Validate file type
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'application/pdf'];
    if (!allowedTypes.includes(file.type)) {
      setUploadError('Please upload a JPG, PNG, or PDF file');
      return;
    }

    // Validate file size (5MB)
    const maxSize = 5 * 1024 * 1024; // 5MB in bytes
    if (file.size > maxSize) {
      setUploadError('File size must be less than 5MB');
      return;
    }

    setUploadError('');
    setIdFile(file);
    setUploadingId(true);
    setUploadProgress(0);

    try {
      // Convert file to base64 for socket.io transmission
      const reader = new FileReader();
      reader.onload = async (event) => {
        const base64Data = event.target.result.split(',')[1];
        
        // Simulate upload progress
        const progressInterval = setInterval(() => {
          setUploadProgress(prev => {
            if (prev >= 90) {
              clearInterval(progressInterval);
              return 90;
            }
            return prev + 10;
          });
        }, 100);

        // Store the file data - actual upload will happen during registration
        setTimeout(() => {
          clearInterval(progressInterval);
          setUploadProgress(100);
          setUploadingId(false);
          
          // Store the base64 data for upload during registration
          setIdFile({
            name: file.name,
            type: file.type,
            size: file.size,
            data: base64Data
          });
        }, 500);
      };
      
      reader.readAsDataURL(file);
    } catch (error) {
      console.error('File processing error:', error);
      setUploadError('Failed to process file');
      setUploadingId(false);
    }
  };

  const handleSendOTP = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    // Phone validation like august-web-app
    const cleanPhoneNumber = phoneNumber.replace(/\D/g, '');

    if (cleanPhoneNumber.length < 7 || cleanPhoneNumber.length > 15) {
      setError('Please enter a valid phone number (7-15 digits)');
      setLoading(false);
      return;
    }


    const formattedPhone = '+' + cleanPhoneNumber;

    try {
      let response;
      console.log('Sending OTP for mode:', mode);
      console.log('Phone number:', formattedPhone);
      
      if (mode === 'register') {
        response = await authAPI.sendOTP(formattedPhone);
        console.log('Register mode response:', response);
        setStep('otp');
      }
      else {
        response = await authAPI.sendOTP(formattedPhone, true);
        console.log('Login mode response:', response);
        
        if(!response.success) {
          console.log('Login failed, switching to register mode');
          setError(err.message + ' Switching to register mode.');
          setMode('register');
        } else {
          console.log('Login OTP sent successfully');
          setStep('otp');
        }
      }
      console.log('REQUEST ID IN RESPONSE OF REQ OTP: ', response.requestId)
      setRequestId(response.requestId)
      console.log('OTP sent via:', response.provider);
      console.log('Full response:', response);
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const handleVerifyOTP = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    const otpString = otp.join('');
    const formattedPhone = '+' + phoneNumber.replace(/\D/g, '');

    try {
      if (mode === 'register') {
        // Registration flow
        const userData = {
          name,
          organization,
          license_number: licenseNumber || undefined,
          specialization: specialization === 'Other' ? customSpecialization : specialization,
          verification_type: licenseNumber ? 'mci' : 'id_upload',
          id_file_data: !licenseNumber && idFile ? idFile : null
        };
        console.log("Sending request id to verify: ", requestId)
        const response = await authAPI.verifyOTP(formattedPhone, otpString, true, userData, false, requestId);

        if (response.isRegistration) {
          setError('');
          setRegistrationSuccess(true);
          return;
        }
      } else {
        // Login flow
        const response = await authAPI.verifyOTP(formattedPhone, otpString, false, null, false, requestId);

        // Store tokens and user data
        localStorage.setItem('access_token', response.access_token); // For WebSocket auth
        localStorage.setItem('authToken', response.access_token); // Keep for backward compatibility
        // Refresh token is now stored in HTTP-only cookie by the server
        localStorage.setItem('user', JSON.stringify(response.user));
        localStorage.setItem('userPhone', phoneNumber);
        // Clear logout flag on successful login
        localStorage.removeItem('isLoggedOut');

        // Dispatch auth event for WebSocket
        window.dispatchEvent(new Event('auth_login'));

        onLoginSuccess(response.user);
      }
    } catch (err) {
      if (err.message.includes('already registered')) {
        setError(err.message + ' Switching to login mode.');
        setMode('login');
      } else if (err.message.includes('pending approval')) {
        setError('Account pending approval. Please contact admin.');
      } else {
        setError(err.message);
      }
    } finally {
      setLoading(false);
    }
  };

  const resetForm = () => {
    setStep('phone');
    setMode('login');
    setPhoneNumber('');
    setOtp(['', '', '', '', '', '']);
    setError('');
    setName('');
    setOrganization('');
    setLicenseNumber('');
    setSpecialization('');
    setCustomSpecialization('');
    setRegistrationSuccess(false);
    setIdFile(null);
    setUploadingId(false);
    setUploadProgress(0);
    setUploadError('');
  };

  const handleSwitchToLogin = () => {
    // Reset to phone step and switch to login mode
    setStep('phone');
    setMode('login');
    setError('');
    // Clear registration-specific data
    setName('');
    setOrganization('');
    setLicenseNumber('');
    setSpecialization('');
    setCustomSpecialization('');
    setIdFile(null);
  };

  return (
    <Box
      sx={{
        minHeight: '100vh',
        display: 'flex',
        alignItems: { xs: 'flex-start', md: 'center' },
        justifyContent: 'center',
        bgcolor: { xs: '#fff', md: 'background.default' },
        p: { xs: 0, md: 2 },
        position: 'relative',
        overflow: { xs: 'auto', md: 'hidden' },
        '&::before': {
          content: '""',
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundImage: {
            xs: 'none',
            md: `
              radial-gradient(circle at 20% 50%, rgba(40, 108, 84, 0.04) 0%, transparent 50%),
              radial-gradient(circle at 80% 80%, rgba(40, 108, 84, 0.03) 0%, transparent 50%),
              radial-gradient(circle at 40% 20%, rgba(40, 108, 84, 0.02) 0%, transparent 50%)
            `
          },
          pointerEvents: 'none',
          zIndex: 0,
        }
      }}
    >
      {/* Login Button - Only show on registration screen */}
      {step === 'register' && (
        <Button
          variant="outlined"
          startIcon={<LoginIcon />}
          onClick={handleSwitchToLogin}
          sx={{
            position: 'fixed',
            top: 16,
            right: 16,
            zIndex: 1300,
            textTransform: 'none',
            borderColor: '#111',
            color: '#111',
            bgcolor: 'background.paper',
            px: { xs: 1.5, md: 2 },
            py: 0.75,
            minWidth: { xs: 'auto', md: '100px' },
            fontSize: { xs: '0.875rem', md: '0.9rem' },
            fontWeight: 500,
            boxShadow: 1,
            '&:hover': {
              bgcolor: '#111',
              color: 'white',
              borderColor: '#111'
            }
          }}
        >
          Login
        </Button>
      )}

      {/* Two column container */}
      <Box sx={{
        display: 'flex',
        flexDirection: { xs: 'column', md: 'row' },
        maxWidth: { xs: '100%', md: 1200 },
        width: '100%',
        gap: { xs: 0, md: 3 },
        alignItems: { xs: 'stretch', md: 'center' },
        position: 'relative',
        zIndex: 1,
        p: { xs: 0, md: 2 },
        minHeight: { xs: '100vh', md: 'auto' },
        height: { xs: 'auto', md: 'auto' },
        overflow: { xs: 'visible', md: 'visible' },
      }}>
        {/* Left side - Form */}
        <Card sx={{
          flex: { xs: '0 0 auto', md: '0 0 calc(50% - 12px)' },
          maxWidth: { xs: '100%', md: 'calc(50% - 12px)' },
          width: '100%',
          height: { xs: 'auto', md: 'calc(100vh - 64px)' },
          maxHeight: { xs: 'auto', md: '1000px' },
          boxShadow: 'none',
          borderRadius: { xs: 0, md: 3 },
          overflow: { xs: 'visible', md: 'hidden' },
          backgroundColor: '#fff',
          display: 'flex',
          flexDirection: 'column',
          margin: { xs: 0, md: 'auto' },
        }}>
          <CardContent sx={{
            px: '42px',
            py: { xs: 2, sm: 3, md: 2.5 },
            display: 'block',
            flexDirection: 'column',
            justifyContent: 'center',
            width: '100%',
            height: '100%',
            boxSizing: 'border-box',
            overflowY: 'scroll',
            overflowX: 'hidden',
            scrollbarWidth: 'none', // Firefox
            msOverflowStyle: 'none', // IE/Edge
            '&::-webkit-scrollbar': {
              display: 'none' // Chrome, Safari, Edge
            }
          }}>
            {/* Mobile logo inside card */}
            <Box
              sx={{
                display: { xs: 'block', sm: 'none' },
                mb: 3,
                textAlign: 'center'
              }}
            >
              <Image
                src={AugustLogo}
                alt="August Logo"
                width={120}
                height={44}
                priority
              />
            </Box>
            <Box sx={{ textAlign: 'left', mb: { xs: 0.75, md: 1 } }}>
              <Box sx={{ mt: 2, ml: '-12px', display: { xs: 'none', sm: 'block' } }}>
                <Image
                  src={AugustLogo}
                  alt="August Logo"
                  width={180}
                  height={66}
                  priority
                />
              </Box>
              <Typography variant="h6" component="h1" gutterBottom sx={{ fontWeight: 600, mb: 0.5, fontSize: { xs: '1.25rem', md: '1.4rem' } }}>
                Built for doctors
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ opacity: 0.8, fontSize: { xs: '0.9rem', md: '1rem' }, mb: 0.5 }}>
                Accurate AI built for busy doctors
              </Typography>
            </Box>

            {error && (
              <Alert
                severity="error"
                sx={{
                  mb: 3,
                  borderRadius: 2,
                  '& .MuiAlert-icon': {
                    fontSize: 24
                  }
                }}
              >
                {error}
              </Alert>
            )}

            {registrationSuccess ? (
              // Registration Success Panel
              <Box
                sx={{
                  textAlign: 'center',
                  py: 4,
                  px: 2,
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  justifyContent: 'center',
                  minHeight: '400px'
                }}
              >
                {/* Main Message */}
                <Typography
                  variant="body1"
                  sx={{
                    mb: 4,
                    color: 'text.secondary',
                    lineHeight: 1.6,
                    fontSize: { xs: '1rem', md: '1.1rem' },
                    maxWidth: '400px'
                  }}
                >
                  Thank you for registering with August for Doctors. We&apos;ve received your registration details.
                </Typography>

                {/* Next Steps Box */}
                <Box
                  sx={{
                    bgcolor: 'rgba(40, 108, 84, 0.05)',
                    borderRadius: 2,
                    p: 3,
                    mb: 4,
                    border: '1px solid rgba(40, 108, 84, 0.1)',
                    maxWidth: '400px',
                    width: '100%'
                  }}
                >
                  <Typography
                    variant="h6"
                    sx={{
                      fontWeight: 600,
                      mb: 2,
                      color: 'primary.main',
                      fontSize: '1.1rem'
                    }}
                  >
                    What happens next?
                  </Typography>
                  
                  <Box sx={{ textAlign: 'left', mb: 2 }}>
                    <Typography
                      variant="body2"
                      sx={{
                        display: 'flex',
                        alignItems: 'flex-start',
                        mb: 1.5,
                        fontSize: '0.95rem',
                        lineHeight: 1.5
                      }}
                    >
                      <Box
                        sx={{
                          width: 6,
                          height: 6,
                          borderRadius: '50%',
                          bgcolor: 'primary.main',
                          mr: 1.5,
                          mt: 0.75,
                          flexShrink: 0
                        }}
                      />
                      You'll be notified via SMS once your account is approved
                    </Typography>
                    
                    <Typography
                      variant="body2"
                      sx={{
                        display: 'flex',
                        alignItems: 'flex-start',
                        mb: 1.5,
                        fontSize: '0.95rem',
                        lineHeight: 1.5
                      }}
                    >
                      <Box
                        sx={{
                          width: 6,
                          height: 6,
                          borderRadius: '50%',
                          bgcolor: 'primary.main',
                          mr: 1.5,
                          mt: 0.75,
                          flexShrink: 0
                        }}
                      />
                      Account approval typically takes 1-2 business days
                    </Typography>
                    
                    <Typography
                      variant="body2"
                      sx={{
                        display: 'flex',
                        alignItems: 'flex-start',
                        fontSize: '0.95rem',
                        lineHeight: 1.5
                      }}
                    >
                      <Box
                        sx={{
                          width: 6,
                          height: 6,
                          borderRadius: '50%',
                          bgcolor: 'primary.main',
                          mr: 1.5,
                          mt: 0.75,
                          flexShrink: 0
                        }}
                      />
                      We may contact you to verify your identity
                    </Typography>
                  </Box>
                </Box>

                {/* Contact Information */}
                <Box
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    mb: 4,
                    p: 2,
                    bgcolor: 'background.default',
                    borderRadius: 2,
                    maxWidth: '400px',
                    width: '100%'
                  }}
                >
                  <Email sx={{ color: 'primary.main', mr: 1, fontSize: 20 }} />
                  <Typography
                    variant="body2"
                    sx={{
                      color: 'text.secondary',
                      fontSize: '0.9rem'
                    }}
                  >
                    Questions? Contact us at{' '}
                    <Typography
                      component="span"
                      sx={{
                        color: 'primary.main',
                        fontWeight: 600,
                        fontSize: '0.9rem'
                      }}
                    >
                      <EMAIL>
                    </Typography>
                  </Typography>
                </Box>

              </Box>
            ) : step === 'phone' ? (
              <Box
                component="form"
                onSubmit={handleSendOTP}
                sx={{
                  transition: 'all 0.3s ease-in-out',
                  minHeight: { xs: 'auto', md: 'auto' },
                  display: 'flex',
                  flexDirection: 'column',
                  flex: 1,
                }}
              >
                {/* Mode Toggle
                <Box sx={{
                  display: 'flex',
                  mb: { xs: 1.5, md: 2 },
                  borderRadius: 2,
                  overflow: 'hidden',
                  bgcolor: 'background.default',
                  p: 0.5,
                  boxShadow: 'inset 0px 2px 4px rgba(0,0,0,0.06)'
                }}>
                  <Button
                    onClick={() => setMode('login')}
                    fullWidth
                    variant={mode === 'login' ? 'contained' : 'text'}
                    size="medium"
                    sx={{
                      borderRadius: 1.5,
                      mr: 0.5,
                      transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                      fontSize: { xs: '0.85rem', md: '0.95rem' },
                      py: { xs: 0.75, md: 1 },
                      ...(mode === 'login' && {
                        boxShadow: '0px 2px 8px rgba(0,0,0,0.1)',
                      })
                    }}
                  >
                    Login
                  </Button>
                  <Button
                    onClick={() => setMode('register')}
                    fullWidth
                    variant={mode === 'register' ? 'contained' : 'text'}
                    size="medium"
                    sx={{
                      borderRadius: 1.5,
                      transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                      fontSize: { xs: '0.85rem', md: '0.95rem' },
                      py: { xs: 0.75, md: 1 },
                      ...(mode === 'register' && {
                        boxShadow: '0px 2px 8px rgba(0,0,0,0.1)',
                      })
                    }}
                  >
                    Register
                  </Button>
                </Box> */}

                <Box sx={{ mb: 1.5 }}>
                  <PhoneInput
                    autoFocus
                    country={detectedCountry}
                    countryCodeEditable={true}
                    value={phoneNumber}
                    onChange={handlePhoneChange}
                    onKeyDown={handleKeyPress}
                    disabled={loading}
                    containerStyle={{
                      border: 'none',
                      width: '100%'
                    }}
                    inputStyle={{
                      width: '100%',
                      height: '50px',
                      fontSize: '15px',
                      borderRadius: '10px',
                      paddingLeft: '72px',
                      border: '1px solid rgba(60, 66, 87, 0.2)',
                      transition: 'all 0.3s ease-in-out',
                      '&:hover': {
                        borderColor: 'rgba(60, 66, 87, 0.4)',
                      },
                      '&:focus': {
                        borderColor: 'rgb(40, 108, 84)',
                        boxShadow: '0px 2px 12px rgba(40, 108, 84, 0.15)',
                      }
                    }}
                    buttonStyle={{
                      border: 'none',
                      backgroundColor: 'transparent',
                      borderRight: '1px solid rgba(0, 0, 0, 0.23)',
                      borderRadius: '10px 0 0 10px',
                      paddingRight: '10px',
                      paddingLeft: '10px',
                      paddingTop: '4px',
                      paddingBottom: '4px',
                      height: '50px'
                    }}
                    dropdownStyle={{
                      width: '300px',
                      maxHeight: '200px'
                    }}
                  />
                </Box>

                {/* Registration Fields */}
                <Collapse in={mode === 'register'} timeout={400}>
                  <Box sx={{ transition: 'opacity 0.3s ease-in-out', opacity: mode === 'register' ? 1 : 0 }}>
                    <TextField
                      fullWidth
                      size="small"
                      label="Full Name"
                      value={name}
                      onChange={(e) => setName(e.target.value)}
                      placeholder="Dr. John Smith"
                      required={mode === 'register'}
                      disabled={loading}
                      sx={{ 
                        mb: 1.5,
                        '& .MuiOutlinedInput-root': {
                          height: '50px',
                          '& fieldset': {
                            borderColor: 'rgba(60, 66, 87, 0.2)'
                          },
                          '&:hover fieldset': {
                            borderColor: 'rgba(60, 66, 87, 0.4)'
                          },
                          '&.Mui-focused fieldset': {
                            borderColor: 'primary.main'
                          }
                        },
                        '& .MuiInputBase-input': {
                          fontSize: { xs: '0.9rem', md: '1rem' }
                        },
                        '& .MuiInputLabel-root': {
                          fontSize: { xs: '0.875rem', md: '0.9rem' }
                        }
                      }}
                    />
                    <TextField
                      fullWidth
                      size="small"
                      label="Organization/Hospital"
                      value={organization}
                      onChange={(e) => setOrganization(e.target.value)}
                      placeholder="City General Hospital"
                      required={mode === 'register'}
                      disabled={loading}
                      sx={{ 
                        mb: 1.5,
                        '& .MuiOutlinedInput-root': {
                          height: '50px',
                          '& fieldset': {
                            borderColor: 'rgba(60, 66, 87, 0.2)'
                          },
                          '&:hover fieldset': {
                            borderColor: 'rgba(60, 66, 87, 0.4)'
                          },
                          '&.Mui-focused fieldset': {
                            borderColor: 'primary.main'
                          }
                        },
                        '& .MuiInputBase-input': {
                          fontSize: { xs: '0.9rem', md: '1rem' }
                        },
                        '& .MuiInputLabel-root': {
                          fontSize: { xs: '0.875rem', md: '0.9rem' }
                        }
                      }}
                    />
                    <FormControl fullWidth sx={{ 
                      mb: 1.5,
                      '& .MuiOutlinedInput-root': {
                        height: '50px',
                        '& fieldset': {
                          borderColor: 'rgba(60, 66, 87, 0.2)'
                        },
                        '&:hover fieldset': {
                          borderColor: 'rgba(60, 66, 87, 0.4)'
                        },
                        '&.Mui-focused fieldset': {
                          borderColor: 'primary.main'
                        }
                      },
                      '& .MuiInputBase-root': {
                        fontSize: { xs: '1rem', md: '1.1rem' }
                      },
                      '& .MuiInputLabel-root': {
                        fontSize: { xs: '0.95rem', md: '1rem' }
                      }
                    }} size="small">
                      <InputLabel id="specialization-label">Specialization</InputLabel>
                      <Select
                        labelId="specialization-label"
                        value={specialization}
                        onChange={(e) => {
                          setSpecialization(e.target.value);
                          if (e.target.value !== 'Other') {
                            setCustomSpecialization('');
                          }
                        }}
                        label="Specialization"
                        disabled={loading}
                        size="small"
                      >
                        {SPECIALITIES.map((spec) => (
                          <MenuItem key={spec} value={spec}>
                            {spec}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>

                    {specialization === 'Other' && (
                      <TextField
                        fullWidth
                        size="small"
                        label="Please specify your specialization"
                        value={customSpecialization}
                        onChange={(e) => setCustomSpecialization(e.target.value)}
                        placeholder="Enter your specialization"
                        disabled={loading}
                        sx={{ 
                          mb: 1.5,
                          '& .MuiOutlinedInput-root': {
                            height: '50px'
                          },
                          '& .MuiInputBase-input': {
                            fontSize: { xs: '0.9rem', md: '1rem' }
                          },
                          '& .MuiInputLabel-root': {
                            fontSize: { xs: '0.875rem', md: '0.9rem' }
                          }
                        }}
                      />
                    )}
                    
                    {/* Identity Verification Box */}
                    {(() => {
                      // Calculate values for identity verification
                      const showRegistrationNumber = detectedCountry === 'in' || detectedCountry === 'us';
                      const registrationLabel = detectedCountry === 'in' ? 'MCI Registration Number' : 
                                               detectedCountry === 'us' ? 'NPI Number' : '';
                      const registrationPlaceholder = detectedCountry === 'in' ? 'MD123456' : 
                                                     detectedCountry === 'us' ? '**********' : '';
                      
                      return (
                        <Box 
                          key={`identity-box-${detectedCountry}`}
                          sx={{ 
                            border: '1px solid rgba(0, 0, 0, 0.12)',
                            borderRadius: 2,
                            p: 2,
                            mb: 1.5,
                            bgcolor: '#f9f8f6'
                          }}>
                          <Typography variant="subtitle1" sx={{ fontWeight: 600, mb: 1 }}>
                            {showRegistrationNumber ? 'Verify Your Identity' : 'Upload Medical License/ID'}
                          </Typography>
                          
                          {/* Explanatory text */}
                          <Typography variant="body2" sx={{ mb: 2, color: 'rgba(0,0,0,0.8)', lineHeight: 1.6 }}>
                            August for Doctors is free for verified health care professionals (HCPs). If you are eligible for access, please provide either your official identification number or upload proof of occupational status.
                          </Typography>
                          
                          <Typography variant="body2" sx={{ mb: 2, color: 'rgba(0,0,0,0.8)', lineHeight: 1.6 }}>
                            For example:
                            <br />• A picture of an ID badge bearing name and professional status.
                            <br />• A copy of board certification documents or verification printout from a recognized certifying body.
                          </Typography>
                            
                            {/* Registration Number Field - Only for IN/US */}
                            {showRegistrationNumber && (
                              <>
                                <TextField
                                  fullWidth
                                  size="small"
                                  label={registrationLabel}
                                  value={licenseNumber}
                                  onChange={(e) => setLicenseNumber(e.target.value)}
                                  placeholder={registrationPlaceholder}
                                  disabled={loading}
                        sx={{ 
                          mb: 0.25,
                          '& .MuiOutlinedInput-root': {
                            height: '50px',
                            backgroundColor: '#fff',
                            '& fieldset': {
                              borderColor: 'rgba(60, 66, 87, 0.2)'
                            },
                            '&:hover fieldset': {
                              borderColor: 'rgba(60, 66, 87, 0.4)'
                            },
                            '&.Mui-focused fieldset': {
                              borderColor: 'primary.main'
                            }
                          },
                          '& .MuiInputBase-input': {
                            fontSize: { xs: '0.9rem', md: '1rem' }
                          },
                          '& .MuiInputLabel-root': {
                            fontSize: { xs: '0.875rem', md: '0.9rem' }
                          }
                        }}
                                />
                                {detectedCountry === 'in' && (
                                  <Typography 
                                    variant="caption" 
                                    onClick={() => setMciHelpModalOpen(true)}
                                    sx={{ 
                                      color: 'primary.main',
                                      fontWeight: 'bold',
                                      fontSize: '12px',
                                      cursor: 'pointer',
                                      display: 'block',
                                      textAlign: 'right',
                                      mb: 2,
                                      '&:hover': {
                                        textDecoration: 'underline'
                                      }
                                    }}
                                  >
                                    Don't have an MCI number?
                                  </Typography>
                                )}
                                {detectedCountry === 'us' && (
                                  <Typography 
                                    variant="caption" 
                                    sx={{ 
                                      color: 'text.secondary',
                                      fontSize: '12px',
                                      display: 'block',
                                      textAlign: 'right',
                                      mb: 2
                                    }}
                                  >
                                    Don't have an NPI number?
                                  </Typography>
                                )}
                              </>
                            )}
                            
                            {/* OR Divider - Only if showing registration number */}
                            {showRegistrationNumber && (
                              <Box sx={{ 
                                display: 'flex', 
                                alignItems: 'center', 
                                my: 2 
                              }}>
                                <Divider sx={{ flex: 1 }} />
                                <Typography sx={{ mx: 2, color: 'text.secondary', fontSize: '0.875rem' }}>
                                  OR
                                </Typography>
                                <Divider sx={{ flex: 1 }} />
                              </Box>
                            )}
                      
                            {/* ID Upload Section - Always shown */}
                            <Box>
                              <Typography variant="body2" sx={{ mb: 1, fontWeight: 500 }}>
                                {showRegistrationNumber ? 'Upload Medical ID/License' : 'Upload your medical license or professional ID'}
                              </Typography>
                        
                        <Box sx={{ position: 'relative' }}>
                          <input
                            type="file"
                            accept="image/jpeg,image/jpg,image/png,application/pdf"
                            onChange={handleIdFileChange}
                            disabled={loading || uploadingId}
                            style={{ display: 'none' }}
                            id="id-file-upload"
                          />
                          <label htmlFor="id-file-upload">
                            <Button
                              variant="outlined"
                              component="span"
                              disabled={loading || uploadingId}
                              startIcon={idFile ? <AttachFile /> : <CloudUpload />}
                              sx={{
                                width: '100%',
                                justifyContent: 'flex-start',
                                bgcolor: '#fff',
                                color: idFile ? 'success.main' : 'text.secondary',
                                borderColor: idFile ? 'success.main' : 'rgba(0, 0, 0, 0.23)',
                                '&:hover': {
                                  borderColor: idFile ? 'success.dark' : 'rgba(0, 0, 0, 0.4)',
                                  bgcolor: '#fff'
                                }
                              }}
                            >
                              {uploadingId ? 'Uploading...' : idFile ? idFile.name : 'Choose File'}
                            </Button>
                          </label>
                          
                          {/* Upload Progress */}
                          {uploadingId && (
                            <LinearProgress 
                              variant="determinate" 
                              value={uploadProgress} 
                              sx={{ 
                                position: 'absolute', 
                                bottom: 0, 
                                left: 0, 
                                right: 0,
                                borderRadius: '0 0 4px 4px'
                              }} 
                            />
                          )}
                        </Box>
                        
                        <Typography variant="caption" color="text.secondary" sx={{ mt: 0.5, display: 'block' }}>
                          Accepted: JPG, PNG, PDF (Max 5MB)
                        </Typography>
                        
                        {/* Upload Error */}
                        {uploadError && (
                          <Typography variant="caption" color="error" sx={{ mt: 0.5, display: 'block' }}>
                            {uploadError}
                          </Typography>
                        )}
                      </Box>
                          </Box>
                        );
                      })()}
                  </Box>
                </Collapse>

                {/* Submit button */}
                <Box sx={{ mt: 2 }}>
                  <Button
                    type="submit"
                    fullWidth
                    variant="contained"
                    size="large"
                    disabled={loading || !phoneNumber || (mode === 'register' && (!name || !organization || ((detectedCountry === 'in' || detectedCountry === 'us') ? (!licenseNumber && !idFile) : !idFile)))}
                    sx={{
                      mb: { xs: 0.5, md: 0.75 },
                      py: { xs: 0.9, md: 1 },
                      fontSize: { xs: '0.9rem', md: '1rem' },
                      fontWeight: 600,
                      boxShadow: '0px 4px 12px rgba(40, 108, 84, 0.2)',
                      '&:hover': {
                        boxShadow: '0px 6px 20px rgba(40, 108, 84, 0.3)',
                      },
                      '&:disabled': {
                        boxShadow: 'none',
                      }
                    }}
                  >
                    {loading ? (
                      <CircularProgress size={24} color="inherit" />
                    ) : (
                      mode === 'register' ? 'Send OTP to Register' : 'Send OTP to Login'
                    )}
                  </Button>
                </Box>

                {/* Bottom text section - pushed to bottom */}
                <Box>
                  <Fade in={mode === 'register'} timeout={1000}>
                    <Typography variant="caption" display="block" textAlign="center" sx={{ mt: 1, lineHeight: 1.8, fontSize: { xs: '0.75rem', md: '0.8rem' } }}>
                      Already have an account?{' '}
                      <a
                        href="#"
                        onClick={(e) => {
                          e.preventDefault();
                          setMode('login');
                        }}
                        style={{
                          color: 'rgb(40, 108, 84)',
                          textDecoration: 'none',
                          fontWeight: 600,
                          cursor: 'pointer'
                        }}
                        onMouseEnter={(e) => e.target.style.textDecoration = 'underline'}
                        onMouseLeave={(e) => e.target.style.textDecoration = 'none'}
                      >
                        Login here
                      </a>
                    </Typography>
                  </Fade>

                  {mode === 'login' && (
                    <Fade in={mode === 'login'} timeout={500}>
                      <Typography variant="caption" display="block" textAlign="center" sx={{ mt: 1, lineHeight: 1.2, fontSize: { xs: '0.75rem', md: '0.8rem' } }}>
                        Don&apos;t have an account?{' '}
                        <a
                          href="#"
                          onClick={(e) => {
                            e.preventDefault();
                            setMode('register');
                          }}
                          style={{
                            color: 'rgb(40, 108, 84)',
                            textDecoration: 'none',
                            fontWeight: 600,
                            cursor: 'pointer'
                          }}
                          onMouseEnter={(e) => e.target.style.textDecoration = 'underline'}
                          onMouseLeave={(e) => e.target.style.textDecoration = 'none'}
                        >
                          Register here
                        </a>
                      </Typography>
                    </Fade>
                  )}

                  <Fade in={true} timeout={300}>
                    <Typography variant="caption" display="block" textAlign="center" color="text.secondary" sx={{ lineHeight: 1.2, fontSize: { xs: '0.7rem', md: '0.75rem' }, transition: 'all 0.3s ease-in-out' }}>
                      {mode === 'register'
                        ? 'We\'ll send a verification code via SMS to complete your registration'
                        : 'We\'ll send a verification code via SMS to your registered mobile number'
                      }
                    </Typography>
                  </Fade>
                  <Collapse in={mode === 'register'} timeout={400}>
                    <Box sx={{ mt: 1 }}>
                      <Fade in={mode === 'register'} timeout={600}>
                        <Typography variant="caption" display="block" textAlign="center" color="text.secondary" sx={{ mb: 0.3, lineHeight: 1.3, fontSize: { xs: '0.7rem', md: '0.75rem' } }}>
                          By registering, you agree to our{' '}
                          <a
                            href="#"
                            style={{
                              color: 'rgb(40, 108, 84)',
                              textDecoration: 'none',
                              fontWeight: 600
                            }}
                            onMouseEnter={(e) => e.target.style.textDecoration = 'underline'}
                            onMouseLeave={(e) => e.target.style.textDecoration = 'none'}
                          >
                            Terms and Conditions
                          </a>
                        </Typography>
                      </Fade>
                      <Fade in={mode === 'register'} timeout={800}>
                        <Typography variant="caption" display="block" textAlign="center" color="text.secondary" sx={{ mt: 3, lineHeight: 1.4, fontSize: { xs: '0.7rem', md: '0.75rem' }, fontWeight: 'bold' }}>
                          August for Doctors is only available to registered healthcare practitioners and medical students.
                          We may contact you to verify your identity before giving you access.
                        </Typography>
                      </Fade>
                    </Box>
                  </Collapse>
                </Box>
              </Box>
            ) : (
              <Box component="form" onSubmit={handleVerifyOTP}>
                <Box sx={{ mb: 4, textAlign: 'center' }}>
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 1, fontSize: { xs: '1rem', md: '1.1rem' } }}>
                    Enter the 6-digit code sent via SMS to
                  </Typography>
                  <Typography variant="body1" sx={{ fontWeight: 600, fontSize: { xs: '1.1rem', md: '1.2rem' } }}>
                    {phoneNumber}
                  </Typography>
                </Box>

                {/* OTP Input Boxes */}
                <Box sx={{ mb: 3 }}>
                  <Typography variant="body2" sx={{ mb: 3, textAlign: 'center', color: 'text.secondary', fontWeight: 500, fontSize: { xs: '0.95rem', md: '1rem' } }}>
                    Verification Code
                  </Typography>
                  <Box
                    sx={{
                      display: 'flex',
                      gap: 1.5,
                      justifyContent: 'center',
                      mb: 1
                    }}
                    onPaste={handleOtpPaste}
                  >
                    {otp.map((digit, index) => (
                      <TextField
                        key={index}
                        inputRef={(el) => (otpRefs.current[index] = el)}
                        value={digit}
                        onChange={(e) => handleOtpChange(index, e.target.value)}
                        onKeyDown={(e) => handleOtpKeyDown(index, e)}
                        inputProps={{
                          maxLength: 1,
                          style: {
                            textAlign: 'center',
                            fontSize: '1.8rem',
                            fontWeight: 600,
                            padding: '8px 4px'
                          },
                          inputMode: 'numeric',
                          pattern: '[0-9]*'
                        }}
                        sx={{
                          width: 56,
                          height: 50,
                          '& .MuiOutlinedInput-root': {
                            borderRadius: 2,
                            height: '100%',
                            '& fieldset': {
                              borderColor: 'rgba(60, 66, 87, 0.2)',
                              borderWidth: 2
                            },
                            '&:hover fieldset': {
                              borderColor: 'rgba(60, 66, 87, 0.4)'
                            },
                            '&.Mui-focused fieldset': {
                              borderColor: 'primary.main',
                              borderWidth: 2
                            },
                            '&.Mui-disabled fieldset': {
                              borderColor: 'rgba(60, 66, 87, 0.12)'
                            }
                          },
                          '& .MuiInputBase-input': {
                            height: '100%',
                            boxSizing: 'border-box'
                          }
                        }}
                        disabled={loading}
                        variant="outlined"
                      />
                    ))}
                  </Box>
                </Box>

                <Button
                  type="submit"
                  fullWidth
                  variant="contained"
                  size="large"
                  disabled={loading || otp.some(digit => digit === '') || otp.join('').length !== 6}
                  sx={{
                    mb: 2,
                    py: 1.5,
                    fontSize: '0.95rem',
                    fontWeight: 600,
                    boxShadow: '0px 4px 12px rgba(40, 108, 84, 0.2)',
                    '&:hover': {
                      boxShadow: '0px 6px 20px rgba(40, 108, 84, 0.3)',
                    },
                    '&:disabled': {
                      boxShadow: 'none',
                    }
                  }}
                >
                  {loading ? (
                    <CircularProgress size={24} color="inherit" />
                  ) : (
                    mode === 'register' ? 'Verify & Register' : 'Verify & Login'
                  )}
                </Button>

                <Button
                  fullWidth
                  variant="text"
                  onClick={resetForm}
                  disabled={loading}
                  sx={{
                    color: 'text.secondary',
                    '&:hover': {
                      color: 'text.primary',
                      backgroundColor: 'rgba(0,0,0,0.04)',
                    }
                  }}
                >
                  Use Different Number
                </Button>

                <Typography variant="caption" display="block" textAlign="center" color="text.secondary" sx={{ mt: 2 }}>
                  {mode === 'register'
                    ? 'After verification, your account will be pending approval'
                    : 'Enter the 6-digit code from your SMS message'
                  }
                </Typography>
              </Box>
            )}
          </CardContent>
        </Card>

        {/* Right side - Carousel */}
        <Card sx={{
          display: 'flex',
          flex: { xs: '0 0 auto', md: '0 0 calc(50% - 12px)' },
          width: { xs: '100%', md: 'auto' },
          height: { xs: '400px', md: 'calc(100vh - 64px)' },
          maxHeight: { xs: '400px', md: '1000px' },
          position: 'relative',
          alignItems: 'flex-end',
          justifyContent: 'flex-start',
          overflow: 'hidden',
          backgroundImage: 'url(https://res.cloudinary.com/dpgnd3ad7/image/upload/v1752396921/doctor-background_fywkju.jpg)',
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          backgroundRepeat: 'no-repeat',
          borderRadius: { xs: 0, md: 3 },
          boxShadow: { xs: 'none', md: '0px 4px 20px rgba(0,0,0,0.08)' },
          mt: { xs: 0, md: 0 },
        }}>
          {/* Carousel content */}
          <Box sx={{
            position: 'relative',
            zIndex: 1,
            textAlign: 'left',
            p: { xs: 3, md: 4 },
            width: '100%',
            background: 'linear-gradient(to top, rgba(0,0,0,0.8), transparent)',
          }}>
            <Typography variant="h4" sx={{
              color: 'white',
              fontWeight: 700,
              mb: 2,
              fontSize: { xs: '1.75rem', md: '2rem' },
              textShadow: '0 2px 4px rgba(0,0,0,0.3)'
            }}>
              {carouselSlides[carouselIndex].title}
            </Typography>

            <Typography variant="body1" sx={{
              color: 'white',
              mb: { xs: 3, md: 4 },
              lineHeight: 1.6,
              fontSize: { xs: '1rem', md: '1.125rem' },
              maxWidth: { xs: '100%', md: '500px' }
            }}>
              {carouselSlides[carouselIndex].description}
            </Typography>

            {/* Carousel dots */}
            <Box sx={{ display: 'flex', justifyContent: 'flex-start', gap: 1 }}>
              {carouselSlides.map((_, index) => (
                <Box
                  key={index}
                  onClick={() => setCarouselIndex(index)}
                  sx={{
                    width: index === carouselIndex ? 32 : 8,
                    height: 4,
                    backgroundColor: 'white',
                    borderRadius: 2,
                    opacity: index === carouselIndex ? 1 : 0.4,
                    transition: 'all 0.3s ease',
                    cursor: 'pointer',
                    '&:hover': { opacity: index === carouselIndex ? 1 : 0.6 }
                  }}
                />
              ))}
            </Box>
          </Box>
        </Card>
      </Box>
      
      {/* MCI Help Modal */}
      <Modal
        open={mciHelpModalOpen}
        onClose={() => setMciHelpModalOpen(false)}
        aria-labelledby="mci-help-modal-title"
        aria-describedby="mci-help-modal-description"
      >
        <Box sx={{
          position: 'absolute',
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)',
          width: { xs: '90%', sm: 400 },
          bgcolor: 'background.paper',
          borderRadius: 2,
          boxShadow: 24,
          p: 4,
        }}>
          <Typography id="mci-help-modal-title" variant="h6" component="h2" sx={{ mb: 2, fontWeight: 600 }}>
            Registration Information
          </Typography>
          <Typography id="mci-help-modal-description" sx={{ mb: 2, lineHeight: 1.6 }}>
            August for Doctors is only available for registered doctors, nurses and medical students. 
            You can write to us at <strong><EMAIL></strong> with your details to get access. 
            We may contact you to verify your identity.
          </Typography>
          <Typography sx={{ mb: 3, lineHeight: 1.6, fontStyle: 'italic' }}>
            If you're a student or a nurse, please send across a copy of your student/work ID along with the email to ensure smooth verification.
          </Typography>
          <Button 
            onClick={() => setMciHelpModalOpen(false)}
            variant="contained"
            fullWidth
            sx={{ mt: 1 }}
          >
            Got it
          </Button>
        </Box>
      </Modal>
    </Box>
  );
}
