'use client';

import { useState, useEffect } from 'react';
import { Box, CircularProgress, Typography } from '@mui/material';
import { checkAndRefreshAuth } from '@/lib/client-auth';

export default function AuthWrapper({ children, onAuthChange }) {
  const [authState, setAuthState] = useState({
    isAuthenticated: false,
    loading: true,
    user: null
  });

  useEffect(() => {
    const validateAuth = async () => {
      try {
        const { isAuthenticated, needsLogin } = await checkAndRefreshAuth();
        
        if (isAuthenticated) {
          // Get user data from localStorage
          const userData = localStorage.getItem('user');
          const user = userData ? JSON.parse(userData) : null;
          
          setAuthState({
            isAuthenticated: true,
            loading: false,
            user
          });
          
          if (onAuthChange) {
            onAuthChange({ isAuthenticated: true, user });
          }
        } else {
          setAuthState({
            isAuthenticated: false,
            loading: false,
            user: null
          });
          
          if (onAuthChange) {
            onAuthChange({ isAuthenticated: false, user: null });
          }
        }
      } catch (error) {
        console.error('Auth validation error:', error);
        setAuthState({
          isAuthenticated: false,
          loading: false,
          user: null
        });
        
        if (onAuthChange) {
          onAuthChange({ isAuthenticated: false, user: null });
        }
      }
    };

    validateAuth();
  }, [onAuthChange]);

  const handleLoginSuccess = (userData) => {
    setAuthState({
      isAuthenticated: true,
      loading: false,
      user: userData
    });
    
    if (onAuthChange) {
      onAuthChange({ isAuthenticated: true, user: userData });
    }
  };

  const handleLogout = async () => {
    // Import logout function
    const { logout } = await import('@/lib/client-auth');
    
    // Perform logout
    await logout();
    
    setAuthState({
      isAuthenticated: false,
      loading: false,
      user: null
    });
    
    if (onAuthChange) {
      onAuthChange({ isAuthenticated: false, user: null });
    }
    
    // Force redirect to login page after logout
    window.location.href = '/';
  };

  if (authState.loading) {
    return (
      <Box 
        display="flex" 
        flexDirection="column"
        alignItems="center" 
        justifyContent="center" 
        minHeight="100vh"
        sx={{ 
          backgroundColor: '#f9f8f6', // Match theme background
          color: 'rgb(40, 108, 84)' // Match theme primary color
        }}
      >
        <CircularProgress 
          size={60} 
          sx={{ 
            color: 'rgb(40, 108, 84)', // Match theme primary color
            mb: 2 
          }} 
        />
        <Typography 
          variant="h6" 
          sx={{ 
            mb: 1,
            color: 'rgb(40, 108, 84)', // Match theme primary color
            fontWeight: 500
          }}
        >
          Loading...
        </Typography>
        <Typography 
          variant="body2" 
          sx={{ 
            color: 'rgba(40, 108, 84, 0.7)', // Slightly transparent primary color
            fontWeight: 400
          }}
        >
        </Typography>
      </Box>
    );
  }

  return children({
    isAuthenticated: authState.isAuthenticated,
    user: authState.user,
    onLoginSuccess: handleLoginSuccess,
    onLogout: handleLogout
  });
}
