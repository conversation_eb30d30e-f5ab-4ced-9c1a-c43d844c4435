'use client';

import { useState, useEffect, useRef } from 'react';
import { Box } from '@mui/material';

export default function WaveformAnimation({ isRecording, isPaused, audioStream }) {
  const [audioLevels, setAudioLevels] = useState([0, 0, 0, 0, 0]);
  const audioContextRef = useRef(null);
  const analyserRef = useRef(null);
  const animationFrameRef = useRef(null);
  const sourceRef = useRef(null);

  useEffect(() => {
    if (!isRecording || !audioStream) {
      // Reset levels when not recording
      setAudioLevels([0, 0, 0, 0, 0]);
      return;
    }

    const setupAudioAnalysis = async () => {
      try {
        // Create audio context
        audioContextRef.current = new (window.AudioContext || window.webkitAudioContext)();
        const audioContext = audioContextRef.current;

        // Create analyser node
        analyserRef.current = audioContext.createAnalyser();
        const analyser = analyserRef.current;
        analyser.fftSize = 256; // Larger FFT size for better frequency resolution
        analyser.smoothingTimeConstant = 0.3; // Less smoothing for more responsive bars

        // Create source from stream
        sourceRef.current = audioContext.createMediaStreamSource(audioStream);
        sourceRef.current.connect(analyser);

        // Start analyzing audio
        const analyzeAudio = () => {
          if (!analyser || isPaused) {
            // Show minimal levels when paused
            setAudioLevels([0.1, 0.1, 0.1, 0.1, 0.1]);
            animationFrameRef.current = requestAnimationFrame(analyzeAudio);
            return;
          }

          const bufferLength = analyser.frequencyBinCount;
          const dataArray = new Uint8Array(bufferLength);
          analyser.getByteFrequencyData(dataArray);

          // Use time domain data for better overall audio level detection
          const timeDataArray = new Uint8Array(analyser.fftSize);
          analyser.getByteTimeDomainData(timeDataArray);

          // Calculate overall volume level
          let totalVolume = 0;
          for (let i = 0; i < timeDataArray.length; i++) {
            const sample = (timeDataArray[i] - 128) / 128; // Convert to -1 to 1 range
            totalVolume += Math.abs(sample);
          }
          const avgVolume = totalVolume / timeDataArray.length;

          // Create 5 levels with some variation based on frequency data and overall volume
          const levels = [];
          const baseLevel = Math.min(1, avgVolume * 8); // Amplify volume for better visibility
          
          for (let i = 0; i < 5; i++) {
            // Use frequency data to add variation between bars
            const freqStart = Math.floor((i * bufferLength) / 5);
            const freqEnd = Math.floor(((i + 1) * bufferLength) / 5);
            
            let freqSum = 0;
            for (let j = freqStart; j < freqEnd && j < bufferLength; j++) {
              freqSum += dataArray[j];
            }
            const freqAvg = freqSum / (freqEnd - freqStart) / 255;
            
            // Combine base volume with frequency variation
            const variation = freqAvg * 0.5; // Reduce frequency influence
            const level = Math.min(1, Math.max(0.05, baseLevel + variation));
            levels.push(level);
          }

          setAudioLevels(levels);
          animationFrameRef.current = requestAnimationFrame(analyzeAudio);
        };

        analyzeAudio();
      } catch (error) {
        console.error('Error setting up audio analysis:', error);
        // Fallback to minimal visual feedback
        setAudioLevels([0.1, 0.1, 0.1, 0.1, 0.1]);
      }
    };

    setupAudioAnalysis();

    // Cleanup function
    return () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
      if (sourceRef.current) {
        sourceRef.current.disconnect();
      }
      if (audioContextRef.current && audioContextRef.current.state !== 'closed') {
        audioContextRef.current.close();
      }
    };
  }, [isRecording, audioStream, isPaused]);

  if (!isRecording) return null;

  return (
    <Box
      sx={{
        display: 'flex',
        alignItems: 'center',
        gap: '2px',
        height: '20px',
        mx: 1,
      }}
    >
      {audioLevels.map((level, index) => (
        <Box
          key={index}
          sx={{
            width: '3px',
            height: `${4 + (level * 14)}px`, // 4px minimum, up to 18px based on audio level
            backgroundColor: isPaused ? '#f59e0b' : '#ef4444',
            borderRadius: '1.5px',
            opacity: isPaused ? 0.6 : 1,
            transition: 'height 0.1s ease-out, opacity 0.3s ease',
            minHeight: '4px',
            maxHeight: '18px',
          }}
        />
      ))}
    </Box>
  );
}