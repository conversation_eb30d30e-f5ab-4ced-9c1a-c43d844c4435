'use client';

import { useState, useRef, useEffect } from 'react';
import { v4 as uuidv4 } from 'uuid';
import { Box, Button, Tooltip, Snackbar, Alert } from '@mui/material';
import { Mic, Stop, Pause, PlayArrow, CheckCircle, Error as ErrorIcon } from '@mui/icons-material';
import wsClient from '../lib/websocket-client';
import { threadsAPI, messagesAPI } from '../lib/api';
import WaveformAnimation from './WaveformAnimation';
import TranscriptionPortal from "../components/TranscriptionPortal";

export default function TranscriptionButton({ threadId, onTranscriptionResult, onRecordingStop }) {
  const [isRecording, setIsRecording] = useState(false);
  const [isPaused, setIsPaused] = useState(false);
  const [error, setError] = useState(null);
  const [currentSession, setCurrentSession] = useState(null);
  // Upload queue management
  const [uploadQueue, setUploadQueue] = useState([]);
  const [uploadStatus, setUploadStatus] = useState({});
  const [latestTranscription, setLatestTranscription] = useState(null);
  const [chunkStatus, setChunkStatus] = useState({ open: false, message: '', severity: 'success' });

  const mediaRecorderRef = useRef(null);
  const audioChunksRef = useRef([]);
  const chunkIntervalRef = useRef(null);
  const streamRef = useRef(null);
  const transcriptionUnsubscribeRef = useRef(null);
  const sequenceNumberRef = useRef(1);
  const sessionRef = useRef(null); // Store session in ref to avoid state timing issues
  // Upload queue management refs
  const uploadingRef = useRef(new Set());
  const chunkUploadUnsubscribeRef = useRef(null);

  // Parse speaker segments from transcription text
  const parseSpeakerSegments = (transcriptionText) => {
    if (!transcriptionText) return [];
    
    const segments = [];
    const speakerPattern = /\[Speaker (\d+)\]: ([^[\]]*?)(?=\[Speaker \d+\]:|$)/g;
    let match;
    
    while ((match = speakerPattern.exec(transcriptionText)) !== null) {
      const speakerNum = match[1];
      const text = match[2].trim();
      
      if (text) {
        segments.push({
          speaker: `Speaker ${speakerNum}`,
          text: text
        });
      }
    }
    
    return segments;
  };

  // Queue chunk for upload
  const queueChunk = (audioBlob, session, isFinalChunk = false) => {
    const chunkId = uuidv4();
    const chunkData = {
      id: chunkId,
      blob: audioBlob,
      session: session,
      sequenceNumber: sequenceNumberRef.current,
      retries: 0,
      status: 'queued',
      createdAt: new Date(),
      isFinalChunk: isFinalChunk || false
    };
    
    console.log('📋 Queuing chunk for upload:', { 
      chunkId, 
      sequenceNumber: chunkData.sequenceNumber,
      sessionId: session?.requestId,
      isFinalChunk: chunkData.isFinalChunk
    });
    
    setUploadQueue(prev => [...prev, chunkData]);
    setUploadStatus(prev => ({
      ...prev,
      [chunkId]: { status: 'queued', timestamp: new Date() }
    }));
    
    // Increment sequence number for next chunk
    sequenceNumberRef.current += 1;
    
    // Process the queue
    processUploadQueue(chunkData);
    
    return chunkId;
  };

  // Process upload queue
  const processUploadQueue = async (chunkData) => {
    if (uploadingRef.current.has(chunkData.id)) {
      console.log('🔄 Chunk already uploading:', chunkData.id);
      return;
    }
    
    uploadingRef.current.add(chunkData.id);
    
    setUploadStatus(prev => ({
      ...prev,
      [chunkData.id]: { status: 'uploading', timestamp: new Date() }
    }));
    
    try {
      await uploadChunk(chunkData);
    } catch (error) {
      console.error('Failed to upload chunk:', error);
      uploadingRef.current.delete(chunkData.id);
      
      setUploadStatus(prev => ({
        ...prev,
        [chunkData.id]: { status: 'failed', error: error.message, timestamp: new Date() }
      }));
    }
  };

  // Upload individual chunk
  const uploadChunk = async (chunkData) => {
    console.log('📤 Uploading chunk:', {
      chunkId: chunkData.id,
      isFinalChunk: chunkData.isFinalChunk
    });
    
    // Create FormData
    const formData = new FormData();
    formData.append('requestId', chunkData.session.requestId);
    formData.append('transcriptionSessionId', chunkData.session.requestId);
    formData.append('referenceChunkId', chunkData.id);
    formData.append('sequenceNumber', chunkData.sequenceNumber.toString());
    formData.append('threadId', chunkData.session.threadId);
    formData.append('status', 'processing');
    formData.append('files', chunkData.blob, `${chunkData.id}.webm`);
    
    // Add final chunk flag if this is the last chunk
    console.log('🔍 Checking isFinalChunk flag:', {
      chunkId: chunkData.id,
      isFinalChunk: chunkData.isFinalChunk,
      isFinalChunkType: typeof chunkData.isFinalChunk
    });
    
    if (chunkData.isFinalChunk) {
      formData.append('isFinalChunk', 'True');
      console.log('✅ Added isFinalChunk=True to formData');
    } else {
      console.log('❌ isFinalChunk is false, not adding to formData');
    }

    // Send via HTTP FormData
    await messagesAPI.uploadAudioChunk(formData);
    console.log('✅ HTTP upload completed for chunk:', chunkData.id);
    
    // Note: WebSocket confirmation will be handled by event listeners
  };

  // Set up event listeners for transcription events
  useEffect(() => {
    const handleTranscriptionStarted = (event) => {
      console.log('Transcription started:', event.detail);
    };

    const handleTranscriptionStopped = (event) => {
      console.log('Transcription stopped:', event.detail);
      setCurrentSession(null);
      setIsRecording(false);
      setLatestTranscription(null);
      sequenceNumberRef.current = 1;
    };

    const handleTranscriptionError = (event) => {
      console.error('Transcription error:', event.detail);
      setError(event.detail.error);
    };

    const handleTranscriptionResult = (event) => {
      const data = event.detail;
      console.log('Transcription result:', data);

      // Update latest transcription for display
      if (data.status === 'success' && data.transcription) {
        setLatestTranscription(data.transcription);
      }

      // Call callback if provided
      if (onTranscriptionResult && data.status === 'success') {
        const segments = parseSpeakerSegments(data.transcription);
        onTranscriptionResult({
          chunkId: data.chunkId,
          transcription: data.transcription,
          segments: segments,
          speakers: data.speakers,
          timestamp: data.timestamp,
          requestId: data.requestId || data.sessionId
        });
      }
    };

    const handleChunkTranscription = (event) => {
      const data = event.detail;
      console.log('Chunk transcription:', {
        chunkId: data.chunkId,
        requestId: data.requestId,
        isFinalChunk: data.isFinalChunk,
        transcriptionLength: data.transcription?.length
      });
      
      // Note: onTranscriptionResult is NOT called here anymore
      // TranscriptionDisplay now handles chunk_transcription events directly
      // onTranscriptionResult should only be called on transcription_result events
    };

    // Chunk upload event handlers
    const handleChunkUploadSuccess = (event) => {
      const { chunkId, sequenceNumber, sessionId, timestamp } = event.detail;
      console.log('✅ Chunk upload confirmed:', { chunkId, sequenceNumber });
      
      // Remove from uploading set
      uploadingRef.current.delete(chunkId);
      
      // Update queue - remove completed chunk
      setUploadQueue(prev => prev.filter(chunk => chunk.id !== chunkId));
      
      // Update status
      setUploadStatus(prev => ({
        ...prev,
        [chunkId]: { status: 'completed', timestamp: new Date(timestamp) }
      }));
    };

    const handleChunkUploadFailed = (event) => {
      const { chunkId, error, retryable, retryAfter, sessionId } = event.detail;
      console.error('❌ Chunk upload failed:', { chunkId, error, retryable });
      
      // Remove from uploading set
      uploadingRef.current.delete(chunkId);
      
      if (retryable) {
        // Find chunk and update retry count
        setUploadQueue(prev => prev.map(chunk => {
          if (chunk.id === chunkId && chunk.retries < 3) {
            const updatedChunk = { ...chunk, retries: chunk.retries + 1, status: 'retry' };
            
            // Schedule retry
            setTimeout(() => {
              console.log('🔄 Retrying chunk upload:', chunkId);
              processUploadQueue(updatedChunk);
            }, retryAfter || 1000);
            
            return updatedChunk;
          }
          return chunk;
        }));
        
        setUploadStatus(prev => ({
          ...prev,
          [chunkId]: { 
            status: 'retrying', 
            error: error, 
            retries: prev[chunkId]?.retries + 1 || 1,
            timestamp: new Date() 
          }
        }));
      } else {
        // Mark as permanently failed
        setUploadStatus(prev => ({
          ...prev,
          [chunkId]: { status: 'failed', error: error, timestamp: new Date() }
        }));
        
        setError(`Failed to upload audio chunk: ${error}`);
      }
    };

    const handleChunkTranscriptionReceived = (data) => {
      console.log('Chunk transcription received:', data);

      // Only process if this chunk belongs to our current session
      if (!currentSession || data.requestId !== currentSession.requestId) {
        console.log('Ignoring chunk for different session');
        return;
      }

      // Update the transcription display with the chunk transcription
      if (data.transcription && data.status === 'completed') {
        setLatestTranscription(data.transcription);
        
        // Also call the parent callback if provided
        if (onTranscriptionResult) {
          onTranscriptionResult({
            chunkId: data.referenceChunkId,
            transcription: data.transcription,
            requestId: data.requestId,
            sequenceNumber: data.sequenceNumber
          });
        }
      }
    };

    // Add event listeners
    window.addEventListener('transcription_started', handleTranscriptionStarted);
    window.addEventListener('transcription_stopped', handleTranscriptionStopped);
    window.addEventListener('transcription_error', handleTranscriptionError);
    window.addEventListener('transcription_result', handleTranscriptionResult);
    window.addEventListener('chunk_transcription', handleChunkTranscription);
    window.addEventListener('chunk_upload_success', handleChunkUploadSuccess);
    window.addEventListener('chunk_upload_failed', handleChunkUploadFailed);

    // Add window unload handler to stop recording if user closes tab/browser
    const handleWindowUnload = () => {
      if (isRecording && currentSession) {
        // Send stop signal immediately (no need to wait for response)
        wsClient.stopTranscription(currentSession.requestId).catch(() => {
          // Ignore errors on unload
        });
      }
    };

    window.addEventListener('beforeunload', handleWindowUnload);

    return () => {
      window.removeEventListener('transcription_started', handleTranscriptionStarted);
      window.removeEventListener('transcription_stopped', handleTranscriptionStopped);
      window.removeEventListener('transcription_error', handleTranscriptionError);
      window.removeEventListener('transcription_result', handleTranscriptionResult);
      window.removeEventListener('chunk_transcription', handleChunkTranscription);
      window.removeEventListener('chunk_upload_success', handleChunkUploadSuccess);
      window.removeEventListener('chunk_upload_failed', handleChunkUploadFailed);
      window.removeEventListener('beforeunload', handleWindowUnload);
      
      // Unsubscribe from transcription results if any
      if (transcriptionUnsubscribeRef.current) {
        transcriptionUnsubscribeRef.current();
        transcriptionUnsubscribeRef.current = null;
      }
      
      // Unsubscribe from chunk upload events if any
      if (chunkUploadUnsubscribeRef.current) {
        chunkUploadUnsubscribeRef.current();
        chunkUploadUnsubscribeRef.current = null;
      }
    };
  }, [onTranscriptionResult]);

  // Start recording
  const startRecording = async () => {
    try {
      setError(null);
      
      if (!wsClient.isConnected()) {
        setError('WebSocket not connected. Please refresh the page.');
        return;
      }

      // Get user media
      const stream = await navigator.mediaDevices.getUserMedia({ 
        audio: {
          sampleRate: 16000,
          channelCount: 1,
          echoCancellation: true,
          noiseSuppression: true
        }
      });
      
      streamRef.current = stream;

      // Start transcription session via WebSocket client - backend generates request_id
      try {
        const response = await wsClient.startTranscription(threadId);
        
        // Store session info with request_id
        const sessionInfo = {
          requestId: response.requestId,
          threadId: threadId,
          startTime: response.startTime
        };
        setCurrentSession(sessionInfo);
        sessionRef.current = sessionInfo; // Also store in ref
        
        // Reset sequence number for new session
        sequenceNumberRef.current = 1;
        
        // Subscribe to transcription results for this session
        transcriptionUnsubscribeRef.current = wsClient.subscribeToTranscription(response.requestId, (data) => {
          console.log('Received transcription result via subscription:', data);
        });
        
        // Subscribe to chunk upload events for this session
        chunkUploadUnsubscribeRef.current = wsClient.subscribeToChunkUploads(response.requestId, (data) => {
          console.log('Received chunk upload event via subscription:', data);
        });
      } catch (error) {
        console.error('Failed to start transcription session:', error);
        setError('Failed to start transcription: ' + error.message);
        stream.getTracks().forEach(track => track.stop());
        return;
      }

      // Setup MediaRecorder
      const mediaRecorder = new MediaRecorder(stream, {
        mimeType: 'audio/webm;codecs=opus'
      });
      
      mediaRecorderRef.current = mediaRecorder;
      audioChunksRef.current = [];

      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          audioChunksRef.current.push(event.data);
        }
      };

      mediaRecorder.onstop = async (event) => {
        console.log('🎬 Original onstop handler called, checking if final chunk');
        const audioBlob = new Blob(audioChunksRef.current, { type: 'audio/webm' });
        const sessionForChunk = sessionRef.current;
        
        // Check if this is a final chunk (when stopping recording)
        const isFinalChunk = event && event.isFinalChunk === true;
        console.log('🔍 Final chunk status in original handler:', { isFinalChunk });
        
        if (sessionForChunk && sessionForChunk.requestId) {
          await processAudioChunkWithSession(audioBlob, sessionForChunk, isFinalChunk);
        } else {
          console.log("🔴 No session available for chunk processing");
        }
        audioChunksRef.current = [];
      };

      // Start recording
      mediaRecorder.start();
      setIsRecording(true);
      
      // Dispatch event to notify that recording has started
      window.dispatchEvent(new CustomEvent('recording_started', { 
        detail: { threadId } 
      }));

      // Set up chunk interval (every 5 seconds for responsive streaming)
      chunkIntervalRef.current = setInterval(() => {
        if (mediaRecorder.state === 'recording') {
          mediaRecorder.stop();
          mediaRecorder.start();
        }
      }, 5000);

    } catch (error) {
      console.error('Error starting recording:', error);
      setError('Failed to start recording: ' + error.message);
    }
  };

  // Stop recording
  const stopRecording = async () => {
    console.log('🛑 Stopping recording...', {
      isRecording,
      isPaused,
      currentSession: currentSession?.requestId,
      mediaRecorderState: mediaRecorderRef.current?.state
    });
    
    // Store session info before clearing
    const sessionToStop = currentSession;
    
    // Stop the media recorder (handle both 'recording' and 'paused' states)
    if (mediaRecorderRef.current && 
        (mediaRecorderRef.current.state === 'recording' || mediaRecorderRef.current.state === 'paused')) {
      
      const originalOnStop = mediaRecorderRef.current.onstop;
      mediaRecorderRef.current.onstop = async (event) => {
        console.log('📦 Processing final audio chunk...');
        
        // Mark this event as a final chunk
        event.isFinalChunk = true;
        console.log('🏷️ Marked event as final chunk:', { isFinalChunk: event.isFinalChunk });
        
        // Process the final chunk first using the original handler
        if (originalOnStop) {
          await originalOnStop(event);
          console.log('✅ Final chunk processed');
        } else {
          // Fallback: manually process the final chunk
          console.log("🟡 No original onstop handler found, processing manually");
          const audioBlob = new Blob(audioChunksRef.current, { type: 'audio/webm' });
          const sessionForChunk = sessionRef.current;
          if (sessionForChunk && sessionForChunk.requestId && audioChunksRef.current.length > 0) {
            console.log('🔴 Processing final chunk manually with isFinalChunk=true:', {
              audioChunksLength: audioChunksRef.current.length,
              sessionRequestId: sessionForChunk.requestId
            });
            await processAudioChunkWithSession(audioBlob, sessionForChunk, true);
            console.log('✅ Final chunk processed manually');
          } else {
            console.log('⚠️ Skipping final chunk - missing requirements:', {
              hasSession: !!sessionForChunk,
              hasRequestId: !!sessionForChunk?.requestId,
              audioChunksLength: audioChunksRef.current.length
            });
          }
          audioChunksRef.current = [];
        }
        
        // Clear the interval after processing the final chunk
        if (chunkIntervalRef.current) {
          clearInterval(chunkIntervalRef.current);
          chunkIntervalRef.current = null;
        }
        
        // Then stop the transcription session via WebSocket client
        if (sessionToStop) {
          try {
            // Add a small delay to ensure the last chunk is sent
            await new Promise(resolve => setTimeout(resolve, 500));
            
            // Emit stop directly as a temporary workaround
            console.log('📍 Attempting to stop transcription session:', sessionToStop.requestId);
            
            const stopPromise = wsClient.stopTranscription(sessionToStop.requestId);
            
            // Set a shorter timeout for the promise
            const timeoutPromise = new Promise((_, reject) => 
              setTimeout(() => reject(new Error('Stop timeout')), 3000)
            );
            
            try {
              await Promise.race([stopPromise, timeoutPromise]);
              console.log('✅ Transcription session stopped successfully');
            } catch (promiseError) {
              console.log('⚠️ Stop promise failed/timed out, but server likely processed it:', promiseError.message);
              // The server probably processed it anyway, so we'll continue
            }
          } catch (error) {
            console.error('Failed to stop transcription session:', error);
            // Still continue with cleanup even if stop fails
          }
        }
      };
      
      // Resume if paused, then stop
      if (mediaRecorderRef.current.state === 'paused') {
        mediaRecorderRef.current.resume();
      }
      
      mediaRecorderRef.current.stop();
    } else {
      // Clear interval if no active recording
      if (chunkIntervalRef.current) {
        clearInterval(chunkIntervalRef.current);
        chunkIntervalRef.current = null;
      }
      
      // If no active recording, still send stop signal to ensure session cleanup
      if (sessionToStop) {
        try {
          await wsClient.stopTranscription(sessionToStop.requestId);
          console.log('✅ Transcription session stopped successfully (no active recording)');
        } catch (error) {
          console.error('Failed to stop transcription session:', error);
        }
      }
    }
    
    if (streamRef.current) {
      streamRef.current.getTracks().forEach(track => track.stop());
    }
    
    // Unsubscribe from transcription results
    if (transcriptionUnsubscribeRef.current) {
      transcriptionUnsubscribeRef.current();
      transcriptionUnsubscribeRef.current = null;
    }
    
    // Unsubscribe from chunk upload events
    if (chunkUploadUnsubscribeRef.current) {
      chunkUploadUnsubscribeRef.current();
      chunkUploadUnsubscribeRef.current = null;
    }
    
    // Clear upload queue and status
    setUploadQueue([]);
    setUploadStatus({});
    uploadingRef.current.clear();
    
    setIsRecording(false);
    setIsPaused(false);
    
    // Call the onRecordingStop callback if provided
    if (onRecordingStop) {
      onRecordingStop();
    }
  };

  // Pause recording
  const pauseRecording = () => {
    console.log('⏸️ Pausing recording...', {
      mediaRecorderState: mediaRecorderRef.current?.state
    });
    
    if (mediaRecorderRef.current && mediaRecorderRef.current.state === 'recording') {
      // Clear the chunk interval first
      if (chunkIntervalRef.current) {
        clearInterval(chunkIntervalRef.current);
      }
      
      // Stop current recording to send accumulated audio
      mediaRecorderRef.current.stop();
      
      // Start new recording and immediately pause it
      setTimeout(() => {
        if (mediaRecorderRef.current) {
          mediaRecorderRef.current.start();
          setTimeout(() => {
            if (mediaRecorderRef.current && mediaRecorderRef.current.state === 'recording') {
              mediaRecorderRef.current.pause();
              setIsPaused(true);
            }
          }, 50); // Small delay to ensure start completes
        }
      }, 100); // Delay to ensure stop completes and audio is processed
    }
  };

  // Resume recording
  const resumeRecording = () => {
    if (mediaRecorderRef.current && mediaRecorderRef.current.state === 'paused') {
      mediaRecorderRef.current.resume();
      setIsPaused(false);
      
      // Restart the chunk interval
      chunkIntervalRef.current = setInterval(() => {
        if (mediaRecorderRef.current && mediaRecorderRef.current.state === 'recording') {
          mediaRecorderRef.current.stop();
          mediaRecorderRef.current.start();
        }
      }, 5000);
    }
  };

  // Process audio chunk with explicit session (now uses queue system)
  const processAudioChunkWithSession = async (audioBlob, session, isFinalChunk = false) => {
    try {
      console.log('📋 processAudioChunkWithSession called:', {
        hasAudioBlob: !!audioBlob,
        hasSession: !!session,
        sessionRequestId: session?.requestId,
        isFinalChunk: isFinalChunk,
        isFinalChunkType: typeof isFinalChunk
      });
      
      if (!session || !session.requestId) {
        console.error('No session provided to send audio chunk');
        return;
      }
      
      // Queue the chunk for upload instead of uploading directly
      const chunkId = queueChunk(audioBlob, session, isFinalChunk);
      console.log('✅ Audio chunk queued for upload:', chunkId);
      
    } catch (error) {
      console.error('Error processing audio chunk:', error);
      setError('Failed to process audio chunk: ' + error.message);
    }
  };

  // Process audio chunk (legacy - for interval chunks)
  const processAudioChunk = async (audioBlob) => {
    await processAudioChunkWithSession(audioBlob, currentSession);
  };

  return (
    <Box sx={{ position: "relative" }}>
      {/* Transcription Display */}
      <TranscriptionPortal isRecording={isRecording} isPaused={isPaused} />

      {/* Show error if any */}
      {error && (
        <Box
          sx={{
            color: "error.main",
            fontSize: "0.75rem",
            position: "fixed",
            bottom: 0,
          }}
        >
          {error}
        </Box>
      )}

      {/* Single button when not recording - matching file attachment style */}
      {!isRecording && (
        <Tooltip title="Start voice recording">
          <Button
            onClick={startRecording}
            disabled={!wsClient.isConnected()}
            variant="outlined"
            sx={{
              minWidth: 34,
              width: 34,
              height: 34,
              p: 0,
              border: "1px solid rgba(0, 0, 0, 0.08)",
              borderRadius: 1.5,
              backgroundColor: "#ffffff",
              color: "text.primary",
              transition: "all 0.2s ease",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              "&:hover": {
                backgroundColor: "rgba(0, 0, 0, 0.02)",
                borderColor: "rgba(0, 0, 0, 0.15)",
              },
              "&:disabled": {
                backgroundColor: "rgba(0, 0, 0, 0.02)",
                borderColor: "rgba(0, 0, 0, 0.08)",
              },
            }}
          >
            <Mic sx={{ fontSize: 18 }} />
          </Button>
        </Tooltip>
      )}

      {/* Two buttons when recording - styled to match */}
      {isRecording && (
        <Box sx={{ display: "flex", alignItems: "center", gap: 0.5 }}>
          <Tooltip title="Stop recording">
            <Button
              onClick={stopRecording}
              variant="outlined"
              sx={{
                minWidth: 34,
                width: 34,
                height: 34,
                p: 0,
                border: "1px solid rgba(239, 68, 68, 0.3)",
                borderRadius: 1.5,
                backgroundColor: "rgba(239, 68, 68, 0.1)",
                color: "#ef4444",
                transition: "all 0.2s ease",
                "&:hover": {
                  backgroundColor: "rgba(239, 68, 68, 0.2)",
                  borderColor: "rgba(239, 68, 68, 0.5)",
                },
              }}
            >
              <Stop sx={{ fontSize: 18 }} />
            </Button>
          </Tooltip>
          <Tooltip title={isPaused ? "Resume recording" : "Pause recording"}>
            <Button
              onClick={isPaused ? resumeRecording : pauseRecording}
              variant="outlined"
              sx={{
                minWidth: 34,
                width: 34,
                height: 34,
                p: 0,
                border: isPaused
                  ? "1px solid rgba(34, 197, 94, 0.3)"
                  : "1px solid rgba(245, 158, 11, 0.3)",
                borderRadius: 1.5,
                backgroundColor: isPaused
                  ? "rgba(34, 197, 94, 0.1)"
                  : "rgba(245, 158, 11, 0.1)",
                color: isPaused ? "#22c55e" : "#f59e0b",
                transition: "all 0.2s ease",
                "&:hover": {
                  backgroundColor: isPaused
                    ? "rgba(34, 197, 94, 0.2)"
                    : "rgba(245, 158, 11, 0.2)",
                  borderColor: isPaused
                    ? "rgba(34, 197, 94, 0.5)"
                    : "rgba(245, 158, 11, 0.5)",
                },
              }}
            >
              {isPaused ? (
                <PlayArrow sx={{ fontSize: 18 }} />
              ) : (
                <Pause sx={{ fontSize: 18 }} />
              )}
            </Button>
          </Tooltip>

          {/* Waveform Animation */}
          <WaveformAnimation
            isRecording={isRecording}
            isPaused={isPaused}
            audioStream={streamRef.current}
          />
        </Box>
      )}

      {/* Upload queue status - show when recording and chunks are uploading */}
      {isRecording && uploadQueue.length > 0 && (
        <Box
          sx={{
            fontSize: "0.7rem",
            color: "text.secondary",
            mt: 0.5,
            display: "flex",
            alignItems: "center",
            gap: 0.5,
          }}
        >
          <Box
            sx={{
              width: 6,
              height: 6,
              borderRadius: "50%",
              backgroundColor: uploadQueue.some((chunk) =>
                uploadingRef.current.has(chunk.id)
              )
                ? "warning.main"
                : "success.main",
              animation: uploadQueue.some((chunk) =>
                uploadingRef.current.has(chunk.id)
              )
                ? "pulse 1.5s infinite"
                : "none",
              "@keyframes pulse": {
                "0%": { opacity: 1 },
                "50%": { opacity: 0.5 },
                "100%": { opacity: 1 },
              },
            }}
          />
          {uploadQueue.length} chunk{uploadQueue.length !== 1 ? "s" : ""}
          {uploadingRef.current.size > 0 ? "uploading" : "queued"}
        </Box>
      )}

      {/* Show upload errors if any */}
      {Object.values(uploadStatus).some(
        (status) => status.status === "failed"
      ) && (
        <Box
          sx={{
            fontSize: "0.7rem",
            color: "error.main",
            mt: 0.5,
            display: "flex",
            alignItems: "center",
            gap: 0.5,
          }}
        >
          ⚠️ Some chunks failed to upload
        </Box>
      )}

      {/* Chunk upload status notification */}
      <Snackbar
        open={chunkStatus.open}
        autoHideDuration={3000}
        onClose={() => setChunkStatus({ ...chunkStatus, open: false })}
        anchorOrigin={{ vertical: "bottom", horizontal: "center" }}
      >
        <Alert
          onClose={() => setChunkStatus({ ...chunkStatus, open: false })}
          severity={chunkStatus.severity}
          icon={
            chunkStatus.severity === "success" ? <CheckCircle /> : <ErrorIcon />
          }
          sx={{
            width: "100%",
            alignItems: "center",
            "& .MuiAlert-icon": {
              fontSize: "1.2rem",
            },
            "& .MuiAlert-message": {
              fontSize: "0.875rem",
            },
          }}
        >
          {chunkStatus.message}
        </Alert>
      </Snackbar>
    </Box>
  );
}
