"use client";

import React, { useEffect, useMemo, useRef } from "react";
import "@blocknote/core/fonts/inter.css";
import { BlockNoteSchema, defaultInlineContentSpecs, filterSuggestionItems } from "@blocknote/core";
import { 
  SuggestionMenuController,
  getDefaultReactSlashMenuItems,
  useCreateBlockNote 
} from "@blocknote/react";
import { BlockNoteView } from "@blocknote/mantine";
import "@blocknote/mantine/style.css";

// Helper function to parse inline markdown styles
const parseInlineMarkdown = (text) => {
  if (!text) return [];
  
  const content = [];
  let currentPos = 0;
  
  // Combined regex for all inline patterns
  // Matches: **bold**, *italic*, _italic_, `code`
  const inlineRegex = /(\*\*([^*]+)\*\*)|(\*([^*]+)\*)|(_([^_]+)_)|(`([^`]+)`)/g;
  
  let match;
  while ((match = inlineRegex.exec(text)) !== null) {
    // Add any text before the match
    if (match.index > currentPos) {
      const plainText = text.substring(currentPos, match.index);
      if (plainText) {
        content.push({
          type: "text",
          text: plainText,
          styles: {}
        });
      }
    }
    
    // Process the matched pattern
    if (match[1]) {
      // Bold text (**)
      content.push({
        type: "text",
        text: match[2],
        styles: { bold: true }
      });
    } else if (match[3]) {
      // Italic text (*)
      content.push({
        type: "text",
        text: match[4],
        styles: { italic: true }
      });
    } else if (match[5]) {
      // Italic text (_)
      content.push({
        type: "text",
        text: match[6],
        styles: { italic: true }
      });
    } else if (match[7]) {
      // Code text (`)
      content.push({
        type: "text",
        text: match[8],
        styles: { code: true }
      });
    }
    
    currentPos = match.index + match[0].length;
  }
  
  // Add any remaining text after the last match
  if (currentPos < text.length) {
    const remainingText = text.substring(currentPos);
    if (remainingText) {
      content.push({
        type: "text",
        text: remainingText,
        styles: {}
      });
    }
  }

// If no inline formatting was found, return the original text
  if (content.length === 0 && text) {
    content.push({
      type: "text",
      text: text,
      styles: {}
    });
  }
  
  return content;
};
// Helper function to parse table
const parseTable = (lines, startIndex) => {
  const tableLines = [];
  let i = startIndex;
  
  // Find all table lines
  while (i < lines.length && lines[i].includes('|')) {
    tableLines.push(lines[i]);
    i++;
  }
  
  if (tableLines.length < 2) return null; // Need at least header and one data row
  
  // Parse header
  const headerCells = tableLines[0].split('|')
    .map(cell => cell.trim())
    .filter(cell => cell !== '');
  
  // Skip separator line (second line)
  const dataRows = tableLines.slice(2);
  
  // Parse data rows
  const content = {
    type: "table",
    content: {
      type: "tableContent",
      rows: [
        // Header row
        {
          cells: headerCells.map(cell => [
            {
              type: "text",
              text: cell,
              styles: { bold: true }
            }
          ])
        },
        // Data rows
        ...dataRows.map(row => ({
          cells: row.split('|')
            .map(cell => cell.trim())
            .filter(cell => cell !== '')
            .map(cell => parseInlineMarkdown(cell))
        }))
      ]
    }
  };
  
  return {
    block: content,
    linesConsumed: tableLines.length
  };
};

// Helper function to convert markdown to BlockNote blocks
const markdownToBlocks = (markdown) => {
  if (!markdown) return undefined;

  const blocks = [];
  const lines = markdown.split('\n');
  
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i];
    
    // Skip empty lines
    if (line.trim() === '') {
      continue;
    }
    
    // Check for table
    if (line.includes('|')) {
      const tableResult = parseTable(lines, i);
      if (tableResult) {
        blocks.push(tableResult.block);
        i += tableResult.linesConsumed - 1; // Skip the consumed lines
        continue;
      }
    }
    
    // Headers
    if (line.startsWith('### ')) {
      blocks.push({
        type: "heading",
        props: { level: 3 },
        content: parseInlineMarkdown(line.substring(4))
      });
    } else if (line.startsWith('## ')) {
      blocks.push({
        type: "heading",
        props: { level: 2 },
        content: parseInlineMarkdown(line.substring(3))
      });
    } else if (line.startsWith('# ')) {
      blocks.push({
        type: "heading",
        props: { level: 1 },
        content: parseInlineMarkdown(line.substring(2))
      });
    }
    // Bullet lists
    else if (line.startsWith('- ') || line.startsWith('* ')) {
      blocks.push({
        type: "bulletListItem",
        content: parseInlineMarkdown(line.substring(2))
      });
    }
    // Numbered lists
    else if (/^\d+\.\s/.test(line)) {
      const match = line.match(/^\d+\.\s(.+)/);
      if (match) {
        blocks.push({
          type: "numberedListItem",
          content: parseInlineMarkdown(match[1])
        });
      }
    }
    // Regular paragraphs
    else {
      blocks.push({
        type: "paragraph",
        content: parseInlineMarkdown(line)
      });
    }
  }
  
  return blocks.length > 0 ? blocks : undefined;
};

// Helper function to convert inline content to markdown
const inlineContentToMarkdown = (content) => {
  if (!content || !Array.isArray(content)) return '';
  
  return content.map(item => {
    if (!item.text) return '';
    
    let text = item.text;
    
    // Apply styles
    if (item.styles) {
      if (item.styles.bold) {
        text = `**${text}**`;
      }
      if (item.styles.italic) {
        text = `*${text}*`;
      }
      if (item.styles.code) {
        text = `\`${text}\``;
      }
      // Note: If text has both bold and italic, bold takes precedence
      // You could enhance this to handle nested styles if needed
    }
    
    return text;
  }).join('');
};

// Helper function to convert BlockNote blocks to markdown
const blocksToMarkdown = (blocks) => {
  if (!blocks || blocks.length === 0) return '';
  
  const lines = [];
  
  for (const block of blocks) {
    // Handle tables
    if (block.type === 'table') {
      const rows = block.content?.rows || [];
      if (rows.length > 0) {
        // Header row
        const headerRow = rows[0];
        if (headerRow?.cells) {
          const headerLine = '| ' + headerRow.cells.map(cell => 
            inlineContentToMarkdown(cell)
          ).join(' | ') + ' |';
          lines.push(headerLine);
          
          // Separator line
          const separatorLine = '|' + headerRow.cells.map(() => '-------').join('|') + '|';
          lines.push(separatorLine);
          
          // Data rows
          for (let i = 1; i < rows.length; i++) {
            const row = rows[i];
            if (row?.cells) {
              const dataLine = '| ' + row.cells.map(cell => 
                inlineContentToMarkdown(cell)
              ).join(' | ') + ' |';
              lines.push(dataLine);
            }
          }
        }
      }
      continue;
    }
    
    const content = inlineContentToMarkdown(block.content);

    // Convert based on block type
    switch (block.type) {
      case 'heading':
        const level = block.props?.level || 1;
        const prefix = '#'.repeat(level);
        if (content.trim()) {
          lines.push(`${prefix} ${content}`);
        }
        break;
        
      case 'bulletListItem':
        if (content.trim()) {
          lines.push(`- ${content}`);
        }
        break;
        
      case 'numberedListItem':
        if (content.trim()) {
          lines.push(`1. ${content}`);
        }
        break;
        
      case 'paragraph':
      default:
        if (content.trim()) {
          lines.push(content);
        }
        break;
    }
  }
  
  return lines.join('\n');
};

const BlockNoteEditor = ({ markdownContent, setMarkdownContent, placeholder, onSave, editable = true }) => {
  const editorRef = useRef(null);

  // Create custom schema without file upload blocks
  const schema = BlockNoteSchema.create({
    inlineContentSpecs: defaultInlineContentSpecs,
  });

  // Initialize editor with custom schema
  const editor = useCreateBlockNote({
    schema,
    initialContent: markdownToBlocks(markdownContent || placeholder),
    uploadFile: undefined, // Disable file uploads in the editor
  });

  // Update editor content when markdown changes from outside
  useEffect(() => {
    const blocks = markdownToBlocks(markdownContent);
    if (blocks && blocks.length > 0) {
      // Only update if content is different
      const currentMarkdown = blocksToMarkdown(editor.document);
      if (currentMarkdown !== markdownContent) {
        editor.replaceBlocks(editor.document, blocks);
      }
    }
  }, [markdownContent]);

  // Handle editor changes
  const handleChange = () => {
    if (!editable) return; // Don't update content when not editable

    const markdown = blocksToMarkdown(editor.document);
    if (markdown !== markdownContent) {
      setMarkdownContent(markdown);
    }
  };

  // Handle auto-save when editor loses focus
  const handleBlur = () => {
    if (!editable || !onSave) return; // Don't save when not editable

    const currentMarkdown = blocksToMarkdown(editor.document);
    if (currentMarkdown && currentMarkdown.trim()) {
      onSave(currentMarkdown);
    }
  };

  // Set up blur event listener
  useEffect(() => {
    const editorElement = editorRef.current;
    if (editorElement) {
      const editorContainer = editorElement.querySelector('.bn-editor');
      if (editorContainer) {
        editorContainer.addEventListener('blur', handleBlur, true);
        return () => {
          editorContainer.removeEventListener('blur', handleBlur, true);
        };
      }
    }
  }, [editor, onSave]);

  // Filter out file-related slash menu items
  const filteredSlashMenuItems = useMemo(() => {
    const defaultItems = getDefaultReactSlashMenuItems(editor);
    // Remove file upload related items
    return defaultItems.filter(item => 
      !['Image', 'Video', 'Audio', 'File'].includes(item.title)
    );
  }, [editor]);

  return (
    <>
      <style>{`
        .bn-editor {
          background-color: transparent !important;
        }
        .bn-container .bn-editor {
          background-color: transparent !important;
        }
        ${!editable ? `
        .bn-editor {
          opacity: 0.7;
          pointer-events: none;
        }
        .bn-container .bn-editor {
          opacity: 0.7;
          pointer-events: none;
        }
        ` : ''}
      `}</style>
      <div ref={editorRef}>
        <BlockNoteView
          editor={editor}
          onChange={handleChange}
          slashMenu={false}
          theme="light"
          editable={editable}
          data-theming-css-variables-demo
        >
          <SuggestionMenuController
            triggerCharacter="/"
            getItems={async (query) =>
              filterSuggestionItems(filteredSlashMenuItems, query)
            }
          />
        </BlockNoteView>
      </div>
    </>
  );
};

export default BlockNoteEditor;