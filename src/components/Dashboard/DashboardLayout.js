'use client';

import { useState, useEffect, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import {
  Box,
  IconButton,
  Drawer,
  useTheme,
  useMediaQuery
} from '@mui/material';
import {
  Menu as MenuIcon
} from '@mui/icons-material';
import ThreadList from './ThreadList';
import ChatInterface from './ChatInterface';
import { setupAuthCheck } from '@/lib/client-auth';

const DRAWER_WIDTH = 300;
const COLLAPSED_DRAWER_WIDTH = 60;

export default function DashboardLayout({ user, onLogout, selectedThreadId, initialThread, onThreadChange,onThreadChangew }) {
  const router = useRouter();
  // const [selectedThread] = useState(initialThread || null);
  const [mobileOpen, setMobileOpen] = useState(false);
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  const handleDrawerToggle = useCallback(() => {
    setMobileOpen(prev => !prev);
  }, [mobileOpen]);

  const handleSidebarToggle = useCallback(() => {
    setSidebarCollapsed(prev => !prev);
  }, [sidebarCollapsed]);

  const handleLogout = useCallback(() => {
    localStorage.removeItem('authToken');
    localStorage.removeItem('user');
    onLogout();
  }, [onLogout]);

  const handleThreadSelect = useCallback((thread) => {
    // In production, use window.location for more reliable navigation
    if (process.env.NODE_ENV === 'production') {
      window.location.href = `/chat/${thread.id}`;
    } else {
      router.push(`/chat/${thread.id}`);
    }
  }, [router]);

  const handleThreadCreated = useCallback((newThread) => {
    // In production, use window.location for more reliable navigation
    if (process.env.NODE_ENV === 'production') {
      window.location.href = `/chat/${newThread.id}`;
    } else {
      router.push(`/chat/${newThread.id}`);
    }
  }, [router]);

  // Set up auth check on mount
  useEffect(() => {
    const cleanup = setupAuthCheck(60000); // Check every minute
    
    // Cleanup on unmount
    return cleanup;
  }, []);

  const currentDrawerWidth = sidebarCollapsed ? COLLAPSED_DRAWER_WIDTH : DRAWER_WIDTH;

  const drawer = (
    <ThreadList
      onThreadSelect={handleThreadSelect}
      selectedThreadId={selectedThreadId}
      user={user}
      onLogout={handleLogout}
      collapsed={sidebarCollapsed}
      onToggleCollapse={isMobile ? handleDrawerToggle : handleSidebarToggle}
    />
  );

  console.log(onThreadChange,'this is in dashboard')

  return (
    <Box sx={{ display: 'flex', height: '100vh' }}>
      {/* Mobile Menu Button */}
      {isMobile && (!mobileOpen) &&(
        <IconButton
          color="primary"
          edge="start"
          onClick={handleDrawerToggle}
          sx={{ 
            position: 'fixed',
            top: 16,
            left: 16,
            zIndex: theme.zIndex.drawer + 2,
            bgcolor: 'background.paper',
            boxShadow: 2,
            '&:hover': {
              bgcolor: 'background.paper',
            }
          }}
        >
          <MenuIcon />
        </IconButton>
      )}

      {/* Sidebar */}
      <Box
        component="nav"
        sx={{ width: { md: currentDrawerWidth }, flexShrink: { md: 0 } }}
      >
        {/* Mobile drawer */}
        <Drawer
          variant="temporary"
          open={mobileOpen}
          onClose={handleDrawerToggle}
          ModalProps={{ keepMounted: true }}
          sx={{
            display: { xs: 'block', md: 'none' },
            '& .MuiDrawer-paper': {
              boxSizing: 'border-box',
              width: DRAWER_WIDTH
            },
          }}
        >
          {drawer}
        </Drawer>

        {/* Desktop drawer */}
        <Drawer
          variant="permanent"
          sx={{
            display: { xs: 'none', md: 'block' },
            '& .MuiDrawer-paper': {
              boxSizing: 'border-box',
              width: currentDrawerWidth,
              transition: theme.transitions.create('width', {
                easing: theme.transitions.easing.sharp,
                duration: theme.transitions.duration.enteringScreen,
              }),
            },
          }}
          open
        >
          {drawer}
        </Drawer>
      </Box>

      {/* Main Content */}
      <Box
        component="main"
        sx={{
          flexGrow: 1,
          width: { md: `calc(100% - ${currentDrawerWidth}px)` },
          height: '100vh',
          transition: theme.transitions.create('width', {
            easing: theme.transitions.easing.sharp,
            duration: theme.transitions.duration.enteringScreen,
          }),
        }}
      >
        <ChatInterface 
          thread={initialThread} 
          onThreadChange={onThreadChange} // setThread
          user={user} 
          onThreadCreated={handleThreadCreated}
          showHeader={!selectedThreadId}
        />
      </Box>
    </Box>
  );
}
