'use client';

import React, { useState, useEffect } from 'react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import {
  Box,
  Button,
  TextField,
  Typography,
  CircularProgress,
  Paper,
  Avatar,
  Chip,
  Tooltip
} from '@mui/material';
import BlockNoteEditor from '../DynamicBlockNoteEditor'
import {
  AutoAwesome,
  PersonOutline,
  Description
} from '@mui/icons-material';
import ControlledFileInput from '../FileAttachment/ControlledFileInput'; 
import TranscriptionButton from '../TranscriptionButton';
import { THREAD_TYPES, threadsAPI, messagesAPI, saveSOAPNotes as apiSaveSOAPNotes } from '@/lib/api';
import wsClient from '@/lib/websocket-client';


// Comprehensive SOAP Notes Converter
// function convertToSoapMarkdown(soapData) {
//   try {
//     // Handle null or undefined
//     if (!soapData) {
//       return '';
//     }

//     // If it's already a string, try to parse it first, otherwise use as-is
//     let data;
//     if (typeof soapData === 'string') {
//       try {
//         // Try to parse as JSON first
//         data = JSON.parse(soapData);
//       } catch (e) {
//         // If parsing fails, check if it's already markdown-like
//         if (soapData.includes('#') || soapData.includes('##')) {
//           return soapData; // Already formatted markdown
//         }
//         // If it's plain text, wrap it in a basic structure
//         return `# SOAP Notes\n\n${soapData}`;
//       }
//     } else if (typeof soapData === 'object') {
//       data = soapData;
//     } else {
//       return `# SOAP Notes\n\n${String(soapData)}`;
//     }

//     let markdown = '';

//     // Helper function to convert snake_case or camelCase to Title Case
//     const toTitleCase = (str) => {
//       return str
//         .replace(/([A-Z])/g, ' $1') // Add space before capital letters
//         .replace(/_/g, ' ') // Replace underscores with spaces
//         .replace(/\b\w/g, (char) => char.toUpperCase()) // Capitalize first letter of each word
//         .trim();
//     };

//     // Helper function to format arrays
//     const formatArray = (arr) => {
//       if (Array.isArray(arr)) {
//         return arr.map(item => `- ${item}`).join('\n') + '\n';
//       }
//       return arr;
//     };

//     // Helper function to add section
//     const addSection = (title, content, level = 2) => {
//       if (content && String(content).trim()) {
//         const headerPrefix = '#'.repeat(level);
//         markdown += `${headerPrefix} ${title}\n\n`;
        
//         if (Array.isArray(content)) {
//           markdown += formatArray(content);
//         } else {
//           markdown += `${content}\n`;
//         }
//         markdown += '\n';
//       }
//     };

//     // Check if this looks like a SOAP structure
//     const soapSections = ['subjective', 'objective', 'assessment', 'plan'];
//     const hasSoapStructure = soapSections.some(section => 
//       data.hasOwnProperty(section) || data.hasOwnProperty(section.toUpperCase())
//     );

//     if (hasSoapStructure) {
//       // Process SOAP sections in order
//       const sectionOrder = ['subjective', 'objective', 'assessment', 'plan', 'metadata'];
      
//       sectionOrder.forEach(sectionKey => {
//         const sectionData = data[sectionKey] || data[sectionKey.toUpperCase()];
        
//         if (sectionData && typeof sectionData === 'object' && sectionData !== null) {
//           // Add main section header
//           markdown += `# ${toTitleCase(sectionKey)}\n\n`;
          
//           // Process subsections
//           Object.entries(sectionData).forEach(([subKey, subValue]) => {
//             const subTitle = toTitleCase(subKey);
//             addSection(subTitle, subValue);
//           });
//         }
//       });
//     } else {
//       // Handle non-SOAP structured data
//       if (typeof data === 'object') {
//         Object.entries(data).forEach(([key, value]) => {
//           if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
//             // Nested object - treat as main section
//             markdown += `# ${toTitleCase(key)}\n\n`;
//             Object.entries(value).forEach(([subKey, subValue]) => {
//               addSection(toTitleCase(subKey), subValue);
//             });
//           } else {
//             // Simple key-value pair
//             addSection(toTitleCase(key), value, 2);
//           }
//         });
//       } else {
//         // Fallback for unexpected data types
//         markdown += `# Notes\n\n${String(data)}\n\n`;
//       }
//     }

//     return markdown.trim();
//   } catch (error) {
//     console.error('Error converting to SOAP markdown:', error);
//     // Return original data as fallback
//     return typeof soapData === 'string' ? soapData : JSON.stringify(soapData, null, 2);
//   }
// }

const patientCasePlaceholder = `# Patient Case

## Demographics
Age, gender, relevant background

## Chief Complaint

## History of Present Illness

## Past Medical History

## Current Medications

## Physical Examination

## Questions for AI Analysis`;

export default function PatientCaseInterface({ thread, onThreadCreated, showHeader, generateTitleForThread }) {
  const [markdownContent, setMarkdownContent] = useState('');
  const [aiResponse, setAiResponse] = useState('');
  const [isGenerating, setIsGenerating] = useState(false);
  const [isGeneratingSOAP, setIsGeneratingSOAP] = useState(false);
  const [attachedFiles, setAttachedFiles] = useState([]);
  const [completedFiles, setCompletedFiles] = useState([]);
  const [fileErrors, setFileErrors] = useState([]);
  const [isWaitingForTranscriptions, setIsWaitingForTranscriptions] = useState(false);
  const [transcriptionsPending, setTranscriptionsPending] = useState(false);
  const [isSaving, setIsSaving] = useState(false);

  // Upload file to Azure Blob Storage
  const uploadFileToAzure = async (file, threadId) => {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('threadId', threadId);
    formData.append('userId', localStorage.getItem('userPhone') || 'unknown');

    const response = await fetch('/api/files/upload-azure', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('access_token')}`
      },
      body: formData
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error || 'Upload failed');
    }

    return await response.json();
  };

  // Helper function to get simplified file type
  const getSimplifiedFileType = (mimeType) => {
    if (!mimeType) return 'document';

    if (mimeType.startsWith('image/')) return 'image';
    if (mimeType === 'application/pdf') return 'pdf';
    if (mimeType.includes('word') || mimeType.includes('document')) return 'document';
    if (mimeType.includes('spreadsheet') || mimeType.includes('excel')) return 'spreadsheet';
    if (mimeType.includes('text/')) return 'text';

    return 'document';
  };

  // Save SOAP notes when content changes
  const saveSOAPNotes = async (content) => {
    if (!thread?.id || isSaving) return;

    try {
      setIsSaving(true);

      // Upload files to Azure Blob Storage if they haven't been uploaded yet
      const formattedAttachments = [];
      for (const file of completedFiles) {
        let fileUrl = file.uploadedFile?.url || file.url;

        // If the file URL is local (starts with /api/files), upload to Azure
        if (fileUrl && fileUrl.startsWith('/api/files/')) {
          console.log('Uploading file to Azure Blob Storage:', file.name);
          try {
            const uploadResult = await uploadFileToAzure(file.originalFile, thread.id);
            fileUrl = uploadResult.url;
            console.log('File uploaded successfully:', uploadResult);
          } catch (uploadError) {
            console.error('Failed to upload file to Azure:', uploadError);
            // Continue with local URL as fallback
          }
        }

        formattedAttachments.push({
          url: fileUrl,
          name: file.name || file.originalName,
          size: file.size,
          messageType: getSimplifiedFileType(file.type)
        });
      }

      await apiSaveSOAPNotes(thread.id, content, thread.type, formattedAttachments);
    } catch (error) {
      console.error('Error saving SOAP notes:', error);
    } finally {
      setIsSaving(false);
    }
  };

  // Local file handling functions for PatientCaseInterface
  const handleFilesChange = (filesOrUpdater) => {
    console.log('🔧 PatientCaseInterface: handleFilesChange called', {
      isFunction: typeof filesOrUpdater === 'function'
    });

    if (typeof filesOrUpdater === 'function') {
      // Functional update
      setAttachedFiles(prevFiles => {
        const newFiles = filesOrUpdater(prevFiles);
        // Update completed files - keep the full file object with originalFile property
        const completed = newFiles?.filter(f => f.status === 'completed') || [];
        setCompletedFiles(completed);
        return newFiles;
      });
    } else {
      // Direct update
      const files = filesOrUpdater;
      setAttachedFiles(files || []);

      // Update completed files - keep the full file object with originalFile property
      const completed = files?.filter(f => f.status === 'completed') || [];
      setCompletedFiles(completed);
    }
  };

  // Handle file removal
  const handleFileRemove = (fileId) => {
    console.log('🔧 PatientCaseInterface: handleFileRemove called', fileId);
    const updatedFiles = attachedFiles.filter(f => f.id !== fileId);
    handleFilesChange(updatedFiles);
  };

  // Handle file errors
  const handleFileErrors = (errors) => {
    console.log('🔧 PatientCaseInterface: handleFileErrors called', errors);
    setFileErrors(errors);
  };
 


  // Set up WebSocket event listeners
  useEffect(() => {
    const handleMessageResponse = (event) => {
      console.log('PatientCaseInterface received message_response event:', event.detail);
      const { threadId, response } = event.detail;
      
      // Only handle if this is for our current thread
      if (!thread || threadId !== thread.id) {
        console.log('Ignoring response for different thread:', { currentThread: thread?.id, responseThread: threadId });
        return;
      }
      
      console.log('Processing AI response:', response);
      
      // Update AI response
      setAiResponse(response.content);
      setIsGenerating(false);
    };

    const handleMessageError = (event) => {
      console.error('Message error:', event.detail);
      setIsGenerating(false);
      // TODO: Show error state
    };

    const handleSoapNotesGenerated = (event) => {
      const { threadId, soapNotes } = event.detail;
      
      // Only handle if this is for our current thread
      if (!thread || threadId !== thread.id) return;
      
      console.log('SOAP notes generated:', soapNotes);
      
      // Convert SOAP notes to markdown format and update content
      if (soapNotes) {
        // const markdownSoap = convertToSoapMarkdown(soapNotes);
        console.log('Converted SOAP to markdown:', soapNotes);
        setMarkdownContent(soapNotes);
      }
      
      // Stop the generating loader
      setIsGeneratingSOAP(false);
    };

    const handleTranscriptionComplete = (event) => {
      const { threadId, totalChunks, totalDuration } = event.detail;
      
      // Only handle if this is for our current thread
      if (!thread || threadId !== thread.id) return;
      
      console.log('Transcription complete:', { threadId, totalChunks, totalDuration });
      
      // Mark transcriptions as complete
      setTranscriptionsPending(false);
      setIsWaitingForTranscriptions(false);
    };

    // Track when recording starts (transcriptions become pending)
    const handleRecordingStarted = () => {
      setTranscriptionsPending(true);
    };

    window.addEventListener('message_response', handleMessageResponse);
    window.addEventListener('messageError', handleMessageError);
    window.addEventListener('soap_notes_generated', handleSoapNotesGenerated);
    window.addEventListener('transcription_complete', handleTranscriptionComplete);
    window.addEventListener('recording_started', handleRecordingStarted);

    return () => {
      window.removeEventListener('message_response', handleMessageResponse);
      window.removeEventListener('messageError', handleMessageError);
      window.removeEventListener('soap_notes_generated', handleSoapNotesGenerated);
      window.removeEventListener('transcription_complete', handleTranscriptionComplete);
      window.removeEventListener('recording_started', handleRecordingStarted);
    };
  }, [thread]);

  // Auto-load latest SOAP notes when thread changes
  useEffect(() => {
    const loadLatestSOAPNotes = async () => {
      // Only load for existing threads with an ID
      if (!thread?.id) return;
      
      // Don't overwrite if user has already started editing
      if (markdownContent.trim()) return;
      
      try {
        console.log('Loading latest SOAP notes for thread:', thread.id);
        
        // Fetch messages for the thread
        const messages = await messagesAPI.getMessages(thread.id);
        
        // Find the latest SOAP note message
        const soapNoteMessages = messages
          .filter(msg => msg.sender == 'user')
          .sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
        
        if (soapNoteMessages.length > 0) {
          const latestSoapNote = soapNoteMessages[0];
          console.log('Found latest SOAP note:', latestSoapNote);
          
          // Convert and set the SOAP note
          // const markdownSoap = convertToSoapMarkdown(latestSoapNote.content);
          setMarkdownContent(latestSoapNote.content);
          console.log('Loaded SOAP notes into editor', latestSoapNote.content);
        } else {
          console.log('No SOAP notes found for this thread');
        }
      } catch (error) {
        console.error('Failed to load SOAP notes:', error);
      }
    };
    
    loadLatestSOAPNotes();
  }, [thread?.id]); // Only depend on thread.id to avoid re-running when thread object updates

  // Reset title trigger when switching threads
 

  // When markdown content becomes available (e.g., after transcription), trigger title generation
  useEffect(() => {
    if (!thread?.id) return;
    generateTitleForThread(thread.id,'patient-case');
  }, [markdownContent]);

  // Handle manual SOAP note generation
  const handleGenerateSOAPNotes = async () => {
    if (!thread) {
      console.error('No thread available for SOAP note generation');
      return;
    }

    console.log('Manual SOAP note generation requested for thread:', thread.id);
    
    // Check if transcriptions are still pending
    if (transcriptionsPending) {
      console.log('Transcriptions are still pending, cannot generate SOAP notes yet');
      // The button should be disabled in this state, but just in case
      return;
    }
    
    // Generate SOAP notes immediately
    generateSOAPNotes();
  };

  const generateSOAPNotes = async () => {
    try {
      setIsGeneratingSOAP(true);
      setIsWaitingForTranscriptions(false);
      
      // Import the API function dynamically to avoid circular dependencies
      const { generateSoapNotes } = await import('@/lib/api');
      
      // Check if current content looks like SOAP notes (contains key sections)
      let currentEditedNotes = null;
      if (markdownContent && 
          (markdownContent.includes('Subjective') || 
           markdownContent.includes('Objective') || 
           markdownContent.includes('Assessment') || 
           markdownContent.includes('Plan') ||
           markdownContent.includes('# S') || // Markdown headers
           markdownContent.includes('# O') ||
           markdownContent.includes('# A') ||
           markdownContent.includes('# P'))) {
        currentEditedNotes = markdownContent;
      }
      
      console.log('Calling generateSoapNotes API for dialogue:', thread.id, {
        hasEditedNotes: !!currentEditedNotes,
        editedNotesLength: currentEditedNotes?.length || 0
      });
      
      await generateSoapNotes(thread.id, currentEditedNotes);
      
      // The SOAP notes will be received via WebSocket event
      console.log('SOAP note generation request sent');
    } catch (error) {
      console.error('Failed to generate SOAP notes:', error);
      setIsGeneratingSOAP(false);
      setIsWaitingForTranscriptions(false);
      // TODO: Show error notification
    }
  };

  // Handle generating AI analysis
  const handleGenerateAnalysis = async () => {
    console.log('Generate AI Analysis clicked', {
      hasContent: !!markdownContent.trim(),
      contentLength: markdownContent.length,
      isGenerating,
      isGeneratingSOAP,
      thread,
      hasAttachments: completedFiles.length > 0
    });

    if (!markdownContent.trim() || isGenerating) return;

    // If we have attachments, use the save endpoint instead of generating analysis
    if (completedFiles.length > 0) {
      console.log('Attachments detected, saving to /save endpoint instead of generating analysis');
      await saveSOAPNotes(markdownContent);
      return;
    }

    setIsGenerating(true);
    setAiResponse(''); // Clear previous response

    // Extract original File objects from completed files
    const messageAttachments = completedFiles.map(fileMetadata => {
      if (fileMetadata.originalFile && fileMetadata.originalFile instanceof File) {
        return fileMetadata.originalFile;
      }
      console.error('Missing original File object for attachment:', fileMetadata.name);
      return null;
    }).filter(file => file !== null);

    try {
      // If no thread exists, create one first
      let currentThread = thread;
      if (!currentThread) {
        console.log('No thread exists, creating new thread...');
        const threadTitle = markdownContent.length > 50
          ? markdownContent.substring(0, 50) + '...'
          : markdownContent;

        // Create thread and navigate
        currentThread = await threadsAPI.createThread(
          THREAD_TYPES.PATIENT_CASE,
          threadTitle,
          markdownContent
        );
        console.log('Thread created:', currentThread);

        // Dispatch event and navigate
        window.dispatchEvent(new CustomEvent('threadCreated', {
          detail: { thread: currentThread }
        }));

        onThreadCreated(currentThread);
      }

      // Check WebSocket connection
      console.log('WebSocket connection status:', {
        isConnected: wsClient.isConnected(),
        socket: wsClient.socket,
        authenticated: wsClient.authenticated
      });

      console.log('Sending message via WebSocket...', {
        threadId: currentThread.id,
        contentPreview: markdownContent.substring(0, 100),
        threadType: THREAD_TYPES.PATIENT_CASE,
        attachmentCount: messageAttachments.length
      });

      // Send message via WebSocket
      await wsClient.sendMessage(
        currentThread.id,
        markdownContent,
        THREAD_TYPES.PATIENT_CASE,
        messageAttachments
      );

      console.log('Message sent successfully');

      // Clear attachments after sending
      setAttachedFiles([]);
      setCompletedFiles([]);

    } catch (error) {
      console.error('Failed to generate analysis:', error);
      setIsGenerating(false);
      // TODO: Show error state
    }
  };

  return (
    <Box sx={{ height: "100%", display: "flex", flexDirection: "column" }}>
      {/* Header - only show when thread is selected and showHeader is true */}
      {thread && showHeader && (
        <Paper
          elevation={1}
          sx={{
            p: 2,
            borderRadius: 0,
            borderBottom: 1,
            borderColor: "divider",
          }}
        >
          <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
            <Avatar sx={{ bgcolor: "error.light", color: "error.main" }}>
              <PersonOutline />
            </Avatar>
            <Box sx={{ flex: 1 }}>
              <Typography variant="h6" noWrap>
                {thread.title}
              </Typography>
              <Chip
                label="Patient Case"
                size="small"
                color="error"
                variant="outlined"
              />
            </Box>
          </Box>
        </Paper>
      )}

      {/* Main Content - Split Screen */}
      <Box sx={{ flex: 1, display: "flex", overflow: "hidden" }}>
        {/* Left Panel  */}
        <Box sx={{ 
          flex: 1, 
          display: "flex",
          flexDirection: "column",
          borderRight: 1,
          borderColor: "divider",
          position: "relative",
          justifyContent:"space-between",
        }}>
          {/* SOAP Notes Generation Loader */}
          {isGeneratingSOAP && (
            <Box
              sx={{
                position: "absolute",
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
                bgcolor: "rgba(255, 255, 255, 0.95)",
                display: "flex",
                flexDirection: "column",
                alignItems: "center",
                justifyContent: "center",
                zIndex: 10,
                gap: 2,
              }}
            >
              <CircularProgress size={48} />
              <Typography variant="h6" color="text.secondary">
                {isWaitingForTranscriptions
                  ? "Waiting for transcriptions to complete..."
                  : "Generating SOAP notes..."}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {isWaitingForTranscriptions
                  ? "SOAP notes will be generated once all audio is transcribed"
                  : "This may take a few moments"}
              </Typography>
            </Box>
          )}
          
          {/* BlockNote Editor - Takes 70% of left panel */}
          <Box sx={{ display: "flex", flexDirection: "column", overflow:"auto", height:"100%", bgcolor: "transparent" }}>
            {/* Read-only indicator when attachments are present */}
             <Box
              sx={{
                "& .bn-editor": {
                  opacity: "1 !important",
                },
                "& .bn-container .bn-editor": {
                  opacity: "1 !important",
                },
              }}
            >
              <BlockNoteEditor
                key={`soap-editor-${thread?.id || 'new'}`}
                markdownContent={markdownContent}
                placeholder={patientCasePlaceholder}
                setMarkdownContent={setMarkdownContent}
                onSave={saveSOAPNotes}
                editable={true} // Always editable with black text
              />
            </Box>
            
            {/* Attached Files Display */}
            {attachedFiles.length > 0 && (
              <Box sx={{ p: 2, borderTop: 1, borderColor: 'divider' }}>
                <Typography variant="subtitle2" color="text.secondary" sx={{ mb: 1 }}>
                  Attached Files:
                </Typography>
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                  {attachedFiles.map((file, index) => (
                    <Chip
                      key={index}
                      label={file.name}
                      size="small"
                      onDelete={() => handleFileRemove(file.id)}
                      icon={<Description fontSize="small" />}
                      sx={{
                        '& .MuiChip-label': { maxWidth: '150px' }
                      }}
                    />
                  ))}
                </Box>
              </Box>
            )}
          </Box>
          
          {/* Transcription Root - Takes 30% of left panel */}
          <Box 
            id="transcription-root"
          >
          </Box>
        </Box>

        {/* Right Panel - AI Response */}
        <Box
          sx={{
            flex: 1,
            overflow: "auto",
            p: 3,
            bgcolor: "background.default",
          }}
        >
          {isGenerating ? (
            <Box
              sx={{
                display: "flex",
                flexDirection: "column",
                alignItems: "center",
                justifyContent: "center",
                height: "100%",
                gap: 2,
              }}
            >
              <CircularProgress />
              <Typography color="text.secondary">
                Generating AI analysis...
              </Typography>
            </Box>
          ) : aiResponse ? (
            <Box
              sx={{
                "& .bn-editor": {
                  opacity: "1 !important",
                },
                "& .bn-container .bn-editor": {
                  opacity: "1 !important",
                },
                position: "relative",
              }}
            >
              <BlockNoteEditor
                key={`ai-response-editor-${thread?.id || 'new'}`}
                markdownContent={aiResponse}
                setMarkdownContent={() => {}} // Block changes from propagating outside
                placeholder="AI analysis will appear here"
                editable={true} // Keep editable for consistent behavior and black text
              />
              {/* Overlay to prevent any editing interaction */}
              <Box
                sx={{
                  position: "absolute",
                  top: 0,
                  left: 0,
                  right: 0,
                  bottom: 0,
                  backgroundColor: "transparent",
                  pointerEvents: "all",
                  cursor: "default",
                  zIndex: 1,
                }}
                onClick={(e) => e.preventDefault()}
                onMouseDown={(e) => e.preventDefault()}
                onKeyDown={(e) => e.preventDefault()}
              />
            </Box>
          ) : (
            <Box
              sx={{
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                height: "100%",
                color: "text.secondary",
              }}
            >
              <Typography variant="body1" sx={{ textAlign: "center" }}>
                AI analysis will appear here after you click &quot;Generate AI
                Analysis&quot;
              </Typography>
            </Box>
          )}
        </Box>
      </Box>

      {/* Control Bar - Fixed at bottom */}
      <Box
        sx={{
          p: 2,
          borderTop: 1,
          borderColor: "divider",
          bgcolor: "background.paper",
        }}
      >
        <Box
          sx={{
            display: "flex",
            alignItems: "center",
            gap: 2,
            justifyContent: "space-between",
          }}
        >
          <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
            <ControlledFileInput
              onFilesChange={handleFilesChange}
              disabled={isGenerating}
              currentFiles={attachedFiles}
              onError={handleFileErrors}
            />
            <TranscriptionButton
              threadId={thread?.id}
              onTranscriptionResult={(result) => {
                if (result.transcription) {
                  setMarkdownContent(
                    (prev) => prev + (prev ? "\n\n" : "") + result.transcription
                  );
                }
              }}
              onRecordingStop={() => {
                // Removed automatic SOAP generation on recording stop
              }}
            />
            {thread && (
              <Tooltip
                title={
                  isGeneratingSOAP
                    ? "SOAP notes are being generated"
                    : transcriptionsPending
                    ? "Waiting for transcriptions to complete"
                    : "Generate SOAP notes from transcriptions"
                }
              >
                <span>
                  <Button
                    variant="outlined"
                    onClick={()=>{
                      if(completedFiles.length === 0) {
handleGenerateSOAPNotes()
                      }
                      else {
   saveSOAPNotes(markdownContent)
                      }
                    
                    }
                    }
                    disabled={isGeneratingSOAP || transcriptionsPending}
                    startIcon={
                      isGeneratingSOAP ? (
                        <CircularProgress size={20} />
                      ) : (
                        <Description />
                      )
                    }
                    sx={{
                      textTransform: "none",
                      px: 2,
                      py: 1,
                    }}
                  >
                    {isGeneratingSOAP
                      ? "Generating SOAP..."
                      : transcriptionsPending
                      ? "Transcribing..."
                      : "Generate SOAP Notes"}
                  </Button>
                </span>
              </Tooltip>
            )}
            {completedFiles.length > 0 && (
              <Typography variant="caption" color="text.secondary">
                {completedFiles.length} file(s) attached
              </Typography>
            )}
          </Box>

          {/* Only show Generate AI Analysis button when no attachments are present */}
          {completedFiles.length === 0 && (
            <Tooltip
              title={
                isGeneratingSOAP
                  ? "Please wait for SOAP notes to be generated"
                  : !markdownContent.trim()
                  ? "Enter patient information to generate analysis"
                  : ""
              }
            >
              <span>
                <Button
                  variant="contained"
                  onClick={handleGenerateAnalysis}
                  disabled={
                    !markdownContent.trim() || isGenerating || isGeneratingSOAP
                  }
                  startIcon={
                    isGenerating ? (
                      <CircularProgress size={20} />
                    ) : (
                      <AutoAwesome />
                    )
                  }
                  sx={{
                    textTransform: "none",
                    px: 3,
                    py: 1,
                  }}
                >
                  {isGenerating ? "Generating..." : "Generate AI Analysis"}
                </Button>
              </span>
            </Tooltip>
          )}
        </Box>

        {/* File Errors */}
        {fileErrors.length > 0 && (
          <Box sx={{ mt: 1 }}>
            {fileErrors.map((error, index) => (
              <Typography
                key={index}
                variant="caption"
                color="error"
                sx={{ display: "block" }}
              >
                {error.message}
              </Typography>
            ))}
          </Box>
        )}
      </Box>
    </Box>
  );
}