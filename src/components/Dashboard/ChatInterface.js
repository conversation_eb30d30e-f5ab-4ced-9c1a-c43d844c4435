'use client';

import React, { useState, useEffect, useRef } from 'react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import rehypeRaw from 'rehype-raw';
import { v4 as uuidv4 } from 'uuid';
import {
  Box,
  Paper,
  Typography,
  TextField,
  IconButton,
  CircularProgress,
  Avatar,
  Chip,
  Alert,
  useTheme,
  useMediaQuery,
  keyframes
} from '@mui/material';
import {
  Send,
  PersonOutline,
  Search,
  LightbulbOutlined,
  Close,
  ErrorOutline,
  ContentCopy,
  ThumbUpAltOutlined,
  ThumbDownAltOutlined,
  Start
} from '@mui/icons-material';
import { messagesAPI, threadsAPI, THREAD_TYPES, generateTitleAndSummary, feedbackAPI } from '@/lib/api';
import ConversationTypeToggle from './ConversationTypeToggle';
import ControlledFileInput from '../FileAttachment/ControlledFileInput';
import AttachmentDisplay from '../FileAttachment/AttachmentDisplay';
import { getFileIcon, getFileCategory, formatFileSize } from '@/lib/fileValidation';
import wsClient from '@/lib/websocket-client';
import PatientCaseInterface from './PatientCaseInterface';

const threadTypeConfig = {
  [THREAD_TYPES.PATIENT_CASE]: {
    label: 'Patient Case',
    icon: PersonOutline,
    color: 'error',
    bgcolor: 'error.light',
    thinkingMessages: ['Analyzing symptoms', 'Reviewing case details', 'Evaluating differential diagnosis', 'Considering treatment options', 'Assessing patient data']
  },
  [THREAD_TYPES.RESEARCH]: {
    label: 'Research',
    icon: Search,
    color: 'info',
    bgcolor: 'info.light',
    thinkingMessages: ['Searching literature', 'Reviewing evidence', 'Analyzing studies', 'Investigating findings', 'Processing research data']
  },
  [THREAD_TYPES.QUICK_FACTS]: {
    label: 'Chat',
    icon: LightbulbOutlined,
    color: 'success',
    bgcolor: 'success.light',
    thinkingMessages: ['Thinking', 'Processing your message', 'Analyzing your question', 'Preparing response', 'Working on it']
  }
};

// Typing indicator animation
const bounce = keyframes`
  0%, 60%, 100% {
    transform: translateY(0);
  }
  30% {
    transform: translateY(-10px);
  }
`;

// Slide animation for message rotation
const slideOutUp = keyframes`
  from {
    opacity: 1;
    transform: translateY(0);
  }
  to {
    opacity: 0;
    transform: translateY(-20px);
  }
`;

const slideInUp = keyframes`
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
`;

// Function to get a rotating thinking message based on thread type
function getThinkingMessage(threadType) {
  // Convert underscore format to hyphen format to match threadTypeConfig keys
  const normalizedThreadType = threadType?.replace(/_/g, '-');
  
  const messages = threadTypeConfig[normalizedThreadType]?.thinkingMessages || ['Processing your request'];
  const now = Date.now();
  
  // Different rotation speeds for different thread types
  let rotationInterval;
  switch (normalizedThreadType) {
    case 'research':
      rotationInterval = 4000; // 4 seconds for research
      break;
    case 'patient-case':
    case 'chat':
    default:
      rotationInterval = 2000; // 2 seconds for others
      break;
  }
  
  const index = Math.floor(now / rotationInterval) % messages.length;
  return messages[index];
}

// Typing indicator component
function TypingIndicator({ threadType }) {
  const [currentMessage, setCurrentMessage] = React.useState('');
  const [isAnimating, setIsAnimating] = React.useState(false);
  const [elapsedTime, setElapsedTime] = React.useState(0);
  
  React.useEffect(() => {
    // Update message immediately
    setCurrentMessage(getThinkingMessage(threadType));
    
    // Get the rotation interval for this thread type
    const normalizedThreadType = threadType?.replace(/_/g, '-');
    let rotationInterval;
    switch (normalizedThreadType) {
      case 'research':
        rotationInterval = 4000; // 4 seconds for research
        break;
      case 'patient-case':
      case 'chat':
      default:
        rotationInterval = 2000; // 2 seconds for others
        break;
    }
    
    // Update at the appropriate interval with animation
    const interval = setInterval(() => {
      setIsAnimating(true);
      
      // After the slide-out animation, update the message
      setTimeout(() => {
        setCurrentMessage(getThinkingMessage(threadType));
        setIsAnimating(false);
      }, 200); // Match the animation duration
    }, rotationInterval);
    
    return () => clearInterval(interval);
  }, [threadType]);
  
  // Timer effect
  React.useEffect(() => {
    const timerInterval = setInterval(() => {
      setElapsedTime(prev => prev + 1);
    }, 1000);
    
    return () => clearInterval(timerInterval);
  }, []);
  
  return (
    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
      <Box sx={{ display: 'flex', gap: 0.2, alignItems: 'center', mt: 0.5 }}>
        {[0, 1, 2].map((index) => (
          <Box
            key={index}
            sx={{
              width: 3,
              height: 3,
              borderRadius: '50%',
              backgroundColor: 'text.secondary',
              animation: `${bounce} 1.4s infinite ease-in-out`,
              animationDelay: `${index * 0.16}s`
            }}
          />
        ))}
      </Box>
      <Box sx={{ 
        position: 'relative', 
        overflow: 'hidden',
        minHeight: '20px',
        width: '200px', // Fixed width to prevent timer from moving
        display: 'flex',
        alignItems: 'center'
      }}>
        <Typography 
          variant="body2" 
          color="text.secondary"
          sx={{
            animation: isAnimating 
              ? `${slideOutUp} 0.2s ease-in-out forwards` 
              : `${slideInUp} 0.2s ease-in-out`,
            transition: 'all 0.2s ease-in-out',
            whiteSpace: 'nowrap'
          }}
        >
          {currentMessage}
        </Typography>
      </Box>
      
      {/* Timer display */}
      <Typography 
        variant="caption" 
        sx={{ 
          color: 'text.secondary',
          fontSize: '0.75rem',
          ml: 'auto', // Push to the right
          minWidth: '30px', // Ensure consistent width
          textAlign: 'right'
        }}
      >
        {elapsedTime}s
      </Typography>
    </Box>
  );
}

// Consistent send button styles
const sendButtonStyles = {
  color: '#111',
  p: 1.5,
  borderRadius: 1.5,
  bgcolor: 'rgba(0, 0, 0, 0.05)',
  '&:hover': {
    bgcolor: 'rgba(0, 0, 0, 0.1)',
    color: '#111'
  },
  '&:disabled': {
    color: 'action.disabled',
    bgcolor: 'rgba(0, 0, 0, 0.02)'
  },
  flexShrink: 0
};

// File Preview Square Component
function FilePreviewSquare({ fileData, onRemove }) {
  const { name, size, type, status, progress, previewUrl } = fileData;
  const fileIcon = getFileIcon(type);
  const category = getFileCategory(type);
  const isImage = category === 'image';
  
  return (
    <Box
      sx={{
        position: 'relative',
        width: 60,
        height: 60,
        borderRadius: 2,
        overflow: 'hidden',
        backgroundColor: status === 'failed' ? 'error.100' : 'grey.100',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        border: 'none',
        cursor: 'pointer'
      }}
      title={`${name} (${formatFileSize(size)})`}
    >
      {/* File Preview/Icon */}
      {isImage && previewUrl ? (
        <Box
          component="img"
          src={previewUrl}
          alt={name}
          sx={{
            width: '100%',
            height: '100%',
            objectFit: 'cover'
          }}
        />
      ) : (
        <Typography sx={{ fontSize: '1.5rem', color: 'text.secondary' }}>
          {fileIcon}
        </Typography>
      )}

      {/* Upload Progress Overlay */}
      {status === 'uploading' && (
        <Box
          sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            width: '100%',
            height: '100%',
            backgroundColor: 'rgba(255, 255, 255, 0.8)',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            flexDirection: 'column'
          }}
        >
          <CircularProgress size={20} />
          <Typography variant="caption" sx={{ mt: 0.5, fontSize: '10px' }}>
            {progress}%
          </Typography>
        </Box>
      )}

      {/* Error Overlay */}
      {status === 'failed' && (
        <Box
          sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            width: '100%',
            height: '100%',
            backgroundColor: 'rgba(244, 67, 54, 0.1)',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center'
          }}
        >
          <ErrorOutline sx={{ color: 'error.main', fontSize: '1.2rem' }} />
        </Box>
      )}

      {/* Remove Button */}
      <IconButton
        size="small"
        onClick={onRemove}
        sx={{
          zIndex: 1000,
          position: 'absolute',
          right: 2,
          top: 2,
          width: 20,
          height: 20,
          backgroundColor: 'rgba(0, 0, 0, 0.6)',
          color: 'white',
          border: 'none',
          '&:hover': {
            backgroundColor: 'rgba(244, 67, 54, 0.8)'
          },
          '& .MuiSvgIcon-root': {
            fontSize: 14
          }
        }}
      >
        <Close />
      </IconButton>
    </Box>
  );
}

export default function ChatInterface({ thread, onThreadCreated, showHeader = true, onThreadChange ,user}) {
  const [messages, setMessages] = useState([]);
  const [newMessage, setNewMessage] = useState('');
  const [selectedType, setSelectedType] = useState(THREAD_TYPES.RESEARCH);
  const [loading, setLoading] = useState(false);
  const [sendingMessage, setSendingMessage] = useState(false);
  const [attachedFiles, setAttachedFiles] = useState([]);
  const [completedFiles, setCompletedFiles] = useState([]);
  const [fileErrors, setFileErrors] = useState([]);
  const [userMessageCount, setUserMessageCount] = useState(0);
  const [titleGenerated, setTitleGenerated] = useState(false);
  const messagesEndRef = useRef(null);
  const countRef = useRef(0)

  console.log('this is in chatinterface',onThreadChange)
  
  const theme = useTheme();
  const loadedThreadId = useRef(null);

  // Function to generate title and summary for chat and research threads
  const generateTitleForThread = async (dialogueId, threadType) => {
    // Only generate titles for chat and research modes, not patient cases
    // if (threadType === THREAD_TYPES.PATIENT_CASE) {
    //   return;
    // }
    if(countRef.current > 0){
      return
    }
    try {
      countRef.current++
      console.log(`Generating title for ${threadType} dialogue: ${dialogueId}`);
      const result = await generateTitleAndSummary(dialogueId);

      if (result && result.title) {
        // Update the thread title in the sidebar
        window.dispatchEvent(new CustomEvent('threadTitleUpdated', {
          detail: {
            threadId: dialogueId,
            title: result.title,
            summary: result.summary
          }
        }));

        setTitleGenerated(true);
        console.log(`Title generated successfully for dialogue ${dialogueId}: ${result.title}`);
      }
    } catch (error) {
      console.error('Failed to generate title:', error);
    }
  };

  const loadMessages = async () => {
    setLoading(true);
    try {
      const data = await messagesAPI.getMessages(thread.id);
      setMessages(data);

      // Count user messages for title generation logic
      const userMessages = data.filter(msg => msg.sender === 'user');
      setUserMessageCount(userMessages.length);

      // Check if title has been generated (if thread has a meaningful title)
      const hasGeneratedTitle = thread?.title &&
        !thread.title.includes('...') &&
        thread.title !== 'New Conversation' &&
        thread.title !== 'Loading...';
      setTitleGenerated(hasGeneratedTitle);

    } catch (error) {
      console.error('Failed to load messages:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleOptimisticMessage = () => {
    // Add detailed debugging
    console.log('🔧 handleOptimisticMessage called');
    console.log('🔧 Current thread:', thread);
    console.log('🔧 Current thread ID:', thread?.id);
    
    // Check if already processed to prevent double processing
    const alreadyProcessed = sessionStorage.getItem('optimisticMessageProcessed');
    console.log('🔧 Already processed?', alreadyProcessed);
    if (alreadyProcessed) {
      console.log('🔄 Optimistic message already processed, skipping');
      return false;
    }
    
    // Check for optimistic message from sessionStorage
    const optimisticMessageData = sessionStorage.getItem('optimisticMessage');
    console.log('🔍 Checking for optimistic message:', optimisticMessageData ? 'Found' : 'Not found');
    console.log('🔍 Raw optimistic message data:', optimisticMessageData);
    
    if (optimisticMessageData) {
      const optimisticMessage = JSON.parse(optimisticMessageData);
      console.log('📝 Parsed optimistic message:', optimisticMessage);
      console.log('🧵 Current thread ID:', thread?.id);
      console.log('🧵 Optimistic thread ID:', optimisticMessage.threadId);
      console.log('🎯 Thread ID match:', optimisticMessage.threadId === thread?.id);
      
      // Check if this message belongs to the current thread (handle both temp and real IDs)
      if (optimisticMessage.threadId === thread.id || 
          (thread.isTemporary && optimisticMessage.threadId === thread.id)) {
        // Add optimistic message and AI thinking placeholder
        const aiThinkingMessage = {
          id: uuidv4(),
          threadId: thread.id,
          sender: 'ai',
          content: 'AI is thinking...',
          timestamp: new Date().toISOString(),
          isThinking: true
        };
        
        console.log('✅ Setting optimistic messages:', [optimisticMessage, aiThinkingMessage]);
        setMessages([optimisticMessage, aiThinkingMessage]);
        setSendingMessage(true);
        
        // Mark as processed
        sessionStorage.setItem('optimisticMessageProcessed', 'true');
        console.log('✅ Marked as processed in sessionStorage');
        return true; // Indicates we have optimistic state
      } else {
        console.log('❌ Thread ID mismatch, not showing optimistic message');
        console.log('❌ Expected:', thread?.id, 'Got:', optimisticMessage.threadId);
      }
    } else {
      console.log('❌ No optimistic message data found in sessionStorage');
    }
    return false;
  };

  useEffect(() => {
    console.log('🔧 useEffect [thread] triggered. Thread:', thread);
    console.log('🔧 loadedThreadId.current:', loadedThreadId.current);
    
    if (thread) {
      // Check if this is a new thread or if we've already loaded this thread
      const isNewThread = !loadedThreadId.current || loadedThreadId.current !== thread.id;
      console.log('🔧 isNewThread:', isNewThread);
      
      if (isNewThread) {
        console.log('🔧 Processing new thread, resetting messages');
        // Reset messages for new thread
        setMessages([]);
        loadedThreadId.current = thread.id;
        
        // Handle optimistic message first, then load existing messages
        console.log('🔧 About to call handleOptimisticMessage');
        const hasOptimisticState = handleOptimisticMessage();
        console.log('🔧 hasOptimisticState:', hasOptimisticState);
        
        // ALWAYS try to handle optimistic state again after a small delay
        // This ensures we catch any optimistic messages that weren't processed initially
        setTimeout(() => {
          console.log('🔧 Re-checking for optimistic message after delay');
          const stillHasOptimistic = sessionStorage.getItem('optimisticMessage');
          const notProcessed = !sessionStorage.getItem('optimisticMessageProcessed');
          
          if (stillHasOptimistic && notProcessed) {
            console.log('🔧 Found unprocessed optimistic message, processing now');
            handleOptimisticMessage();
          }
        }, 50);
        
        // Only load messages from API if we don't have optimistic state
        if (!hasOptimisticState) {
          console.log('🔧 No optimistic state, loading messages from API');
          // Small delay to ensure optimistic state is not overwritten
          setTimeout(() => {
            loadMessages();
          }, 100);
        } else {
          console.log('🔧 Has optimistic state, will check later');
          // If we have optimistic state, still try to load messages after a longer delay
          // This ensures we don't miss any existing messages in the thread
          setTimeout(() => {
            const currentOptimistic = sessionStorage.getItem('optimisticMessage');
            if (!currentOptimistic) {
              // Only load if optimistic state has been cleared (meaning message was processed)
              loadMessages();
            }
          }, 2000);
        }
      } else {
        console.log('🔧 Same thread, not processing again');
      }
    } else {
      console.log('🔧 No thread provided');
    }

    return () => {
      console.log('🔧 Cleanup running for thread:', thread?.id, 'titleGenerated:', titleGenerated, 'userMessageCount:', userMessageCount);
      if ( !titleGenerated && userMessageCount > 0 && userMessageCount < 3 &&
          (thread.type === THREAD_TYPES.RESEARCH || thread.type === THREAD_TYPES.QUICK_FACTS)) {
        console.log('🔧 Cleanup: Generating title for thread on switch:', thread.id);
        // Generate title when switching away from a thread that has messages but no title yet
        // thread.id is actually the dialogueId from the backend
        generateTitleForThread(thread.id, thread.type);
      }
    }

  }, [thread, titleGenerated, userMessageCount]);


  useEffect(() => {
    // Subscribe to WebSocket messages for this thread
    if (thread) {
      const unsubscribe = wsClient.subscribeToThread(thread.id, (data) => {
        console.log('WebSocket message received for thread:', data);
        
        // Add the AI response to messages
        const aiResponse = {
          id: 'ai-' + Date.now(),
          threadId: thread.id,
          sender: 'ai',
          content: data.response.content || data.response,
          timestamp: data.timestamp || new Date().toISOString(),
          attachments: data.response.attachments || [],
          messageId: data.messageId,
          isStream: data.isStream,
          isComplete: data.isComplete,
          chunkId: data.chunkId
        };
        
        setMessages(prev => {
          // Check if we already have this AI response (by content or ID)
          const responseExists = prev.some(msg => 
            msg.sender === 'ai' && 
            (msg.content === aiResponse.content || msg.id === aiResponse.id)
          );
          
          if (responseExists) {
            return prev; // Don't add duplicate
          }
          
          // Replace any thinking message with the actual response
          const hasThinkingMessage = prev.some(msg => msg.isThinking);
          
          if (hasThinkingMessage) {
            return prev.map(msg => {
              if (msg.isThinking) {
                return aiResponse;
              }
              return msg;
            });
          } else {

            const lastMessage = prev[prev.length - 1];
            
            if (aiResponse.isStream && aiResponse.messageId === lastMessage.messageId) {
              // Create a new message object instead of mutating the existing one
              if (aiResponse.chunkId > lastMessage.chunkId) {
                // This is a new chunk, replace the last message with the new one
                const updatedMessage = {
                  ...lastMessage,
                  content: lastMessage.content + aiResponse.content
                };
                return [...prev.slice(0, -1), updatedMessage];
              } else {
                // This is a duplicate chunk, ignore it
                console.log('⚠️ Ignoring duplicate chunk:', aiResponse.chunkId);
                return prev;
              }
            }

            // Just append the new message
            return [...prev, aiResponse];
          }
        });
        
        setSendingMessage(false);
      });
      
      return () => {
        unsubscribe();
      };
    }
  }, [thread]);

  useEffect(() => {
    // Listen for background message responses (only for new threads with sessionStorage)
    const handleMessageResponse = (event) => {
      const { response } = event.detail;
      
      // Only handle if we have an optimistic message in sessionStorage for this thread
      const optimisticMessageData = sessionStorage.getItem('optimisticMessage');
      if (!optimisticMessageData || !thread) return;
      
      const optimisticMessage = JSON.parse(optimisticMessageData);
      if (optimisticMessage.threadId !== thread.id) return; // Not for this thread
      
      setMessages(prev => {
        // If we have no messages yet (race condition), set them directly
        if (prev.length === 0) {
          return [response.userMessage, response.aiResponse];
        }
        
        // Otherwise, replace the temporary messages
        return prev.map(msg => {
          // Replace the temporary user message with the real one
          if (msg.id === optimisticMessage.id) {
            return response.userMessage;
          }
          // Replace the AI thinking message with the real response
          if (msg.isThinking) {
            return response.aiResponse;
          }
          return msg;
        });
      });
      
      setSendingMessage(false);
      
      // Clean up sessionStorage after successful response (but delay slightly)
      setTimeout(() => {
        console.log('🔧 Cleaning up sessionStorage after successful response');
        sessionStorage.removeItem('optimisticMessage');
        sessionStorage.removeItem('optimisticMessageProcessed');
      }, 100);
    };

    const handleMessageError = (event) => {
      const { tempMessageId } = event.detail;
      
      // Only handle if we have an optimistic message in sessionStorage for this thread
      const optimisticMessageData = sessionStorage.getItem('optimisticMessage');
      if (!optimisticMessageData || !thread) return;
      
      const optimisticMessage = JSON.parse(optimisticMessageData);
      if (optimisticMessage.threadId !== thread.id) return; // Not for this thread
      
      setMessages(prev => {
        // If we have no messages yet, create error state
        if (prev.length === 0) {
          return [{
            id: tempMessageId,
            sender: 'user',
            content: 'Message failed to send',
            timestamp: new Date().toISOString()
          }, {
            id: 'error-' + Date.now(),
            sender: 'ai',
            content: 'Sorry, there was an error processing your message. Please try again.',
            timestamp: new Date().toISOString(),
            isError: true
          }];
        }
        
        // Otherwise, update existing messages
        return prev.map(msg => {
          // Keep the user message, but show error for AI response
          if (msg.isThinking) {
            return {
              ...msg,
              content: 'Sorry, there was an error processing your message. Please try again.',
              isThinking: false,
              isError: true
            };
          }
          return msg;
        });
      });
      
      setSendingMessage(false);
      
      // Clean up sessionStorage after error (but delay slightly)
      setTimeout(() => {
        console.log('🔧 Cleaning up sessionStorage after error');
        sessionStorage.removeItem('optimisticMessage');
        sessionStorage.removeItem('optimisticMessageProcessed');
      }, 100);
    };

    // Handler for message_sent confirmation from server
    const handleMessageSent = (event) => {
      const { userMessage, threadId } = event.detail;
      
      // Only process if this is for our current thread
      if (!thread || threadId !== thread.id) return;
      
      // Check if we already have this message (avoid duplicates)
      setMessages(prev => {
        const messageExists = prev.some(msg => 
          (msg.id === userMessage.id) || 
          (msg.content === userMessage.content && msg.sender === 'user' && 
           Math.abs(new Date(msg.timestamp) - new Date(userMessage.timestamp)) < 1000)
        );
        
        if (!messageExists) {
          // Add the user message if it doesn't exist
          // Find the position - should be before any AI thinking message
          const thinkingIndex = prev.findIndex(msg => msg.isThinking);
          if (thinkingIndex !== -1) {
            // Insert before the thinking message
            const newMessages = [...prev];
            newMessages.splice(thinkingIndex, 0, userMessage);
            return newMessages;
          } else {
            // Just append
            return [...prev, userMessage];
          }
        }
        
        return prev; // No changes if message already exists
      });
    };

    window.addEventListener('messageResponse', handleMessageResponse);
    window.addEventListener('messageError', handleMessageError);
    window.addEventListener('message_sent', handleMessageSent);

    return () => {
      window.removeEventListener('messageResponse', handleMessageResponse);
      window.removeEventListener('messageError', handleMessageError);
      window.removeEventListener('message_sent', handleMessageSent);
      // Don't clean up sessionStorage on unmount - let the response handlers do it
    };
  }, [thread]);

  

  const handleSendMessage = async (e) => {
    e.preventDefault();
    // Allow empty send only for Patient Case; otherwise require text or files
    if (sendingMessage) return;
    if (selectedType !== THREAD_TYPES.PATIENT_CASE && (!newMessage.trim() && completedFiles.length === 0)) return;

    const messageContent = newMessage.trim() || ''; // Allow empty message if files attached
    setNewMessage('');
    setSendingMessage(true);
    
    // Extract original File objects from completed files for WebSocket sending
    // The completedFiles contain metadata, but WebSocket needs raw File objects
    const messageAttachments = completedFiles.map(fileMetadata => {
      // Try to find the original File object that was stored during file selection
      // If the File object is preserved in the metadata, use it
      if (fileMetadata.originalFile && fileMetadata.originalFile instanceof File) {
        return fileMetadata.originalFile;
      }
      // Fallback: log error and skip this attachment
      console.error('Missing original File object for attachment:', fileMetadata.name);
      return null;
    }).filter(file => file !== null); // Remove null entries

    try {
      let currentThread = thread;
      
      // If no thread is selected, create a new one ONLY after message is sent successfully
      if (!currentThread) {
        // For chat and research modes, start with empty title (will be generated later)
        // For patient cases, use the message content as title
        let threadTitle;
        if (selectedType === THREAD_TYPES.RESEARCH || selectedType === THREAD_TYPES.QUICK_FACTS) {
          threadTitle = '';
        } else {
          const baseTitle = messageContent || 'Patient Case';
          threadTitle = baseTitle.length > 50 ? baseTitle.substring(0, 50) + '...' : baseTitle;
        }
        
        // Create a temporary thread for immediate navigation
        const tempThread = {
          id: uuidv4(),
          type: selectedType,
          title: threadTitle,
          lastMessage: messageContent,
          lastUpdated: new Date().toISOString(),
          messageCount: 1,
          isTemporary: true // Mark as temporary
        };
        
        // Navigate immediately with optimistic state
        const optimisticMessage = {
          id: uuidv4(),
          threadId: tempThread.id,
          threadType: selectedType, // Store the thread type for the router
          sender: 'user',
          content: messageContent,
          timestamp: new Date().toISOString(),
          attachments: completedFiles // Use metadata for display
        };
        
        // Clear any previous optimistic state first
        console.log('🔧 Clearing optimisticMessageProcessed from sessionStorage');
        sessionStorage.removeItem('optimisticMessageProcessed');
        
        // Store optimistic state and navigate immediately
        console.log('🔧 Storing optimistic message in sessionStorage:', optimisticMessage);
        sessionStorage.setItem('optimisticMessage', JSON.stringify(optimisticMessage));
        console.log('🔧 Stored in sessionStorage. Verifying:', sessionStorage.getItem('optimisticMessage'));
        
        // Navigate with temporary thread
        onThreadCreated(tempThread);
        
        // Send message via WebSocket WITHOUT creating thread first
        wsClient.sendMessage(tempThread.id, messageContent, selectedType, messageAttachments)
          .then(response => {
            console.log('Message sent via WebSocket:', response);
            
            // After successful message send, update thread ID in optimistic storage
            const optimisticData = sessionStorage.getItem('optimisticMessage');
            if (optimisticData) {
              const parsedData = JSON.parse(optimisticData);
              parsedData.threadId = response.threadId || response.dialogueId;
              sessionStorage.setItem('optimisticMessage', JSON.stringify(parsedData));
            }
            
            // Create the actual thread object for the UI
            const realThread = {
              id: response.threadId || response.dialogueId,
              type: selectedType,
              title: threadTitle, // Will be empty for chat/research, will be generated later
              lastMessage: messageContent,
              lastUpdated: new Date().toISOString(),
              messageCount: 1
            };
            
            // Update the thread in the sidebar
            window.dispatchEvent(new CustomEvent('threadCreated', { 
              detail: { thread: realThread, replaceTemp: tempThread.id } 
            }));
            
            // Update current thread reference
            currentThread = realThread;
          })
          .catch(error => {
            console.error('Failed to send message via WebSocket:', error);
            // Signal error to thread page
            window.dispatchEvent(new CustomEvent('messageError', { 
              detail: { 
                error: error.message, 
                tempMessageId: optimisticMessage.id 
              } 
            }));
          });
        
        return; // Exit early for new threads
      }

      // For existing threads, use optimistic UI too
      const tempUserMessage = {
        id: uuidv4(),
        threadId: currentThread.id,
        sender: 'user',
        content: messageContent,
        timestamp: new Date().toISOString(),
        attachments: completedFiles // Use file metadata for display, not raw File objects
      };

      const tempAiMessage = {
        id: uuidv4(),
        threadId: currentThread.id,
        sender: 'ai',
        content: 'AI is thinking...',
        timestamp: new Date().toISOString(),
        isThinking: true,
        attachments: []
      };

      // Add optimistic messages immediately
      setMessages(prev => [...prev, tempUserMessage, tempAiMessage]);

      // Update user message count and check for title generation
      const newUserMessageCount = userMessageCount + 1;
      setUserMessageCount(newUserMessageCount);

      // Generate title after 3 user messages for chat and research modes
      if (!titleGenerated && newUserMessageCount === 3 &&
          (threadType === THREAD_TYPES.RESEARCH || threadType === THREAD_TYPES.QUICK_FACTS)) {
        // currentThread.id is actually the dialogueId from the backend
        generateTitleForThread(currentThread.id, threadType);
      }

      // Scroll to bottom after user sends a message
      scrollToBottom();

      // Send message via WebSocket - use the thread's actual type
      const threadType = currentThread.type || selectedType;
      console.log('🔧 DEBUG - Thread object:', currentThread);
      console.log('🔧 DEBUG - Thread type from object:', currentThread.type);
      console.log('🔧 DEBUG - Selected type fallback:', selectedType);
      console.log('🔧 DEBUG - Final thread type being sent:', threadType);
      
      if (currentThread.type && currentThread.type !== threadType) {
        console.error('🚨 THREAD TYPE MISMATCH!', {
          expected: currentThread.type,
          actual: threadType,
          thread: currentThread
        });
      }
      
      wsClient.sendMessage(currentThread.id, messageContent, threadType, messageAttachments)
        .then(response => {
          console.log('Message sent via WebSocket:', response);
          // The actual AI response will come through the WebSocket message_response event
        })
        .catch(error => {
          console.error('Failed to send message via WebSocket:', error);
          // Remove the thinking message and show error
          setMessages(prev => prev.map(msg => {
            if (msg.id === tempAiMessage.id) {
              return {
                ...msg,
                content: 'Sorry, there was an error sending your message. Please try again.',
                isThinking: false,
                isError: true
              };
            }
            return msg;
          }));
        });
      
      // Clear attachments after successful send
      setAttachedFiles([]);
      setCompletedFiles([]);
      setFileErrors([]);
    } catch (err) {
      console.error('Failed to send message:', err);
      setNewMessage(messageContent); // Restore message on error
    } finally {
      setSendingMessage(false);
    }
  };

  // Handle file attachment changes
  const handleFilesChange = (filesOrUpdater) => {
    console.log('🔧 ChatInterface: handleFilesChange called', { 
      isFunction: typeof filesOrUpdater === 'function'
    });
    
    if (typeof filesOrUpdater === 'function') {
      // Functional update
      setAttachedFiles(prevFiles => {
        const newFiles = filesOrUpdater(prevFiles);
        // Update completed files - keep the full file object with originalFile property
        const completed = newFiles?.filter(f => f.status === 'completed') || [];
        setCompletedFiles(completed);
        return newFiles;
      });
    } else {
      // Direct update
      const files = filesOrUpdater;
      setAttachedFiles(files || []);
      
      // Update completed files - keep the full file object with originalFile property
      const completed = files?.filter(f => f.status === 'completed') || [];
      setCompletedFiles(completed);
    }
  };

  // Handle file removal
  const handleFileRemove = (fileId) => {
    const updatedFiles = attachedFiles.filter(f => f.id !== fileId);
    handleFilesChange(updatedFiles);
  };

  // Handle file errors
 
  const handleFileErrors = (errors) => {
    setFileErrors(errors);
  };

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const formatTime = (timestamp) => {
    return new Date(timestamp).toLocaleTimeString([], { 
      hour: '2-digit', 
      minute: '2-digit' 
    });
  };

  const getPlaceholderText = () => {
    switch (selectedType) {
      case THREAD_TYPES.PATIENT_CASE:
        return 'Describe your patient case, symptoms, or clinical question...';
      case THREAD_TYPES.RESEARCH:
        return 'Ask about medical research, studies, or evidence-based practices...';
      case THREAD_TYPES.QUICK_FACTS:
        return 'Chat about any medical topic or question...';
      default:
        return 'Ask me anything about medicine...';
    }
  };

  // Function to process citations and create clickable links
  const processCitations = (content) => {
    if (!content) return content;
    
    // Extract paper URLs from the content - looking for numbered list items with URLs
    const papers = [];
    const lines = content.split('\n');
    
    // Find lines that start with numbers and contain PubMed URLs
    lines.forEach((line) => {
      const numberMatch = line.match(/^\d+\.\s/);
      const urlMatch = line.match(/https:\/\/pubmed\.ncbi\.nlm\.nih\.gov\/(\d+)/);
      
      if (numberMatch && urlMatch) {
        const number = parseInt(numberMatch[0]);
        papers.push({ number, url: urlMatch[0] });
      }
    });
    
    // Create mapping of citation numbers to URLs
    const citationMap = {};
    papers.forEach((paper) => {
      citationMap[paper.number] = paper.url;
    });
    
    // Also try to extract URLs in order if no numbered list found
    if (papers.length === 0) {
      const urlRegex = /https:\/\/pubmed\.ncbi\.nlm\.nih\.gov\/\d+/g;
      const urls = content.match(urlRegex) || [];
      urls.forEach((url, index) => {
        citationMap[index + 1] = url;
      });
    }
    
    // Replace [number] patterns with clickable superscript links
    // Handle both <sup>[number]</sup> and plain [number] patterns
    let processedContent = content;
    
    // Replace <sup>[number]</sup> pattern
    processedContent = processedContent.replace(/<sup>\[(\d+)\]<\/sup>/g, (match, number) => {
      const url = citationMap[parseInt(number)];
      if (url) {
        return `<sup><a href="${url}" target="_blank" rel="noopener noreferrer">[${number}]</a></sup>`;
      }
      return match;
    });
    
    // Replace plain [number] pattern in text (not in URLs or markdown links)
    processedContent = processedContent.replace(/(?<!https?:\/\/[^\s\]]*)\[(\d+)\](?![^\s\[\]]*\))/g, (match, number) => {
      const url = citationMap[parseInt(number)];
      if (url) {
        return `<sup><a href="${url}" target="_blank" rel="noopener noreferrer">[${number}]</a></sup>`;
      }
      return match;
    });
    
    return processedContent;
  };

  return (
    <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* Header - only show when thread is selected and showHeader is true */}
      {thread && showHeader && (
        <Paper
          elevation={1}
          sx={{
            p: 2,
            borderRadius: 0,
            borderBottom: 1,
            borderColor: 'divider'
          }}
        >
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <Avatar sx={{ bgcolor: threadTypeConfig[thread.type].bgcolor, color: threadTypeConfig[thread.type].color }}>
              {React.createElement(threadTypeConfig[thread.type].icon)}
            </Avatar>
            <Box sx={{ flex: 1 }}>
              <Typography variant="h6" noWrap>
                {thread.title}
              </Typography>
              <Chip
                label={threadTypeConfig[thread.type].label}
                size="small"
                color={threadTypeConfig[thread.type].color}
                variant="outlined"
              />
            </Box>
          </Box>
        </Paper>
      )}

      {!thread ? (
        // Welcome State - Centered Layout
        <Box
          sx={{
            height: '100%',
            display: 'flex',
            flexDirection: 'column',
            bgcolor: 'background.default'
          }}
        >
          {/* Welcome Content - Centered */}
          <Box
            sx={{
              flex: 1,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              p: 3
            }}
          >
            <Box sx={{ textAlign: 'center', maxWidth: 800, width: '100%' }}>
              <Typography variant="h4" gutterBottom color="text.primary" fontWeight="medium" sx={{ mb: 6 }}>
                Hey  {user?.name || 'Doc'}, how can I help you today?
              </Typography>

              {/* Attached Files Preview */}
              {attachedFiles.length > 0 && (
                <Box sx={{ maxWidth: 600, mx: 'auto', mb: 2 }}>
                  <Box sx={{ 
                    display: 'flex', 
                    flexWrap: 'wrap', 
                    gap: 1,
                    alignItems: 'center',
                    justifyContent: 'flex-start'
                  }}>
                    {attachedFiles.map((fileData) => (
                      <FilePreviewSquare
                        key={fileData.id}
                        fileData={fileData}
                        onRemove={() => handleFileRemove(fileData.id)}
                      />
                    ))}
                  </Box>
                </Box>
              )}

              {/* Centered Input Box */}
              <Paper
                component="form"
                onSubmit={handleSendMessage}
                elevation={0}
                sx={{
                  p: 1,
                  borderRadius: 2,
                  bgcolor: '#ffffff',
                  maxWidth: 800,
                  width: '100%',
                  mx: 'auto',
                  border: '1px solid rgba(31, 30, 29, 0.15)',
                  minHeight: 48,
                  boxShadow: '0 2px 8px rgba(0, 0, 0, 0.08)',
                  transition: 'box-shadow 0.2s ease'
                }}
              >
                {/* Text Area */}
                <TextField
                  fullWidth
                  multiline
                  minRows={1}
                  maxRows={4}
                  placeholder={getPlaceholderText()}
                  value={newMessage}
                  onChange={(e) => setNewMessage(e.target.value)}
                  disabled={selectedType === THREAD_TYPES.PATIENT_CASE}
                  variant="outlined"
                  sx={{ 
                    mb: 1.5,
                    '& .MuiOutlinedInput-root': {
                      borderRadius: 1.5,
                      fontSize: '1rem',
                      bgcolor: '#ffffff',
                      minHeight: 36,
                      '& fieldset': {
                        border: 'none',
                      },
                      '&:hover fieldset': {
                        border: 'none',
                      },
                      '&.Mui-focused fieldset': {
                        border: 'none',
                      },
                      '&:hover': {
                        boxShadow: 'none',
                      },
                      '&.Mui-focused': {
                        boxShadow: 'none',
                      },
                      '& .MuiInputBase-input': {
                        padding: '4px 8px'
                      }
                    }
                  }}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter' && !e.shiftKey) {
                      e.preventDefault();
                      handleSendMessage(e);
                    }
                  }}
                />
                
                {/* Controls Row */}
                {/* Controls Row */}
                <Box
                  sx={{
                    display: 'flex',
                    flexDirection: { xs: 'column', sm: 'row' },
                    justifyContent: { xs: 'stretch', sm: 'space-between' },
                    alignItems: { xs: 'stretch', sm: 'end' },
                    gap: { xs: 1, sm: 2 },
                    mt: 2
                  }}
                >
                  {/* Row for mobile: attachment + send, then conversation type below */}
                  {/* Row for desktop: attachment + conversation type, send button at end */}
                  {/* Mobile (xs): */}
                  <Box
                    sx={{
                      display: 'flex',
                      flexDirection: { xs: 'row', sm: 'row' },
                      alignItems: { xs: 'center', sm: 'center' },
                      justifyContent: { xs: 'space-between', sm: 'flex-start' },
                      gap: { xs: 1, sm: 0.75 }
                    }}
                  >
                    {/* File Attachment Button */}
                    { selectedType !== THREAD_TYPES.RESEARCH && <Box sx={{ display: 'flex', justifyContent: 'center' }}>
                      <ControlledFileInput
                        onFilesChange={handleFilesChange}
                        disabled={selectedType === THREAD_TYPES.RESEARCH}
                        currentFiles={attachedFiles}
                        onError={handleFileErrors}
                      />
                    </Box>}
                    {/* Send Button (mobile: right of attachment, desktop: hidden here) */}
                   {selectedType === THREAD_TYPES.PATIENT_CASE ? <Box
                      sx={{
                        display: { xs: 'flex', sm: 'none' },
                        justifyContent: 'center'
                      }}
                    >
                      <IconButton
                        type="submit"
                        size="large"
                        disabled={sendingMessage}
                        sx={sendButtonStyles}
                      >
                        <Start  size={20} sx={{ color: '#111' }}/>
                      </IconButton>
                    </Box> : <Box
                      sx={{
                        display: { xs: 'flex', sm: 'none' },
                        justifyContent: 'center'
                      }}
                    >
                      <IconButton
                        type="submit"
                        size="large"
                        disabled={(!newMessage.trim() && completedFiles.length === 0) || sendingMessage}
                        sx={sendButtonStyles}
                      >
                        {sendingMessage ? <CircularProgress size={20} sx={{ color: '#111' }} /> : <Send />}
                      </IconButton>
                    </Box>}
                    {/* Conversation Type Toggle (desktop only, inline) */}
                    <Box
                      sx={{
                        display: { xs: 'none', sm: 'flex' },
                        justifyContent: { sm: 'flex-start' },
                        alignItems: 'center',
                        ml: { sm: 1 }
                      }}
                    >
                      <ConversationTypeToggle
                        value={selectedType}
                        onChange={setSelectedType}
                        onThreadChange={onThreadChange}
                      />
                    </Box>
                  </Box>
                  {/* Conversation Type Toggle (mobile only, below row) */}
                  <Box
                    sx={{
                      display: { xs: 'flex', sm: 'none' },
                      justifyContent: 'center',
                      mt: 0.5
                    }}
                  >
                    <ConversationTypeToggle
                      value={selectedType}
                      onThreadChange={onThreadChange}
                      onChange={setSelectedType}
                    />
                  </Box>
                  {/* Send Button (desktop only, at end) */}
                  {selectedType === THREAD_TYPES.PATIENT_CASE ? <Box
                      sx={{
                        display: { xs: 'none', sm: 'flex' },
                        justifyContent: 'center'
                      }}
                    >
                      <IconButton
                        type="submit"
                        size="large"
                        disabled={sendingMessage}
                        sx={sendButtonStyles}
                      >
                        <Start  size={20} sx={{ color: '#111' }}/>
                      </IconButton>
                    </Box> : <Box
                      sx={{
                        display: { xs: 'none', sm: 'flex' },
                        justifyContent: 'center'
                      }}
                    >
                      <IconButton
                        type="submit"
                        size="large"
                        disabled={(!newMessage.trim() && completedFiles.length === 0) || sendingMessage}
                        sx={sendButtonStyles}
                      >
                        {sendingMessage ? <CircularProgress size={20} sx={{ color: '#111' }} /> : <Send />}
                      </IconButton>
                    </Box>}
                </Box>
                {/* File Errors */}
                {fileErrors.length > 0 && (
                  <Box sx={{ mt: 1 }}>
                    {fileErrors.map((error, index) => (
                      <Alert 
                        key={index} 
                        severity="error" 
                        size="small"
                        sx={{ mt: 0.5 }}
                      >
                        {error.message}
                      </Alert>
                    ))}
                  </Box>
                )}
              </Paper>
            </Box>
          </Box>
        </Box>
      ) : (
        // Conversation Mode
        (() => {
          console.log('🔍 Thread type check:', {
            threadType: thread?.type,
            expectedType: THREAD_TYPES.PATIENT_CASE,
            isPatientCase: thread?.type === THREAD_TYPES.PATIENT_CASE,
            thread: thread
          });

          if(thread?.type === THREAD_TYPES.PATIENT_CASE || selectedType === THREAD_TYPES.PATIENT_CASE){
            return <PatientCaseInterface
              thread={thread}
              generateTitleForThread={generateTitleForThread}
              onThreadCreated={onThreadCreated}
              showHeader={showHeader}
            />
          }
           else if(thread?.type === THREAD_TYPES.RESEARCH || selectedType === THREAD_TYPES.RESEARCH || selectedType === THREAD_TYPES.CHAT|| thread?.type === THREAD_TYPES.CHAT ){
       return      <Box
            sx={{
              height: '100%',
              display: 'flex',
              flexDirection: 'column',
              bgcolor: 'background.default'
            }}
          >
          {/* Messages Area */}
          <Box
            sx={{
              flex: 1,
              overflow: 'auto',
              p: 3,
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center'
            }}
          >
            {/* Container for centering messages */}
            <Box
              sx={{
                width: '100%',
                maxWidth: '800px',
                display: 'flex',
                flexDirection: 'column'
              }}
            >
            {loading ? (
              <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
                <CircularProgress />
              </Box>
            ) : (
              <>
                {(() => {
                  // Aggregate consecutive AI messages within 60 seconds
                  const aggregatedMessages = [];
                  let currentAiGroup = null;
                  
                  messages.forEach((message, index) => {
                    if (message.sender === 'ai' && !message.isThinking) {
                      // Check if this AI message should be grouped with the previous one
                      if (currentAiGroup && 
                          index > 0 && 
                          messages[index - 1].sender === 'ai' &&
                          !messages[index - 1].isThinking) {
                        // Check if within 60 seconds of the first message in the group
                        const firstTimestamp = new Date(currentAiGroup.timestamp).getTime();
                        const currentTimestamp = new Date(message.timestamp).getTime();
                        const timeDiff = Math.abs(currentTimestamp - firstTimestamp) / 1000; // in seconds
                        
                        if (timeDiff <= 60) {
                          // Aggregate the content
                          currentAiGroup.content += '\n\n' + message.content;
                          return; // Skip adding this message separately
                        }
                      }
                      
                      // Start a new AI group
                      currentAiGroup = { ...message };
                      aggregatedMessages.push(currentAiGroup);
                    } else {
                      // Not an AI message or is thinking message, reset grouping
                      currentAiGroup = null;
                      aggregatedMessages.push(message);
                    }
                  });
                  
                  return aggregatedMessages.map((message) => (
                  <Box
                    key={message.id}
                    sx={{
                      display: 'flex',
                      justifyContent: message.sender === 'user' ? 'flex-end' : 'flex-start',
                      mb: 2
                    }}
                  >
                    <Box
                      sx={{
                        maxWidth: '100%',
                        display: 'flex',
                        flexDirection: 'column',
                        alignItems: message.sender === 'user' ? 'flex-end' : 'flex-start'
                      }}
                    >
                      <Box
                        sx={{
                          display: 'flex',
                          flexDirection: message.sender === 'user' ? 'row-reverse' : 'row',
                          alignItems: 'flex-start',
                          gap: 1,
                          width: '100%'
                        }}
                      >

                        <Paper
                          elevation={0}
                          sx={{
                            p: 2,
                            bgcolor: message.sender === 'user' ? 'rgb(220, 242, 225)' : 'transparent',
                            color: '#000000',
                            borderRadius: '12px'
                          }}
                        >
                          {message.isThinking ? (
                            <TypingIndicator threadType={thread?.type || selectedType} />
                          ) : (
                            <Box
                              sx={{
                                // Paragraphs - better spacing and line height
                                '& p': { 
                                  margin: 0,
                                  marginBottom: 1.5,
                                  lineHeight: 1.6,
                                  fontSize: '16px',
                                  color: '#000000',
                                  '&:last-child': { marginBottom: 0 }
                                },
                                
                                // Headings - much more opinionated and bold
                                '& h1': {
                                  fontSize: '1.75rem',
                                  fontWeight: 700,
                                  marginBottom: 1.5,
                                  marginTop: 2,
                                  color: '#000000',
                                  letterSpacing: '-0.025em',
                                  '&:first-of-type': { marginTop: 0 }
                                },
                                '& h2': {
                                  fontSize: '1.5rem',
                                  fontWeight: 700,
                                  marginBottom: 1.2,
                                  marginTop: 2,
                                  color: '#000000',
                                  letterSpacing: '-0.025em',
                                  '&:first-of-type': { marginTop: 0 }
                                },
                                '& h3': {
                                  fontSize: '1.25rem',
                                  fontWeight: 700,
                                  marginBottom: 1,
                                  marginTop: 1.5,
                                  color: '#000000',
                                  letterSpacing: '-0.025em',
                                  '&:first-of-type': { marginTop: 0 }
                                },
                                '& h4': {
                                  fontSize: '1.125rem',
                                  fontWeight: 700,
                                  marginBottom: 0.8,
                                  marginTop: 1.5,
                                  color: '#000000',
                                  '&:first-of-type': { marginTop: 0 }
                                },
                                '& h5, & h6': {
                                  fontSize: '1rem',
                                  fontWeight: 700,
                                  marginBottom: 0.5,
                                  marginTop: 1,
                                  color: '#000000',
                                  '&:first-of-type': { marginTop: 0 }
                                },
                                
                                // Lists - better spacing and bullets
                                '& ul, & ol': { 
                                  margin: 0,
                                  paddingLeft: 2.5,
                                  marginBottom: 1.5,
                                  fontSize: '16px',
                                  '&:last-child': { marginBottom: 0 }
                                },
                                '& li': { 
                                  marginBottom: 0.5,
                                  lineHeight: 1.6,
                                  fontSize: '16px',
                                  color: '#000000',
                                  '& p': { marginBottom: 0.5 }
                                },
                                '& ul > li': {
                                  listStyleType: 'disc'
                                },
                                '& ul ul > li': {
                                  listStyleType: 'circle'
                                },
                                '& ol > li': {
                                  listStyleType: 'decimal'
                                },
                                '& ol ol > li': {
                                  listStyleType: 'lower-alpha'
                                },
                                
                                // Blockquotes - more prominent
                                '& blockquote': {
                                  borderLeft: '4px solid',
                                  borderColor: 'primary.main',
                                  paddingLeft: 2,
                                  paddingY: 1,
                                  margin: 0,
                                  marginBottom: 1.5,
                                  fontStyle: 'italic',
                                  backgroundColor: 'rgba(0, 0, 0, 0.02)',
                                  borderRadius: 1,
                                  '&:last-child': { marginBottom: 0 },
                                  '& p': { 
                                    marginBottom: 0.5,
                                    '&:last-child': { marginBottom: 0 }
                                  }
                                },
                                
                                // Inline code - more distinctive
                                '& code': {
                                  backgroundColor: 'rgba(var(--mui-palette-primary-mainChannel) / 0.08)',
                                  color: 'primary.dark',
                                  padding: '2px 6px',
                                  borderRadius: 1.5,
                                  fontFamily: '"Fira Code", "SF Mono", Monaco, Inconsolata, "Roboto Mono", monospace',
                                  fontSize: '0.875em',
                                  fontWeight: 500,
                                  border: '1px solid rgba(var(--mui-palette-primary-mainChannel) / 0.12)'
                                },
                                
                                // Code blocks - much better styling
                                '& pre': {
                                  backgroundColor: 'rgba(0, 0, 0, 0.06)',
                                  border: '1px solid rgba(0, 0, 0, 0.1)',
                                  padding: 2,
                                  borderRadius: 2,
                                  overflowX: 'auto',
                                  margin: 0,
                                  marginBottom: 1.5,
                                  fontFamily: '"Fira Code", "SF Mono", Monaco, Inconsolata, "Roboto Mono", monospace',
                                  fontSize: '0.875rem',
                                  lineHeight: 1.5,
                                  '&:last-child': { marginBottom: 0 }
                                },
                                '& pre code': {
                                  backgroundColor: 'transparent',
                                  padding: 0,
                                  border: 'none',
                                  color: 'inherit'
                                },
                                
                                // Text formatting - more emphasis
                                '& strong, & b': { 
                                  fontWeight: 700,
                                  color: '#000000'
                                },
                                '& em, & i': { 
                                  fontStyle: 'italic',
                                  color: '#000000'
                                },
                                
                                // Links - better styling
                                '& a': {
                                  color: 'primary.main',
                                  textDecoration: 'underline',
                                  textDecorationColor: 'rgba(var(--mui-palette-primary-mainChannel) / 0.3)',
                                  textUnderlineOffset: '2px',
                                  '&:hover': {
                                    textDecorationColor: 'primary.main'
                                  }
                                },
                                
                                // Superscript citations
                                '& sup': {
                                  fontSize: '0.7em',
                                  lineHeight: 1,
                                  verticalAlign: 'super',
                                  margin: '0',
                                  padding: '0',
                                  '& a': {
                                    color: 'primary.main',
                                    textDecoration: 'none',
                                    padding: '0',
                                    margin: '0',
                                    fontWeight: 600,
                                    display: 'inline',
                                    '&:hover': {
                                      textDecoration: 'underline'
                                    }
                                  }
                                },
                                
                                // Tables - much better styling
                                '& table': {
                                  width: '100%',
                                  borderCollapse: 'collapse',
                                  margin: 0,
                                  marginBottom: 1.5,
                                  fontSize: '0.9rem',
                                  boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
                                  borderRadius: 2,
                                  overflow: 'hidden',
                                  '&:last-child': { marginBottom: 0 }
                                },
                                '& th, & td': {
                                  border: 'none',
                                  borderBottom: '1px solid rgba(0, 0, 0, 0.1)',
                                  padding: 1.5,
                                  textAlign: 'left',
                                  verticalAlign: 'top'
                                },
                                '& th': {
                                  backgroundColor: 'rgba(var(--mui-palette-primary-mainChannel) / 0.08)',
                                  fontWeight: 700,
                                  color: '#000000',
                                  fontSize: '0.875rem',
                                  textTransform: 'uppercase',
                                  letterSpacing: '0.05em'
                                },
                                '& tbody tr': {
                                  '&:nth-of-type(even)': {
                                    backgroundColor: 'rgba(0, 0, 0, 0.02)'
                                  },
                                  '&:hover': {
                                    backgroundColor: 'rgba(var(--mui-palette-primary-mainChannel) / 0.04)'
                                  }
                                },
                                
                                // Horizontal rules
                                '& hr': {
                                  border: 'none',
                                  height: '2px',
                                  backgroundColor: 'divider',
                                  margin: '2rem 0',
                                  borderRadius: 1
                                }
                              }}
                            >
                              <ReactMarkdown 
                                remarkPlugins={[remarkGfm]}
                                rehypePlugins={[rehypeRaw]}
                                components={{
                                  a: ({ node, ...props }) => (
                                    <a {...props} target="_blank" rel="noopener noreferrer" />
                                  )
                                }}
                              >
                                {processCitations(message.content)}
                              </ReactMarkdown>
                              
                              {/* AI Response Action Buttons - Only for AI messages */}
                              {message.sender === 'ai' && (
                                <Box sx={{ 
                                  display: 'flex', 
                                  alignItems: 'center', 
                                  justifyContent: 'space-between',
                                  mt: 1.5,
                                  pt: 1,
                                  borderTop: '1px solid rgba(0, 0, 0, 0.05)'
                                }}>
                                  {/* Timestamp on the left */}
                                  <Typography
                                    variant="caption"
                                    sx={{
                                      opacity: 0.5,
                                      fontSize: '0.75rem',
                                      color: 'text.secondary'
                                    }}
                                  >
                                    {formatTime(message.timestamp)}
                                  </Typography>
                                  
                                  {/* All buttons grouped on the right */}
                                  <Box sx={{ display: 'flex', gap: 0.5 }}>
                                    {/* Copy Button */}
                                    <IconButton
                                      onClick={() => {
                                        navigator.clipboard.writeText(message.content);
                                      }}
                                      size="small"
                                      title="Copy"
                                      sx={{
                                        color: 'rgba(0, 0, 0, 0.3)',
                                        '&:hover': {
                                          color: 'rgba(0, 0, 0, 0.6)',
                                          backgroundColor: 'rgba(0, 0, 0, 0.03)'
                                        }
                                      }}
                                    >
                                      <ContentCopy sx={{ fontSize: 14 }} />
                                    </IconButton>
                                    
                                    {/* Thumbs Up */}
                                    <IconButton
                                      onClick={async () => {
                                        try {
                                          await feedbackAPI.sendFeedback({
                                            feedback: 'THUMBS_UP',
                                            providerMessageId: message.id,
                                          });
                                          console.log('✅ Feedback sent for message:', message.id);
                                        } catch (e) {
                                          console.error('Failed to send feedback:', e);
                                        }
                                      }}
                                      size="small"
                                      title="Good response"
                                      sx={{
                                        color: 'rgba(0, 0, 0, 0.3)',
                                        '&:hover': {
                                          color: 'rgba(45, 119, 48, 0.8)',
                                          backgroundColor: 'rgba(34, 100, 36, 0.05)'
                                        }
                                      }}
                                    >
                                      <ThumbUpAltOutlined sx={{ fontSize: 14 }} />
                                    </IconButton>
                                    
                                    {/* Thumbs Down */}
                                    <IconButton
                                     onClick={async () => {
                                      try {
                                        await feedbackAPI.sendFeedback({
                                          feedback: 'THUMBS_DOWN',
                                          providerMessageId: message.id,
                                        });
                                        console.log('✅ Feedback sent for message:', message.id);
                                      } catch (e) {
                                        console.error('Failed to send feedback:', e);
                                      }
                                    }}
                                    size="small"
                                      title="Poor response"
                                      sx={{
                                        color: 'rgba(0, 0, 0, 0.3)',
                                        '&:hover': {
                                          color: 'rgba(190, 55, 45, 0.8)',
                                          backgroundColor: 'rgba(133, 44, 37, 0.05)'
                                        }
                                      }}
                                    >
                                      <ThumbDownAltOutlined sx={{ fontSize: 14 }} />
                                    </IconButton>
                                  </Box>
                                </Box>
                              )}
                            </Box>
                          )}
                          
                          {/* Attachments */}
                          {message.attachments && message.attachments.length > 0 && (
                            <AttachmentDisplay 
                              attachments={message.attachments} 
                              compact={true}
                            />
                          )}
                        </Paper>
                      </Box>
                      
                      {/* Timestamp only for user messages */}
                      {message.sender === 'user' && (
                        <Typography
                          variant="caption"
                          sx={{
                            mt: 0.5,
                            opacity: 0.6,
                            fontSize: '0.75rem',
                            textAlign: 'right',
                            alignSelf: 'flex-end'
                          }}
                        >
                          {formatTime(message.timestamp)}
                        </Typography>
                      )}
                    </Box>
                  </Box>
                  ));
                })()}

                {sendingMessage && !messages.some(msg => msg.isThinking) && (
                  <Box sx={{ display: 'flex', justifyContent: 'flex-start', mb: 2 }}>
                    <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 1 }}>
                      <Paper
                        elevation={0}
                        sx={{
                          p: 2,
                          bgcolor: 'transparent',
                          borderRadius: '12px'
                        }}
                      >
                        <TypingIndicator threadType={thread?.type || selectedType} />
                      </Paper>
                    </Box>
                  </Box>
                )}

                <div ref={messagesEndRef} />
              </>
            )}
            </Box>
          </Box>

          {/* Input Box for Conversation Mode */}
          <Box sx={{ p: { xs: 1, sm: 2, md: 3 }, bgcolor: 'background.default' }}>
            <Paper
              component="form"
              onSubmit={handleSendMessage}
              elevation={0}
              sx={{
                p: 1,
                borderRadius: 2,
                bgcolor: '#ffffff',
                maxWidth: { xs: '100%', sm: 700, md: 900 },
                mx: 'auto',
                border: '1px solid rgba(31, 30, 29, 0.15)',
                minHeight: 60,
                boxShadow: '0 2px 8px rgba(0, 0, 0, 0.08)',
                transition: 'box-shadow 0.2s ease'
              }}
            >
              {/* Attached Files Preview for Conversation Mode */}
              {attachedFiles.length > 0 && (
                <Box sx={{ mb: 2 }}>
                  <Box sx={{ 
                    display: 'flex', 
                    flexWrap: 'wrap', 
                    gap: 1,
                    alignItems: 'center'
                  }}>
                    {attachedFiles.map((fileData) => (
                      <FilePreviewSquare
                        key={fileData.id}
                        fileData={fileData}
                        onRemove={() => handleFileRemove(fileData.id)}
                      />
                    ))}
                  </Box>
                </Box>
              )}
            
            {/* Text Area */}
            <TextField
              fullWidth
              multiline
              minRows={1}
              maxRows={4}
              placeholder={`Continue the conversation...`}
              value={newMessage}
              onChange={(e) => setNewMessage(e.target.value)}
              disabled={sendingMessage}
              variant="outlined"
              sx={{ 
                mb: 1.5,
                '& .MuiOutlinedInput-root': {
                  borderRadius: 1.5,
                  fontSize: '1rem',
                  bgcolor: '#ffffff',
                  minHeight: 36,
                  '& fieldset': {
                    border: 'none',
                  },
                  '&:hover fieldset': {
                    border: 'none',
                  },
                  '&.Mui-focused fieldset': {
                    border: 'none',
                  },
                  '&:hover': {
                    boxShadow: 'none',
                  },
                  '&.Mui-focused': {
                    boxShadow: 'none',
                  },
                  '& .MuiInputBase-input': {
                    padding: '4px 8px'
                  }
                }
              }}
              onKeyDown={(e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                  e.preventDefault();
                  handleSendMessage(e);
                }
              }}
            />
            
            {/* Controls Row */}
            <Box sx={{ 
              display: 'flex', 
              justifyContent: 'space-between', 
              alignItems: 'center', 
              mt: 2,
              gap: 2
            }}>
              {/* File Attachment Icon */}
              <ControlledFileInput
                onFilesChange={handleFilesChange}
                disabled={thread?.type === THREAD_TYPES.RESEARCH}
                currentFiles={attachedFiles}
                onError={handleFileErrors}
              />
              
              {/* Send Button */}
              <IconButton
                type="submit"
                size="large"
                disabled={(!newMessage.trim() && completedFiles.length === 0) || sendingMessage}
                sx={sendButtonStyles}
              >
                {sendingMessage ? <CircularProgress size={20} sx={{ color: '#111' }} /> : <Send />}
              </IconButton>
            </Box>
            
            {/* File Errors */}
            {fileErrors.length > 0 && (
              <Box sx={{ mt: 1 }}>
                {fileErrors.map((error, index) => (
                  <Alert 
                    key={index} 
                    severity="error" 
                    size="small"
                    sx={{ mt: 0.5 }}
                  >
                    {error.message}
                  </Alert>
                ))}
              </Box>
            )}
          </Paper>
          
          {/* Disclaimer Text */}
          <Typography 
            variant="caption" 
            sx={{ 
              display: 'block',
              textAlign: 'center',
              color: 'text.secondary',
              mt: 1,
              fontSize: '0.75rem'
            }}
          >
            August for Doctors can make mistakes. Check important info.
          </Typography>
          </Box>
        </Box>
          } 
        
        })()
      )}
    </Box>
  );
}
