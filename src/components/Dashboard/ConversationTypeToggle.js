'use client';

import {
  ToggleButton,
  ToggleButtonGroup,
  Typography,
  Tooltip
} from '@mui/material';
import {
  PersonOutline,
  Search,
  LightbulbOutlined
} from '@mui/icons-material';
import { THREAD_TYPES } from '@/lib/api';

const threadTypeConfig = {
  [THREAD_TYPES.PATIENT_CASE]: {
    label: 'Patient Case',
    icon: PersonOutline,
    color: 'error',
    buttonColor: '#48B58D',
    description: 'Discuss patient cases'
  },
  [THREAD_TYPES.RESEARCH]: {
    label: 'Research',
    icon: Search,
    color: 'info',
    buttonColor: '#489FB5',
    description: 'Medical research'
  },
  [THREAD_TYPES.QUICK_FACTS]: {
    label: 'Chat',
    icon: LightbulbOutlined,
    color: 'success',
    buttonColor: '#E0A890',
    description: 'General medical chat'
  }
};

export default function ConversationTypeToggle({ value, onChange,onThreadChange }) {

  console.log("this is in convo toggle", onThreadChange);
  return (
    <ToggleButtonGroup
      value={value}
      exclusive
      onChange={(event, newValue) => {
        console.log("what is onthreadchange",onThreadChange) // undefined
        if (newValue !== null) {
          onChange(newValue);
         onThreadChange(newValue);
        }
      }}
      size="small"
      sx={{
        '& .MuiToggleButton-root': {
          px: 1.5,
          py: 0.8,
          border: '1px solid rgba(0, 0, 0, 0.08)',
          borderRadius: 1.5,
          mr: 0.75,
          mb: 0,
          display: 'flex',
          flexDirection: 'row',
          alignItems: 'center',
          gap: 0.75,
          minWidth: 90,
          textTransform: 'none',
          backgroundColor: '#ffffff',
          color: 'text.primary',
          fontSize: '0.875rem',
          fontWeight: 500,
          transition: 'all 0.2s ease',
          '&:hover': {
            backgroundColor: 'rgba(0, 0, 0, 0.02)',
            borderColor: 'rgba(0, 0, 0, 0.15)',
          }
        }
      }}
    >
      {Object.entries(threadTypeConfig).map(([type, config]) => {
        const IconComponent = config?.icon;
        // const isSelected = false;
        
        const button = (
          <ToggleButton 
            key={type} 
            value={type}
            // disabled={isPatientCase}
            sx={{
              '&.Mui-selected': {
                backgroundColor: config.buttonColor,
                color: 'white !important',
                borderColor: config.buttonColor,
                '&:hover': {
                  backgroundColor: config.buttonColor,
                  opacity: 0.9
                },
                '& .MuiTypography-root': {
                  color: 'white !important'
                },
                '& .MuiSvgIcon-root': {
                  color: 'white !important'
                }
              },
              '&.Mui-disabled': {
                opacity: 0.5,
                cursor: 'not-allowed'
              }
            }}
          >
            <IconComponent sx={{ fontSize: 18 }} />
            <Typography 
              component="span" 
              sx={{ 
                fontWeight: 500, 
                fontSize: '0.875rem',
                lineHeight: 1.2
              }}
            >
              {config.label}
            </Typography>
          </ToggleButton>
        );
        
        // if (isPatientCase) {
        //   return (
        //     <Tooltip key={type} title="Coming soon" arrow>
        //       <span>{button}</span>
        //     </Tooltip>
        //   );
        // }
        
        return button;
      })}
    </ToggleButtonGroup>
  );
}
