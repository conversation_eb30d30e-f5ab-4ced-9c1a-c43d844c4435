'use client';

import { useState, useEffect, memo } from 'react';

// Module-level cache to persist threads across component remounts
let threadsCache = null;
let hasLoadedThreads = false;
import { useRouter } from 'next/navigation';
import {
  Box,
  Card,
  CardContent,
  Typography,
  IconButton,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  Button,
  Skeleton,
  Avatar,
  ListItemIcon
} from '@mui/material';
import {
  Add,
  PersonOutline,
  Search,
  LightbulbOutlined,
  Person,
  Logout,
  KeyboardArrowLeft,
  ChevronRight
} from '@mui/icons-material';
import { threadsAPI, THREAD_TYPES } from '@/lib/api';
import Image from 'next/image';
import AugustLogo from '@/app/august-logo.svg';

const threadTypeConfig = {
  [THREAD_TYPES.PATIENT_CASE]: {
    label: 'Patient Case',
    icon: PersonOutline,
    color: 'error',
    barColor: '#48B58D',
    description: 'Discuss patient cases and treatment plans'
  },
  [THREAD_TYPES.RESEARCH]: {
    label: 'Research',
    icon: Search,
    color: 'info',
    barColor: '#489FB5',
    description: 'Medical research and literature review'
  },
  [THREAD_TYPES.QUICK_FACTS]: {
    label: 'Chat',
    icon: LightbulbOutlined,
    color: 'success',
    barColor: '#E0A890',
    description: 'General medical chat'
  }
};

function ThreadList({ onThreadSelect, selectedThreadId, user, onLogout, collapsed, onToggleCollapse }) {
  const router = useRouter();
  const [threads, setThreads] = useState(threadsCache || []);
  const [loading, setLoading] = useState(!hasLoadedThreads);

  useEffect(() => {
    // Only load threads if we haven't loaded them before globally
    if (!hasLoadedThreads) {
      loadThreads();
    }
    
    // Listen for new threads being created
    const handleNewThread = (event) => {
      const { thread, replaceTemp } = event.detail;
      
      // Skip temporary threads from showing in sidebar
      if (thread?.isTemporary) {
        console.log('Skipping temporary thread from sidebar:', thread.id);
        return;
      }
      
      // Add thread if it has an ID and is not temporary
      if (thread && thread.id) {
        setThreads(prev => {
          // If replacing a temporary thread, remove it first
          let updated = prev;
          if (replaceTemp) {
            updated = prev.filter(existingThread => existingThread.id !== replaceTemp);
          }
          
          // Check if thread already exists to prevent duplicates
          const exists = updated.some(existingThread => existingThread.id === thread.id);
          if (exists) {
            return prev; // No change if thread already exists
          }
          
          const finalThreads = [thread, ...updated];
          threadsCache = finalThreads; // Update cache
          return finalThreads;
        });
      }
    };

    // Listen for thread title updates
    const handleTitleUpdate = (event) => {
      const { threadId, title, summary } = event.detail;

      setThreads(prev => {
        const updated = prev.map(thread => {
          if (thread.id === threadId) {
            return {
              ...thread,
              title: title,
              summary: summary
            };
          }
          return thread;
        });
        threadsCache = updated; // Update cache
        return updated;
      });
    };

    window.addEventListener('threadCreated', handleNewThread);
    window.addEventListener('threadTitleUpdated', handleTitleUpdate);

    return () => {
      window.removeEventListener('threadCreated', handleNewThread);
      window.removeEventListener('threadTitleUpdated', handleTitleUpdate);
    };
  }, []);

  const loadThreads = async () => {
    try {
      const data = await threadsAPI.getThreads();
      
      // Filter out invalid threads with comprehensive validation
      const filteredThreads = data.filter(thread => {
        // Ensure thread exists and has required fields
        if (!thread || !thread.id) {
          console.warn('Filtered out thread missing ID:', thread);
          return false;
        }
        
        // Ensure thread has valid type for UI configuration
        if (!thread.type || !threadTypeConfig[thread.type]) {
          console.warn('Filtered out thread with invalid type:', thread);
          return false;
        }
        
        // Ensure thread has a title, but allow empty titles for chat and research modes
        // (they will be generated automatically)
        if (!thread.title || thread.title.trim() === '') {
          // Allow empty titles for chat and research modes
          if (thread.type === THREAD_TYPES.RESEARCH || thread.type === THREAD_TYPES.QUICK_FACTS) {
            // Set a placeholder title that will be replaced when generated
            thread.title = '';
          } else {
            console.warn('Filtered out thread with empty title:', thread);
            return false;
          }
        }
        
        return true;
      });
      
      console.log(`Filtered ${data.length} threads down to ${filteredThreads.length} valid threads for display`);
      
      setThreads(filteredThreads);
      threadsCache = filteredThreads; // Cache the filtered data
      hasLoadedThreads = true; // Mark as loaded globally
    } catch (error) {
      console.error('Failed to load threads:', error);
    } finally {
      setLoading(false);
    }
  };



  const getTimeCategory = (timestamp) => {
    const now = new Date();
    
    // Handle both Unix timestamps and ISO strings
    let date;
    if (typeof timestamp === 'number') {
      // Unix timestamp in milliseconds
      date = new Date(timestamp);
    } else if (typeof timestamp === 'string') {
      // ISO string or other date string
      date = new Date(timestamp);
    } else {
      // Fallback to current date if invalid
      date = now;
    }
    
    // Ensure the date is valid
    if (isNaN(date.getTime())) {
      console.warn('Invalid date:', timestamp);
      return 'Older';
    }
    
    const diffInDays = Math.floor((now - date) / (1000 * 60 * 60 * 24));
    
    if (diffInDays === 0) return 'Today';
    if (diffInDays === 1) return 'Yesterday';
    if (diffInDays <= 7) return 'This Week';
    if (diffInDays <= 14) return 'Last Week';
    return 'Older';
  };

  const groupThreadsByTime = (threads) => {
    const groups = {
      'Today': [],
      'Yesterday': [],
      'This Week': [],
      'Last Week': [],
      'Older': []
    };

    threads.forEach(thread => {
      const category = getTimeCategory(thread.lastUpdated);
      groups[category].push(thread);
    });

    // Remove empty groups and return only groups with threads
    return Object.entries(groups).filter(([, threads]) => threads.length > 0);
  };

  if (loading) {
    return (
      <Box sx={{ p: 2 }}>
        {[1, 2, 3].map(i => (
          <Card key={i} sx={{ mb: 2 }}>
            <CardContent>
              <Skeleton variant="text" width="60%" />
              <Skeleton variant="text" width="80%" />
              <Skeleton variant="text" width="40%" />
            </CardContent>
          </Card>
        ))}
      </Box>
    );
  }

  return (
    <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* Header with App Title */}
      <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider' }}>
        {collapsed ? (
          // Collapsed Header
          <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', gap: 1 }}>
            <IconButton
              onClick={onToggleCollapse}
              size="small"
              sx={{ color: 'primary.main' }}
            >
              <ChevronRight />
            </IconButton>
            <Box 
              onClick={() => router.push('/')}
              sx={{ 
                cursor: 'pointer', 
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                width: 32,
                height: 32,
                borderRadius: '8px',
                bgcolor: '#206E55',
                color: 'white',
                fontSize: '18px',
                fontWeight: 'bold',
                '&:hover': { 
                  opacity: 0.8,
                  transform: 'scale(1.05)',
                  transition: 'all 0.2s'
                } 
              }}
            >
              a
            </Box>
            <IconButton
              onClick={() => router.push('/')}
              size="small"
              sx={{ 
                color: '#111',
                border: 1,
                borderColor: '#111',
                '&:hover': {
                  bgcolor: '#111',
                  color: 'white'
                }
              }}
            >
              <Add />
            </IconButton>
          </Box>
        ) : (
          // Expanded Header
          <>
            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 1.5 }}>
              <Box 
                sx={{ 
                  display: 'flex', 
                  alignItems: 'center', 
                  gap: 1, 
                  cursor: 'pointer',
                  '&:hover': { opacity: 0.8 }
                }}
                onClick={() => router.push('/')}
              >
                <Image 
                  src={AugustLogo} 
                  alt="August Logo" 
                  width={100} 
                  height={37}
                  style={{ objectFit: 'contain' }}
                />
              </Box>
              <IconButton
                onClick={onToggleCollapse}
                size="small"
                sx={{ color: 'text.secondary' }}
              >
                <KeyboardArrowLeft />
              </IconButton>
            </Box>
            
            <Button
              variant="outlined"
              size="small"
              onClick={() => router.push('/')}
              startIcon={<Add />}
              sx={{
                mb: 1.5,
                width: '100%',
                py: 0.75,
                fontSize: '0.8rem',
                textTransform: 'none',
                fontWeight: 'medium',
                bgcolor: 'transparent',
                borderColor: '#111',
                color: '#111',
                '&:hover': {
                  bgcolor: '#111',
                  color: 'white',
                  borderColor: '#111'
                }
              }}
            >
              New Conversation
            </Button>
          </>
        )}
      </Box>

      <Box sx={{ flex: 1, overflow: 'auto' }}>
        {!collapsed ? (
          // Expanded thread list
          groupThreadsByTime(threads).map(([timeGroup, groupThreads]) => (
            <Box key={timeGroup}>
              {/* Time Group Header */}
              <Box sx={{ px: 2, pt: 1.5, pb: 0.5 }}>
                <Typography variant="caption" color="text.secondary" sx={{ fontSize: '0.7rem', fontWeight: 600 }}>
                  {timeGroup}
                </Typography>
              </Box>
              
              {/* Threads in this group */}
              <List sx={{ p: 0 }}>
                {groupThreads.map((thread) => {
                  const config = threadTypeConfig[thread.type] || threadTypeConfig[THREAD_TYPES.PATIENT_CASE];
                  
                  if (!config) {
                    console.warn('No configuration found for thread type:', thread.type);
                    return null;
                  }
                  
                  return (
                    <ListItem
                      key={thread.id}
                      component="div"
                      selected={selectedThreadId === thread.id}
                      onClick={() => onThreadSelect(thread)}
                      sx={{
                        py: 0.5,
                        px: 0,
                        position: 'relative',
                        cursor: 'pointer',
                        '&:hover': {
                          bgcolor: selectedThreadId === thread.id ? 'rgba(25, 118, 210, 0.12)' : 'action.hover'
                        },
                        '&': {
                          bgcolor: selectedThreadId === thread.id && 'rgba(25, 118, 210, 0.08)',
                          borderRight:selectedThreadId === thread.id && '3px solid #1976d2',
                          '& .MuiTypography-root': {
                            fontWeight:selectedThreadId === thread.id && 700,
                            color:selectedThreadId === thread.id && 'primary.main'
                          }
                        }
                      }}
                    >
                      {/* Colored bar */}
                      <Box
                        sx={{
                          position: 'absolute',
                          left: 0,
                          top: 0,
                          bottom: 0,
                          width: '4px',
                          bgcolor: config.barColor
                        }}
                      />
                      
                      <ListItemText
                        sx={{ pl: 3 }}
                        primary={
                          <Typography
                            variant="body2"
                            sx={{
                              fontWeight: 'medium',
                              fontSize: '0.875rem',
                              overflow: 'hidden',
                              textOverflow: 'ellipsis',
                              whiteSpace: 'nowrap',
                              fontStyle: !thread.title || thread.title.trim() === '' ? 'italic' : 'normal',
                              color: !thread.title || thread.title.trim() === '' ? 'text.secondary' : 'inherit'
                            }}
                          >
                            {thread.title && thread.title.trim() !== '' ? thread.title : 'New conversation...'}
                          </Typography>
                        }
                      />
                    </ListItem>
                  );
                })}
              </List>
            </Box>
          ))
        ) : (
          // Collapsed thread list - just icons
          <></>
        )}

        {threads.length === 0 &&  (!collapsed) && (
          <Box sx={{ textAlign: 'center', py: 6, px: 2 }}>
            <Typography variant="body1" color="text.secondary" gutterBottom sx={{ fontSize: '0.9rem' }}>
              No conversations yet
            </Typography>
            <Typography variant="caption" color="text.secondary" sx={{ fontSize: '0.75rem' }}>
              Start a new conversation to get help with patient cases, research, or quick medical facts
            </Typography>
          </Box>
        )}
      </Box>

      {/* Account Management Section */}
      <Box sx={{ borderTop: 1, borderColor: 'divider' }}>
        <List sx={{ p: 0 }}>
          <ListItem
            sx={{
              py: collapsed ? 1 : 2,
              bgcolor: 'background.paper',
              flexDirection: collapsed ? 'column' : 'row',
              alignItems: 'center',
              gap: collapsed ? 1 : 0
            }}
          >
            {collapsed ? (
              // Collapsed account section
              <>
                <Avatar sx={{ bgcolor: 'primary.main', width: 32, height: 32 }}>
                  <Person />
                </Avatar>
                <IconButton
                  onClick={onLogout}
                  color="error"
                  size="small"
                >
                  <Logout />
                </IconButton>
              </>
            ) : (
              // Expanded account section
              <>
                <ListItemIcon>
                  <Avatar sx={{ bgcolor: 'primary.main', width: 40, height: 40 }}>
                    <Person />
                  </Avatar>
                </ListItemIcon>
                <ListItemText
                  primary={
                    <Typography variant="body2" fontWeight="medium" sx={{ fontSize: '0.85rem' }}>
                      {user?.name || 'Doctor'}
                    </Typography>
                  }
                  secondary={
                    <Typography variant="caption" color="text.secondary" sx={{ fontSize: '0.7rem' }}>
                      {user?.specialization || ' '}
                    </Typography>
                  }
                />
                <ListItemSecondaryAction>
                  <IconButton
                    edge="end"
                    onClick={onLogout}
                    color="error"
                    size="small"
                  >
                    <Logout />
                  </IconButton>
                </ListItemSecondaryAction>
              </>
            )}
          </ListItem>
        </List>
      </Box>
    </Box>
  );
}

// Memoize ThreadList to prevent unnecessary re-renders
// Only re-render when actual data props change, not function references
export default memo(ThreadList, (prevProps, nextProps) => {
  return (
    prevProps.selectedThreadId === nextProps.selectedThreadId &&
    prevProps.collapsed === nextProps.collapsed &&
    prevProps.user?.name === nextProps.user?.name &&
    prevProps.user?.specialization === nextProps.user?.specialization
  );
});
