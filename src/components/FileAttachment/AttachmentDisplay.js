'use client';

import React, { useState } from 'react';
import {
  Box,
  Paper,
  Typography,
  IconButton,
  Dialog,
  DialogContent,
  DialogActions,
  Button,
  Grid,
  Chip,
  Tooltip
} from '@mui/material';
import {
  Download,
  Visibility,
  Close
} from '@mui/icons-material';
import { 
  formatFileSize, 
  getFileIcon, 
  getFileCategory 
} from '@/lib/fileValidation';
import { getFileData, getFileThumbnail, downloadFile } from '@/lib/fileStorage';

export default function AttachmentDisplay({ attachments = [], compact = false }) {
  const [selectedAttachment, setSelectedAttachment] = useState(null);
  const [previewOpen, setPreviewOpen] = useState(false);

  if (!attachments || attachments.length === 0) {
    return null;
  }

  const handlePreview = (attachment) => {
    const category = getFileCategory(attachment.mimeType);
    if (category === 'image' || attachment.mimeType === 'application/pdf') {
      setSelectedAttachment(attachment);
      setPreviewOpen(true);
    }
  };

  const handleDownload = (attachment) => {
    try {
      downloadFile(attachment.id, attachment.filename);
    } catch (error) {
      console.error('Download failed:', error);
    }
  };

  const handleClosePreview = () => {
    setPreviewOpen(false);
    setSelectedAttachment(null);
  };

  if (compact) {
    return (
      <Box sx={{ mt: 1 }}>
        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
          {attachments.map((attachment) => (
            <CompactAttachmentChip
              key={attachment.id}
              attachment={attachment}
              onPreview={() => handlePreview(attachment)}
              onDownload={() => handleDownload(attachment)}
            />
          ))}
        </Box>
        
        {/* Preview Dialog */}
        <AttachmentPreviewDialog
          attachment={selectedAttachment}
          open={previewOpen}
          onClose={handleClosePreview}
        />
      </Box>
    );
  }

  return (
    <Box sx={{ mt: 2 }}>
      <Grid container spacing={1}>
        {attachments.map((attachment) => (
          <Grid item xs={12} sm={6} md={4} key={attachment.id}>
            <AttachmentCard
              attachment={attachment}
              onPreview={() => handlePreview(attachment)}
              onDownload={() => handleDownload(attachment)}
            />
          </Grid>
        ))}
      </Grid>
      
      {/* Preview Dialog */}
      <AttachmentPreviewDialog
        attachment={selectedAttachment}
        open={previewOpen}
        onClose={handleClosePreview}
      />
    </Box>
  );
}

// Compact chip display for attachments
function CompactAttachmentChip({ attachment, onPreview, onDownload }) {
  const fileIcon = getFileIcon(attachment.mimeType);
  const category = getFileCategory(attachment.mimeType);
  const canPreview = category === 'image' || attachment.mimeType === 'application/pdf';

  return (
    <Chip
      icon={<span style={{ fontSize: '14px' }}>{fileIcon}</span>}
      label={
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
          <Typography variant="caption" sx={{ maxWidth: 80, overflow: 'hidden', textOverflow: 'ellipsis' }}>
            {attachment.filename}
          </Typography>
          <Typography variant="caption" color="text.secondary" sx={{ fontSize: '10px' }}>
            ({formatFileSize(attachment.size)})
          </Typography>
        </Box>
      }
      size="small"
      variant="outlined"
      onClick={canPreview ? onPreview : onDownload}
      sx={{
        cursor: 'pointer',
        '&:hover': {
          backgroundColor: 'action.hover'
        }
      }}
    />
  );
}

// Full attachment card display
function AttachmentCard({ attachment, onPreview, onDownload }) {
  const fileIcon = getFileIcon(attachment.mimeType);
  const category = getFileCategory(attachment.mimeType);
  const canPreview = category === 'image' || attachment.mimeType === 'application/pdf';
  const thumbnailData = attachment.thumbnailUrl ? getFileThumbnail(attachment.id) : null;

  return (
    <Paper
      elevation={1}
      sx={{
        p: 1.5,
        borderRadius: 2,
        transition: 'all 0.2s ease',
        '&:hover': {
          elevation: 2,
          transform: 'translateY(-1px)'
        }
      }}
    >
      {/* Preview/Thumbnail */}
      <Box
        sx={{
          width: '100%',
          height: 80,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          backgroundColor: 'grey.100',
          borderRadius: 1,
          mb: 1,
          cursor: canPreview ? 'pointer' : 'default',
          overflow: 'hidden'
        }}
        onClick={canPreview ? onPreview : undefined}
      >
        {thumbnailData && category === 'image' ? (
          <Box
            component="img"
            src={thumbnailData}
            alt={attachment.filename}
            sx={{
              width: '100%',
              height: '100%',
              objectFit: 'cover'
            }}
          />
        ) : (
          <Typography sx={{ fontSize: '2rem' }}>
            {fileIcon}
          </Typography>
        )}
      </Box>

      {/* File Info */}
      <Typography
        variant="body2"
        sx={{
          fontWeight: 'medium',
          overflow: 'hidden',
          textOverflow: 'ellipsis',
          whiteSpace: 'nowrap',
          mb: 0.5
        }}
      >
        {attachment.filename}
      </Typography>
      
      <Typography variant="caption" color="text.secondary" sx={{ display: 'block', mb: 1 }}>
        {formatFileSize(attachment.size)}
      </Typography>

      {/* Action Buttons */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Box sx={{ display: 'flex', gap: 0.5 }}>
          {canPreview && (
            <Tooltip title="Preview">
              <IconButton size="small" onClick={onPreview}>
                <Visibility sx={{ fontSize: 16 }} />
              </IconButton>
            </Tooltip>
          )}
          <Tooltip title="Download">
            <IconButton size="small" onClick={onDownload}>
              <Download sx={{ fontSize: 16 }} />
            </IconButton>
          </Tooltip>
        </Box>
        
        <Chip
          label={getFileCategory(attachment.mimeType).toUpperCase()}
          size="small"
          variant="outlined"
          sx={{ fontSize: '10px', height: 20 }}
        />
      </Box>
    </Paper>
  );
}

// Preview dialog for images and PDFs
function AttachmentPreviewDialog({ attachment, open, onClose }) {
  if (!attachment) return null;

  const category = getFileCategory(attachment.mimeType);
  const fileData = attachment ? getFileData(attachment.id) : null;

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: { height: '80vh' }
      }}
    >
      <DialogContent sx={{ p: 0, display: 'flex', flexDirection: 'column' }}>
        {/* Header */}
        <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider', display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <Box>
            <Typography variant="h6" noWrap>
              {attachment.filename}
            </Typography>
            <Typography variant="caption" color="text.secondary">
              {formatFileSize(attachment.size)} • {attachment.mimeType}
            </Typography>
          </Box>
          <IconButton onClick={onClose} size="small">
            <Close />
          </IconButton>
        </Box>

        {/* Preview Content */}
        <Box sx={{ flex: 1, display: 'flex', alignItems: 'center', justifyContent: 'center', p: 2 }}>
          {category === 'image' && fileData ? (
            <Box
              component="img"
              src={fileData}
              alt={attachment.filename}
              sx={{
                maxWidth: '100%',
                maxHeight: '100%',
                objectFit: 'contain'
              }}
            />
          ) : attachment.mimeType === 'application/pdf' && fileData ? (
            <Box sx={{ width: '100%', height: '100%', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
              <Typography color="text.secondary">
                PDF preview not available in demo mode. Click download to view.
              </Typography>
            </Box>
          ) : (
            <Box sx={{ textAlign: 'center' }}>
              <Typography sx={{ fontSize: '4rem', mb: 2 }}>
                {getFileIcon(attachment.mimeType)}
              </Typography>
              <Typography variant="h6" color="text.secondary">
                Preview not available
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Click download to view this file
              </Typography>
            </Box>
          )}
        </Box>
      </DialogContent>

      <DialogActions>
        <Button onClick={() => downloadFile(attachment.id, attachment.filename)} startIcon={<Download />}>
          Download
        </Button>
        <Button onClick={onClose}>
          Close
        </Button>
      </DialogActions>
    </Dialog>
  );
}
