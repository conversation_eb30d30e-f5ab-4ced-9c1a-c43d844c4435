'use client';

import React, { useState, useRef } from 'react';
import { v4 as uuidv4 } from 'uuid';
import {
  Box,
  IconButton,
  Typography,
  Alert,
  Tooltip,
  CircularProgress
} from '@mui/material';
import {
  AttachFile,
  Close,
  CloudUpload,
  Error
} from '@mui/icons-material';
import { 
  validateFiles, 
  formatFileSize, 
  getFileIcon, 
  getFileCategory,
  FILE_SIZE_LIMITS 
} from '@/lib/fileValidation';
import { fileAPI } from '@/lib/fileStorage';

export default function FileAttachmentInput({ 
  onFilesChange, 
  disabled = false, 
  maxFiles = FILE_SIZE_LIMITS.MAX_FILES_PER_MESSAGE,
  showFileList = true 
}) {
  const [attachedFiles, setAttachedFiles] = useState([]);
  // const [uploadProgress] = useState({});
  const [errors, setErrors] = useState([]);
  const [isDragOver, setIsDragOver] = useState(false);
  const fileInputRef = useRef(null);

  // Handle file selection
  const handleFileSelect = (selectedFiles) => {
    console.log('🔧 FileAttachmentInput: handleFileSelect called', selectedFiles);
    const filesArray = Array.from(selectedFiles);
    
    // Validate files
    const validation = validateFiles(filesArray, attachedFiles);
    
    if (!validation.isValid) {
      console.log('🔧 FileAttachmentInput: validation failed', validation.errors);
      setErrors(validation.errors);
      return;
    }
    
    setErrors([]);
    
    // Add files to state with pending upload status
    const newFiles = filesArray.map(file => ({
      id: uuidv4(),
      file,
      originalFile: file, // Preserve original File object for WebSocket sending
      name: file.name,
      size: file.size,
      type: file.type,
      status: 'pending', // 'pending', 'uploading', 'completed', 'failed'
      progress: 0,
      uploadedFileId: null,
      previewUrl: null
    }));
    
    console.log('🔧 FileAttachmentInput: newFiles created', newFiles);
    
    const updatedFiles = [...attachedFiles, ...newFiles];
    setAttachedFiles(updatedFiles);
    
    // Start upload process
    uploadFiles(newFiles);
    
    // Notify parent component
    onFilesChange?.(updatedFiles);
  };

  // Upload files to storage
  const uploadFiles = async (filesToUpload) => {
    console.log('🔧 FileAttachmentInput: uploadFiles called', filesToUpload);
    
    for (const fileData of filesToUpload) {
      try {
        // Update status to uploading
        updateFileStatus(fileData.id, 'uploading', 0);
        
        // Simulate upload with progress
        const uploadedFile = await fileAPI.upload([fileData.file], (progressData) => {
          if (progressData.filename === fileData.file.name) {
            updateFileStatus(fileData.id, 'uploading', progressData.progress);
          }
        });
        
        console.log('🔧 FileAttachmentInput: upload completed', uploadedFile);
        
        // Create preview for images
        let previewUrl = null;
        if (getFileCategory(fileData.file.type) === 'image') {
          previewUrl = await createImagePreview(fileData.file);
        }
        
        // Update with completed status
        updateFileStatus(fileData.id, 'completed', 100, uploadedFile[0], previewUrl);
        
      } catch (error) {
        console.error('Upload failed:', error);
        updateFileStatus(fileData.id, 'failed', 0, null, null, error.message);
      }
    }
  };

  // Update file upload status
  const updateFileStatus = (fileId, status, progress, uploadedFile = null, previewUrl = null, error = null) => {
    setAttachedFiles(prev => {
      const updatedFiles = prev.map(file => {
        if (file.id === fileId) {
          const updated = {
            ...file,
            status,
            progress,
            error
          };
          
          if (uploadedFile) {
            updated.uploadedFileId = uploadedFile.id;
            updated.uploadedFile = uploadedFile;
          }
          
          // Always preserve the original File object for WebSocket sending
          if (!updated.originalFile && file.file) {
            updated.originalFile = file.file;
          }
          
          if (previewUrl) {
            updated.previewUrl = previewUrl;
          }
          
          return updated;
        }
        return file;
      });
      
      // Notify parent component when status changes, especially when completed
      if (status === 'completed' || status === 'failed') {
        onFilesChange?.(updatedFiles.filter(f => f.status === 'completed'));
      }
      
      return updatedFiles;
    });
  };

  // Create image preview
  const createImagePreview = (file) => {
    return new Promise((resolve) => {
      const reader = new FileReader();
      reader.onload = () => resolve(reader.result);
      reader.onerror = () => resolve(null);
      reader.readAsDataURL(file);
    });
  };

  // Remove file
  const removeFile = (fileId) => {
    const updatedFiles = attachedFiles.filter(f => f.id !== fileId);
    setAttachedFiles(updatedFiles);
    onFilesChange?.(updatedFiles);
    
    // Clear upload progress for this file (upload progress not used in current implementation)
  };

  // Handle drag and drop
  const handleDrop = (e) => {
    e.preventDefault();
    setIsDragOver(false);
    
    if (disabled) return;
    
    const files = e.dataTransfer.files;
    if (files.length > 0) {
      handleFileSelect(files);
    }
  };

  const handleDragOver = (e) => {
    e.preventDefault();
    if (!disabled) {
      setIsDragOver(true);
    }
  };

  const handleDragLeave = (e) => {
    e.preventDefault();
    setIsDragOver(false);
  };

  // Trigger file input
  const triggerFileInput = () => {
    console.log('🔧 FileAttachmentInput: triggerFileInput called', { disabled, hasRef: !!fileInputRef.current });
    if (!disabled && fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  // Get completed files for parent component
  const getCompletedFiles = () => {
    return attachedFiles
      .filter(f => f.status === 'completed' && f.uploadedFile)
      .map(f => f.uploadedFile);
  };

  // Update parent when files complete upload
  React.useEffect(() => {
    const completedFiles = getCompletedFiles();
    console.log('🔧 FileAttachmentInput: attachedFiles changed', { 
      attachedFiles: attachedFiles.length, 
      completedFiles: completedFiles.length 
    });
    onFilesChange?.(attachedFiles, completedFiles);
  }, [attachedFiles]);

  const remainingSlots = maxFiles - attachedFiles.length;
  const hasErrors = errors.length > 0;

  return (
    <Box
      onDrop={handleDrop}
      onDragOver={handleDragOver}
      onDragLeave={handleDragLeave}
    >
      {/* File Input */}
      <input
        ref={fileInputRef}
        type="file"
        multiple
        accept=".jpg,.jpeg,.png,.gif,.webp,.pdf,.doc,.docx,.txt,.csv,.json,.xls,.xlsx,.dcm,.dicom"
        style={{ display: 'none' }}
        onChange={(e) => handleFileSelect(e.target.files)}
        disabled={disabled}
      />

      {/* Attachment Button */}
      <Tooltip title={`Attach files (${remainingSlots} remaining)`}>
        <IconButton
          onClick={triggerFileInput}
          disabled={disabled || remainingSlots <= 0}
          size="small"
          sx={{ 
            color: 'text.secondary',
            '&:hover': { color: 'primary.main' }
          }}
        >
          <AttachFile />
        </IconButton>
      </Tooltip>

      {/* File Preview Squares */}
      {showFileList && attachedFiles.length > 0 && (
        <Box sx={{ mt: 2 }}>
          <Box sx={{ 
            display: 'flex', 
            flexWrap: 'wrap', 
            gap: 1,
            alignItems: 'center'
          }}>
            {attachedFiles.map((fileData) => (
              <FilePreviewSquare
                key={fileData.id}
                fileData={fileData}
                onRemove={() => removeFile(fileData.id)}
              />
            ))}
            
            {/* Add More Button (if remaining slots) */}
            {remainingSlots > 0 && (
              <Box
                onClick={triggerFileInput}
                sx={{
                  width: 60,
                  height: 60,
                  borderRadius: 2,
                  border: '2px dashed',
                  borderColor: 'grey.400',
                  backgroundColor: 'grey.50',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  cursor: disabled ? 'not-allowed' : 'pointer',
                  transition: 'all 0.2s ease',
                  '&:hover': disabled ? {} : {
                    borderColor: 'primary.main',
                    backgroundColor: 'primary.50'
                  }
                }}
              >
                <CloudUpload sx={{ 
                  fontSize: '1.5rem', 
                  color: disabled ? 'action.disabled' : 'text.secondary' 
                }} />
              </Box>
            )}
          </Box>
          
          {/* File Count */}
          <Typography variant="caption" color="text.secondary" sx={{ 
            display: 'block', 
            mt: 1, 
            fontWeight: 'medium' 
          }}>
            {attachedFiles.length}/{maxFiles} files attached
          </Typography>
        </Box>
      )}

      {/* Drag and Drop Zone (when dragging over) */}
      {isDragOver && (
        <Box
          onDrop={handleDrop}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          sx={{
            position: 'fixed',
            top: 0,
            left: 0,
            width: '100%',
            height: '100%',
            backgroundColor: 'rgba(25, 118, 210, 0.1)',
            border: '3px dashed',
            borderColor: 'primary.main',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            zIndex: 9999,
            pointerEvents: 'none'
          }}
        >
          <Typography variant="h5" color="primary.main" sx={{ fontWeight: 'bold' }}>
            Drop files here to attach
          </Typography>
        </Box>
      )}

      {/* Error Messages */}
      {hasErrors && (
        <Box sx={{ mt: 1 }}>
          {errors.map((error, index) => (
            <Alert 
              key={index} 
              severity="error" 
              size="small"
              sx={{ mt: 0.5 }}
            >
              {error.message}
            </Alert>
          ))}
        </Box>
      )}
    </Box>
  );
}

// File Preview Square Component
function FilePreviewSquare({ fileData, onRemove }) {
  const { name, size, type, status, progress, previewUrl } = fileData;
  const fileIcon = getFileIcon(type);
  const category = getFileCategory(type);
  const isImage = category === 'image';
  
  return (
    <Tooltip 
      title={`${name} (${formatFileSize(size)})`} 
      arrow 
      placement="top"
    >
      <Box
        sx={{
          position: 'relative',
          width: 60,
          height: 60,
          borderRadius: 2,
          overflow: 'hidden',
          backgroundColor: status === 'failed' ? 'error.100' : 'grey.100',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          border: 'none',
          cursor: 'pointer'
        }}
      >
      {/* File Preview/Icon */}
      {isImage && previewUrl ? (
        <Box
          component="img"
          src={previewUrl}
          alt={name}
          sx={{
            width: '100%',
            height: '100%',
            objectFit: 'cover'
          }}
        />
      ) : (
        <Typography sx={{ fontSize: '1.5rem', color: 'text.secondary' }}>
          {fileIcon}
        </Typography>
      )}

      {/* Upload Progress Overlay */}
      {status === 'uploading' && (
        <Box
          sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            width: '100%',
            height: '100%',
            backgroundColor: 'rgba(255, 255, 255, 0.8)',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            flexDirection: 'column'
          }}
        >
          <CircularProgress size={20} />
          <Typography variant="caption" sx={{ mt: 0.5, fontSize: '10px' }}>
            {progress}%
          </Typography>
        </Box>
      )}

      {/* Error Overlay */}
      {status === 'failed' && (
        <Box
          sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            width: '100%',
            height: '100%',
            backgroundColor: 'rgba(244, 67, 54, 0.1)',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center'
          }}
        >
          <Error sx={{ color: 'error.main', fontSize: '1.2rem' }} />
        </Box>
      )}

      {/* Remove Button */}
      <IconButton
        size="small"
        onClick={onRemove}
        sx={{
          position: 'absolute',
          top: -8,
          right: -8,
          width: 20,
          height: 20,
          backgroundColor: 'rgba(0, 0, 0, 0.6)',
          color: 'white',
          border: 'none',
          '&:hover': {
            backgroundColor: 'rgba(244, 67, 54, 0.8)'
          },
          '& .MuiSvgIcon-root': {
            fontSize: 14
          }
        }}
      >
        <Close />
      </IconButton>
    </Box>
    </Tooltip>
  );
}
