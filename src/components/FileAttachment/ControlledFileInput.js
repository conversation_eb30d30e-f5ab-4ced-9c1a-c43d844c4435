'use client';

import React, { useRef } from 'react';
import { v4 as uuidv4 } from 'uuid';
import {
  <PERSON><PERSON><PERSON>,
  Button
} from '@mui/material';
import {
  AttachFile
} from '@mui/icons-material';
import { 
  validateFiles, 
  FILE_SIZE_LIMITS 
} from '@/lib/fileValidation';
import { fileAPI } from '@/lib/fileStorage';

export default function ControlledFileInput({ 
  onFilesChange, 
  disabled = false, 
  maxFiles = FILE_SIZE_LIMITS.MAX_FILES_PER_MESSAGE,
  currentFiles = [],
  onError = () => {}
}) {
  const fileInputRef = useRef(null);

  // Handle file selection
  const handleFileSelect = async (selectedFiles) => {
    console.log('🔧 ControlledFileInput: handleFileSelect called', selectedFiles);
    const filesArray = Array.from(selectedFiles);

    // Reset the file input value to allow re-selecting the same files
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }

    // Validate files
    const validation = validateFiles(filesArray, currentFiles);

    if (!validation.isValid) {
      console.log('🔧 ControlledFileInput: validation failed', validation.errors);
      onError(validation.errors);
      return;
    }

    // Clear any previous errors
    onError([]);

    // Add files to state with pending upload status
    const newFiles = filesArray.map(file => ({
      id: uuidv4(),
      file,
      originalFile: file, // Preserve original File object for WebSocket sending
      name: file.name,
      size: file.size,
      type: file.type,
      status: 'pending', // 'pending', 'uploading', 'completed', 'failed'
      progress: 0,
      uploadedFileId: null,
      previewUrl: null
    }));

    console.log('🔧 ControlledFileInput: newFiles created', newFiles);

    const updatedFiles = [...currentFiles, ...newFiles];
    onFilesChange(updatedFiles);

    // Start upload process
    uploadFiles(newFiles, updatedFiles, onFilesChange);
  };

  // Upload files to storage
  const uploadFiles = async (filesToUpload, _, updateCallback) => {
    console.log('🔧 ControlledFileInput: uploadFiles called', filesToUpload);
    
    // Process each file upload while maintaining current state
    for (const fileData of filesToUpload) {
      try {
        // Update status to uploading
        updateCallback(prevFiles => prevFiles.map(f => 
          f.id === fileData.id 
            ? { ...f, status: 'uploading', progress: 0 }
            : f
        ));
        
        // Simulate upload with progress
        const uploadedFile = await fileAPI.upload([fileData.file], (progressData) => {
          if (progressData.filename === fileData.file.name) {
            updateCallback(prevFiles => prevFiles.map(f => 
              f.id === fileData.id 
                ? { ...f, status: 'uploading', progress: progressData.progress }
                : f
            ));
          }
        });
        
        console.log('🔧 ControlledFileInput: upload completed', uploadedFile);
        
        // Create preview for images
        let previewUrl = null;
        if (fileData.file.type.startsWith('image/')) {
          previewUrl = await createImagePreview(fileData.file);
        }
        
        // Update with completed status
        updateCallback(prevFiles => prevFiles.map(f => 
          f.id === fileData.id 
            ? { 
                ...f, 
                status: 'completed', 
                progress: 100, 
                uploadedFileId: uploadedFile[0].id,
                uploadedFile: uploadedFile[0],
                originalFile: f.originalFile || f.file, // Preserve original File object
                previewUrl 
              }
            : f
        ));
        
      } catch (error) {
        console.error('Upload failed:', error);
        updateCallback(prevFiles => prevFiles.map(f => 
          f.id === fileData.id 
            ? { ...f, status: 'failed', progress: 0, error: error.message }
            : f
        ));
      }
    }
  };

  // Create image preview
  const createImagePreview = (file) => {
    return new Promise((resolve) => {
      const reader = new FileReader();
      reader.onload = () => resolve(reader.result);
      reader.onerror = () => resolve(null);
      reader.readAsDataURL(file);
    });
  };

  // Trigger file input
  const triggerFileInput = () => {
    console.log('🔧 ControlledFileInput: triggerFileInput called', { disabled, hasRef: !!fileInputRef.current });
    if (!disabled && fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  const remainingSlots = maxFiles - currentFiles.length;

  return (
    <>
      {/* File Input */}
      <input
        ref={fileInputRef}
        type="file"
        multiple
        accept=".jpg,.jpeg,.png,.gif,.webp,.pdf,.doc,.docx,.txt,.csv,.json,.xls,.xlsx,.dcm,.dicom"
        style={{ display: 'none' }}
        onChange={(e) => handleFileSelect(e.target.files)}
        disabled={disabled}
      />

      {/* Attachment Button */}
      <Tooltip title={`Attach files (${remainingSlots} remaining)`}>
        <Button
          onClick={triggerFileInput}
          disabled={disabled || remainingSlots <= 0}
          variant="outlined"
          sx={{ 
            minWidth: 34,
            width: 34,
            height: 34,
            p: 0,
            border: '1px solid rgba(0, 0, 0, 0.08)',
            borderRadius: 1.5,
            backgroundColor: '#ffffff',
            color: 'text.primary',
            transition: 'all 0.2s ease',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            '&:hover': {
              backgroundColor: 'rgba(0, 0, 0, 0.02)',
              borderColor: 'rgba(0, 0, 0, 0.15)',
            },
            '&:disabled': {
              backgroundColor: 'rgba(0, 0, 0, 0.02)',
              borderColor: 'rgba(0, 0, 0, 0.08)',
            }
          }}
        >
          <AttachFile sx={{ fontSize: 18 }} />
        </Button>
      </Tooltip>
    </>
  );
}