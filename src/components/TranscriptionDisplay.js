"use client"

import { useState, useEffect, useRef } from "react"
import { Box, Typography, CircularProgress, IconButton, Collapse, TextField } from "@mui/material"
import { keyframes } from "@mui/system"
import { ExpandMore, ExpandLess } from "@mui/icons-material"
import { audioAPI } from "../lib/api"

// Sliding animation for new text
const slideUp = keyframes`
  0% {
    transform: translateY(20px);
    opacity: 0;
  }
  10% {
    transform: translateY(0);
    opacity: 1;
  }
  90% {
    transform: translateY(0);
    opacity: 1;
  }
  100% {
    transform: translateY(-20px);
    opacity: 0;
  }
`

// Pulsing animation for listening state
const pulse = keyframes`
  0%, 100% {
    opacity: 0.5;
  }
  50% {
    opacity: 1;
  }
`

export default function TranscriptionDisplay({ isRecording, isPaused = false, onTranscriptionUpdate }) {
  const [transcriptions, setTranscriptions] = useState([])
  const [currentInterim, setCurrentInterim] = useState(null) // deepgram interim only
  const [isExpanded, setIsExpanded] = useState(true)
  const [hasRecorded, setHasRecorded] = useState(false)
  const [editingId, setEditingId] = useState(null)
  const [editText, setEditText] = useState("")
  const listEndRef = useRef(null) // legacy marker (not required anymore)
  const scrollContainerRef = useRef(null)

  // Audio streaming/polling refs
  const sessionIdRef = useRef(null)
  const audioCtxRef = useRef(null)
  const processorRef = useRef(null)
  const sourceRef = useRef(null)
  const mediaStreamRef = useRef(null)
  const sequenceRef = useRef(0)
  const pollTimerRef = useRef(null)
  const lastTranscriptTsRef = useRef(0)
  // Streaming batching/backpressure
  const sendTimerRef = useRef(null)
  const sendQueueRef = useRef([]) // holds Uint8Array PCM16 chunks
  const sendingRef = useRef(false)

  // Save transcriptions when recording stops or pauses
  useEffect(() => {
    if (!isRecording || isPaused) {
      if (editingId !== null) {
        handleSaveEdit()
      }
      if (onTranscriptionUpdate) {
        onTranscriptionUpdate(transcriptions)
      }
    }
  }, [isRecording, isPaused])

  useEffect(() => {
    if (isRecording) {
      setHasRecorded(true)
    }

    // Handle chunk transcription events
    const handleChunkTranscription = (event) => {
      const data = event.detail
      console.log("TranscriptionDisplay: Chunk transcription received:", {
        chunkId: data.chunkId,
        requestId: data.requestId,
        isFinalChunk: data.isFinalChunk,
        provider: data.type || data.provider || "deepgram",
        transcriptionLength: data.transcription?.length,
      })

      if (data.transcription && data.transcription.trim()) {
        // Add new transcription
        const provider = data.type || data.provider || "deepgram"
        const sortKey = Number(data.serverTimestamp || data.timestamp || Date.now())
        const newTranscription = {
          id: data.id || `${provider}-${sortKey}-${Math.random().toString(36).slice(2, 7)}`,
          text: data.transcription,
          sortKey,
          chunkId: data.chunkId,
          requestId: data.requestId,
          isFinalChunk: !!data.isFinalChunk,
          provider,
        }

        if (provider === "deepgram" && !newTranscription.isFinalChunk) {
          // Keep interim separately so it always renders at bottom
          setCurrentInterim((prev) => {
            // Preserve id/sortKey if same interim stream
            if (prev) {
              newTranscription.id = prev.id
              newTranscription.sortKey = prev.sortKey || newTranscription.sortKey
            }
            return newTranscription
          })
        } else {
          if (provider === "gemini") {
            // Gemini transcription - replace any existing Deepgram final transcription for similar timeframe
            setTranscriptions((prev) => {
              const norm = (s) => (s || "").trim().toLowerCase()
              const filtered = prev.filter((item) => {
                if (item.provider === "deepgram" && item.isFinalChunk) {
                  const timeDiff = Math.abs((item.sortKey || 0) - sortKey)
                  const similarText = norm(item.text) === norm(newTranscription.text)
                  return !(similarText || timeDiff <= 30000) // drop if similar or within 30s
                }
                return true // Keep all non-Deepgram final transcriptions
              })

              // Avoid duplicate Gemini messages with same text within the same timeframe
              const hasSimilarGemini = filtered.some((item) => {
                if (item.provider !== "gemini") return false
                const timeDiff = Math.abs((item.sortKey || 0) - sortKey)
                return timeDiff <= 5000 && norm(item.text) === norm(newTranscription.text)
              })
              if (hasSimilarGemini) {
                return filtered
              }

              // Add the new Gemini transcription
              const next = [...filtered, newTranscription]
              next.sort((a, b) => (a.sortKey || 0) - (b.sortKey || 0))
              return next
            })
            // Clear interim since we have a better transcription
            setCurrentInterim(null)
          } else if (provider === "deepgram" && newTranscription.isFinalChunk) {
            // Deepgram final transcription - only add if no Gemini transcription exists for similar timeframe
            setTranscriptions((prev) => {
              // Check if there's already a Gemini transcription for similar timeframe
              const hasGeminiForTimeframe = prev.some((item) => {
                if (item.provider === "gemini") {
                  const timeDiff = Math.abs((item.sortKey || 0) - sortKey)
                  return timeDiff <= 5000 // Within 5 seconds
                }
                return false
              })

              if (hasGeminiForTimeframe) {
                // Don't add Deepgram final if Gemini already exists
                return prev
              }

              // Add Deepgram final transcription
              const norm = (s) => (s || "").trim().toLowerCase()
              const exists = prev.some(
                (obj) =>
                  obj.id === newTranscription.id ||
                  (obj.text === newTranscription.text &&
                    obj.provider === newTranscription.provider &&
                    Math.abs((obj.sortKey || 0) - (newTranscription.sortKey || 0)) < 5),
              )
              // Also avoid dup by similar text in close timeframe for Deepgram
              const similarTextExists = prev.some((obj) => {
                if (obj.provider !== "deepgram") return false
                const timeDiff = Math.abs((obj.sortKey || 0) - (newTranscription.sortKey || 0))
                return timeDiff <= 2000 && norm(obj.text) === norm(newTranscription.text)
              })
              const next = exists || similarTextExists ? prev : [...prev, newTranscription]
              next.sort((a, b) => (a.sortKey || 0) - (b.sortKey || 0))
              return next
            })
            setCurrentInterim(null)
          }
        }
      }
    }

    // Add event listener for chunk transcriptions
    window.addEventListener("chunk_transcription", handleChunkTranscription)

    return () => {
      window.removeEventListener("chunk_transcription", handleChunkTranscription)
    }
  }, [isRecording])

  // Helper: downsample Float32Array from inputSampleRate to 16000 and encode PCM16 LE
  const encodePCM16 = (float32Buffer, inputSampleRate) => {
    const targetRate = 16000
    if (inputSampleRate === targetRate) {
      const pcm = new Int16Array(float32Buffer.length)
      for (let i = 0; i < float32Buffer.length; i++) {
        const s = Math.max(-1, Math.min(1, float32Buffer[i]))
        pcm[i] = s < 0 ? s * 0x8000 : s * 0x7fff
      }
      return pcm
    }
    const ratio = inputSampleRate / targetRate
    const newLength = Math.floor(float32Buffer.length / ratio)
    const pcm = new Int16Array(newLength)
    let offsetResult = 0
    let offsetBuffer = 0
    while (offsetResult < newLength) {
      const nextOffsetBuffer = Math.round((offsetResult + 1) * ratio)
      let accum = 0,
        count = 0
      for (let i = offsetBuffer; i < nextOffsetBuffer && i < float32Buffer.length; i++) {
        accum += float32Buffer[i]
        count++
      }
      const s = Math.max(-1, Math.min(1, accum / count))
      pcm[offsetResult] = s < 0 ? s * 0x8000 : s * 0x7fff
      offsetResult++
      offsetBuffer = nextOffsetBuffer
    }
    return pcm
  }

  const handleEdit = (id, currentText) => {
    setEditingId(id)
    setEditText(currentText)
  }

  const handleSaveEdit = () => {
    if (editingId === null) return

    setTranscriptions((prev) =>
      prev.map((item) => (item.id === editingId ? { ...item, text: editText, isEdited: true } : item)),
    )
    setEditingId(null)
  }

  const handleKeyDown = (e) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault()
      handleSaveEdit()
    } else if (e.key === "Escape") {
      setEditingId(null)
    }
  }

  const toggleExpand = () => {
    setIsExpanded(!isExpanded)
  }

  // Start/stop audio session and streaming when isRecording changes
  useEffect(() => {
    let stopped = false
    const start = async () => {
      if (!isRecording || isPaused) return
      try {
        // Start backend session
        const startResp = await audioAPI.startSession()
        const sessionId = startResp.sessionId
        sessionIdRef.current = sessionId

        // Mic capture
        const stream = await navigator.mediaDevices.getUserMedia({
          audio: { channelCount: 1, noiseSuppression: true, echoCancellation: true },
          video: false,
        })
        mediaStreamRef.current = stream

        const AudioContextCtor = window.AudioContext || window.webkitAudioContext
        const audioCtx = new AudioContextCtor({ sampleRate: 48000 })
        audioCtxRef.current = audioCtx

        const source = audioCtx.createMediaStreamSource(stream)
        sourceRef.current = source

        // Larger buffer to reduce callback churn
        const processor = audioCtx.createScriptProcessor(8192, 1, 1)
        processorRef.current = processor

        source.connect(processor)
        processor.connect(audioCtx.destination)

        processor.onaudioprocess = (e) => {
          if (stopped || isPaused) return
          const input = e.inputBuffer.getChannelData(0)
          const pcm16 = encodePCM16(input, audioCtx.sampleRate)
          // push raw bytes to queue (Uint8Array view over underlying buffer)
          sendQueueRef.current.push(new Uint8Array(pcm16.buffer.slice(0)))
        }

        // Flush queue every ~200ms to form ~200ms frames (3200 samples @16k -> 6400 bytes)
        const flush = async () => {
          if (stopped || isPaused || sendingRef.current) return
          const queue = sendQueueRef.current
          if (!queue.length) return
          // Concatenate all queued chunks into one payload (bounded to ~200ms worth ~6400-12k bytes)
          let total = 0
          for (let i = 0; i < queue.length; i++) total += queue[i].length
          // Cap to avoid oversized payloads
          const maxBytes = 16000 /*samples/sec*/ * 2 /*bytes*/ * 0.25 // ~250ms
          let bytesToTake = Math.min(total, maxBytes)
          const out = new Uint8Array(bytesToTake)
          let offset = 0
          while (queue.length && bytesToTake > 0) {
            const chunk = queue[0]
            const take = Math.min(bytesToTake, chunk.length)
            out.set(chunk.subarray(0, take), offset)
            offset += take
            bytesToTake -= take
            if (take < chunk.length) {
              // put back remainder
              queue[0] = chunk.subarray(take)
            } else {
              queue.shift()
            }
          }
          // Base64 encode and send
          sendingRef.current = true
          try {
            let binary = ""
            for (let i = 0; i < out.length; i++) binary += String.fromCharCode(out[i])
            const b64 = btoa(binary)
            sequenceRef.current += 1
            await audioAPI.sendChunk(undefined, sessionIdRef.current, {
              audioData: b64,
              mimeType: "audio/pcm16",
              sequenceNumber: sequenceRef.current,
            })
          } catch (err) {
            console.error("Stream send error:", err)
          } finally {
            sendingRef.current = false
          }
        }
        sendTimerRef.current = setInterval(flush, 50)

        // Start polling transcripts
        const tick = async () => {
          if (stopped) return
          try {
            const data = await audioAPI.pollTranscripts(
              undefined,
              sessionIdRef.current,
              lastTranscriptTsRef.current || undefined,
            )
            if (data && Array.isArray(data.transcripts) && data.transcripts.length) {
              for (const t of data.transcripts) {
                // Update last seen timestamp
                if (typeof t.timestamp === "number") {
                  lastTranscriptTsRef.current = Math.max(lastTranscriptTsRef.current || 0, t.timestamp)
                }
                // Dispatch to existing UI handler
                const text = t.text || t.transcription || ""
                // Ignore ultra-short interim to reduce noise
                if ((t.type === "deepgram" || t.isFinal !== undefined) && !t.isFinal && text.trim().length < 2) continue
                if (!text) continue

                const inferredType = t.type || (t.isFinal !== undefined ? "deepgram" : "gemini")
                window.dispatchEvent(
                  new CustomEvent("chunk_transcription", {
                    detail: {
                      transcription: text,
                      type: inferredType,
                      isFinalChunk: t.isFinal !== undefined ? !!t.isFinal : true,
                      chunkId: t.id || `srv-${Date.now()}`,
                      requestId: sessionIdRef.current,
                      timestamp: t.timestamp,
                    },
                  }),
                )
              }
            }
          } catch (err) {
            console.warn("Transcript poll error:", err?.message || err)
          }
        }
        pollTimerRef.current = setInterval(tick, 500)
      } catch (err) {
        console.error("Failed to start audio session/stream:", err)
      }
    }

    const stop = async () => {
      stopped = true
      if (pollTimerRef.current) {
        clearInterval(pollTimerRef.current)
        pollTimerRef.current = null
      }
      if (sendTimerRef.current) {
        clearInterval(sendTimerRef.current)
        sendTimerRef.current = null
      }
      if (processorRef.current) {
        try {
          processorRef.current.disconnect()
        } catch {}
        processorRef.current.onaudioprocess = null
        processorRef.current = null
      }
      if (sourceRef.current) {
        try {
          sourceRef.current.disconnect()
        } catch {}
        sourceRef.current = null
      }
      if (mediaStreamRef.current) {
        try {
          mediaStreamRef.current.getTracks().forEach((t) => t.stop())
        } catch {}
        mediaStreamRef.current = null
      }
      if (audioCtxRef.current) {
        try {
          await audioCtxRef.current.close()
        } catch {}
        audioCtxRef.current = null
      }
      if (sessionIdRef.current) {
        try {
          // If we have an interim at pause/stop, promote it to a Deepgram final locally
          if (currentInterim && currentInterim.text && currentInterim.text.trim()) {
            const nowTs = Date.now()
            window.dispatchEvent(
              new CustomEvent("chunk_transcription", {
                detail: {
                  transcription: currentInterim.text,
                  type: "deepgram",
                  isFinalChunk: true,
                  chunkId: currentInterim.chunkId || `local-final-${nowTs}`,
                  requestId: sessionIdRef.current,
                  timestamp: nowTs,
                },
              }),
            )
            setCurrentInterim(null)
          }

          await audioAPI.endSession(undefined, sessionIdRef.current)

          // After ending, try to fetch any final Gemini transcripts for a short window
          const tryPollFinals = async () => {
            const maxAttempts = 18 // ~18 * 300ms = ~5.4s
            for (let i = 0; i < maxAttempts; i++) {
              try {
                const data = await audioAPI.pollTranscripts(
                  undefined,
                  sessionIdRef.current,
                  lastTranscriptTsRef.current || undefined,
                )
                if (data && Array.isArray(data.transcripts) && data.transcripts.length) {
                  let sawGemini = false
                  for (const t of data.transcripts) {
                    if (typeof t.timestamp === "number") {
                      lastTranscriptTsRef.current = Math.max(lastTranscriptTsRef.current || 0, t.timestamp)
                    }
                    const text = t.text || t.transcription || ""
                    if ((t.type === "deepgram" || t.isFinal !== undefined) && !t.isFinal && text.trim().length < 2)
                      continue
                    if (!text) continue

                    const inferredType = t.type || (t.isFinal !== undefined ? "deepgram" : "gemini")
                    if (inferredType === "gemini") sawGemini = true

                    window.dispatchEvent(
                      new CustomEvent("chunk_transcription", {
                        detail: {
                          transcription: text,
                          type: inferredType,
                          isFinalChunk: t.isFinal !== undefined ? !!t.isFinal : true,
                          chunkId: t.id || `srv-${Date.now()}`,
                          requestId: sessionIdRef.current,
                          timestamp: t.timestamp,
                        },
                      }),
                    )
                  }
                  // If we saw Gemini in this batch, we can shorten further waiting
                  if (sawGemini && i > 2) break
                }
              } catch (e) {
                console.warn("Finalization poll error:", e?.message || e)
              }
              await new Promise((r) => setTimeout(r, 300))
            }
          }
          await tryPollFinals()
        } catch {}
      }
      sessionIdRef.current = null
      sequenceRef.current = 0
      lastTranscriptTsRef.current = 0
      sendQueueRef.current = []
      sendingRef.current = false
      // Clear any lingering interim once we stop
      setCurrentInterim(null)
    }

    if (isRecording && !isPaused) {
      start()
    } else {
      // stop when toggled off or paused
      stop()
    }

    return () => {
      stop()
    }
  }, [isRecording, isPaused])

  // Auto-scroll inside the scrollable container to the bottom
  useEffect(() => {
    const el = scrollContainerRef.current
    if (!el) return
    // Use rAF to ensure DOM updates have been painted
    requestAnimationFrame(() => {
      try {
        el.scrollTop = el.scrollHeight
      } catch {}
    })
  }, [transcriptions.length, isExpanded, isRecording, isPaused, currentInterim?.text])

  if (!hasRecorded) {
    return null
  }

  return (
    <Box
      sx={{
        // position: 'fixed',
        bottom: isExpanded ? "70px" : "60",
        // left: '38.8%',
        // transform: 'translateX(-50%)',
        width: "590px",
        maxHeight: "300px",
        overflow: "hidden",
        backgroundColor: "rgba(255, 255, 255, 0.95)",
        border: "1px solid rgba(0, 0, 0, 0.1)",
        boxShadow: "0 4px 12px rgba(0, 0, 0, 0.1)",
        borderRadius: "8px 8px 0 0",
        transition: "all 0.3s ease",
        // zIndex: 1000,
      }}
    >
      {/* Header with toggle button */}
      <Box
        sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          padding: "8px 16px",
          backgroundColor: "#f5f5f5",
          borderBottom: "1px solid #e0e0e0",
          cursor: "pointer",
        }}
        onClick={toggleExpand}
      >
        <Typography variant="subtitle2" sx={{ color: "#666" }}>
          Transcription
        </Typography>
        <IconButton
          size="small"
          onClick={(e) => {
            e.stopPropagation()
            toggleExpand()
          }}
        >
          {isExpanded ? <ExpandMore /> : <ExpandLess />}
        </IconButton>
      </Box>

      <Collapse in={isExpanded}>
        <Box
          sx={{
            padding: "16px",
            height: "200px",
            overflowY: "auto",
            "&::-webkit-scrollbar": {
              width: "6px",
            },
            "&::-webkit-scrollbar-track": {
              background: "#f1f1f1",
            },
            "&::-webkit-scrollbar-thumb": {
              background: "#888",
              borderRadius: "3px",
            },
            "&::-webkit-scrollbar-thumb:hover": {
              background: "#555",
            },
          }}
          ref={scrollContainerRef}
        >
          {transcriptions.length === 0 ? (
            <Box
              sx={{
                display: "flex",
                flexDirection: "row",
                alignItems: "center",
                justifyContent: "center",
                height: "100%",
                gap: 1,
              }}
            >
              {isRecording && !isPaused && (
                <CircularProgress
                  size={20}
                  thickness={3}
                  sx={{
                    color: "#ef4444",
                    animation: `${pulse} 1.5s ease-in-out infinite`,
                  }}
                />
              )}
              <Typography
                variant="body2"
                sx={{
                  color: "#666666",
                  fontSize: "0.9rem",
                  animation: isRecording && !isPaused ? `${pulse} 1.5s ease-in-out infinite` : "none",
                }}
              >
                {isRecording
                  ? isPaused
                    ? "Recording paused"
                    : "Listening..."
                  : "Start speaking to see transcriptions"}
              </Typography>
            </Box>
          ) : (
            transcriptions.map((transcription, index) => (
              <Box
                key={transcription.id}
                sx={{
                  marginBottom: 2,
                  padding: "8px 12px",
                  backgroundColor: index === transcriptions.length - 1 ? "rgba(0, 0, 0, 0.02)" : "transparent",
                  borderRadius: "4px",
                  borderLeft:
                    index === transcriptions.length - 1
                      ? `3px solid ${transcription.provider === "gemini" ? "#7e22ce" : "#3f51b5"}`
                      : "none",
                  position: "relative",
                  "&:hover .edit-button": {
                    opacity: 1,
                  },
                }}
              >
                {editingId === transcription.id ? (
                  <TextField
                    fullWidth
                    multiline
                    variant="outlined"
                    value={editText}
                    onChange={(e) => setEditText(e.target.value)}
                    onKeyDown={handleKeyDown}
                    onBlur={handleSaveEdit}
                    autoFocus
                    size="small"
                    sx={{
                      "& .MuiOutlinedInput-root": {
                        padding: "8px",
                        fontSize: "0.875rem",
                        lineHeight: 1.5,
                      },
                      "& .MuiOutlinedInput-input": {
                        padding: "8px",
                      },
                    }}
                  />
                ) : (
                  <Box
                    onClick={() => handleEdit(transcription.id, transcription.text)}
                    sx={{
                      cursor: "pointer",
                      padding: "8px",
                      borderRadius: "4px",
                      "&:hover": {
                        backgroundColor: "rgba(0, 0, 0, 0.02)",
                      },
                    }}
                  >
                    <Typography
                      variant="body2"
                      sx={{
                        color: transcription.isEdited
                          ? "#1976d2"
                          : transcription.provider === "gemini"
                            ? "#6b21a8"
                            : "text.primary",
                        lineHeight: 1.5,
                        whiteSpace: "pre-wrap",
                      }}
                    >
                      {transcription.text}
                      {transcription.isEdited && (
                        <Typography component="span" variant="caption" color="text.secondary" ml={1}>
                          (edited)
                        </Typography>
                      )}
                      <Typography
                        component="span"
                        variant="caption"
                        ml={1}
                        sx={{ color: transcription.provider === "gemini" ? "#7e22ce" : "#3f51b5" }}
                      >
                        [{transcription.provider === "gemini" ? "Gemini" : "Deepgram"}
                        {!transcription.isFinalChunk && transcription.provider === "deepgram" ? " • interim" : ""}]
                      </Typography>
                    </Typography>
                  </Box>
                )}
                {index === transcriptions.length - 1 && isRecording && !isPaused && (
                  <Box
                    sx={{
                      width: "8px",
                      height: "8px",
                      backgroundColor: "#ef4444",
                      borderRadius: "50%",
                      display: "inline-block",
                      marginRight: "8px",
                      animation: `${pulse} 1.5s ease-in-out infinite`,
                    }}
                  />
                )}
              </Box>
            ))
          )}
          {currentInterim && isRecording && !isPaused && (
            <Box
              key={currentInterim.id}
              sx={{
                marginBottom: 2,
                padding: "8px 12px",
                backgroundColor: "rgba(0, 0, 0, 0.02)",
                borderRadius: "4px",
                borderLeft: `3px solid #3f51b5`,
                position: "relative",
              }}
            >
              <Typography
                variant="body2"
                sx={{
                  color: "text.primary",
                  lineHeight: 1.5,
                  whiteSpace: "pre-wrap",
                }}
              >
                {currentInterim.text}
                <Typography component="span" variant="caption" ml={1} sx={{ color: "#3f51b5" }}>
                  [Deepgram • interim]
                </Typography>
              </Typography>
            </Box>
          )}
        </Box>
      </Collapse>
      {/* listEndRef kept for backward-compat; auto-scroll now uses scrollContainerRef */}
      <div ref={listEndRef} style={{ height: 0 }} />
    </Box>
  )
}
