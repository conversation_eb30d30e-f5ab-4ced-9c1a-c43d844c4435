"use client";
import React, { useState, useEffect, useRef } from "react";
import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";
import { Box } from "@mui/material";

const MarkdownTextEditor = ({
  markdownContent,
  setMarkdownContent,
  patientCasePlaceholder,
  onSave,
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const editableRef = useRef(null);

  const handleDoubleClick = () => {
    setIsEditing(true);
    // Focus the editable div after a short delay to ensure it's rendered
    setTimeout(() => {
      if (editableRef.current) {
        editableRef.current.focus();
        // Place cursor at the end
        const range = document.createRange();
        const selection = window.getSelection();
        range.selectNodeContents(editableRef.current);
        range.collapse(false);
        selection.removeAllRanges();
        selection.addRange(range);
      }
    }, 10);
  };

  // Function to convert HTML back to markdown
  const convertHTMLToMarkdown = (htmlContent) => {
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = htmlContent;
    
    let markdown = '';
    const processNode = (node) => {
      if (node.nodeType === Node.TEXT_NODE) {
        return node.textContent;
      } else if (node.nodeType === Node.ELEMENT_NODE) {
        const tagName = node.tagName.toLowerCase();
        const textContent = node.textContent;
        
        switch (tagName) {
          case 'h1':
            return `# ${textContent}`;
          case 'h2':
            return `## ${textContent}`;
          case 'h3':
            return `### ${textContent}`;
          case 'div':
            // Check if it's a list item (contains bullet or number)
            if (textContent.startsWith('• ')) {
              return `- ${textContent.substring(2)}`;
            } else if (/^\d+\.\s/.test(textContent)) {
              return textContent;
            } else {
              return textContent;
            }
          case 'br':
            return '\n';
          default:
            return textContent;
        }
      }
      return '';
    };

    const lines = [];
    for (let child of tempDiv.childNodes) {
      const processedContent = processNode(child);
      if (processedContent.trim() || child.tagName?.toLowerCase() === 'br') {
        lines.push(processedContent);
      }
    }
    
    return lines.join('\n').replace(/\n+/g, '\n').trim();
  };

  const handleBlur = () => {
    setIsEditing(false);
    // Update the markdown content when editing is done
    if (editableRef.current) {
      const htmlContent = editableRef.current.innerHTML;
      const markdownContent = convertHTMLToMarkdown(htmlContent);
      setMarkdownContent(markdownContent);
      // Save the content using the provided save function
      onSave?.(markdownContent);
    }
  };

  const handleKeyDown = (e) => {
    // Handle Enter key to create new lines
    if (e.key === 'Enter') {
      e.preventDefault();
      document.execCommand('insertLineBreak', false, null);
    }
  };

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (
        editableRef.current &&
        !editableRef.current.contains(event.target)
      ) {
        console.log("Clicked outside the box");
        setIsEditing(false);
        // Update content when clicking outside
        if (editableRef.current) {
          const htmlContent = editableRef.current.innerHTML;
          const markdownContent = convertHTMLToMarkdown(htmlContent);
          setMarkdownContent(markdownContent);
          // Save the content when clicking outside
          onSave?.(markdownContent);
        }
      }
    };

    if (isEditing) {
      document.addEventListener("mousedown", handleClickOutside);
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isEditing, setMarkdownContent]);

  // Function to convert markdown to styled HTML for editing
  const convertMarkdownToStyledHTML = (markdown) => {
    if (!markdown) return patientCasePlaceholder || '';
    
    return markdown
      .split('\n')
      .map(line => {
        // Headers
        if (line.startsWith('### ')) {
          return `<h3 style="font-size: 1.25rem; font-weight: 700; margin-bottom: 1rem; margin-top: 1.5rem; margin-bottom:12px; line-height: 1.6;">${line.substring(4)}</h3>`;
        }
        if (line.startsWith('## ')) {
          return `<h2 style="font-size: 1.5rem; font-weight: 700; margin-bottom: 1.2rem; margin-top: 2rem; margin-bottom:12px; line-height: 1.6;">${line.substring(3)}</h2>`;
        }
        if (line.startsWith('# ')) {
          return `<h1 style="font-size: 1.75rem; font-weight: 700; margin-bottom: 1.5rem; margin-top: 2rem; margin-bottom:12px; line-height: 1.6;">${line.substring(2)}</h1>`;
        }
        
        // Lists
        if (line.startsWith('- ') || line.startsWith('* ')) {
          return `<div style="margin-bottom: 0.5rem; line-height: 1.6; font-size: 16px; padding-left: 1.5rem;">• ${line.substring(2)}</div>`;
        }
        if (/^\d+\.\s/.test(line)) {
          const match = line.match(/^(\d+)\.\s(.+)/);
          return `<div style="margin-bottom: 0.5rem; line-height: 1.6; font-size: 16px; padding-left: 1.5rem;">${match[1]}. ${match[2]}</div>`;
        }
        
        // Empty lines
        if (line.trim() === '') {
          return '<br>';
        }
        
        // Regular paragraphs
        return `<div style="margin-bottom: 1.5rem; line-height: 1.6; font-size: 16px; margin: 0;">${line}</div>`;
      })
      .join('');
  };

  const markdownStyles = {
    paddingLeft: "1rem",
    paddingRight: "1rem",
    paddingTop: "1rem",

    paddingBottom: "1rem",
    minHeight: "200px",
    outline: "none",
    "& p": {
      margin: 0,
      marginBottom: 1.5,
      lineHeight: 1.6,
      fontSize: "16px",
      "&:last-child": { marginBottom: 0 },
    },
    "& h1": {
      fontSize: "1.75rem",
      fontWeight: 700,
      marginBottom: 1.5,
      marginTop: 2,
      "&:first-of-type": { marginTop: 0 },
    },
    "& h2": {
      fontSize: "1.5rem",
      fontWeight: 700,
      marginBottom: 1.2,
      marginTop: 2,
      "&:first-of-type": { marginTop: 2 },
    },
    "& h3": {
      fontSize: "1.25rem",
      fontWeight: 700,
      marginBottom: 1,
      marginTop: 1.5,
      "&:first-of-type": { marginTop: 0 },
    },
    "& ul, & ol": {
      margin: 0,
      paddingLeft: 2.5,
      marginBottom: 1.5,
      fontSize: "16px",
      "&:last-child": { marginBottom: 0 },
    },
    "& li": {
      marginBottom: 0.5,
      lineHeight: 1.6,
      fontSize: "16px",
    },
    "& blockquote": {
      borderLeft: "4px solid",
      borderColor: "primary.main",
      paddingLeft: 2,
      paddingY: 1,
      margin: 0,
      marginBottom: 1.5,
      fontStyle: "italic",
      backgroundColor: "rgba(0, 0, 0, 0.02)",
      borderRadius: 1,
    },
    "& code": {
      backgroundColor:
        "rgba(var(--mui-palette-primary-mainChannel) / 0.08)",
      color: "primary.dark",
      padding: "2px 6px",
      borderRadius: 1.5,
      fontFamily: "monospace",
      fontSize: "0.875em",
    },
    "& pre": {
      backgroundColor: "rgba(0, 0, 0, 0.03)",
      padding: 2,
      borderRadius: 1,
      overflow: "auto",
      marginBottom: 1.5,
      "& code": {
        backgroundColor: "transparent",
        padding: 0,
        fontSize: "0.875rem",
      },
    },
  };

  return (
    <Box
      onDoubleClick={handleDoubleClick}
      style={{overflow:"auto"}}
      sx={markdownStyles}
    >
      {isEditing ? (
        <div
          ref={editableRef}
          contentEditable
          suppressContentEditableWarning
          onBlur={handleBlur}
          onKeyDown={handleKeyDown}
          style={{
            outline: 'none',
            minHeight: 'inherit',
            whiteSpace: 'pre-wrap',
            wordWrap: 'break-word',
            fontFamily: 'inherit',
            fontSize: 'inherit',
            lineHeight: 'inherit',
          }}
          dangerouslySetInnerHTML={{
            __html: convertMarkdownToStyledHTML(markdownContent || patientCasePlaceholder),
          }}
        />
      ) : (
        <ReactMarkdown remarkPlugins={[remarkGfm]}>
          {markdownContent || patientCasePlaceholder}
        </ReactMarkdown>
      )}
    </Box>
  );
};

export default MarkdownTextEditor;