// TranscriptionPortal.js
"use client";

import ReactDOM from "react-dom";
import TranscriptionDisplay from "./TranscriptionDisplay";

export default function TranscriptionPortal({ isRecording, isPaused }) {
  if (typeof window === "undefined") return null;

  const portalTarget = document.getElementById("transcription-root");
  if (!portalTarget) return null;

  return ReactDOM.createPortal(
    <TranscriptionDisplay isRecording={isRecording} isPaused={isPaused} />,
    portalTarget
  );
}
