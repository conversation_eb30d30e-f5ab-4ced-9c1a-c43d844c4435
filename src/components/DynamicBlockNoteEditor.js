"use client";

import dynamic from "next/dynamic";

// Dynamic import of BlockNoteEditor with SSR disabled
export const BlockNoteEditor = dynamic(
  () => import("./BlockNoteEditor"),
  { 
    ssr: false,
    loading: () => (
      <div style={{ 
        padding: "16px", 
        minHeight: "400px",
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        color: "#666"
      }}>
        Loading editor...
      </div>
    )
  }
);

export default BlockNoteEditor;