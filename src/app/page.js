'use client';

import Head from 'next/head';
import { ThemeProvider } from '@mui/material/styles';
import { CssBaseline } from '@mui/material';
import theme from '@/theme/theme';
import LoginForm from '@/components/Auth/LoginForm';
import DashboardLayout from '@/components/Dashboard/DashboardLayout';
import AuthWrapper from '@/components/Auth/AuthWrapper';

export default function Home() {
  return (
    <>
      <Head>
        <title>August for Doctors</title>
        <meta name="description" content="Exclusively for Verified Doctors, Students & Nurses in India" />
        <meta property="og:title" content="August for Doctors" />
        <meta property="og:description" content="Exclusively for Verified Doctors, Students & Nurses in India" />
        <meta property="og:image" content="https://res.cloudinary.com/dpgnd3ad7/image/upload/v1752409575/august-for-doctors-meta_f2ps1f.jpg" />
        <meta property="og:image:width" content="1200" />
        <meta property="og:image:height" content="630" />
        <meta property="og:image:alt" content="August for Doctors" />
        <meta name="twitter:card" content="summary_large_image" />
        <meta name="twitter:title" content="August for Doctors" />
        <meta name="twitter:description" content="Exclusively for Verified Doctors, Students & Nurses in India" />
        <meta name="twitter:image" content="https://res.cloudinary.com/dpgnd3ad7/image/upload/v1752409575/august-for-doctors-meta_f2ps1f.jpg" />
      </Head>
      <ThemeProvider theme={theme}>
        <CssBaseline />
        <AuthWrapper>
          {({ isAuthenticated, user, onLoginSuccess, onLogout }) => (
            isAuthenticated ? (
              <DashboardLayout 
                user={user} 
                onLogout={onLogout}
                selectedThreadId={null}
                initialThread={null}
                onThreadChange={null}
                onThreadChangew={null}
              />
            ) : (
              <LoginForm onLoginSuccess={onLoginSuccess} />
            )
          )}
        </AuthWrapper>
      </ThemeProvider>
    </>
  );
}
