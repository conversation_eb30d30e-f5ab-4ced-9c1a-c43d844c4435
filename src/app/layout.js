import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_<PERSON><PERSON> } from "next/font/google";
import "./globals.css";

const nunitoSans = Raleway({
  variable: "--font-nunito-sans",
  subsets: ["latin"],
  weight: ["300", "400", "500", "600", "700"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata = {
  title: "August for Doctors",
  description: "Exclusively for Verified Doctors, Students & Nurses in India",
  openGraph: {
    title: "August for Doctors",
    description: "Exclusively for Verified Doctors, Students & Nurses in India",
    images: [
      {
        url: "https://res.cloudinary.com/dpgnd3ad7/image/upload/v1752409575/august-for-doctors-meta_f2ps1f.jpg",
        width: 1200,
        height: 630,
        alt: "August for Doctors"
      }
    ],
  },
  twitter: {
    card: "summary_large_image",
    title: "August for Doctors", 
    description: "Exclusively for Verified Doctors, Students & Nurses in India",
    images: ["https://res.cloudinary.com/dpgnd3ad7/image/upload/v1752409575/august-for-doctors-meta_f2ps1f.jpg"],
  },
};

export default function RootLayout({ children }) {
  return (
    <html lang="en">
      <body
        className={`${nunitoSans.variable} ${geistMono.variable} antialiased`}
      >
        {children}
      </body>
    </html>
  );
}