'use client';

import { useState, useEffect, useCallback } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { ThemeProvider } from '@mui/material/styles';
import { CssBaseline } from '@mui/material';
import theme from '@/theme/theme';
import LoginForm from '@/components/Auth/LoginForm';
import DashboardLayout from '@/components/Dashboard/DashboardLayout';
import AuthWrapper from '@/components/Auth/AuthWrapper';
import { threadsAPI } from '@/lib/api';

export default function ThreadPage() {
  const params = useParams();
  const router = useRouter();
  const [thread, setThread] = useState(null);

  const loadThreadInBackground = useCallback(async () => {
    try {
      const threads = await threadsAPI.getThreads();
      const foundThread = threads.find(t => t.id === params.uuid);
      if (foundThread) {
        // Update the existing thread object instead of replacing it
        setThread(prev => ({
          ...prev,
          ...foundThread
        }));
      } else {
        // Check if this is a newly created thread with optimistic state
        const optimisticMessage = sessionStorage.getItem('optimisticMessage');
        if (optimisticMessage) {
          try {
            const parsed = JSON.parse(optimisticMessage);
            if (parsed.threadId === params.uuid) {
              // This is a new thread that might not be in Gatekeeper yet
              console.log('New thread detected, waiting for it to be created in Gatekeeper...');
              
              // Set a basic thread info and don't redirect
              setThread(prev => ({
                ...prev,
                title: parsed.content?.substring(0, 50) + '...' || 'New Conversation',
                type: parsed.threadType || 'patient-case' // Use thread type from optimistic message
              }));
              
              // Try to reload threads after a delay
              setTimeout(async () => {
                try {
                  const updatedThreads = await threadsAPI.getThreads();
                  const updatedThread = updatedThreads.find(t => t.id === params.uuid);
                  if (updatedThread) {
                    setThread(prev => ({
                      ...prev,
                      ...updatedThread
                    }));
                  }
                } catch (error) {
                  console.error('Error reloading threads:', error);
                }
              }, 2000);
              
              return;
            }
          } catch (parseError) {
            console.error('Error parsing optimistic message:', parseError);
          }
        }
        
        // Only redirect if it's truly not found and not a new thread
        console.warn('Thread not found and not a new thread, redirecting to home');
        router.push('/');
      }
    } catch (error) {
      console.error('Failed to load thread:', error);
      router.push('/');
    }
  }, [params.uuid, router]);

  const loadThread = async () => {
    try {
      const threads = await threadsAPI.getThreads();
      const foundThread = threads.find(t => t.id === params.uuid);
      if (foundThread) {
        setThread(foundThread);
      } else {
        // Thread not found, redirect to dashboard
        router.push('/');
      }
    } catch (error) {
      console.error('Failed to load thread:', error);
      router.push('/');
    }
  };

  const handleAuthChange = useCallback(({ isAuthenticated, user }) => {
    if (isAuthenticated && user) {
      // Check if we have optimistic message data to get the correct thread type
      let threadType = 'patient-case'; // Default type
      const optimisticMessage = sessionStorage.getItem('optimisticMessage');
      if (optimisticMessage) {
        try {
          const parsed = JSON.parse(optimisticMessage);
          if (parsed.threadId === params.uuid && parsed.threadType) {
            threadType = parsed.threadType;
          }
        } catch (parseError) {
          console.error('Error parsing optimistic message for thread type:', parseError);
        }
      }
      
      // Create thread object with UUID from params - no double updates
      const currentThread = {
        id: params.uuid,
        title: 'Loading...', // Will be updated when we get real data
        type: thread // Don't set a default type - let it be determined from the actual thread data
      };
      setThread(currentThread);
      
      // Load the full thread data in background and update the existing thread
      loadThreadInBackground();
    }
  }, [params.uuid, loadThreadInBackground]);

  const handleLoginSuccess = (userData) => {
    loadThread();
  };

  const handleLogout = () => {
    router.push('/');
  };

  console.log("setThread this is in top level",setThread);

  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <AuthWrapper onAuthChange={handleAuthChange}>
        {({ isAuthenticated, user, onLoginSuccess, onLogout }) => (
          isAuthenticated ? (
            <DashboardLayout 
              user={user} 
              onLogout={onLogout}
              selectedThreadId={params.uuid}
              initialThread={thread}
              onThreadChange={setThread}
              onThreadChangew={setThread}
        />
          ) : (
            <LoginForm onLoginSuccess={(userData) => {
              onLoginSuccess(userData);
              handleLoginSuccess(userData);
            }} />
          )
        )}
      </AuthWrapper>
    </ThemeProvider>
  );
}
