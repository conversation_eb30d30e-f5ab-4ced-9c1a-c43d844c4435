import { NextResponse } from 'next/server';

const GATEKEEPER_URL = process.env.GATEKEEPER_URL || 'https://gatekeeper-staging.getbeyondhealth.com';

export async function POST(request, { params }) {
  try {
    // Get the authorization header
    const authHeader = request.headers.get('authorization');
    if (!authHeader) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Read the body from the incoming request
    const body = await request.json();
    const { feedback, providerMessageId, userId } = body || {};

    if (!feedback) {
      return NextResponse.json({ error: 'feedback is required' }, { status: 400 });
    }

    if (!userId) {
      // The backend get-user endpoint returns sanitized data without id; require userId from client
      return NextResponse.json({ error: 'userId is required' }, { status: 400 });
    }

    // Build payload with provided userId and forward to Gatekeeper
    const payload = {
      feedback,
      source: 'WEB',
      providerMessageId,
      user: { userId },
    };

    const response = await fetch(`${GATEKEEPER_URL}/user/practitioner-dashboard/feedback`, {
      method: 'POST',
      headers: {
        'Authorization': authHeader,
        'Content-Type': 'application/json',
        'x-tenant': 'practitioner-dashboard',
      },
      body: JSON.stringify(payload),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      console.error('Feedback submission error:', errorData);
      return NextResponse.json({ error: errorData.error || 'Failed to submit feedback' }, { status: response.status });
    }

    const data = await response.json();
    return NextResponse.json(data);

  } catch (error) {
    console.error('Feedback route error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
