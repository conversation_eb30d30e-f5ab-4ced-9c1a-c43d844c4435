import { NextResponse } from 'next/server';

const GATEKEEPER_BASE_URL = process.env.GATEKEEPER_URL || 'https://gatekeeper-staging.getbeyondhealth.com';
const GATEKEEPER_TENANT = 'practitioner-dashboard';

export async function POST(request) {
  try {
    const authHeader = request.headers.get('authorization');

    const body = await request.json();
    const {
      requestId,
      dialogueId,
      text,
      phoneNumber,
      dialogueType,
      attachments
    } = body;

    // Prepare save payload with all required fields
    const savePayload = {
      text: text,
      sender: "human",
      source: "WEB",
      requestId: requestId,
      timestamp: Date.now(),
      dialogueId: dialogueId,
      phoneNumber: phoneNumber,
      dialogueType: dialogueType,
    };

    // Add attachments if any
    if (attachments && attachments.length > 0) {
      savePayload.attachments = attachments.map(file => ({
        url: file.url,
        name: file.name || file.originalName,
        size: file.size,
        messageType: file.messageType || file.type
      }));
    }

    const response = await fetch(`${GATEKEEPER_BASE_URL}/c/${GATEKEEPER_TENANT}/save`, {
      method: 'POST',
      headers: {
        ...(authHeader && { 'Authorization': authHeader }),
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(savePayload),
    });

    const data = await response.json();

    if (!response.ok) {
      return NextResponse.json(
        { error: data.error || 'Failed to save SOAP notes' },
        { status: response.status }
      );
    }

    return NextResponse.json({
      success: true,
      gatekeeperResponse: data,
      timestamp: new Date(savePayload.timestamp).toISOString()
    });
  } catch (error) {
    console.error('Save SOAP notes error:', error);
    return NextResponse.json(
      { error: 'Failed to save SOAP notes' },
      { status: 500 }
    );
  }
}
