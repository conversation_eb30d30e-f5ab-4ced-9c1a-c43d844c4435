import { NextResponse } from 'next/server';

const GATEKEEPER_URL = process.env.GATEKEEPER_URL || 'https://gatekeeper-staging.getbeyondhealth.com';

export async function POST(request, { params }) {
  try {
    // Get the authorization header
    const authHeader = request.headers.get('authorization');
    if (!authHeader) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Await params in Next.js 15
    const { dialogueId } = await params;
    if (!dialogueId) {
      return NextResponse.json({ error: 'Dialogue ID is required' }, { status: 400 });
    }

    // Forward the request to Gatekeeper with all relevant headers
    const response = await fetch(`${GATEKEEPER_URL}/user/practitioner-dashboard/dialogues/${dialogueId}/generate-title-and-summary`, {
      method: 'POST',
      headers: {
        'Authorization': authHeader,
        'Content-Type': 'application/json',
        'Accept': '*/*',
        'Accept-Encoding': 'gzip, deflate',
        'Accept-Language': '*',
        'User-Agent': 'node',
        'x-tenant': 'practitioner-dashboard',
      },
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      console.error('Generate title/summary error:', errorData);
      return NextResponse.json(
        { error: errorData.error || 'Failed to generate title and summary' },
        { status: response.status }
      );
    }

    const data = await response.json();
    console.log(`Generated title/summary for dialogue: ${dialogueId}`);

    return NextResponse.json(data);
  } catch (error) {
    console.error('Generate title/summary error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}