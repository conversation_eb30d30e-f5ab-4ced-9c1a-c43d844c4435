import { NextResponse } from 'next/server';

const GATEKEEPER_URL = process.env.GATEKEEPER_URL || 'https://gatekeeper-staging.getbeyondhealth.com';

export async function POST(request, { params }) {
  try {
    // Get the authorization header
    const authHeader = request.headers.get('authorization');
    if (!authHeader) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Await params in Next.js 15
    const { dialogueId } = await params;
    if (!dialogueId) {
      return NextResponse.json({ error: 'Dialogue ID is required' }, { status: 400 });
    }

    // Parse request body to get currentEditedNotes
    const body = await request.json().catch(() => ({}));
    const { currentEditedNotes } = body;

    console.log(`[SOAP Notes] Forwarding manual SOAP generation request for dialogue: ${dialogueId}`, {
      hasEditedNotes: !!currentEditedNotes,
      editedNotesLength: currentEditedNotes?.length || 0
    });

    // Build request body for Gatekeeper
    const requestBody = {};
    if (currentEditedNotes) {
      requestBody.currentEditedNotes = currentEditedNotes;
    }

    // Forward the request to Gatekeeper (external user routes are at /user/practitioner-dashboard)
    const response = await fetch(`${GATEKEEPER_URL}/user/practitioner-dashboard/dialogues/${dialogueId}/generate-soap-notes`, {
      method: 'POST',
      headers: {
        'Authorization': authHeader,
        'Content-Type': 'application/json',
        'x-tenant': 'practitioner-dashboard',
      },
      body: JSON.stringify(requestBody),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      console.error('[SOAP Notes] Gatekeeper error:', errorData);
      return NextResponse.json(
        { error: errorData.error || 'Failed to generate SOAP notes' },
        { status: response.status }
      );
    }

    const data = await response.json();
    console.log(`[SOAP Notes] Successfully initiated SOAP generation for dialogue: ${dialogueId}`);

    return NextResponse.json(data);
  } catch (error) {
    console.error('[SOAP Notes] Error generating SOAP notes:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}