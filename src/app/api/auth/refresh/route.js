// POST /api/auth/refresh
import { NextResponse } from 'next/server';
import { refreshTokenViaGatekeeper } from '@/lib/auth-utils';

export async function POST(request) {
  try {
    const { refresh_token, phone } = await request.json();
    
    // Validate inputs
    if (!refresh_token || !phone) {
      return NextResponse.json(
        { success: false, error: 'Refresh token and phone number are required' },
        { status: 400 }
      );
    }
    
    // Refresh token via Gatekeeper
    const gatekeeperResult = await refreshTokenViaGatekeeper(phone, refresh_token);
    
    if (!gatekeeperResult.success) {
      return NextResponse.json(
        { 
          success: false, 
          error: gatekeeperResult.error || 'Token refresh failed',
          code: 'INVALID_REFRESH_TOKEN'
        },
        { status: 401 }
      );
    }
    
    // Return the new tokens from Gatekeeper
    return NextResponse.json({
      success: true,
      access_token: gatekeeperResult.data.accessToken,
      refresh_token: gatekeeperResult.data.refreshToken,
      expires_in: 3600 // 1 hour
    });
    
  } catch (error) {
    console.error('Refresh token error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}