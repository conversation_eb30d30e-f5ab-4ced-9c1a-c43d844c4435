import { NextResponse } from 'next/server';

const GATEKEEPER_BASE_URL = process.env.GATEKEEPER_URL || 'https://gatekeeper-staging.getbeyondhealth.com';
const GATEKEEPER_TENANT = 'practitioner-dashboard';
const GATEKEEPER_STATIC_TOKEN = process.env.GATEKEEPER_STATIC_TOKEN || 'm}0/m9ZL`k{|Mz:Ca{7k8PF(gJV"Xz/j';

export async function POST(request) {
  try {
    const { phoneNumber, source = 'WEB', user_role = 'DOCTOR' } = await request.json();

    const response = await fetch(`${GATEKEEPER_BASE_URL}/c/${GATEKEEPER_TENANT}/register`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${GATEKEEPER_STATIC_TOKEN}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        source,
        phoneNumber,
        user_role
      }),
    });

    const data = await response.json();

    if (!response.ok) {
      if (response.status === 400 && data.message && data.message.includes('already register')) {
        return NextResponse.json(
          { error: 'Phone number already registered. Please use login instead.' },
          { status: 400 }
        );
      }
      return NextResponse.json(
        { error: data.message || 'Registration failed' },
        { status: response.status }
      );
    }

    return NextResponse.json({
      success: true,
      message: data.message
    });
  } catch (error) {
    console.error('Registration error:', error);
    return NextResponse.json(
      { error: 'Failed to register user' },
      { status: 500 }
    );
  }
}