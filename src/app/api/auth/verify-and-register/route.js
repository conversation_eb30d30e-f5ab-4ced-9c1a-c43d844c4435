import { NextResponse } from 'next/server';
import { verifyOTPViaGatekeeper } from '@/lib/auth-utils';
import { BlobServiceClient, StorageSharedKeyCredential } from '@azure/storage-blob';

// Helper function to upload file to Azure Blob Storage
async function uploadIdentityDocument(fileData, fileName, phoneNumber) {
  const accountName = process.env.AZURE_STORAGE_ACCOUNT_NAME || 'augustbuckets';
  const accountKey = process.env.AZURE_STORAGE_ACCOUNT_KEY;
  const containerName = process.env.REPORTS_BUCKET || 'doctor-attachments';
  
  if (!accountName || !accountKey) {
    throw new Error('Azure Storage credentials are not configured');
  }
  
  try {
    const credential = new StorageSharedKeyCredential(accountName, accountKey);
    const blobServiceClient = new BlobServiceClient(
      `https://${accountName}.blob.core.windows.net`,
      credential
    );
    
    const containerClient = blobServiceClient.getContainerClient(containerName);
    const blobName = `identity-verification/${phoneNumber.replace('+', '')}/${Date.now()}_${fileName}`;
    const blockBlobClient = containerClient.getBlockBlobClient(blobName);
    
    // Convert base64 to buffer
    const buffer = Buffer.from(fileData, 'base64');
    
    // Upload with appropriate content type
    const contentType = fileName.toLowerCase().endsWith('.pdf') ? 'application/pdf' : 'image/jpeg';
    await blockBlobClient.upload(buffer, buffer.length, {
      blobHTTPHeaders: { blobContentType: contentType }
    });
    
    return blockBlobClient.url;
  } catch (error) {
    console.error('Azure blob upload error:', error);
    throw new Error('Failed to upload identity document');
  }
}

export async function POST(request) {
  try {
    console.log("HIT")
    const { phone, otp, name, specialty, license, hospital, idFileData, requestId } = await request.json();
    
    // Validate requestId is present
    if (!requestId) {
      return NextResponse.json(
        { error: 'Request ID is required for OTP verification' },
        { status: 400 }
      );
    }

    // Log all request arguments
    console.log('Verify and register request:', {
      phone,
      otp,
      name,
      specialty,
      license,
      hospital,
      requestId,
      idFileData: idFileData ? {
        name: idFileData.name,
        type: idFileData.type,
        size: idFileData.size,
        // Omit actual file data from logs
        hasData: !!idFileData.data
      } : null
    });

    // Validate required fields
    if (!phone || !otp) {
      return NextResponse.json(
        { error: 'Phone and OTP are required' },
        { status: 400 }
      );
    }

    // Handle ID file upload if provided
    let licenseValue = license;
    if (!license && idFileData && idFileData.data && idFileData.name) {
      try {
        console.log('Uploading identity document for:', phone);
        licenseValue = await uploadIdentityDocument(idFileData.data, idFileData.name, phone);
        console.log('Identity document uploaded successfully:', licenseValue);
      } catch (uploadError) {
        console.error('Failed to upload identity document:', uploadError);
        return NextResponse.json(
          { error: 'Failed to upload identity document' },
          { status: 500 }
        );
      }
    }

    // Build registration data object with Gatekeeper's expected field names
    const registrationData = {};
    if (name) registrationData.name = name;
    if (hospital) registrationData.organization = hospital;  // Map hospital to organization
    if (licenseValue) registrationData.license_number = licenseValue;  // Map license to license_number (or blob URL)
    if (specialty) registrationData.speciality = specialty;   // Map specialty to speciality (note the spelling)

    console.log('Calling verifyOTPViaGatekeeper with registration data:', JSON.stringify(registrationData, null, 2));

    // Single API call to verify OTP and register user
    const gatekeeperResult = await verifyOTPViaGatekeeper(phone, otp, registrationData, requestId);
    
    console.log('Gatekeeper verify-otp (with registration) result:', JSON.stringify(gatekeeperResult, null, 2));
    
    if (!gatekeeperResult.success) {
      return NextResponse.json(
        { 
          error: gatekeeperResult.error || 'OTP verification failed',
          code: 'OTP_INVALID'
        },
        { status: 400 }
      );
    }

    // Check if we got tokens (successful registration/login)
    if (gatekeeperResult.externalTokens?.accessToken) {
      // Set refresh token in HTTP-only cookie
      const { setRefreshTokenCookie } = await import('@/lib/cookie-utils');
      setRefreshTokenCookie(gatekeeperResult.externalTokens.refreshToken);
      
      // User successfully registered or already exists
      return NextResponse.json({
        success: true,
        message: 'Registration successful!',
        access_token: gatekeeperResult.externalTokens.accessToken,
        user: {
          id: gatekeeperResult.data?.user?.userId || gatekeeperResult.data?.user?.id,
          phone: phone,
          name: name || 'Doctor',
          specialty: specialty,
          license: license,
          hospital: hospital,
          access: gatekeeperResult.data?.access !== false
        }
      });
    }

    // If no tokens but OTP was valid, account might be pending approval
    return NextResponse.json({
      success: true,
      message: 'Registration successful! Your account is pending admin approval.',
      user: {
        phone: phone,
        name: name,
        specialty: specialty,
        license: license,
        hospital: hospital,
        status: 'pending_approval'
      }
    });
  } catch (error) {
    console.error('Verify and register error:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to verify and register' },
      { status: 500 }
    );
  }
}