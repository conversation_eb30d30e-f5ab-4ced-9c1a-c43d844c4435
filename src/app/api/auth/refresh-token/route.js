import { NextResponse } from 'next/server';
import { getRefreshTokenCookie } from '@/lib/cookie-utils';

const GATEKEEPER_BASE_URL = process.env.GATEKEEPER_URL || 'https://gatekeeper-staging.getbeyondhealth.com';
const GATEKEEPER_TENANT = 'practitioner-dashboard';

export async function POST(request) {
  try {
    // Get refresh token from HTTP-only cookie
    const refreshToken = getRefreshTokenCookie();
    
    if (!refreshToken) {
      return NextResponse.json(
        { error: 'No refresh token found' },
        { status: 401 }
      );
    }

    const response = await fetch(`${GATEKEEPER_BASE_URL}/auth/${GATEKEEPER_TENANT}/refresh-token`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${refreshToken}`,
        'Content-Type': 'application/json',
      }
    });

    const data = await response.json();

    if (!response.ok) {
      return NextResponse.json(
        { error: data.error || 'Token refresh failed' },
        { status: response.status }
      );
    }

    return NextResponse.json({
      success: true,
      access_token: data.accessToken,
      expires_in: data.expiresIn || 3600
    });
  } catch (error) {
    console.error('Token refresh error:', error);
    return NextResponse.json(
      { error: 'Failed to refresh token' },
      { status: 500 }
    );
  }
}