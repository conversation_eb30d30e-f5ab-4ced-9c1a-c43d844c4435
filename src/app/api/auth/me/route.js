// GET /api/auth/me
import { NextResponse } from 'next/server';
import { requireAuth } from '@/lib/auth-middleware';

export async function GET(request) {
  try {
    // Authenticate request
    const auth = await requireAuth(request);
    
    // If authentication failed, requireAuth already returns the error response
    if (auth instanceof NextResponse) {
      return auth;
    }
    
    // Prepare user response (exclude sensitive fields)
    const userResponse = {
      id: auth.user.id,
      name: auth.user.name,
      phone: auth.user.phone,
      role: auth.user.role,
      meta: auth.user.meta,
      tenant_id: auth.user.tenant_id,
      created_at: auth.user.created_at,
      updated_at: auth.user.updated_at
    };
    
    return NextResponse.json({
      success: true,
      user: userResponse
    });
    
  } catch (error) {
    console.error('Get current user error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}