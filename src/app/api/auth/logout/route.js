// POST /api/auth/logout
import { NextResponse } from 'next/server';
import { clearRefreshTokenCookie } from '@/lib/cookie-utils';

export async function POST() {
  // Clear the refresh token cookie
  clearRefreshTokenCookie();
  
  // No other server-side operations needed since we're using Gatekeeper
  // The client will clear the access tokens from localStorage
  
  return NextResponse.json({
    success: true,
    message: 'Logged out successfully'
  });
}