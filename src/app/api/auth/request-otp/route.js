// POST /api/auth/request-otp
import { NextResponse } from 'next/server';
import { requestOTPViaGatekeeper, checkRateLimit } from '@/lib/auth-utils';

export async function POST(request) {
  try {
    const body = await request.json();
    console.log('Request body:', body);
    
    const { phone, phoneNumber, isLogin } = body;
    // Support both 'phone' and 'phoneNumber' for compatibility
    const phoneToUse = phone || phoneNumber;
    
    // Validate phone number
    if (!phoneToUse || !phoneToUse.match(/^\+[1-9]\d{1,14}$/)) {
      return NextResponse.json(
        { success: false, error: 'Invalid phone number format' },
        { status: 400 }
      );
    }
    
    // Check rate limiting
    const rateLimit = await checkRateLimit(phoneToUse, 'otp_request');
    if (!rateLimit.allowed) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Too many OTP requests. Please try again later.',
          code: 'RATE_LIMITED'
        },
        { status: 429 }
      );
    }
    
    // Request OTP via Gatekeeper
    const gatekeeperResult = await requestOTPViaGatekeeper(phoneToUse, isLogin);
    
    if (!gatekeeperResult.success) {
      return NextResponse.json(
        { 
          success: false, 
          error: gatekeeperResult.error || 'Failed to request OTP',
          code: 'OTP_REQUEST_FAILED'
        },
        { status: 500 }
      );
    }
    
    return NextResponse.json({
      success: true,
      requestId: gatekeeperResult.requestId,
      message: 'OTP sent successfully via SMS',
      expires_in: 300, // 5 minutes (standard)
      provider: 'gatekeeper'
    });
    
  } catch (error) {
    console.error('Request OTP error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}