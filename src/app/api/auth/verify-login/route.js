// POST /api/auth/verify-login
import { NextResponse } from 'next/server';
import { 
  verifyOTPViaGatekeeper
} from '@/lib/auth-utils';

export async function POST(request) {
  try {
    const { phone, otp, requestId } = await request.json();
    console.log("verify-login",requestId)
    // Validate required fields
    if (!phone || !otp) {
      return NextResponse.json(
        { success: false, error: 'Phone and OTP are required' },
        { status: 400 }
      );
    }
    
    // Verify OTP via Gatekeeper
    const gatekeeperResult = await verifyOTPViaGatekeeper(phone, otp, {}, requestId);
    if (!gatekeeperResult.success) {
      return NextResponse.json(
        { 
          success: false, 
          error: gatekeeperResult.error || 'OTP verification failed',
          code: 'OTP_INVALID'
        },
        { status: 400 }
      );
    }
    
    // No need to store tokens locally - Gatekeeper manages them
    
    // Use user details from Gatekeeper response
    const gatekeeperUser = gatekeeperResult.data?.user;
    
    if (!gatekeeperUser) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'User data not found in Gatekeeper response',
          status: 'error'
        },
        { status: 400 }
      );
    }
    
    // Check if user has access
    if (gatekeeperResult.data?.access === false) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Account not approved yet',
          status: 'pending_approval'
        },
        { status: 403 }
      );
    }
    
    const user = {
      id: gatekeeperUser.userId || gatekeeperUser.id,
      phone: gatekeeperUser.phone || phone,
      name: 'Doctor', // Default name since Gatekeeper doesn't provide it
      role: 'doctor',
      tenant_id: '5005',
      meta: {}
    };
    
    // Use Gatekeeper tokens directly - no need for local JWT generation
    
    // Prepare user response (exclude sensitive fields)
    const userResponse = {
      id: user.id,
      name: user.name,
      phone: user.phone,
      role: user.role,
      meta: user.meta
    };
    
    // Set refresh token in HTTP-only cookie
    if (gatekeeperResult.externalTokens?.refreshToken) {
      const { setRefreshTokenCookie } = await import('@/lib/cookie-utils');
      setRefreshTokenCookie(gatekeeperResult.externalTokens.refreshToken);
    }
    
    return NextResponse.json({
      success: true,
      access_token: gatekeeperResult.externalTokens?.accessToken,
      expires_in: 3600, // 1 hour
      user: userResponse,
      gatekeeper_token: gatekeeperResult.externalTokens?.accessToken // Also return it explicitly
    });
    
  } catch (error) {
    console.error('Verify login error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}