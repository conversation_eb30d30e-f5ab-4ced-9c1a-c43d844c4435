import { NextResponse } from 'next/server';
import { BlobServiceClient, StorageSharedKeyCredential } from '@azure/storage-blob';
import { v4 as uuidv4 } from 'uuid';

// Azure Blob Storage configuration
const accountName = process.env.AZURE_STORAGE_ACCOUNT_NAME || 'augustbuckets';
const accountKey = process.env.AZURE_STORAGE_ACCOUNT_KEY;
const containerName = process.env.REPORTS_BUCKET || 'beyond-reports-bucket-staging';

// Initialize Azure Blob Service Client
let blobServiceClient = null;
if (accountName && accountKey) {
  const sharedKeyCredential = new StorageSharedKeyCredential(accountName, accountKey);
  blobServiceClient = new BlobServiceClient(
    `https://${accountName}.blob.core.windows.net`,
    sharedKeyCredential
  );
}

// Helper function to get simplified file type
function getSimplifiedFileType(mimeType) {
  if (!mimeType) return 'document';
  
  if (mimeType.startsWith('image/')) return 'image';
  if (mimeType === 'application/pdf') return 'pdf';
  if (mimeType.includes('word') || mimeType.includes('document')) return 'document';
  if (mimeType.includes('spreadsheet') || mimeType.includes('excel')) return 'spreadsheet';
  if (mimeType.includes('text/')) return 'text';
  
  return 'document';
}

export async function POST(request) {
  try {
    // Check if Azure Blob Storage is configured
    if (!blobServiceClient) {
      return NextResponse.json(
        { error: 'Azure Blob Storage not configured' },
        { status: 500 }
      );
    }

    // Parse form data
    const formData = await request.formData();
    const file = formData.get('file');
    const threadId = formData.get('threadId');
    const userId = formData.get('userId');

    if (!file || !threadId || !userId) {
      return NextResponse.json(
        { error: 'Missing required fields: file, threadId, userId' },
        { status: 400 }
      );
    }

    // Validate file
    if (!(file instanceof File)) {
      return NextResponse.json(
        { error: 'Invalid file format' },
        { status: 400 }
      );
    }

    // Generate unique blob name
    const fileExtension = file.name.split('.').pop() || '';
    const uniqueFileName = `${uuidv4()}_${file.name}`;
    const blobName = `${threadId}/${uniqueFileName}`;

    console.log('Uploading file to Azure Blob Storage:', {
      fileName: file.name,
      size: file.size,
      type: file.type,
      blobName,
      containerName
    });

    // Get container client
    const containerClient = blobServiceClient.getContainerClient(containerName);
    
    // Ensure container exists
    try {
      await containerClient.createIfNotExists({
        access: 'blob'
      });
    } catch (error) {
      console.warn('Container creation warning:', error.message);
    }

    // Get block blob client
    const blockBlobClient = containerClient.getBlockBlobClient(blobName);

    // Convert file to buffer
    const arrayBuffer = await file.arrayBuffer();
    const buffer = Buffer.from(arrayBuffer);

    // Upload file with metadata
    const uploadOptions = {
      blobHTTPHeaders: {
        blobContentType: file.type || 'application/octet-stream'
      },
      metadata: {
        originalName: file.name,
        uploadedBy: userId,
        threadId: threadId,
        uploadDate: new Date().toISOString()
      }
    };

    const uploadResult = await blockBlobClient.upload(buffer, buffer.length, uploadOptions);

    // Get the blob URL
    const blobUrl = blockBlobClient.url;

    console.log('File uploaded successfully:', {
      blobUrl,
      etag: uploadResult.etag,
      lastModified: uploadResult.lastModified
    });

    // Return success response with file metadata
    return NextResponse.json({
      success: true,
      url: blobUrl,
      name: file.name,
      originalName: file.name,
      size: file.size,
      type: file.type,
      messageType: getSimplifiedFileType(file.type),
      blobName,
      etag: uploadResult.etag,
      lastModified: uploadResult.lastModified,
      uploadDate: new Date().toISOString()
    });

  } catch (error) {
    console.error('Azure Blob Storage upload error:', error);
    
    return NextResponse.json(
      { 
        error: 'Failed to upload file to Azure Blob Storage',
        details: error.message 
      },
      { status: 500 }
    );
  }
}
