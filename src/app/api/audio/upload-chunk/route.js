import { NextResponse } from 'next/server';

const GK = process.env.GATEKEEPER_URL || process.env.NEXT_PUBLIC_GATEKEEPER_URL;
const TENANT = process.env.NEXT_PUBLIC_AUDIO_SLUG || 'practitioner-dashboard';

export async function POST(request) {
  try {
    const auth = request.headers.get('authorization') || '';
    const formData = await request.formData();

    const res = await fetch(`${GK}/user/${TENANT}/upload-audio-chunk`, {
      method: 'POST',
      headers: {
        ...(auth && { Authorization: auth }),
      },
      body: formData,
    });

    const data = await res.json().catch(() => ({}));
    return NextResponse.json(data, { status: res.status });
  } catch (e) {
    console.error('Proxy audio/upload-chunk error:', e);
    return NextResponse.json({ error: e.message || 'Proxy error' }, { status: 500 });
  }
}
