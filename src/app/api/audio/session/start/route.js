import { NextResponse } from 'next/server';

const GK = process.env.GATEKEEPER_URL || process.env.NEXT_PUBLIC_GATEKEEPER_URL;
const DEFAULT_SLUG = process.env.NEXT_PUBLIC_AUDIO_SLUG || 'practitioner-dashboard';

export async function POST(request) {
  try {
    const auth = request.headers.get('authorization') || '';
    const payload = await request.json();
    const slug = payload.slug || DEFAULT_SLUG;

    const res = await fetch(`${GK}/audio/${slug}/session/start`, {
      method: 'POST',
      headers: {
        ...(auth && { Authorization: auth }),
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(payload),
    });

    const data = await res.json().catch(() => ({}));
    return NextResponse.json(data, { status: res.status });
  } catch (e) {
    console.error('Proxy audio/session/start error:', e);
    return NextResponse.json({ error: e.message || 'Proxy error' }, { status: 500 });
  }
}
