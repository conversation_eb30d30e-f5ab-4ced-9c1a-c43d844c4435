import { NextResponse } from 'next/server';

const GK = process.env.GATEKEEPER_URL || process.env.NEXT_PUBLIC_GATEKEEPER_URL;

export async function GET(request, { params }) {
  const { slug, sessionId } = params || {};
  try {
    const auth = request.headers.get('authorization') || '';
    const url = new URL(request.url);
    const since = url.searchParams.get('since');
    const gkUrl = new URL(`${GK}/audio/${slug}/session/${sessionId}/transcripts`);
    if (since) gkUrl.searchParams.set('since', since);

    const res = await fetch(gkUrl.toString(), {
      method: 'GET',
      headers: {
        ...(auth && { Authorization: auth }),
      },
    });

    const data = await res.json().catch(() => ({}));
    return NextResponse.json(data, { status: res.status });
  } catch (e) {
    console.error('Proxy audio/session/[slug]/[sessionId]/transcripts error:', e);
    return NextResponse.json({ error: e.message || 'Proxy error' }, { status: 500 });
  }
}
