import { NextResponse } from 'next/server';

const GK = process.env.GATEKEEPER_URL || process.env.NEXT_PUBLIC_GATEKEEPER_URL;

export async function POST(request, { params }) {
  const { slug, sessionId } = params || {};
  try {
    const auth = request.headers.get('authorization') || '';

    const res = await fetch(`${GK}/audio/${slug}/session/${sessionId}/end`, {
      method: 'POST',
      headers: {
        ...(auth && { Authorization: auth }),
      },
    });

    const data = await res.json().catch(() => ({}));
    return NextResponse.json(data, { status: res.status });
  } catch (e) {
    console.error('Proxy audio/session/[slug]/[sessionId]/end error:', e);
    return NextResponse.json({ error: e.message || 'Proxy error' }, { status: 500 });
  }
}
