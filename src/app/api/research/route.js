import { NextResponse } from 'next/server';

// Research API configuration
const RESEARCH_API_URL = process.env.RESEARCH_API_URL;
const RESEARCH_API_TIMEOUT = 60000; // 60 seconds

export async function POST(request) {
  try {
    const body = await request.json();
    const { query, k = 10, similarity_threshold = 0.8 } = body;

    // Validate input
    if (!query || typeof query !== 'string' || query.trim().length === 0) {
      return NextResponse.json(
        { error: 'Invalid query parameter' },
        { status: 400 }
      );
    }

    // Prepare research API request
    const researchPayload = {
      query: query.trim(),
      k: Math.min(Math.max(k, 1), 50), // Ensure k is between 1 and 50
      similarity_threshold: Math.min(Math.max(similarity_threshold, 0), 1) // Ensure threshold is between 0 and 1
    };

    console.log('[Research API] Sending query:', researchPayload);

    // Call the research API with timeout
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), RESEARCH_API_TIMEOUT);

    try {
      const response = await fetch(`${RESEARCH_API_URL}/api/research`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(researchPayload),
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        console.error('[Research API] Error response:', response.status);
        throw new Error(`Research API returned status ${response.status}`);
      }

      const data = await response.json();
      console.log('[Research API] Success - found', data.papers?.length || 0, 'papers');

      // Transform the response for the frontend
      const transformedResponse = {
        success: true,
        query: data.query,
        subquestions: data.subquestions || [],
        papers: (data.papers || []).map(paper => ({
          id: paper.id || paper.uuid || paper.pmid || Math.random().toString(36),
          title: paper.title || 'Untitled Research Paper',
          abstract: paper.abstract || paper.content || '',
          url: paper.url || (paper.pmid ? `https://pubmed.ncbi.nlm.nih.gov/${paper.pmid}` : null),
          pmid: paper.pmid,
          similarity_score: paper.similarity_score || 0,
          cross_score: paper.cross_score || 0,
          matched_expansions: paper.matched_expansions || [],
          matched_subquestions: paper.matched_subquestions || []
        })),
        summary: data.summary || '',
        processingTime: data.processing_time || 0,
        metrics: {
          query_expansion_time: data.query_expansion_time,
          vector_search_time: data.vector_search_time,
          similarity_filter_time: data.similarity_filter_time,
          cross_encoder_time: data.cross_encoder_time,
          llm_processing_time: data.llm_processing_time
        }
      };

      return NextResponse.json(transformedResponse);

    } catch (fetchError) {
      if (fetchError.name === 'AbortError') {
        console.error('[Research API] Request timeout after', RESEARCH_API_TIMEOUT, 'ms');
        return NextResponse.json(
          { 
            error: 'Research request timed out. Please try a simpler query.',
            timeout: true 
          },
          { status: 504 }
        );
      }
      throw fetchError;
    }

  } catch (error) {
    console.error('[Research API] Error:', error);
    return NextResponse.json(
      { 
        error: 'Failed to process research request',
        details: error.message 
      },
      { status: 500 }
    );
  }
}