// GET /api/health
import { NextResponse } from 'next/server';

export async function GET() {
  return NextResponse.json({
    success: true,
    message: 'Doctor Dashboard Backend API is healthy',
    timestamp: new Date().toISOString(),
    gatekeeper: {
      url: 'https://gatekeeper-staging.getbeyondhealth.com',
      tenant: 'practitioner-dashboard'
    },
    environment: {
      node_env: process.env.NODE_ENV
    },
    endpoints: {
      auth: [
        'POST /api/auth/request-otp',
        'POST /api/auth/verify-login',
        'POST /api/auth/refresh',
        'GET /api/auth/me',
        'POST /api/auth/logout'
      ]
    }
  });
}