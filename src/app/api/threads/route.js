import { NextResponse } from 'next/server';

const GATEKEEPER_BASE_URL = process.env.GATEKEEPER_URL || 'https://gatekeeper-staging.getbeyondhealth.com';
const GATEKEEPER_TENANT = 'practitioner-dashboard';

export async function GET(request) {
  try {
    console.log('--- [GET /api/threads] ---');
    console.log('Request:', {
      method: request.method,
      url: request.url,
      headers: Object.fromEntries(request.headers.entries()),
    });

    const authHeader = request.headers.get('authorization');
    console.log('Authorization header:', authHeader);

    if (!authHeader) {
      console.log('No authorization token provided');
      return NextResponse.json(
        { error: 'No authorization token provided' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const limit = searchParams.get('limit') || '100';
    console.log('Query params:', { limit });

    const gatekeeperUrl = `${GATEKEEPER_BASE_URL}/user/${GATEKEEPER_TENANT}/get-dialogues?limit=${limit}`;
    console.log('Gatekeeper fetch URL:', gatekeeperUrl);

    const fetchOptions = {
      method: 'GET',
      headers: {
        'Authorization': authHeader,
        'Content-Type': 'application/json',
      },
    };
    console.log('Fetch options:', fetchOptions);

    const response = await fetch(gatekeeperUrl, fetchOptions);

    console.log('Gatekeeper response status:', response.status);
    let data;
    try {
      data = await response.json();
      console.log('Gatekeeper response JSON:', data);
    } catch (jsonErr) {
      console.error('Failed to parse Gatekeeper response as JSON:', jsonErr);
      data = null;
    }

    if (!response.ok) {
      console.log('Gatekeeper response not ok:', { status: response.status, data });
      return NextResponse.json(
        { error: (data && data.error) || 'Failed to fetch threads', raw: data },
        { status: response.status }
      );
    }

    // Filter and transform the response to handle failed threads and null values
    if (data && data.chats && Array.isArray(data.chats)) {
      const filteredChats = data.chats
        .filter(chat => {
          // Filter out failed threads or threads with null/undefined essential fields
          return chat && 
                 chat.id && 
                 chat.status !== 'failed' && 
                 chat.status !== 'discarded' &&
                 chat.title !== null && 
                 chat.title !== undefined &&
                 chat.type !== null && 
                 chat.type !== undefined;
        })
        .map(chat => {
          // Ensure all fields have proper defaults
          return {
            id: chat.id,
            status: chat.status || 'completed',
            title: chat.title || 'Untitled Conversation',
            type: chat.type || 'patient-case',
            lastMessage: chat.lastMessage || chat.title || '',
            lastUpdated: chat.lastUpdated || chat.updatedAt || chat.createdAt || new Date().toISOString(),
            messageCount: chat.messageCount || 1
          };
        });

      console.log(`Filtered ${data.chats.length} threads down to ${filteredChats.length} valid threads`);
      
      // Return in the format expected by the frontend
      const responseData = {
        success: true,
        chats: filteredChats,
        dialogues: filteredChats // Support both field names for compatibility
      };
      
      console.log('Returning filtered threads data to client:', responseData);
      return NextResponse.json(responseData);
    }

    console.log('Returning original threads data to client:', data);
    return NextResponse.json(data);
  } catch (error) {
    console.error('Failed to fetch threads:', error);
    return NextResponse.json(
      { error: 'Failed to fetch threads', details: error?.message, stack: error?.stack },
      { status: 500 }
    );
  }
}
