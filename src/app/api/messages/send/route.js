import { NextResponse } from 'next/server';

const GATEKEEPER_BASE_URL = process.env.GATEKEEPER_URL || 'https://gatekeeper-staging.getbeyondhealth.com';
const GATEKEEPER_TENANT = 'practitioner-dashboard';

// Utility function to generate UUIDs
function generateUUID() {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    var r = Math.random() * 16 | 0, v = c == 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}

export async function POST(request) {
  try {
    const authHeader = request.headers.get('authorization');
    
    const body = await request.json();
    const {
      threadId,
      content,
      attachments,
      threadType,
      userPhone
    } = body;

    // Prepare webhook payload
    const webhookPayload = {
      dialogueId: threadId,
      dialogueType: threadType,
      text: content,
      providerMessageId: generateUUID(),
      sender: 'human',
      source: 'WEB',
      phoneNumber: userPhone,
      timestamp: Date.now(),
      requestId: generateUUID()
    };

    // Add attachments if any
    if (attachments && attachments.length > 0) {
      if (attachments.length === 1) {
        // Single file
        const file = attachments[0];
        webhookPayload.attachment = file.url;
        webhookPayload.fileExtension = file.extension;
        webhookPayload.messageType = file.type;
      } else {
        // Multiple files
        webhookPayload.attachment = attachments.map(file => ({
          url: file.url,
          fileExtension: file.extension,
          messageType: file.type
        }));
      }
    }

    const response = await fetch(`${GATEKEEPER_BASE_URL}/c/${GATEKEEPER_TENANT}/webhook`, {
      method: 'POST',
      headers: {
        ...(authHeader && { 'Authorization': authHeader }),
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(webhookPayload),
    });

    const data = await response.json();

    if (!response.ok) {
      return NextResponse.json(
        { error: data.error || 'Failed to send message' },
        { status: response.status }
      );
    }

    return NextResponse.json({
      success: true,
      gatekeeperResponse: data,
      messageId: webhookPayload.providerMessageId,
      timestamp: new Date(webhookPayload.timestamp).toISOString()
    });
  } catch (error) {
    console.error('Send message error:', error);
    return NextResponse.json(
      { error: 'Failed to send message' },
      { status: 500 }
    );
  }
}