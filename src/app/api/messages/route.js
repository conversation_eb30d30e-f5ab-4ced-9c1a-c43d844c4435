import { NextResponse } from 'next/server';

const GATEKEEPER_BASE_URL = process.env.GATEKEEPER_URL || 'https://gatekeeper-staging.getbeyondhealth.com';
const GATEKEEPER_TENANT = 'practitioner-dashboard';

export async function GET(request) {
  try {
    const authHeader = request.headers.get('authorization');
    if (!authHeader) {
      return NextResponse.json(
        { error: 'No authorization token provided' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const threadId = searchParams.get('threadId');
    const limit = searchParams.get('limit') || '50';

    if (!threadId) {
      return NextResponse.json(
        { error: 'Thread ID is required' },
        { status: 400 }
      );
    }

    const response = await fetch(`${GATEKEEPER_BASE_URL}/user/${GATEKEEPER_TENANT}/get-chats-by-dialogueId?limit=${limit}&dialogue_id=${threadId}`, {
      method: 'GET',
      headers: {
        'Authorization': authHeader,
        'Content-Type': 'application/json',
      },
    });

    const data = await response.json();

    if (!response.ok) {
      return NextResponse.json(
        { error: data.error || 'Failed to fetch messages' },
        { status: response.status }
      );
    }

    return NextResponse.json(data);
  } catch (error) {
    console.error('Failed to fetch messages:', error);
    return NextResponse.json(
      { error: 'Failed to fetch messages' },
      { status: 500 }
    );
  }
}