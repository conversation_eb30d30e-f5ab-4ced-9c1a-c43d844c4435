import winston from 'winston';

// Define log levels
const levels = {
  error: 0,
  warn: 1,
  info: 2,
  http: 3,
  debug: 4,
};

// Define log colors
const colors = {
  error: 'red',
  warn: 'yellow',
  info: 'green',
  http: 'magenta',
  debug: 'white',
};

winston.addColors(colors);

// Define log format
const format = winston.format.combine(
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss:ms' }),
  winston.format.colorize({ all: true }),
  winston.format.printf(
    (info) => `${info.timestamp} ${info.level}: ${info.message}` +
      (info.metadata ? ` ${JSON.stringify(info.metadata)}` : '')
  )
);

// Define transports
const transports = [
  // Console transport
  new winston.transports.Console({
    format: winston.format.combine(
      winston.format.colorize(),
      winston.format.simple()
    )
  }),
  // File transport for errors
  new winston.transports.File({
    filename: 'logs/error.log',
    level: 'error',
    format: winston.format.uncolorize(),
  }),
  // File transport for all logs
  new winston.transports.File({
    filename: 'logs/all.log',
    format: winston.format.uncolorize(),
  }),
];

// Create logger
const logger = winston.createLogger({
  level: process.env.NODE_ENV === 'development' ? 'debug' : 'info',
  levels,
  format,
  transports,
});

// Create wrapper functions that match the existing logger interface
const loggerWrapper = {
  info: (message, metadata) => {
    if (typeof message === 'object') {
      logger.info(JSON.stringify(message));
    } else {
      logger.info(message, { metadata });
    }
  },
  
  error: (message, metadata) => {
    if (typeof message === 'object') {
      logger.error(JSON.stringify(message));
    } else {
      logger.error(message, { metadata });
    }
  },
  
  warn: (message, metadata) => {
    if (typeof message === 'object') {
      logger.warn(JSON.stringify(message));
    } else {
      logger.warn(message, { metadata });
    }
  },
  
  debug: (message, metadata) => {
    if (typeof message === 'object') {
      logger.debug(JSON.stringify(message));
    } else {
      logger.debug(message, { metadata });
    }
  },
  
  http: (message, metadata) => {
    if (typeof message === 'object') {
      logger.http(JSON.stringify(message));
    } else {
      logger.http(message, { metadata });
    }
  }
};

export default loggerWrapper;