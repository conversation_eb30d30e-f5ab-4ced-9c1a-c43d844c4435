const puppeteer = require('puppeteer');
const fs = require('fs');

// Configuration
const CONFIG = {
  baseUrl: 'http://localhost:3000',
  testPhone: '+919819304846',
  headless: false, // Run in visible mode to see the flow
  slowMo: 100, // Slow down for visibility
  timeout: 60000 // Increase timeout for manual OTP entry
};

// Test data
const TEST_DATA = {
  otp: process.env.TEST_OTP || '', // You'll need to enter manually
  patientCase: {
    title: 'Hypertension Management - Mr<PERSON>',
    patientName: '<PERSON><PERSON>',
    age: '52',
    complaint: 'Pat<PERSON> presents with Stage 2 hypertension (BP 160/100), mild headaches, and family history of cardiovascular disease.',
    question: 'What is the recommended first-line treatment for Stage 2 hypertension in a 52-year-old male with no comorbidities?'
  },
  research: {
    topic: 'CRISPR Gene Therapy Latest Advances',
    question: 'What are the recent FDA-approved CRISPR therapies and their success rates in clinical trials?'
  },
  quickFact: {
    drugQuery: 'Amlodipine dosage for hypertension',
    labQuery: 'Normal range for HbA1c',
    calculation: 'Calculate eGFR for 52yo male, creatinine 1.2 mg/dL, weight 75kg'
  }
};

class CompleteDoctorDashboardTest {
  constructor() {
    this.browser = null;
    this.page = null;
    this.screenshotCount = 0;
    this.testResults = {
      passed: [],
      failed: [],
      startTime: new Date()
    };
  }

  async screenshot(name) {
    this.screenshotCount++;
    const filename = `test-screenshots/${this.screenshotCount}-${name}.png`;
    await this.page.screenshot({ path: filename, fullPage: true });
    console.log(`📸 Screenshot: ${filename}`);
  }

  async log(message, type = 'info') {
    const icons = {
      info: '📘',
      success: '✅',
      error: '❌',
      warn: '⚠️',
      step: '👉'
    };
    console.log(`${icons[type] || '📌'} ${message}`);
  }

  async runStep(stepName, stepFunction) {
    this.log(`${stepName}...`, 'step');
    try {
      await stepFunction();
      this.testResults.passed.push(stepName);
      this.log(`${stepName} - Success`, 'success');
    } catch (error) {
      this.testResults.failed.push({ step: stepName, error: error.message });
      this.log(`${stepName} - Failed: ${error.message}`, 'error');
      await this.screenshot('error-' + stepName.replace(/\s+/g, '-'));
      throw error;
    }
  }

  async setup() {
    this.log('Setting up browser...', 'info');
    
    this.browser = await puppeteer.launch({
      headless: CONFIG.headless,
      slowMo: CONFIG.slowMo,
      args: ['--no-sandbox', '--disable-setuid-sandbox'],
      defaultViewport: { width: 1440, height: 900 }
    });
    
    this.page = await this.browser.newPage();
    
    // Monitor console
    this.page.on('console', msg => {
      if (msg.type() === 'error') {
        this.log(`Console error: ${msg.text()}`, 'error');
      }
    });
    
    // Monitor responses
    this.page.on('response', response => {
      if (response.url().includes('/api/')) {
        this.log(`API: ${response.status()} ${response.url()}`, 'info');
      }
    });
  }

  async teardown() {
    if (this.browser) {
      await this.browser.close();
    }
  }

  async waitAndType(selector, text, description = '') {
    await this.page.waitForSelector(selector, { timeout: CONFIG.timeout });
    await this.page.click(selector, { clickCount: 3 });
    await this.page.type(selector, text);
    if (description) {
      this.log(`Typed "${text}" in ${description}`, 'info');
    }
  }

  async waitAndClick(selector, description = '') {
    await this.page.waitForSelector(selector, { timeout: CONFIG.timeout });
    await this.page.click(selector);
    if (description) {
      this.log(`Clicked ${description}`, 'info');
    }
  }

  async run() {
    console.log('\n🏥 Doctor Dashboard Complete Flow Test\n');
    console.log('='.repeat(50) + '\n');
    
    try {
      await this.setup();
      
      // Phase 1: Authentication
      await this.testAuthentication();
      
      // Phase 2: Dashboard Navigation
      await this.testDashboard();
      
      // Phase 3: Patient Case Thread
      await this.testPatientCase();
      
      // Phase 4: Research Thread
      await this.testResearch();
      
      // Phase 5: Quick Facts
      await this.testQuickFacts();
      
      // Phase 6: Additional Features
      await this.testAdditionalFeatures();
      
      // Print summary
      this.printSummary();
      
    } catch (error) {
      this.log(`Test failed: ${error.message}`, 'error');
    } finally {
      await this.teardown();
    }
  }

  async testAuthentication() {
    console.log('\n📱 Phase 1: Authentication\n');
    
    await this.runStep('Navigate to login page', async () => {
      await this.page.goto(CONFIG.baseUrl);
      await this.screenshot('login-page');
    });
    
    await this.runStep('Enter phone number', async () => {
      await this.waitAndType('input[placeholder*="98193"]', CONFIG.testPhone, 'phone input');
      await this.screenshot('phone-entered');
    });
    
    await this.runStep('Request OTP', async () => {
      const [response] = await Promise.all([
        this.page.waitForResponse(res => res.url().includes('/api/auth/request-otp')),
        this.page.click('button[type="submit"]')
      ]);
      
      if (response.status() !== 200) {
        throw new Error(`OTP request failed: ${response.status()}`);
      }
      
      await this.page.waitForSelector('input[maxlength="6"]');
      await this.screenshot('otp-screen');
    });
    
    await this.runStep('Enter OTP', async () => {
      if (TEST_DATA.otp) {
        await this.waitAndType('input[maxlength="6"]', TEST_DATA.otp, 'OTP input');
      } else {
        this.log('Manual OTP entry required. Please enter the OTP received on your phone.', 'warn');
        this.log('Waiting 30 seconds for manual OTP entry...', 'info');
        
        // Wait for user to manually enter OTP and submit
        await this.page.waitForNavigation({ 
          timeout: 30000,
          waitUntil: 'networkidle0' 
        }).catch(() => {
          this.log('No navigation detected. Trying to submit OTP...', 'warn');
        });
      }
      
      await this.screenshot('after-otp');
    });
  }

  async testDashboard() {
    console.log('\n🏠 Phase 2: Dashboard Navigation\n');
    
    await this.runStep('Check dashboard loaded', async () => {
      // Wait for dashboard elements
      await this.page.waitForSelector('main', { timeout: 10000 });
      await this.screenshot('dashboard-main');
      
      // Check URL changed
      const url = this.page.url();
      if (url === CONFIG.baseUrl || url === CONFIG.baseUrl + '/') {
        this.log('Still on login page, attempting to navigate to dashboard...', 'warn');
        await this.page.goto(CONFIG.baseUrl + '/dashboard');
      }
    });
    
    await this.runStep('Check sidebar elements', async () => {
      // Look for thread type buttons/links
      const threadTypes = ['Patient Cases', 'Research', 'Quick Facts'];
      for (const type of threadTypes) {
        const element = await this.page.evaluateHandle((text) => {
          return Array.from(document.querySelectorAll('*')).find(el => 
            el.textContent.includes(text)
          );
        }, type);
        
        if (!element) {
          throw new Error(`Thread type "${type}" not found`);
        }
      }
      
      this.log('All thread types found in sidebar', 'success');
    });
  }

  async testPatientCase() {
    console.log('\n🏥 Phase 3: Patient Case Thread\n');
    
    await this.runStep('Navigate to Patient Cases', async () => {
      const patientCasesElement = await this.page.evaluateHandle(() => {
        return Array.from(document.querySelectorAll('a, button, div[role="button"]')).find(el => 
          el.textContent.includes('Patient Cases')
        );
      });
      
      if (patientCasesElement) {
        await patientCasesElement.click();
        await this.page.waitForTimeout(1000);
      }
      
      await this.screenshot('patient-cases-section');
    });
    
    await this.runStep('Create new patient case', async () => {
      // Look for new/create button
      const newButton = await this.page.evaluateHandle(() => {
        return Array.from(document.querySelectorAll('button')).find(btn => {
          const text = btn.textContent.toLowerCase();
          const ariaLabel = (btn.getAttribute('aria-label') || '').toLowerCase();
          return text.includes('new') || text.includes('create') || text.includes('add') ||
                 ariaLabel.includes('new') || ariaLabel.includes('create');
        });
      });
      
      if (newButton) {
        await newButton.click();
        await this.page.waitForTimeout(1000);
        await this.screenshot('new-patient-case-dialog');
      } else {
        this.log('No new case button found, trying alternative approach...', 'warn');
      }
    });
    
    await this.runStep('Fill patient case details', async () => {
      // Try to fill form fields if they exist
      const selectors = {
        title: ['input[placeholder*="title"]', 'input[name*="title"]', 'input[type="text"]:first-of-type'],
        patient: ['input[placeholder*="patient"]', 'input[name*="patient"]'],
        age: ['input[placeholder*="age"]', 'input[type="number"]'],
        description: ['textarea', 'input[placeholder*="description"]', 'input[placeholder*="complaint"]']
      };
      
      // Title
      for (const selector of selectors.title) {
        try {
          await this.page.waitForSelector(selector, { timeout: 2000 });
          await this.waitAndType(selector, TEST_DATA.patientCase.title, 'title');
          break;
        } catch (e) {
          continue;
        }
      }
      
      // Patient name
      for (const selector of selectors.patient) {
        try {
          await this.page.waitForSelector(selector, { timeout: 2000 });
          await this.waitAndType(selector, TEST_DATA.patientCase.patientName, 'patient name');
          break;
        } catch (e) {
          continue;
        }
      }
      
      // Age
      for (const selector of selectors.age) {
        try {
          await this.page.waitForSelector(selector, { timeout: 2000 });
          await this.waitAndType(selector, TEST_DATA.patientCase.age, 'age');
          break;
        } catch (e) {
          continue;
        }
      }
      
      // Description
      for (const selector of selectors.description) {
        try {
          await this.page.waitForSelector(selector, { timeout: 2000 });
          await this.waitAndType(selector, TEST_DATA.patientCase.complaint, 'complaint');
          break;
        } catch (e) {
          continue;
        }
      }
      
      await this.screenshot('patient-case-filled');
    });
    
    await this.runStep('Submit patient case', async () => {
      // Find submit button
      const submitButton = await this.page.evaluateHandle(() => {
        return Array.from(document.querySelectorAll('button')).find(btn => {
          const text = btn.textContent.toLowerCase();
          return (text.includes('submit') || text.includes('create') || text.includes('save')) &&
                 btn.type === 'submit';
        });
      });
      
      if (submitButton) {
        await submitButton.click();
        await this.page.waitForTimeout(2000);
      } else {
        // Try pressing Enter
        await this.page.keyboard.press('Enter');
        await this.page.waitForTimeout(2000);
      }
      
      await this.screenshot('patient-case-created');
    });
    
    await this.runStep('Send message in patient case', async () => {
      // Find message input
      const messageInput = await this.page.$('textarea[placeholder*="message"], textarea[placeholder*="ask"], textarea[placeholder*="type"]');
      
      if (messageInput) {
        await this.waitAndType(messageInput.selector, TEST_DATA.patientCase.question, 'message');
        await this.screenshot('patient-question-typed');
        
        // Send message
        const sendButton = await this.page.$('button[type="submit"], button[aria-label*="send"]');
        if (sendButton) {
          await sendButton.click();
        } else {
          await this.page.keyboard.press('Enter');
        }
        
        // Wait for response
        this.log('Waiting for AI response...', 'info');
        await this.page.waitForTimeout(5000);
        
        await this.screenshot('patient-case-response');
        
        // Check if response received
        const messages = await this.page.$$('[role="article"], .message, [class*="message"]');
        this.log(`Found ${messages.length} messages in conversation`, 'info');
      } else {
        this.log('Message input not found', 'warn');
      }
    });
  }

  async testResearch() {
    console.log('\n🔬 Phase 4: Research Thread\n');
    
    await this.runStep('Navigate to Research', async () => {
      const researchElement = await this.page.evaluateHandle(() => {
        return Array.from(document.querySelectorAll('a, button, div[role="button"]')).find(el => 
          el.textContent.includes('Research')
        );
      });
      
      if (researchElement) {
        await researchElement.click();
        await this.page.waitForTimeout(1000);
      }
      
      await this.screenshot('research-section');
    });
    
    await this.runStep('Ask research question', async () => {
      const messageInput = await this.page.$('textarea[placeholder*="message"], textarea[placeholder*="ask"], textarea');
      
      if (messageInput) {
        await messageInput.click({ clickCount: 3 });
        await this.page.keyboard.press('Backspace');
        await messageInput.type(TEST_DATA.research.question);
        await this.screenshot('research-question-typed');
        
        // Send
        await this.page.keyboard.press('Enter');
        
        // Wait for response
        this.log('Waiting for research response...', 'info');
        await this.page.waitForTimeout(5000);
        
        await this.screenshot('research-response');
        
        // Check for citations
        const pageContent = await this.page.content();
        if (pageContent.includes('[1]') || pageContent.includes('Reference') || pageContent.includes('Source')) {
          this.log('Research response includes citations', 'success');
        }
      }
    });
  }

  async testQuickFacts() {
    console.log('\n💊 Phase 5: Quick Facts\n');
    
    await this.runStep('Navigate to Quick Facts', async () => {
      const quickFactsElement = await this.page.evaluateHandle(() => {
        return Array.from(document.querySelectorAll('a, button, div[role="button"]')).find(el => 
          el.textContent.includes('Quick Facts')
        );
      });
      
      if (quickFactsElement) {
        await quickFactsElement.click();
        await this.page.waitForTimeout(1000);
      }
      
      await this.screenshot('quick-facts-section');
    });
    
    await this.runStep('Test drug dosage query', async () => {
      const messageInput = await this.page.$('textarea[placeholder*="message"], textarea[placeholder*="ask"], textarea');
      
      if (messageInput) {
        await messageInput.click({ clickCount: 3 });
        await this.page.keyboard.press('Backspace');
        await messageInput.type(TEST_DATA.quickFact.drugQuery);
        
        await this.page.keyboard.press('Enter');
        await this.page.waitForTimeout(3000);
        
        await this.screenshot('drug-dosage-response');
      }
    });
    
    await this.runStep('Test lab value query', async () => {
      const messageInput = await this.page.$('textarea[placeholder*="message"], textarea[placeholder*="ask"], textarea');
      
      if (messageInput) {
        await messageInput.click({ clickCount: 3 });
        await this.page.keyboard.press('Backspace');
        await messageInput.type(TEST_DATA.quickFact.labQuery);
        
        await this.page.keyboard.press('Enter');
        await this.page.waitForTimeout(3000);
        
        await this.screenshot('lab-value-response');
      }
    });
    
    await this.runStep('Test medical calculation', async () => {
      const messageInput = await this.page.$('textarea[placeholder*="message"], textarea[placeholder*="ask"], textarea');
      
      if (messageInput) {
        await messageInput.click({ clickCount: 3 });
        await this.page.keyboard.press('Backspace');
        await messageInput.type(TEST_DATA.quickFact.calculation);
        
        await this.page.keyboard.press('Enter');
        await this.page.waitForTimeout(3000);
        
        await this.screenshot('calculation-response');
      }
    });
  }

  async testAdditionalFeatures() {
    console.log('\n🔧 Phase 6: Additional Features\n');
    
    await this.runStep('Check file attachment capability', async () => {
      // Look for attachment button
      const attachButton = await this.page.$('button[aria-label*="attach"], button[title*="attach"], input[type="file"]');
      
      if (attachButton) {
        this.log('File attachment feature is available', 'success');
        await this.screenshot('attachment-button-found');
      } else {
        this.log('File attachment feature not visible', 'warn');
      }
    });
    
    await this.runStep('Check markdown rendering', async () => {
      // Look for formatted elements in responses
      const formattedElements = await this.page.evaluate(() => {
        return {
          tables: document.querySelectorAll('table').length,
          codeBlocks: document.querySelectorAll('pre, code').length,
          lists: document.querySelectorAll('ul, ol').length,
          bold: document.querySelectorAll('strong, b').length
        };
      });
      
      this.log('Markdown elements found:', 'info');
      Object.entries(formattedElements).forEach(([element, count]) => {
        if (count > 0) {
          this.log(`  - ${element}: ${count}`, 'info');
        }
      });
      
      if (Object.values(formattedElements).some(count => count > 0)) {
        this.log('Markdown rendering is working', 'success');
      }
    });
    
    await this.runStep('Check thread navigation', async () => {
      // Count threads in sidebar
      const threads = await this.page.evaluate(() => {
        // Look for thread items - adjust selector based on actual implementation
        const threadElements = document.querySelectorAll('[role="listitem"], .thread-item, [class*="thread"]');
        return threadElements.length;
      });
      
      this.log(`Found ${threads} threads in sidebar`, 'info');
      
      if (threads > 0) {
        await this.screenshot('thread-list');
      }
    });
  }

  printSummary() {
    const endTime = new Date();
    const duration = (endTime - this.testResults.startTime) / 1000;
    
    console.log('\n' + '='.repeat(50));
    console.log('📊 TEST SUMMARY');
    console.log('='.repeat(50) + '\n');
    
    console.log(`Total Steps: ${this.testResults.passed.length + this.testResults.failed.length}`);
    console.log(`✅ Passed: ${this.testResults.passed.length}`);
    console.log(`❌ Failed: ${this.testResults.failed.length}`);
    console.log(`⏱️  Duration: ${duration.toFixed(2)}s`);
    console.log(`📸 Screenshots: ${this.screenshotCount}`);
    
    if (this.testResults.passed.length > 0) {
      console.log('\n✅ Passed Steps:');
      this.testResults.passed.forEach(step => {
        console.log(`  - ${step}`);
      });
    }
    
    if (this.testResults.failed.length > 0) {
      console.log('\n❌ Failed Steps:');
      this.testResults.failed.forEach(({step, error}) => {
        console.log(`  - ${step}: ${error}`);
      });
    }
    
    // Save results
    const resultsFile = `test-results/complete-flow-${endTime.toISOString().replace(/:/g, '-')}.json`;
    fs.writeFileSync(resultsFile, JSON.stringify({
      ...this.testResults,
      endTime,
      duration,
      screenshotCount: this.screenshotCount
    }, null, 2));
    
    console.log(`\n📄 Results saved to: ${resultsFile}`);
    console.log('\n✨ Test run complete!\n');
  }
}

// Run the test
if (require.main === module) {
  const test = new CompleteDoctorDashboardTest();
  test.run();
}

module.exports = CompleteDoctorDashboardTest;