const puppeteer = require('puppeteer');

(async () => {
  console.log('🚀 Simple Dashboard Test');
  console.log('Testing login and dashboard navigation\n');
  
  const browser = await puppeteer.launch({
    headless: false, // Show browser for debugging
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });
  
  const page = await browser.newPage();
  await page.setViewport({ width: 1280, height: 800 });
  page.setDefaultTimeout(30000);
  
  // Monitor console
  page.on('console', msg => {
    console.log('Console:', msg.type(), msg.text());
  });
  
  try {
    // Step 1: Navigate to login
    console.log('1. Navigating to login page...');
    await page.goto('http://localhost:3000');
    await page.waitForSelector('input[placeholder*="98193"]');
    console.log('   ✓ Login page loaded');
    
    // Step 2: Enter phone number
    console.log('\n2. Entering phone number...');
    await page.type('input[placeholder*="98193"]', '+************');
    console.log('   ✓ Phone number entered');
    
    // Step 3: Submit for OTP
    console.log('\n3. Requesting OTP...');
    await page.click('button[type="submit"]');
    
    // Wait for OTP field
    await page.waitForSelector('input[maxlength="6"]', { timeout: 10000 });
    console.log('   ✓ OTP field displayed');
    
    // Step 4: Enter OTP
    console.log('\n4. Entering OTP...');
    await page.type('input[maxlength="6"]', '123456');
    console.log('   ✓ OTP entered');
    
    // Step 5: Submit OTP
    console.log('\n5. Submitting OTP...');
    // Find and click the verify button
    const buttons = await page.$$('button');
    for (const button of buttons) {
      const text = await page.evaluate(el => el.textContent, button);
      if (text.includes('Verify') || text.includes('Login')) {
        await button.click();
        console.log('   ✓ Clicked verify button');
        break;
      }
    }
    
    // Step 6: Wait for navigation or dashboard
    console.log('\n6. Waiting for dashboard...');
    await new Promise(resolve => setTimeout(resolve, 3000)); // Wait 3 seconds
    
    const currentUrl = page.url();
    console.log('   Current URL:', currentUrl);
    
    // Take screenshot
    await page.screenshot({ path: 'test-results/screenshots/simple-test-result.png', fullPage: true });
    console.log('   ✓ Screenshot saved');
    
    // Check what's on the page
    const pageTitle = await page.title();
    console.log('   Page title:', pageTitle);
    
    // Look for dashboard elements
    const hasNewButton = await page.$('button');
    const hasTextarea = await page.$('textarea');
    const hasThreadList = await page.evaluate(() => {
      return document.body.textContent.includes('Today') || 
             document.body.textContent.includes('Yesterday');
    });
    
    console.log('\n7. Dashboard check:');
    console.log('   Has buttons:', !!hasNewButton);
    console.log('   Has textarea:', !!hasTextarea);
    console.log('   Has thread list:', hasThreadList);
    
    if (currentUrl.includes('/chat/')) {
      console.log('\n✅ Successfully logged in and navigated to chat!');
    } else if (hasThreadList || hasTextarea) {
      console.log('\n✅ Successfully logged in and dashboard loaded!');
    } else {
      console.log('\n❌ Login may have failed - still on login page');
    }
    
  } catch (error) {
    console.error('\n❌ Error:', error.message);
    await page.screenshot({ path: 'test-results/screenshots/simple-test-error.png', fullPage: true });
  } finally {
    await browser.close();
  }
})();