require('dotenv').config({ path: '.env' });
const { createClient } = require('redis');

// Test Redis connection with improved configuration
async function testRedisConnection() {
  console.log('🔍 Testing Redis connection...');
  
  if (!process.env.REDIS_URL || !process.env.REDIS_PASSWORD) {
    console.log('❌ Redis not configured (missing REDIS_URL or REDIS_PASSWORD)');
    return;
  }
  
  const clientConfig = {
    url: `rediss://${process.env.REDIS_URL}:6380`,
    password: process.env.REDIS_PASSWORD,
    socket: {
      tls: true,
      rejectUnauthorized: false,
      connectTimeout: 30000,
      commandTimeout: 10000,
      reconnectDelay: 2000,
      retryDelayOnFailover: 2000,
      maxRetriesPerRequest: 5,
      keepAlive: true,
      noDelay: true,
      pingInterval: 30000,
      lazyConnect: true,
    }
  };

  const client = createClient(clientConfig);
  
  // Add event listeners
  client.on('error', (err) => {
    console.log('❌ Redis Client Error:', err.message);
  });
  
  client.on('connect', () => {
    console.log('✅ Redis Client connected');
  });
  
  client.on('ready', () => {
    console.log('✅ Redis Client ready');
  });
  
  client.on('end', () => {
    console.log('⚠️  Redis Client connection ended');
  });
  
  client.on('reconnecting', () => {
    console.log('🔄 Redis Client reconnecting...');
  });

  try {
    // Connect with timeout
    console.log('🔌 Connecting to Redis...');
    await client.connect();
    
    // Test basic operations
    console.log('🧪 Testing basic operations...');
    await client.set('test-key', 'test-value');
    const value = await client.get('test-key');
    console.log('✅ Basic operations work:', value);
    
    // Test ping
    const pingResult = await client.ping();
    console.log('✅ Ping result:', pingResult);
    
    // Clean up
    await client.del('test-key');
    
    console.log('✅ Redis connection test passed!');
    
    // Keep connection alive for monitoring
    console.log('🔍 Monitoring connection for 60 seconds...');
    console.log('Press Ctrl+C to stop');
    
    let counter = 0;
    const interval = setInterval(async () => {
      try {
        counter++;
        const pingResult = await client.ping();
        console.log(`[${counter}] Ping successful:`, pingResult);
        
        if (counter >= 60) {
          clearInterval(interval);
          await client.quit();
          console.log('✅ Monitoring complete');
          process.exit(0);
        }
      } catch (error) {
        console.log(`[${counter}] Ping failed:`, error.message);
      }
    }, 1000);
    
  } catch (error) {
    console.log('❌ Redis connection test failed:', error.message);
    process.exit(1);
  }
}

// Handle process termination
process.on('SIGINT', async () => {
  console.log('\n🛑 Shutting down...');
  process.exit(0);
});

// Run the test
testRedisConnection().catch(console.error);
