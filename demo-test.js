// Demo test to showcase the E2E testing framework capabilities
const { <PERSON>Suite, Logger, TestUtils } = require('./e2e-test-suite');

class DemoTests extends TestSuite {
  constructor() {
    super('demo', 'Demo Test Suite');
  }

  async runTests() {
    // Test 1: Navigate to home page
    await this.test('Navigate to home page', async () => {
      await this.page.goto(this.baseUrl);
      await TestUtils.waitForPageLoad(this.page);
      
      const title = await this.page.title();
      if (!title.includes('Doctor Dashboard')) {
        throw new Error('Invalid page title');
      }
    });

    // Test 2: Check page elements without authentication
    await this.test('Check public page elements', async () => {
      // Check for logo
      const logo = await this.page.$('h1');
      const logoText = await this.page.evaluate(el => el.textContent, logo);
      Logger.debug(`Logo text: ${logoText}`);
      
      if (!logoText.includes('Doctor Dashboard')) {
        throw new Error('Logo not found');
      }

      // Check for login form
      const loginForm = await this.page.$('form');
      if (!loginForm) {
        throw new Error('Login form not found');
      }
      
      // Take screenshot
      await TestUtils.screenshot(this.page, 'demo-home-page');
    });

    // Test 3: Check form validation
    await this.test('Check form validation', async () => {
      // Try to submit empty form
      const submitButton = await this.page.$('button[type="submit"]');
      
      // Check if button is initially disabled
      const isDisabled = await this.page.evaluate(btn => btn.disabled, submitButton);
      Logger.debug(`Submit button initially disabled: ${isDisabled}`);
      
      // Enter invalid phone number
      await TestUtils.typeInField(this.page, 'input[placeholder*="98193"]', '123');
      
      // Check if button is still disabled (invalid phone)
      const stillDisabled = await this.page.evaluate(btn => btn.disabled, submitButton);
      if (!stillDisabled) {
        throw new Error('Button should be disabled for invalid phone');
      }
      
      // Clear and enter valid phone
      await this.page.evaluate(() => {
        const input = document.querySelector('input[placeholder*="98193"]');
        input.value = '';
      });
      
      await TestUtils.typeInField(this.page, 'input[placeholder*="98193"]', '+919819304846');
      
      // Check if button is now enabled
      const nowEnabled = await this.page.evaluate(btn => !btn.disabled, submitButton);
      if (!nowEnabled) {
        throw new Error('Button should be enabled for valid phone');
      }
    });

    // Test 4: Check responsive design
    await this.test('Check responsive design', async () => {
      // Test mobile viewport
      await this.page.setViewport({ width: 375, height: 667 });
      await TestUtils.waitFor(500);
      await TestUtils.screenshot(this.page, 'demo-mobile-view');
      
      // Test tablet viewport
      await this.page.setViewport({ width: 768, height: 1024 });
      await TestUtils.waitFor(500);
      await TestUtils.screenshot(this.page, 'demo-tablet-view');
      
      // Reset to desktop
      await this.page.setViewport({ width: 1280, height: 800 });
    });

    // Test 5: Check console errors
    await this.test('Check for console errors', async () => {
      const errors = [];
      this.page.on('console', msg => {
        if (msg.type() === 'error') {
          errors.push(msg.text());
        }
      });
      
      // Navigate again to catch any errors
      await this.page.reload();
      await TestUtils.waitFor(1000);
      
      // Ignore rate limit errors for this demo
      const criticalErrors = errors.filter(err => 
        !err.includes('429') && 
        !err.includes('rate limit') &&
        !err.includes('Too many OTP requests')
      );
      
      if (criticalErrors.length > 0) {
        throw new Error(`Console errors found: ${criticalErrors.join(', ')}`);
      }
    });
  }
}

// Run the demo tests
(async () => {
  const demo = new DemoTests();
  try {
    await demo.setup();
    await demo.runTests();
    await demo.teardown();
    
    console.log('\x1b[32m\n✅ Demo tests completed successfully!\x1b[0m');
    console.log('\x1b[36m\nThis demonstrates the E2E framework capabilities:\x1b[0m');
    console.log('\x1b[36m- Page navigation and element detection\x1b[0m');
    console.log('\x1b[36m- Form validation testing\x1b[0m');
    console.log('\x1b[36m- Screenshot capture\x1b[0m');
    console.log('\x1b[36m- Responsive design testing\x1b[0m');
    console.log('\x1b[36m- Console error monitoring\x1b[0m');
    
  } catch (error) {
    console.error('\x1b[31mDemo test failed:\x1b[0m', error.message);
    await demo.teardown();
    process.exit(1);
  }
})();