const express = require('express');
const next = require('next');
const http = require('http');
const { Server } = require("socket.io");
const axios = require('axios');
const { v4: uuidv4 } = require('uuid');
const bodyParser = require('body-parser');
const { createClient } = require('redis');
const { createAdapter } = require('@socket.io/redis-adapter');
const { BlobServiceClient, StorageSharedKeyCredential } = require('@azure/storage-blob');

const dev = process.env.NODE_ENV !== 'production';
const app = next({ dev });
const handle = app.getRequestHandler();
const port = 3001;
const logger = require('./src/utils/logger');


const uploadBytesToBlob = async (containerName, blobName, body) => {
  const accountName = process.env.AZURE_STORAGE_ACCOUNT_NAME || 'augustbuckets';
  const accountKey = process.env.AZURE_STORAGE_ACCOUNT_KEY;
  const credential = new StorageSharedKeyCredential(accountName, accountKey);
  const blobServiceClient = new BlobServiceClient(
      `https://${accountName}.blob.core.windows.net`,
      credential
  );
  if (!accountName || !accountKey) {
      throw new Error('Azure Storage credentials are not properly configured');
  }
  const containerClient = blobServiceClient.getContainerClient(containerName);
  const blockBlobClient = containerClient.getBlockBlobClient(blobName);

  await blockBlobClient.upload(body, body.length);
  return blockBlobClient.url;
};

const uploadFileToBlobFromBuffer = async (file, containerName, blobName) => {
  try {
      logger.info('CONTAINER NAME: ', { function: 'uploadFileToBlobFromBuffer', containerName })
      logger.info('BLOB NAME: ', { function: 'uploadFileToBlobFromBuffer', blobName })
      const blobUrl = await uploadBytesToBlob(containerName, blobName, file);
      logger.info('[BLOB UPLOAD SUCCESS]: ', { function: 'uploadFileToBlobFromBuffer', blobName, containerName, blobUrl })
      return blobUrl;
  } catch (error) {
      logger.error('[BLOB UPLOAD ERROR]: ', { function: 'uploadFileToBlobFromBuffer', error })
      throw error;
  }
}


const setupRedis = async () => {
  const pubClient = createClient({
    url: `rediss://${process.env.REDIS_URL}:6380`,
    password: process.env.REDIS_PASSWORD,
    socket: {
      tls: true,
      rejectUnauthorized: false
    },
    retry_strategy: function (options) {
      if (options.error && options.error.code === 'ECONNREFUSED') {
        // End reconnecting on a specific error
        return new Error('The server refused the connection');
      }
      if (options.total_retry_time > 1000 * 60 * 60) {
        // End reconnecting after a specific timeout
        return new Error('Retry time exhausted');
      }
      // Reconnect after
      return Math.min(options.attempt * 100, 3000);
    }
  });

  const subClient = pubClient.duplicate();

  // Reconnection handling for pub client
  pubClient.on('error', (err) => logger.error('Redis Pub Client Error:', err));
  pubClient.on('reconnecting', () => logger.info('Redis Pub Client Reconnecting...'));
  pubClient.on('ready', () => logger.info('Redis Pub Client Ready'));

  // Reconnection handling for sub client
  subClient.on('error', (err) => logger.error('Redis Sub Client Error:', err));
  subClient.on('reconnecting', () => logger.info('Redis Sub Client Reconnecting...'));
  subClient.on('ready', () => logger.info('Redis Sub Client Ready'));

  await Promise.all([pubClient.connect(), subClient.connect()]);
  return { pubClient, subClient };
};

async function sendMessageToGatekeeper(message, tenant) {
  logger.info("SENDING MESSAGE TO GATEKEEPER:", {
      tenant,
      messageType: message.messageType,
      providerMessageId: message.providerMessageId,
      hasPhoneNumber: !!message.phoneNumber,
      phoneNumber: message.phoneNumber
  });
  
  try {
      const response = await axios({
          method: 'post',
          url: `${process.env.GATEKEEPER_URL}/c/${tenant}/webhook`,
          headers: {
              'Content-Type': 'application/json'
          },
          data: message
      });

      logger.info('GATEKEEPER RESPONSE:', {
          status: response.status,
          providerMessageId: message.providerMessageId
      });
      return response.data;
  } catch (error) {
      logger.error('GATEKEEPER ERROR:', {
          error: error.message,
          providerMessageId: message.providerMessageId
      });
      throw error;
  }
}

app.prepare().then(async () => {
  const server = express();

  server.use(bodyParser.json());
  server.use(bodyParser.urlencoded({ extended: true }));

  try {
    const { pubClient, subClient } = await setupRedis();
    logger.info('Redis connected successfully');

    const httpServer = http.createServer(server);
    const io = new Server(httpServer, {
      maxHttpBufferSize: 1e8,
      cors: {
        origin: (origin, callback) => {
          // Allow all origins in development
          if (dev) {
            callback(null, true);
            return;
          }

          // Allow null origin (same origin requests)
          if (!origin) {
            callback(null, true);
            return;
          }

          // Allow any domain containing meetaugust.ai
          if (origin.includes('meetaugust.ai')) {
            callback(null, true);
          } else {
            callback(new Error('Not allowed by CORS'));
          }
        },
        methods: ["GET", "POST"],
        credentials: true
      }
    });

    io.adapter(createAdapter(pubClient, subClient));

    const verifyBearerToken = (req, res, next) => {
      const bearerHeader = req.headers['authorization'];
      if (typeof bearerHeader !== 'undefined') {
        const bearer = bearerHeader.split(' ');
        const bearerToken = bearer[1];

        if (bearerToken === process.env.BEARER_TOKEN) {
          next();
        } else {
          res.sendStatus(403); 
        }
      } else { 
        res.sendStatus(401);
      }
    };

    server.post('/api/emit-message', verifyBearerToken, (req, res) => {
      logger.info('MESSAGE FROM GATEKEEPER, ', req.body)
      const message = decodeURIComponent(req.body.messageText) || 'Default message';
      const userId = req.body.userId;
      const buttons = req.body.buttons || [];
      const providerMessageId = uuidv4();
      logger.info("EMITTING MESSAGE TO CHAT CLIENT WITH USER ID:", userId);
      io.to(userId).emit("chat message", { text: message, sender: 'assistant', providerMessageId, buttons });
      res.status(200).json({ success: true, message: 'Message emitted', providerMessageId });
    });

    server.get('/health', (req, res) => {
      res.status(200).json({ status: 'healthy', timestamp: new Date().toISOString() });
    });

    io.on("connection", (socket) => {
      logger.info("NEW SOCKET CONNECTION ESTABLISHED");
  
      socket.on("join", (userId) => {
          socket.join(userId);
          logger.info(`USER JOINED ROOM:`, { userId });
      });
  
      socket.on("chat message", async (msg, userId, tenant) => {
          logger.info("CHAT MESSAGE RECEIVED:", { 
              userId, 
              messageType: msg.messageType,
              tenant,
              providerMessageId: msg.providerMessageId,
              hasPhoneNumber: !!msg.phoneNumber,
              phoneNumber: msg.phoneNumber
          });
          
          try {
              // If there's an attachment, handle it
              const filePath = msg.fileExtension ? 
                  `${userId}/${msg.requestId}${msg.fileExtension}` : null;
              
              if (msg.attachment) {
                  logger.info('Processing attachment:', { filePath });
                  const fileUrl = await uploadFileToBlobFromBuffer(
                      msg.attachment, 
                      process.env.REPORTS_BUCKET, 
                      filePath
                  );
                  msg.attachment = fileUrl;
              }
  
              logger.info("Sending message to Gatekeeper");
              const response = await sendMessageToGatekeeper(msg, tenant);
              logger.info("Gatekeeper response received:", { 
                  userId,
                  status: 'success',
                  providerMessageId: msg.providerMessageId 
              });
  
              // Emit success back to client
              socket.emit("message_processed", {
                  success: true,
                  messageId: msg.providerMessageId
              });
          } catch (error) {
              logger.error("ERROR PROCESSING CHAT MESSAGE:", { 
                  userId, 
                  error: error.message,
                  providerMessageId: msg.providerMessageId
              });
  
              // Emit failure back to client
              socket.emit("message_failed", {
                  success: false,
                  message: error.message,
                  messageId: msg.providerMessageId
              });
          }
      });
  
      socket.on("disconnect", () => {
          logger.info("SOCKET DISCONNECTED");
      });
  });

    server.all('*', (req, res) => {
      return handle(req, res);
    });

    httpServer.listen(port, (err) => {
      if (err) {
        logger.error("SERVER FAILED TO START:", err);
        throw err;
      }
      logger.info(`SERVER READY ON http://localhost:${port}`);
    });
  } catch (error) {
    logger.error('Redis connection failed:', error);
    process.exit(1); // Exit if Redis connection fails
  }
});
