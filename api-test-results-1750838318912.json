{"timestamp": "2025-06-25T07:58:27.631Z", "baseUrl": "https://gatekeeper-staging.getbeyondhealth.com", "testPhone": "+919819304846", "apis": {"Request OTP": {"endpoint": "/auth/august/request-otp", "method": "POST", "timestamp": "2025-06-25T07:58:28.438Z", "request": {"headers": {"Content-Type": "application/json"}, "params": {}, "body": {"phoneNumber": "+919819304846"}}, "response": {"status": 200, "statusText": "OK", "data": {"requestId": "test-request-id"}, "headers": {"content-type": "application/json; charset=utf-8", "content-length": "31", "connection": "keep-alive", "date": "Wed, 25 Jun 2025 07:58:28 GMT", "etag": "W/\"1f-FTEg0uJrn0Xdq1WdYZzoE29nors\"", "access-control-allow-origin": "*", "content-security-policy": "default-src 'self';base-uri 'self';font-src 'self' https: data:;form-action 'self';frame-ancestors 'self';img-src 'self' data:;object-src 'none';script-src 'self';script-src-attr 'none';style-src 'self' https: 'unsafe-inline';upgrade-insecure-requests", "cross-origin-opener-policy": "same-origin", "cross-origin-resource-policy": "same-origin", "origin-agent-cluster": "?1", "referrer-policy": "no-referrer", "strict-transport-security": "max-age=15552000; includeSubDomains", "x-content-type-options": "nosniff", "x-dns-prefetch-control": "off", "x-download-options": "noopen", "x-frame-options": "SAMEORIGIN", "x-permitted-cross-domain-policies": "none", "x-xss-protection": "0", "ratelimit-policy": "4;w=1800", "ratelimit-limit": "4", "ratelimit-remaining": "2", "ratelimit-reset": "1433", "x-cache": "Miss from cloudfront", "via": "1.1 4b4ec5abcfacb73a67b78b530b940d8c.cloudfront.net (CloudFront)", "x-amz-cf-pop": "BLR50-P1", "x-amz-cf-id": "h7Ip231gEeocECC4xgFQ3-GCHfA9n7065cdWafady0oKx2VGxRVkew=="}}, "error": null, "success": true}, "Register User": {"endpoint": "/c/august/register", "method": "POST", "timestamp": "2025-06-25T07:58:30.215Z", "request": {"headers": {"Authorization": "Bearer m}0/m9ZL`k{|Mz:Ca{7k8PF(gJV\"Xz/j", "Content-Type": "application/json"}, "params": {}, "body": {"source": "WEB", "phoneNumber": "+919819304846", "user_role": "DOCTOR"}}, "response": {"status": 200, "statusText": "OK", "data": {"success": true, "message": "User registered successfully"}, "headers": {"content-type": "application/json; charset=utf-8", "content-length": "57", "connection": "keep-alive", "date": "Wed, 25 Jun 2025 07:58:30 GMT", "etag": "W/\"39-L4ChN3EFRYuIiTpWUtpbvMTfrpg\"", "access-control-allow-origin": "*", "content-security-policy": "default-src 'self';base-uri 'self';font-src 'self' https: data:;form-action 'self';frame-ancestors 'self';img-src 'self' data:;object-src 'none';script-src 'self';script-src-attr 'none';style-src 'self' https: 'unsafe-inline';upgrade-insecure-requests", "cross-origin-opener-policy": "same-origin", "cross-origin-resource-policy": "same-origin", "origin-agent-cluster": "?1", "referrer-policy": "no-referrer", "strict-transport-security": "max-age=15552000; includeSubDomains", "x-content-type-options": "nosniff", "x-dns-prefetch-control": "off", "x-download-options": "noopen", "x-frame-options": "SAMEORIGIN", "x-permitted-cross-domain-policies": "none", "x-xss-protection": "0", "x-cache": "Miss from cloudfront", "via": "1.1 4b4ec5abcfacb73a67b78b530b940d8c.cloudfront.net (CloudFront)", "x-amz-cf-pop": "BLR50-P1", "x-amz-cf-id": "NVYaapRrORsna40g2IOlmuehuDvs2zlbUQWwZT1z8MSb76Mmd4RT_Q=="}}, "error": null, "success": true}, "Get All Dialogues": {"endpoint": "/user/practitioner-dashboard/get-chats-by-dialogueId", "method": "GET", "timestamp": "2025-06-25T07:58:31.288Z", "request": {"headers": {"Authorization": "Bearer test-jwt", "Content-Type": "application/json"}, "params": {"limit": 100, "dialogue_id": ""}, "body": {}}, "response": null, "error": {"message": "Request failed with status code 401", "code": "ERR_BAD_REQUEST", "status": 401, "statusText": "Unauthorized", "data": {"success": false, "message": "Invalid or expired JWT token", "details": "jwt malformed"}}, "success": false}, "Send Text Message": {"endpoint": "/c/august/webhook", "method": "POST", "timestamp": "2025-06-25T07:58:32.469Z", "request": {"headers": {"Content-Type": "application/json"}, "params": {}, "body": {"dialogueId": "2c16f961-0823-422f-9d3a-8099077f5c3c", "dialogueType": "patient-case", "text": "Test patient case: 45-year-old male with chest pain", "providerMessageId": "03e2bc38-68ed-4de8-9c39-522e8b63f16a", "sender": "human", "source": "WEB", "phoneNumber": "+919819304846", "timestamp": 1750838312291, "requestId": "f361a479-75c8-410c-a906-2ade89bdad18"}}, "response": {"status": 200, "statusText": "OK", "data": {"success": true, "tenantSlug": "august"}, "headers": {"content-type": "application/json; charset=utf-8", "content-length": "38", "connection": "keep-alive", "date": "Wed, 25 Jun 2025 07:58:32 GMT", "etag": "W/\"26-0R6TLVujBjCooWA7tdj+lRRuJD8\"", "access-control-allow-origin": "*", "content-security-policy": "default-src 'self';base-uri 'self';font-src 'self' https: data:;form-action 'self';frame-ancestors 'self';img-src 'self' data:;object-src 'none';script-src 'self';script-src-attr 'none';style-src 'self' https: 'unsafe-inline';upgrade-insecure-requests", "cross-origin-opener-policy": "same-origin", "cross-origin-resource-policy": "same-origin", "origin-agent-cluster": "?1", "referrer-policy": "no-referrer", "strict-transport-security": "max-age=15552000; includeSubDomains", "x-content-type-options": "nosniff", "x-dns-prefetch-control": "off", "x-download-options": "noopen", "x-frame-options": "SAMEORIGIN", "x-permitted-cross-domain-policies": "none", "x-xss-protection": "0", "x-cache": "Miss from cloudfront", "via": "1.1 4b4ec5abcfacb73a67b78b530b940d8c.cloudfront.net (CloudFront)", "x-amz-cf-pop": "BLR50-P1", "x-amz-cf-id": "HHYLocKlgARkM7tpFPN43y3iG3A_yJVV4efcDUSNt5Kb7O6-0YBXkw=="}}, "error": null, "success": true}, "Send Message with File": {"endpoint": "/c/august/webhook", "method": "POST", "timestamp": "2025-06-25T07:58:33.624Z", "request": {"headers": {"Content-Type": "application/json"}, "params": {}, "body": {"dialogueId": "972cea1b-dbbc-47e1-8a89-29685861500f", "dialogueType": "patient-case", "text": "X-ray report attached", "providerMessageId": "3d2b2907-51fd-483b-95fe-42cebcf0164e", "attachment": "https://example-blob.blob.core.windows.net/container/xray-report.png", "fileExtension": ".png", "messageType": "image", "sender": "human", "source": "WEB", "phoneNumber": "+919819304846", "timestamp": 1750838313472, "requestId": "de0d467d-f44e-4595-8a7c-8a773a13b6bd"}}, "response": {"status": 200, "statusText": "OK", "data": {"success": true, "tenantSlug": "august"}, "headers": {"content-type": "application/json; charset=utf-8", "content-length": "38", "connection": "keep-alive", "date": "Wed, 25 Jun 2025 07:58:33 GMT", "etag": "W/\"26-0R6TLVujBjCooWA7tdj+lRRuJD8\"", "access-control-allow-origin": "*", "content-security-policy": "default-src 'self';base-uri 'self';font-src 'self' https: data:;form-action 'self';frame-ancestors 'self';img-src 'self' data:;object-src 'none';script-src 'self';script-src-attr 'none';style-src 'self' https: 'unsafe-inline';upgrade-insecure-requests", "cross-origin-opener-policy": "same-origin", "cross-origin-resource-policy": "same-origin", "origin-agent-cluster": "?1", "referrer-policy": "no-referrer", "strict-transport-security": "max-age=15552000; includeSubDomains", "x-content-type-options": "nosniff", "x-dns-prefetch-control": "off", "x-download-options": "noopen", "x-frame-options": "SAMEORIGIN", "x-permitted-cross-domain-policies": "none", "x-xss-protection": "0", "x-cache": "Miss from cloudfront", "via": "1.1 4b4ec5abcfacb73a67b78b530b940d8c.cloudfront.net (CloudFront)", "x-amz-cf-pop": "BLR50-P1", "x-amz-cf-id": "ja_PlsaQ8NIL1C1CDIBhfAWeBWn-waCEyMdI6mKMxow6RmYX0OB9lw=="}}, "error": null, "success": true}, "Send Message Multiple Files": {"endpoint": "/c/august/webhook", "method": "POST", "timestamp": "2025-06-25T07:58:34.972Z", "request": {"headers": {"Content-Type": "application/json"}, "params": {}, "body": {"dialogueId": "677cb5a1-368f-4291-93f6-0987bb029e75", "dialogueType": "research", "text": "Multiple research papers attached", "providerMessageId": "b1bbcd28-c583-4f07-9521-ac5ea1c4f11a", "attachment": [{"url": "https://example-blob.blob.core.windows.net/container/paper1.pdf", "fileExtension": ".pdf", "messageType": "pdf"}, {"url": "https://example-blob.blob.core.windows.net/container/figure1.png", "fileExtension": ".png", "messageType": "image"}], "sender": "human", "source": "WEB", "phoneNumber": "+919819304846", "timestamp": 1750838314626, "requestId": "05dae863-e901-4087-b3ba-31c65b6b1f95"}}, "response": {"status": 200, "statusText": "OK", "data": {"success": true, "tenantSlug": "august"}, "headers": {"content-type": "application/json; charset=utf-8", "content-length": "38", "connection": "keep-alive", "date": "Wed, 25 Jun 2025 07:58:35 GMT", "etag": "W/\"26-0R6TLVujBjCooWA7tdj+lRRuJD8\"", "access-control-allow-origin": "*", "content-security-policy": "default-src 'self';base-uri 'self';font-src 'self' https: data:;form-action 'self';frame-ancestors 'self';img-src 'self' data:;object-src 'none';script-src 'self';script-src-attr 'none';style-src 'self' https: 'unsafe-inline';upgrade-insecure-requests", "cross-origin-opener-policy": "same-origin", "cross-origin-resource-policy": "same-origin", "origin-agent-cluster": "?1", "referrer-policy": "no-referrer", "strict-transport-security": "max-age=15552000; includeSubDomains", "x-content-type-options": "nosniff", "x-dns-prefetch-control": "off", "x-download-options": "noopen", "x-frame-options": "SAMEORIGIN", "x-permitted-cross-domain-policies": "none", "x-xss-protection": "0", "x-cache": "Miss from cloudfront", "via": "1.1 4b4ec5abcfacb73a67b78b530b940d8c.cloudfront.net (CloudFront)", "x-amz-cf-pop": "BLR50-P1", "x-amz-cf-id": "p_O7rU8SCG8Abd31AS1QfPgeW399r0uWfDG6SH9L7nhqNhuZcSaetw=="}}, "error": null, "success": true}, "All Dialogue Types": {"patient-case": {"success": true, "response": {"success": true, "tenantSlug": "august"}}, "research": {"success": true, "response": {"success": true, "tenantSlug": "august"}}, "quick-fact": {"success": true, "response": {"success": true, "tenantSlug": "august"}}}, "Get Messages by Dialogue ID": {"endpoint": "/user/practitioner-dashboard/get-chats-by-dialogueId", "method": "GET", "timestamp": "2025-06-25T07:58:37.911Z", "request": {"headers": {"Authorization": "Bearer test-jwt", "Content-Type": "application/json"}, "params": {"limit": 30, "dialogue_id": "2c16f961-0823-422f-9d3a-8099077f5c3c"}, "body": {}}, "response": null, "error": {"message": "Request failed with status code 401", "code": "ERR_BAD_REQUEST", "status": 401, "statusText": "Unauthorized", "data": {"success": false, "message": "Invalid or expired JWT token", "details": "jwt malformed"}}, "success": false}}, "testDialogueId": "2c16f961-0823-422f-9d3a-8099077f5c3c"}