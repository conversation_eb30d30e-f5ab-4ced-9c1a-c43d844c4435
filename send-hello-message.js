#!/usr/bin/env node

/**
 * Send Hello Message via Gatekeeper API
 * Sends a simple "hello" message to test the webhook integration
 */

const axios = require('axios');
const { v4: uuidv4 } = require('uuid');

const GATEKEEPER_BASE_URL = 'https://gatekeeper-staging.getbeyondhealth.com';
const TENANT = 'practitioner-dashboard';
const TEST_PHONE = '+************';

// JWT token from previous step
const ACCESS_TOKEN = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************************************************************************************************************************************************.exGzctdbLzM_nBJEwijmCclgE4y_b8Hcxfkl7qQJylM';

console.log('💬 SENDING HELLO MESSAGE VIA GATEKEEPER API');
console.log('============================================');
console.log(`Phone: ${TEST_PHONE}`);
console.log(`Gatekeeper URL: ${GATEKEEPER_BASE_URL}`);
console.log(`Tenant: ${TENANT}`);
console.log(`Access Token: ${ACCESS_TOKEN.substring(0, 50)}...`);
console.log('============================================\n');

async function sendHelloMessage() {
  try {
    // Generate unique IDs for this message
    const dialogueId = uuidv4();
    const messageId = uuidv4();
    const requestId = uuidv4();
    const timestamp = Date.now();

    console.log('📝 MESSAGE DETAILS');
    console.log('------------------');
    console.log('Dialogue ID:', dialogueId);
    console.log('Message ID:', messageId);
    console.log('Request ID:', requestId);
    console.log('Timestamp:', timestamp);
    console.log('Message Text: "Hello"');
    console.log('');

    // Prepare the webhook payload
    const webhookPayload = {
      dialogueId: dialogueId,
      dialogueType: 'quick-fact',
      text: 'Hello',
      providerMessageId: messageId,
      sender: 'human',
      source: 'WEB',
      phoneNumber: TEST_PHONE,
      timestamp: timestamp,
      requestId: requestId
    };

    console.log('🚀 SENDING MESSAGE TO GATEKEEPER');
    console.log('---------------------------------');
    console.log('Request URL:', `${GATEKEEPER_BASE_URL}/c/${TENANT}/webhook`);
    console.log('Request Method: POST');
    console.log('Request Headers:');
    console.log('  Content-Type: application/json');
    console.log('  Authorization: Bearer', ACCESS_TOKEN.substring(0, 20) + '...');
    console.log('Request Body:', JSON.stringify(webhookPayload, null, 2));

    const response = await axios.post(`${GATEKEEPER_BASE_URL}/c/${TENANT}/webhook`, webhookPayload, {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${ACCESS_TOKEN}`
      }
    });

    console.log('\n📨 GATEKEEPER RESPONSE');
    console.log('----------------------');
    console.log('Response Status:', response.status);
    console.log('Response Status Text:', response.statusText);
    console.log('Response Headers:', JSON.stringify(response.headers, null, 2));
    console.log('Response Body:', JSON.stringify(response.data, null, 2));

    if (response.status === 200 || response.status === 201) {
      console.log('\n✅ MESSAGE SENT SUCCESSFULLY!');
      console.log('=============================');
      console.log('✅ Webhook call successful');
      console.log('✅ Message should appear in backend database');
      console.log('✅ AI response should be generated');
      console.log('✅ Response will be delivered via Redis Pub/Sub');
      
      console.log('\n📋 WHAT TO CHECK IN BACKEND:');
      console.log('1. Check for dialogue with ID:', dialogueId);
      console.log('2. Check for message with ID:', messageId);
      console.log('3. Check for user with phone:', TEST_PHONE);
      console.log('4. Check for dialogue type: quick-fact');
      console.log('5. Check message content: "Hello"');
      
      return {
        success: true,
        dialogueId,
        messageId,
        response: response.data
      };
    } else {
      console.log('❌ Unexpected response status:', response.status);
      return null;
    }

  } catch (error) {
    console.error('\n❌ ERROR SENDING MESSAGE');
    console.error('=========================');
    console.error('Error Message:', error.message);
    
    if (error.response) {
      console.error('Error Status:', error.response.status);
      console.error('Error Status Text:', error.response.statusText);
      console.error('Error Headers:', JSON.stringify(error.response.headers, null, 2));
      console.error('Error Body:', JSON.stringify(error.response.data, null, 2));
      
      if (error.response.status === 401) {
        console.log('\n💡 AUTHENTICATION ERROR:');
        console.log('- JWT token may be expired');
        console.log('- Try getting a new token');
      } else if (error.response.status === 403) {
        console.log('\n💡 AUTHORIZATION ERROR:');
        console.log('- User may not have access');
        console.log('- Check user access status');
      } else if (error.response.status === 400) {
        console.log('\n💡 BAD REQUEST ERROR:');
        console.log('- Check payload format');
        console.log('- Verify required fields');
      }
    }
    
    return null;
  }
}

// Run the function
sendHelloMessage()
  .then(result => {
    if (result) {
      console.log('\n🎉 Message sent successfully!');
      console.log('Please check the backend database for:');
      console.log('- Dialogue ID:', result.dialogueId);
      console.log('- Message ID:', result.messageId);
    } else {
      console.log('\n❌ Failed to send message');
    }
  })
  .catch(error => {
    console.error('💥 Script failed:', error);
  });