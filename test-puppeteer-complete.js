const puppeteer = require('puppeteer');
const fs = require('fs');

// Configuration
const BASE_URL = process.env.BASE_URL || 'http://localhost:3000';
const HEADLESS = process.env.HEADLESS !== 'false';
const TEST_PHONE = '+919819304846'; // Always use this number

// Create logs directory
if (!fs.existsSync('test-logs')) {
  fs.mkdirSync('test-logs');
}
if (!fs.existsSync('test-screenshots')) {
  fs.mkdirSync('test-screenshots');
}

// Log file
const logFile = `test-logs/complete-test-${new Date().toISOString().replace(/:/g, '-')}.log`;
const logStream = fs.createWriteStream(logFile, { flags: 'a' });

// Enhanced logging
function log(level, msg, data = null) {
  const timestamp = new Date().toISOString();
  const logEntry = `[${level}] ${timestamp} - ${msg} ${data ? JSON.stringify(data) : ''}`;
  console.log(logEntry);
  logStream.write(logEntry + '\n');
}

// Tracking
const tracking = {
  requests: [],
  responses: [],
  jsonErrors: [],
  consoleErrors: []
};

async function setupMonitoring(page) {
  // Monitor ALL network requests
  await page.setRequestInterception(true);
  
  page.on('request', request => {
    const url = request.url();
    const method = request.method();
    
    // Log all non-asset requests
    if (!url.includes('.woff') && !url.includes('.css') && !url.includes('.js')) {
      const reqData = {
        url,
        method,
        headers: request.headers(),
        postData: request.postData(),
        timestamp: new Date().toISOString()
      };
      
      tracking.requests.push(reqData);
      
      if (method === 'POST' || url.includes('/api/')) {
        log('API', `Request: ${method} ${url}`);
        if (reqData.postData) {
          log('API', 'Request Body:', reqData.postData);
          
          // Check for JSON errors in request
          try {
            if (reqData.postData.trim().startsWith('{') || reqData.postData.trim().startsWith('[')) {
              JSON.parse(reqData.postData);
            }
          } catch (e) {
            log('JSON_ERROR', 'Invalid JSON in request', {
              url,
              error: e.message,
              body: reqData.postData
            });
            tracking.jsonErrors.push({
              type: 'request',
              url,
              error: e.message,
              body: reqData.postData
            });
          }
        }
      }
    }
    
    request.continue();
  });
  
  page.on('response', async response => {
    const url = response.url();
    const status = response.status();
    
    // Log all non-asset responses
    if (!url.includes('.woff') && !url.includes('.css') && !url.includes('.js')) {
      const resData = {
        url,
        status,
        headers: response.headers(),
        timestamp: new Date().toISOString()
      };
      
      // Try to get response body for API calls
      if (url.includes('/api/') || response.request().method() === 'POST') {
        try {
          const text = await response.text();
          resData.body = text;
          
          log('API', `Response: ${status} ${url}`);
          if (text) {
            log('API', 'Response Body:', text);
            
            // Check for JSON errors
            try {
              if (text.trim().startsWith('{') || text.trim().startsWith('[')) {
                JSON.parse(text);
              }
            } catch (e) {
              log('JSON_ERROR', 'Invalid JSON in response', {
                url,
                status,
                error: e.message,
                body: text
              });
              tracking.jsonErrors.push({
                type: 'response',
                url,
                status,
                error: e.message,
                body: text
              });
            }
          }
        } catch (e) {
          log('ERROR', 'Could not read response body', { url, error: e.message });
        }
      }
      
      tracking.responses.push(resData);
    }
  });
  
  // Monitor console
  page.on('console', msg => {
    const type = msg.type();
    const text = msg.text();
    
    if (type === 'error' || text.includes('Error') || text.includes('JSON')) {
      tracking.consoleErrors.push({
        type,
        text,
        timestamp: new Date().toISOString()
      });
      log('CONSOLE', `${type}: ${text}`);
    }
  });
  
  // Monitor page errors
  page.on('pageerror', error => {
    log('PAGE_ERROR', error.message);
    if (error.message.includes('JSON')) {
      tracking.jsonErrors.push({
        type: 'page_error',
        message: error.message,
        stack: error.stack
      });
    }
  });
  
  // Inject JSON.parse wrapper
  await page.evaluateOnNewDocument(() => {
    const originalParse = JSON.parse;
    JSON.parse = function(...args) {
      try {
        return originalParse.apply(this, args);
      } catch (error) {
        console.error('JSON.parse error:', {
          error: error.message,
          input: args[0] ? String(args[0]).substring(0, 200) : 'undefined'
        });
        throw error;
      }
    };
  });
}

async function runTest() {
  log('INFO', 'Starting Complete Doctor Dashboard Test');
  log('INFO', 'Test Phone Number:', TEST_PHONE);
  
  const browser = await puppeteer.launch({
    headless: HEADLESS,
    args: ['--no-sandbox', '--disable-setuid-sandbox'],
    devtools: !HEADLESS
  });
  
  try {
    const page = await browser.newPage();
    await page.setViewport({ width: 1440, height: 900 });
    
    // Set up monitoring
    await setupMonitoring(page);
    
    // Navigate
    log('INFO', 'Navigating to:', BASE_URL);
    const navResponse = await page.goto(BASE_URL, {
      waitUntil: ['networkidle0', 'domcontentloaded'],
      timeout: 30000
    });
    log('SUCCESS', 'Page loaded', { status: navResponse.status() });
    
    // Wait for page to stabilize
    await new Promise(resolve => setTimeout(resolve, 2000));
    await page.screenshot({ path: 'test-screenshots/1-loaded.png' });
    
    // Debug: Log all input fields
    const inputs = await page.evaluate(() => {
      return Array.from(document.querySelectorAll('input')).map(input => ({
        type: input.type,
        placeholder: input.placeholder,
        value: input.value,
        id: input.id,
        name: input.name,
        classes: input.className
      }));
    });
    log('DEBUG', 'Input fields found:', inputs);
    
    // Find phone input using multiple strategies
    log('INFO', 'Looking for phone input...');
    let phoneInput = null;
    
    // Try different selectors
    const selectors = [
      'input[placeholder*="98193"]',
      'input[placeholder*="phone" i]',
      '.MuiTextField-root input[type="text"]',
      'input:not([type="hidden"])'
    ];
    
    for (const selector of selectors) {
      phoneInput = await page.$(selector);
      if (phoneInput) {
        log('DEBUG', `Found input with selector: ${selector}`);
        break;
      }
    }
    
    if (!phoneInput) {
      throw new Error('Phone input not found with any selector');
    }
    
    // Clear and fill phone input
    await phoneInput.click({ clickCount: 3 });
    await page.keyboard.type(TEST_PHONE);
    log('SUCCESS', 'Phone number entered');
    
    await new Promise(resolve => setTimeout(resolve, 1000));
    await page.screenshot({ path: 'test-screenshots/2-phone-entered.png' });
    
    // Find submit button
    log('INFO', 'Looking for submit button...');
    const buttons = await page.evaluate(() => {
      return Array.from(document.querySelectorAll('button')).map(btn => ({
        text: btn.textContent.trim(),
        type: btn.type,
        disabled: btn.disabled,
        classes: btn.className
      }));
    });
    log('DEBUG', 'Buttons found:', buttons);
    
    // Click the submit button
    const submitButton = await page.evaluateHandle(() => {
      const buttons = Array.from(document.querySelectorAll('button'));
      return buttons.find(btn => {
        const text = btn.textContent.toLowerCase();
        return (text.includes('send') || text.includes('otp') || text.includes('login')) && !btn.disabled;
      });
    });
    
    if (!submitButton || !await submitButton.evaluate(el => el !== null)) {
      throw new Error('Submit button not found');
    }
    
    // Monitor for any network activity
    let networkActivityDetected = false;
    const networkPromise = new Promise((resolve) => {
      const listener = (request) => {
        const url = request.url();
        if (url.includes('/api/') || request.method() === 'POST') {
          networkActivityDetected = true;
          page.off('request', listener);
          resolve(request);
        }
      };
      page.on('request', listener);
      
      // Timeout after 5 seconds
      setTimeout(() => resolve(null), 5000);
    });
    
    // Click the button
    log('INFO', 'Clicking submit button...');
    await submitButton.click();
    
    // Wait for network activity or timeout
    const networkRequest = await networkPromise;
    
    if (networkRequest) {
      log('SUCCESS', 'Network activity detected after button click');
    } else {
      log('WARN', 'No network activity detected within 5 seconds');
    }
    
    // Wait for any changes
    await new Promise(resolve => setTimeout(resolve, 3000));
    await page.screenshot({ path: 'test-screenshots/3-after-click.png' });
    
    // Check current state
    const currentState = await page.evaluate(() => {
      // Check for OTP field
      const otpField = document.querySelector('input[maxlength="6"]');
      
      // Check for error messages
      const errors = [];
      document.querySelectorAll('.MuiAlert-root, .error, [role="alert"]').forEach(el => {
        if (el.textContent) errors.push(el.textContent.trim());
      });
      
      // Check if form changed
      const visibleInputs = Array.from(document.querySelectorAll('input:not([type="hidden"])')).map(input => ({
        type: input.type,
        placeholder: input.placeholder,
        maxLength: input.maxLength
      }));
      
      return {
        hasOtpField: !!otpField,
        errors,
        visibleInputs,
        url: window.location.href
      };
    });
    
    log('INFO', 'Current state:', currentState);
    
    if (currentState.hasOtpField) {
      log('SUCCESS', 'OTP field appeared - API call was successful');
    } else if (currentState.errors.length > 0) {
      log('ERROR', 'Errors on page:', currentState.errors);
    } else {
      log('WARN', 'No visible state change after button click');
    }
    
  } catch (error) {
    log('ERROR', 'Test failed:', { message: error.message, stack: error.stack });
    
    // Take error screenshot
    if (browser) {
      const pages = await browser.pages();
      if (pages.length > 0) {
        await pages[0].screenshot({ path: 'test-screenshots/error.png' }).catch(() => {});
      }
    }
    
    throw error;
  } finally {
    // Summary
    console.log('\n========== TEST SUMMARY ==========');
    console.log(`Total Requests: ${tracking.requests.length}`);
    console.log(`Total Responses: ${tracking.responses.length}`);
    console.log(`JSON Errors: ${tracking.jsonErrors.length}`);
    console.log(`Console Errors: ${tracking.consoleErrors.length}`);
    console.log(`Log file: ${logFile}`);
    
    // Show API calls
    const apiCalls = tracking.requests.filter(r => r.url.includes('/api/') || r.method === 'POST');
    if (apiCalls.length > 0) {
      console.log('\n========== API CALLS ==========');
      apiCalls.forEach(call => {
        console.log(`${call.method} ${call.url}`);
        if (call.postData) console.log('Body:', call.postData);
      });
    }
    
    // Show JSON errors
    if (tracking.jsonErrors.length > 0) {
      console.log('\n========== JSON ERRORS ==========');
      tracking.jsonErrors.forEach((error, i) => {
        console.log(`\nError ${i + 1}:`, error);
      });
    }
    
    await browser.close();
    logStream.end();
  }
}

// Run test
runTest()
  .then(() => {
    if (tracking.jsonErrors.length > 0) {
      console.log(`\nTest completed with ${tracking.jsonErrors.length} JSON errors`);
      process.exit(1);
    } else {
      console.log('\nTest completed successfully');
      process.exit(0);
    }
  })
  .catch(error => {
    console.error('\nTest crashed:', error);
    process.exit(1);
  });