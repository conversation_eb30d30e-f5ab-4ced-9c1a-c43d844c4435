#!/usr/bin/env node

// Load environment variables
require('dotenv').config({ path: '.env.local' });

const puppeteer = require('puppeteer');
const fs = require('fs');
const path = require('path');

async function testFileUploadFix() {
  console.log('🔧 Testing File Upload Fix');
  console.log('==========================\n');

  let browser;
  try {
    browser = await puppeteer.launch({ 
      headless: false,
      devtools: true,
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });
    
    const page = await browser.newPage();
    
    // Listen for console logs
    page.on('console', msg => {
      if (msg.text().includes('FileAttachmentInput') || 
          msg.text().includes('WebSocket') || 
          msg.text().includes('originalFile')) {
        console.log('🔍 Browser:', msg.text());
      }
    });
    
    page.on('pageerror', error => {
      console.error('❌ Page Error:', error.message);
    });
    
    // Create a test file
    const testPDFContent = `%PDF-1.4
1 0 obj<</Type/Catalog/Pages 2 0 R>>endobj
2 0 obj<</Type/Pages/Kids[3 0 R]/Count 1>>endobj  
3 0 obj<</Type/Page/Parent 2 0 R/MediaBox[0 0 612 792]>>endobj
xref 0 4
0000000000 65535 f 
0000000010 00000 n 
0000000053 00000 n 
0000000109 00000 n 
trailer<</Size 4/Root 1 0 R>>
startxref 125
%%EOF`;

    const testFilePath = '/tmp/test-medical-report-fix.pdf';
    fs.writeFileSync(testFilePath, testPDFContent);
    console.log('📄 Created test file:', testFilePath);

    // Navigate to the app
    console.log('📱 Navigating to dashboard...');
    await page.goto('http://localhost:3000', { waitUntil: 'networkidle2' });
    
    // Set authentication tokens (using latest from logs)
    await page.evaluate(() => {
      const accessToken = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJlbmNyeXB0ZWQiOnRydWUsImRhdGEiOiJVMkZzZEdWa1gxL0FEOXVkTmhmTXJvcG8xUEhwS0JPVFhtSVVjZnMrWmtlTGkxd1VibjA0YnVscEhkeTJEbUlFUFlVWnNHYjBjVFNiSVBuQnNSY2hkR082VWZZSHRsRUh6c2RxdUM2RDRpNmpkTGxGSzh2b3BkakNCS1NGZUwyWkZzeTIrWFhrZnFJZDBVT21nWlRkelo0Sm5PVVdzek9kTjBUekdNU3RTT3c9IiwiaWF0IjoxNzUwODU2NDk2LCJleHAiOjE3NTA4NTgyOTZ9.j3YwR3gDXFIvaWGuPKF56NAJdcFarVH-t6m8NVEyxKk";
      const user = {
        id: "898a9e7b-6873-4c4c-b21c-f4786ee281ad",
        phone: "+919819304846",
        name: "Test User"
      };
      
      localStorage.setItem('access_token', accessToken);
      localStorage.setItem('user', JSON.stringify(user));
      
      return 'Tokens set';
    });

    // Reload to apply authentication
    await page.reload({ waitUntil: 'networkidle2' });
    
    // Wait for page to load and check for dashboard
    await page.waitForTimeout(3000);
    
    await page.screenshot({ path: 'test-fix-1-authenticated.png' });
    console.log('📸 Screenshot: authenticated state');
    
    // Look for file input elements
    console.log('🔍 Looking for file upload elements...');
    
    // Check for file inputs
    const fileInputs = await page.$$('input[type="file"]');
    console.log('📎 Found file inputs:', fileInputs.length);
    
    if (fileInputs.length > 0) {
      console.log('✅ File input found! Testing upload...');
      
      // Upload the file
      await fileInputs[0].uploadFile(testFilePath);
      console.log('📤 File uploaded to input');
      
      await page.waitForTimeout(2000);
      await page.screenshot({ path: 'test-fix-2-file-uploaded.png' });
      console.log('📸 Screenshot: file uploaded');
      
      // Look for send button and try to send
      const sendButtons = await page.$$('button[type="submit"], button:has-text("Send")');
      if (sendButtons.length > 0) {
        console.log('📤 Found send button, attempting to send message...');
        await sendButtons[0].click();
        
        await page.waitForTimeout(3000);
        await page.screenshot({ path: 'test-fix-3-message-sent.png' });
        console.log('📸 Screenshot: message sent');
      }
    } else {
      console.log('❌ No file inputs found on page');
      
      // Try to find attachment buttons
      const attachButtons = await page.$$('[aria-label*="attach"], [title*="attach"], [class*="attach"]');
      console.log('📎 Found potential attachment buttons:', attachButtons.length);
      
      if (attachButtons.length > 0) {
        console.log('🖱️ Clicking attachment button...');
        await attachButtons[0].click();
        await page.waitForTimeout(1000);
        
        // Look for file inputs again
        const newFileInputs = await page.$$('input[type="file"]');
        console.log('📎 File inputs after click:', newFileInputs.length);
      }
    }
    
    await page.waitForTimeout(5000);
    await page.screenshot({ path: 'test-fix-4-final-state.png' });
    console.log('📸 Screenshot: final state');
    
    // Clean up
    if (fs.existsSync(testFilePath)) {
      fs.unlinkSync(testFilePath);
    }
    
    console.log('\n✅ File upload fix test completed');
    console.log('Check screenshots and browser console for results');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  } finally {
    if (browser) {
      await browser.close();
    }
  }
}

if (require.main === module) {
  testFileUploadFix();
}

module.exports = { testFileUploadFix };