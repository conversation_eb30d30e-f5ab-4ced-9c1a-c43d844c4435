const puppeteer = require('puppeteer');

// This test uses mock authentication to test dashboard features
// In a real scenario, you would enter the actual OTP

async function testDashboardWithMockAuth() {
  console.log('\n🏥 Testing Doctor Dashboard Features (<PERSON><PERSON> Auth)\n');
  
  const browser = await puppeteer.launch({
    headless: false,
    slowMo: 100,
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });
  
  try {
    const page = await browser.newPage();
    await page.setViewport({ width: 1440, height: 900 });
    
    // First, let's check what happens after successful login
    // by navigating directly to the dashboard with a mock session
    
    console.log('1. Setting up mock authentication...');
    
    // Set mock authentication cookie/localStorage
    await page.evaluateOnNewDocument(() => {
      // Mock user data in localStorage
      localStorage.setItem('auth-token', 'mock-token-for-testing');
      localStorage.setItem('user', JSON.stringify({
        id: 'test-doctor-1',
        name: 'Dr. Test User',
        phone: '+************',
        organization: 'Test Hospital',
        license_number: 'TEST123456',
        role: 'doctor'
      }));
    });
    
    // Navigate to dashboard
    console.log('2. Navigating to dashboard...');
    await page.goto('http://localhost:3000/dashboard', { 
      waitUntil: 'networkidle0',
      timeout: 30000 
    });
    
    // Take screenshot of current state
    await page.screenshot({ path: 'test-screenshots/mock-dashboard-attempt.png' });
    
    // Check if we're redirected to login
    const currentUrl = page.url();
    console.log('Current URL:', currentUrl);
    
    if (currentUrl.includes('dashboard')) {
      console.log('✅ Successfully on dashboard page');
      
      // Test dashboard features
      await testDashboardFeatures(page);
    } else {
      console.log('❌ Redirected to login - auth protection is working');
      console.log('Let me try the actual login flow...');
      
      // Do actual login
      await doRealLogin(page);
    }
    
  } catch (error) {
    console.error('Test error:', error);
  } finally {
    console.log('\n📊 Test complete. Browser will close in 5 seconds...');
    await new Promise(resolve => setTimeout(resolve, 5000));
    await browser.close();
  }
}

async function doRealLogin(page) {
  console.log('\n📱 Performing real login...\n');
  
  // Navigate to home
  await page.goto('http://localhost:3000', { waitUntil: 'networkidle0' });
  
  // Enter phone number
  console.log('Entering phone number...');
  await page.type('input[placeholder*="98193"]', '+************');
  await page.screenshot({ path: 'test-screenshots/real-login-phone.png' });
  
  // Click submit
  console.log('Requesting OTP...');
  await page.click('button[type="submit"]');
  
  // Wait for OTP field
  await page.waitForSelector('input[maxlength="6"]', { timeout: 10000 });
  console.log('✅ OTP field appeared');
  
  console.log('\n⚠️  MANUAL STEP REQUIRED:');
  console.log('Please enter the OTP you received on your phone.');
  console.log('The test will continue automatically after you submit the OTP.\n');
  
  // Wait for navigation after OTP submission
  try {
    await page.waitForNavigation({ 
      timeout: 60000, // Wait up to 60 seconds for manual OTP entry
      waitUntil: 'networkidle0' 
    });
    
    console.log('✅ Login successful!');
    await page.screenshot({ path: 'test-screenshots/after-login.png' });
    
    // Now test dashboard features
    await testDashboardFeatures(page);
    
  } catch (e) {
    console.log('⏱️  Timeout waiting for OTP. Please run the test again and enter OTP quickly.');
  }
}

async function testDashboardFeatures(page) {
  console.log('\n🏠 Testing Dashboard Features\n');
  
  // 1. Check main layout
  console.log('Checking dashboard layout...');
  const hasMainContent = await page.$('main') !== null;
  const hasSidebar = await page.$('aside, [class*="sidebar"]') !== null;
  
  console.log(`Main content: ${hasMainContent ? '✅' : '❌'}`);
  console.log(`Sidebar: ${hasSidebar ? '✅' : '❌'}`);
  
  await page.screenshot({ path: 'test-screenshots/dashboard-layout.png' });
  
  // 2. Check for thread types
  console.log('\nChecking thread types...');
  const threadTypes = ['Patient Cases', 'Research', 'Quick Facts'];
  
  for (const type of threadTypes) {
    const exists = await page.evaluate((text) => {
      return document.body.textContent.includes(text);
    }, type);
    
    console.log(`${type}: ${exists ? '✅' : '❌'}`);
  }
  
  // 3. Try to send a message
  console.log('\nTesting message functionality...');
  const messageInput = await page.$('textarea[placeholder*="message"], textarea[placeholder*="ask"], textarea');
  
  if (messageInput) {
    console.log('✅ Message input found');
    
    // Type a test message
    await messageInput.type('What is the normal range for blood pressure?');
    await page.screenshot({ path: 'test-screenshots/message-typed.png' });
    
    // Send message
    await page.keyboard.press('Enter');
    console.log('Message sent, waiting for response...');
    
    // Wait for response
    await page.waitForTimeout(5000);
    await page.screenshot({ path: 'test-screenshots/message-response.png' });
    
    // Count messages
    const messages = await page.$$('[role="article"], .message, [class*="message"]');
    console.log(`Total messages in chat: ${messages.length}`);
  } else {
    console.log('❌ Message input not found');
  }
  
  // 4. Check for file attachment
  console.log('\nChecking file attachment feature...');
  const attachButton = await page.$('button[aria-label*="attach"], button[title*="attach"], input[type="file"]');
  console.log(`File attachment: ${attachButton ? '✅' : '❌'}`);
  
  // 5. Check markdown rendering
  console.log('\nChecking markdown support...');
  const markdownElements = await page.evaluate(() => {
    return {
      tables: document.querySelectorAll('table').length,
      code: document.querySelectorAll('pre, code').length,
      lists: document.querySelectorAll('ul, ol').length
    };
  });
  
  console.log('Markdown elements found:');
  Object.entries(markdownElements).forEach(([element, count]) => {
    console.log(`  ${element}: ${count}`);
  });
  
  // 6. Navigate through sections
  console.log('\nTesting section navigation...');
  
  // Try Patient Cases
  const patientCasesBtn = await page.evaluateHandle(() => {
    return Array.from(document.querySelectorAll('button, a')).find(el => 
      el.textContent.includes('Patient Cases')
    );
  });
  
  if (patientCasesBtn) {
    await patientCasesBtn.click();
    await page.waitForTimeout(1000);
    console.log('✅ Navigated to Patient Cases');
    await page.screenshot({ path: 'test-screenshots/patient-cases.png' });
  }
  
  // Try Research
  const researchBtn = await page.evaluateHandle(() => {
    return Array.from(document.querySelectorAll('button, a')).find(el => 
      el.textContent.includes('Research')
    );
  });
  
  if (researchBtn) {
    await researchBtn.click();
    await page.waitForTimeout(1000);
    console.log('✅ Navigated to Research');
    await page.screenshot({ path: 'test-screenshots/research.png' });
  }
  
  // Try Quick Facts
  const quickFactsBtn = await page.evaluateHandle(() => {
    return Array.from(document.querySelectorAll('button, a')).find(el => 
      el.textContent.includes('Quick Facts')
    );
  });
  
  if (quickFactsBtn) {
    await quickFactsBtn.click();
    await page.waitForTimeout(1000);
    console.log('✅ Navigated to Quick Facts');
    await page.screenshot({ path: 'test-screenshots/quick-facts.png' });
    
    // Test a quick fact query
    const input = await page.$('textarea');
    if (input) {
      await input.click({ clickCount: 3 });
      await input.type('Metformin dosage for Type 2 diabetes');
      await page.keyboard.press('Enter');
      
      console.log('Sent quick fact query, waiting for response...');
      await page.waitForTimeout(3000);
      await page.screenshot({ path: 'test-screenshots/quick-fact-response.png' });
    }
  }
  
  console.log('\n✨ Dashboard feature testing complete!');
}

// Run the test
testDashboardWithMockAuth();