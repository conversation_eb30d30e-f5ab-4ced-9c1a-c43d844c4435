-- Doctor Dashboard Database Utility Scripts
-- Common queries and maintenance operations

-- 1. Check database status
SELECT 'Database Status Check' as operation;
SELECT 
    schemaname,
    tablename,
    n_tup_ins as total_inserts,
    n_tup_upd as total_updates,
    n_tup_del as total_deletes
FROM pg_stat_user_tables 
WHERE schemaname = 'public';

-- 2. View all users with their verification status
SELECT 'User Overview' as operation;
SELECT 
    id,
    phone,
    name,
    role,
    access,
    tenant_id,
    meta->>'verification_status' as verification_status,
    meta->>'organization' as organization,
    created_at
FROM users 
ORDER BY created_at DESC;

-- 3. View active sessions
SELECT 'Active Sessions' as operation;
SELECT 
    s.id,
    s.user_id,
    s.phone,
    s.tenant,
    s.created_at,
    s.expires_at,
    CASE 
        WHEN s.expires_at > NOW() THEN 'Active'
        ELSE 'Expired'
    END as status
FROM web_chat_sessions s
ORDER BY s.created_at DESC;

-- 4. View pending verification tokens
SELECT 'Pending Verification Tokens' as operation;
SELECT 
    identifier,
    token,
    expires,
    CASE 
        WHEN expires > NOW() THEN 'Valid'
        ELSE 'Expired'
    END as status
FROM verification_token
ORDER BY expires DESC;

-- 5. Clean up expired tokens (maintenance query)
-- DELETE FROM verification_token WHERE expires < NOW();
-- DELETE FROM web_chat_sessions WHERE expires_at < NOW();

-- 6. View doctors pending approval
SELECT 'Doctors Pending Approval' as operation;
SELECT 
    id,
    phone,
    name,
    meta->>'organization' as organization,
    meta->>'license_number' as license_number,
    meta->>'specialization' as specialization,
    created_at
FROM users 
WHERE role = 'doctor' 
AND access = false
AND meta->>'verification_status' = 'pending'
ORDER BY created_at ASC;