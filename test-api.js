// API Testing Script for Doctor Dashboard Backend
// Run with: node test-api.js

const API_BASE = 'http://localhost:3000/api';

// Helper function for API calls
async function apiCall(method, endpoint, data = null, token = null) {
  const url = `${API_BASE}${endpoint}`;
  const options = {
    method,
    headers: {
      'Content-Type': 'application/json',
    },
  };
  
  if (token) {
    options.headers['Authorization'] = `Bearer ${token}`;
  }
  
  if (data) {
    options.body = JSON.stringify(data);
  }
  
  try {
    const response = await fetch(url, options);
    const result = await response.json();
    
    console.log(`${method} ${endpoint}:`, {
      status: response.status,
      result
    });
    
    return { status: response.status, data: result };
  } catch (error) {
    console.error(`${method} ${endpoint} failed:`, error.message);
    return { error: error.message };
  }
}

// Test suite
async function runTests() {
  console.log('🧪 Starting Doctor Dashboard API Tests\n');
  
  // Test 1: Health Check
  console.log('1. Testing Health Check');
  await apiCall('GET', '/health');
  console.log();
  
  // Test 2: Request OTP
  console.log('2. Testing Request OTP');
  const testPhone = '+**********';
  await apiCall('POST', '/auth/request-otp', { phone: testPhone });
  console.log();
  
  // Test 3: Invalid phone format
  console.log('3. Testing Invalid Phone Format');
  await apiCall('POST', '/auth/request-otp', { phone: 'invalid' });
  console.log();
  
  // Test 4: Registration (will fail without valid OTP, but tests validation)
  console.log('4. Testing Registration Validation');
  await apiCall('POST', '/auth/verify-and-register', {
    phone: testPhone,
    otp: '123456',
    name: 'Dr. Test User',
    organization: 'Test Hospital',
    license_number: 'MD123456',
    specialization: 'Internal Medicine'
  });
  console.log();
  
  // Test 5: Login attempt
  console.log('5. Testing Login');
  await apiCall('POST', '/auth/login', { phone: testPhone });
  console.log();
  
  // Test 6: Protected endpoint without token
  console.log('6. Testing Protected Endpoint (No Token)');
  await apiCall('GET', '/auth/me');
  console.log();
  
  // Test 7: Admin endpoints without token
  console.log('7. Testing Admin Endpoint (No Token)');
  await apiCall('GET', '/admin/pending-registrations');
  console.log();
  
  console.log('✅ API Tests Completed');
}

// Check if we're in Node.js environment
if (typeof window === 'undefined') {
  // Import fetch for Node.js
  import('node-fetch').then(fetch => {
    global.fetch = fetch.default;
    runTests();
  }).catch(() => {
    console.log('Please install node-fetch: npm install node-fetch');
    console.log('Or run these tests from a browser console');
  });
} else {
  // Browser environment
  runTests();
}