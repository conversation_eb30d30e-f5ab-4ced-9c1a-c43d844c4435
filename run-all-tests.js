#!/usr/bin/env node

const { E2ETestRunner, TestSuite, CONFIG } = require('./e2e-test-suite');
const PatientCaseTestSuite = require('./e2e-test-suites/patient-case-tests');
const ResearchTestSuite = require('./e2e-test-suites/research-tests');
const QuickFactsTestSuite = require('./e2e-test-suites/quick-facts-tests');
const ErrorHandlingTestSuite = require('./e2e-test-suites/error-handling-tests');
const { execSync } = require('child_process');

// Extended test runner with all suites
class ComprehensiveTestRunner extends E2ETestRunner {
  constructor() {
    super();
    
    // Add additional test suites
    this.testSuites.push(
      new PatientCaseTestSuite(),
      new ResearchTestSuite(),
      new QuickFactsTestSuite(),
      new ErrorHandlingTestSuite()
    );
    
    this.authTestCount = 0;
    this.maxAuthBeforeClear = 3; // Clear rate limits after every 3 authentications
  }

  async clearRateLimits() {
    try {
      console.log('\n🔄 Clearing Redis rate limits...');
      execSync('node rate-limit-manager.js clear', { stdio: 'inherit' });
      this.authTestCount = 0;
      return true;
    } catch (error) {
      console.warn('⚠️  Could not clear rate limits:', error.message);
      return false;
    }
  }

  async run() {
    console.log('\n🚀 Starting Comprehensive E2E Test Suite for Doctor Dashboard\n');
    console.log(`Configuration:`);
    console.log(`  - Base URL: ${CONFIG.baseUrl}`);
    console.log(`  - Test Phone: ${CONFIG.testPhone}`);
    console.log(`  - Headless: ${CONFIG.headless}`);
    console.log(`  - Total Test Suites: ${this.testSuites.length}`);
    console.log('\n' + '='.repeat(60) + '\n');

    // Check if specific suite is requested
    const requestedSuite = process.argv[2];
    let suitesToRun = this.testSuites;
    
    if (requestedSuite) {
      suitesToRun = this.testSuites.filter(suite => 
        suite.name.toLowerCase() === requestedSuite.toLowerCase()
      );
      
      if (suitesToRun.length === 0) {
        console.error(`❌ Test suite "${requestedSuite}" not found`);
        console.log('\nAvailable test suites:');
        this.testSuites.forEach(suite => console.log(`  - ${suite.name}`));
        process.exit(1);
      }
      
      console.log(`Running only: ${requestedSuite}\n`);
    }

    // Run test suites
    for (let i = 0; i < suitesToRun.length; i++) {
      const suite = suitesToRun[i];
      
      // Clear rate limits after every 3 suites (except for the first one)
      if (i > 0 && i % this.maxAuthBeforeClear === 0) {
        await this.clearRateLimits();
        await new Promise(resolve => setTimeout(resolve, 2000));
      }
      
      console.log(`\n📋 Running Test Suite: ${suite.name}`);
      console.log('-'.repeat(40));
      
      const result = await suite.run();
      this.processResults(result);
      
      // Track if this suite uses authentication
      if (suite.name !== 'error-handling') {
        this.authTestCount++;
      }
      
      // Add delay between suites
      if (i < suitesToRun.length - 1) {
        console.log('\n⏳ Waiting before next suite...\n');
        await new Promise(resolve => setTimeout(resolve, 2000));
      }
    }

    // Generate final report
    this.generateReport();
  }

  processResults(result) {
    testResults.tests.push(result);
    testResults.totalTests += result.steps.length;
    
    if (result.passed) {
      testResults.passed += result.steps.filter(s => s.passed).length;
      testResults.failed += result.steps.filter(s => !s.passed).length;
    } else {
      testResults.failed += result.steps.length;
      testResults.errors.push(...result.errors);
    }
    
    // Summary for this suite
    console.log(`\nSuite Results:`);
    console.log(`  ✓ Passed: ${result.steps.filter(s => s.passed).length}`);
    console.log(`  ✗ Failed: ${result.steps.filter(s => !s.passed).length}`);
    console.log(`  ⏱️  Duration: ${(result.duration / 1000).toFixed(2)}s`);
    
    // Show failed tests
    const failedSteps = result.steps.filter(s => !s.passed);
    if (failedSteps.length > 0) {
      console.log(`\n  Failed Tests:`);
      failedSteps.forEach(step => {
        console.log(`    ❌ ${step.name}`);
        if (step.error) {
          console.log(`       Error: ${step.error}`);
        }
      });
    }
  }

  generateReport() {
    const testResults = {
      totalTests: this.totalTests || 0,
      passed: this.passed || 0,
      failed: this.failed || 0,
      errors: this.errors || [],
      tests: this.tests || [],
      startTime: this.startTime,
      endTime: new Date(),
      duration: 0
    };

    testResults.duration = testResults.endTime - testResults.startTime;

    console.log('\n' + '='.repeat(60));
    console.log('📊 COMPREHENSIVE TEST REPORT');
    console.log('='.repeat(60) + '\n');
    
    console.log(`Total Test Suites: ${this.testSuites.length}`);
    console.log(`Total Tests: ${testResults.totalTests}`);
    console.log(`✅ Passed: ${testResults.passed}`);
    console.log(`❌ Failed: ${testResults.failed}`);
    console.log(`⏱️  Total Duration: ${(testResults.duration / 1000).toFixed(2)}s`);
    console.log(`📊 Success Rate: ${((testResults.passed / testResults.totalTests) * 100).toFixed(1)}%`);
    
    // Suite-wise breakdown
    console.log('\n📋 Suite-wise Results:');
    console.log('-'.repeat(40));
    
    testResults.tests.forEach(suite => {
      const passed = suite.steps.filter(s => s.passed).length;
      const total = suite.steps.length;
      const percentage = total > 0 ? ((passed / total) * 100).toFixed(1) : 0;
      
      console.log(`${suite.passed ? '✅' : '❌'} ${suite.name}: ${passed}/${total} (${percentage}%)`);
    });

    // Failed tests summary
    if (testResults.failed > 0) {
      console.log('\n❌ Failed Tests Summary:');
      console.log('-'.repeat(40));
      
      testResults.tests.forEach(suite => {
        const failedSteps = suite.steps.filter(s => !s.passed);
        if (failedSteps.length > 0) {
          console.log(`\n${suite.name}:`);
          failedSteps.forEach(step => {
            console.log(`  - ${step.name}`);
            if (step.error) {
              console.log(`    Error: ${step.error}`);
            }
          });
        }
      });
    }

    // Save detailed report
    const timestamp = new Date().toISOString().replace(/:/g, '-');
    const reportPath = `test-results/comprehensive-report-${timestamp}.json`;
    
    try {
      const fs = require('fs');
      fs.writeFileSync(reportPath, JSON.stringify(testResults, null, 2));
      console.log(`\n📄 Detailed report saved to: ${reportPath}`);
    } catch (error) {
      console.error('Failed to save report:', error.message);
    }

    // Test recommendations
    console.log('\n💡 Recommendations:');
    if (testResults.failed === 0) {
      console.log('  ✅ All tests passed! The application is functioning well.');
    } else {
      console.log('  ⚠️  Some tests failed. Please review the errors above.');
      console.log('  📸 Check screenshots in test-results/screenshots/ for visual debugging.');
      console.log('  📝 Check logs in test-results/logs/ for detailed execution logs.');
    }

    // Exit with appropriate code
    process.exit(testResults.failed > 0 ? 1 : 0);
  }
}

// Initialize globals
global.testResults = {
  totalTests: 0,
  passed: 0,
  failed: 0,
  errors: [],
  startTime: new Date(),
  tests: []
};

// Command line help
if (process.argv.includes('--help') || process.argv.includes('-h')) {
  console.log(`
Doctor Dashboard E2E Test Suite

Usage:
  node run-all-tests.js [suite-name] [options]

Test Suites:
  - authentication    : Login and OTP verification tests
  - dashboard-navigation : Dashboard layout and navigation tests  
  - messaging        : Message input and chat functionality tests
  - patient-cases    : Patient case management tests
  - research         : Research section tests
  - quick-facts      : Quick facts and medical queries tests
  - error-handling   : Error handling and edge case tests

Options:
  --help, -h         : Show this help message
  HEADLESS=false     : Run tests in visible browser mode
  TEST_OTP=123456    : Provide OTP for automated login

Examples:
  node run-all-tests.js                    # Run all tests
  node run-all-tests.js authentication     # Run only authentication tests
  HEADLESS=false node run-all-tests.js     # Run with visible browser
`);
  process.exit(0);
}

// Run the tests
const runner = new ComprehensiveTestRunner();

// Extend the runner to use our globals
runner.totalTests = global.testResults.totalTests;
runner.passed = global.testResults.passed;
runner.failed = global.testResults.failed;
runner.errors = global.testResults.errors;
runner.tests = global.testResults.tests;
runner.startTime = global.testResults.startTime;

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log('\n\n⚠️  Tests interrupted by user');
  process.exit(1);
});

runner.run().catch(error => {
  console.error('\n💥 Fatal error running tests:', error);
  process.exit(1);
});