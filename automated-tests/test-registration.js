const puppeteer = require('puppeteer');

// Test configuration
const BASE_URL = 'http://localhost:3000';
const TEST_PHONE = '+919819304846';
const TEST_OTP = '123456';

// Test data for registration
const registrationData = {
  name: 'Dr. Test User',
  hospital: 'Test Hospital',
  license: 'TEST123456',
  specialization: 'General Medicine'
};

// Viewport configurations
const viewports = {
  desktop: { width: 1200, height: 800 },
  mobile: { width: 375, height: 667, isMobile: true }
};

// Utility function to wait
const wait = (ms) => new Promise(resolve => setTimeout(resolve, ms));

// Test registration flow
async function testRegistration(viewportName, viewport) {
  console.log(`\n========== Testing Registration on ${viewportName} ==========`);
  
  const browser = await puppeteer.launch({
    headless: false, // Set to true for CI/CD
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });
  
  try {
    const page = await browser.newPage();
    await page.setViewport(viewport);
    
    // Set up alert handler
    let lastAlertText = null;
    page.on('dialog', async dialog => {
      lastAlertText = dialog.message();
      console.log('Alert detected:', lastAlertText);
      await dialog.accept();
    });
    
    // Capture console logs
    page.on('console', msg => {
      if (msg.type() === 'error') {
        console.log('Browser console error:', msg.text());
      }
    });
    
    // Monitor network requests
    const apiCalls = [];
    page.on('request', request => {
      if (request.url().includes('/api/')) {
        apiCalls.push({
          url: request.url(),
          method: request.method(),
          postData: request.postData()
        });
      }
    });
    
    page.on('response', async response => {
      if (response.url().includes('/api/auth/verify-and-register')) {
        console.log('Registration API response:', response.status());
        try {
          const responseData = await response.json();
          console.log('Response data:', JSON.stringify(responseData, null, 2));
        } catch (e) {
          console.log('Could not parse response as JSON');
        }
      }
    });
    
    console.log('1. Navigating to homepage...');
    await page.goto(BASE_URL, { waitUntil: 'networkidle2' });
    await wait(2000);
    
    console.log('2. Clicking Register button...');
    // Click the Register tab button
    await page.evaluate(() => {
      const buttons = Array.from(document.querySelectorAll('button'));
      const registerButton = buttons.find(btn => btn.textContent === 'Register');
      if (registerButton) {
        registerButton.click();
      }
    });
    await wait(1000);
    
    console.log('3. Filling registration form...');
    
    // Use type method for better React state updates
    const inputs = await page.$$('input');
    
    // Type phone number
    await inputs[0].click({ clickCount: 3 }); // Triple click to select all
    await inputs[0].type(TEST_PHONE);
    
    // Type name
    await inputs[1].click({ clickCount: 3 });
    await inputs[1].type(registrationData.name);
    
    // Type hospital
    await inputs[2].click({ clickCount: 3 });
    await inputs[2].type(registrationData.hospital);
    
    // Type license
    await inputs[3].click({ clickCount: 3 });
    await inputs[3].type(registrationData.license);
    
    // Type specialization
    await inputs[4].click({ clickCount: 3 });
    await inputs[4].type(registrationData.specialization);
    
    await wait(1000);
    
    console.log('4. Taking screenshot of filled form...');
    await page.screenshot({ 
      path: `automated-tests/registration-form-${viewportName}.png`,
      fullPage: true 
    });
    
    console.log('5. Waiting for form validation...');
    await wait(1000); // Give React time to update state
    
    console.log('6. Clicking Send OTP button...');
    const otpButtonClicked = await page.evaluate(() => {
      const buttons = Array.from(document.querySelectorAll('button'));
      const sendOTPButton = buttons.find(btn => 
        btn.textContent.includes('Send OTP to Register')
      );
      if (sendOTPButton && !sendOTPButton.disabled) {
        sendOTPButton.click();
        return true;
      }
      return { clicked: false, disabled: sendOTPButton?.disabled, text: sendOTPButton?.textContent };
    });
    
    if (otpButtonClicked !== true) {
      console.log('❌ Send OTP button issue:', otpButtonClicked);
      const formState = await page.evaluate(() => {
        const inputs = Array.from(document.querySelectorAll('input'));
        return inputs.map(i => ({ placeholder: i.placeholder, value: i.value }));
      });
      console.log('Form state:', formState);
    }
    
    console.log('7. Waiting for OTP screen...');
    await wait(5000); // Increased wait time
    
    // Check for any errors first
    const errorMessage = await page.evaluate(() => {
      const alerts = document.querySelectorAll('.MuiAlert-root');
      if (alerts.length > 0) {
        return Array.from(alerts).map(a => a.textContent).join(', ');
      }
      return null;
    });
    
    if (errorMessage) {
      console.log('❌ Error found:', errorMessage);
    }
    
    // Check if we're on the OTP screen
    const onOTPScreen = await page.evaluate(() => {
      return document.body.textContent.includes('Enter the 6-digit code') ||
             document.querySelector('input[maxlength="6"]') !== null;
    });
    
    if (onOTPScreen) {
      console.log('8. OTP screen loaded successfully!');
      
      console.log('9. Entering OTP...');
      // Find the OTP input and type into it
      const otpInput = await page.$('input[maxlength="6"]');
      if (otpInput) {
        await otpInput.click({ clickCount: 3 }); // Clear any existing value
        await otpInput.type(TEST_OTP);
        console.log('OTP entered successfully');
      } else {
        console.log('❌ Could not find OTP input');
      }
      
      await wait(1000);
      
      // Verify OTP was entered
      const otpValue = await page.evaluate(() => {
        const input = document.querySelector('input[maxlength="6"]');
        return input ? input.value : null;
      });
      console.log('OTP value in input:', otpValue);
      
      console.log('10. Taking screenshot of OTP screen...');
      await page.screenshot({ 
        path: `automated-tests/otp-screen-${viewportName}.png`,
        fullPage: true 
      });
      
      console.log('11. Checking button state...');
      const buttonState = await page.evaluate(() => {
        const buttons = Array.from(document.querySelectorAll('button'));
        const verifyButton = buttons.find(btn => 
          btn.textContent.includes('Verify & Register')
        );
        return {
          exists: !!verifyButton,
          disabled: verifyButton?.disabled,
          text: verifyButton?.textContent,
          className: verifyButton?.className
        };
      });
      console.log('Button state:', buttonState);
      
      console.log('13. Clicking Verify & Register button...');
      const verifyButtonClicked = await page.evaluate(() => {
        const buttons = Array.from(document.querySelectorAll('button'));
        const verifyButton = buttons.find(btn => 
          btn.textContent.includes('Verify & Register')
        );
        if (verifyButton && !verifyButton.disabled) {
          verifyButton.click();
          return true;
        }
        return { clicked: false, disabled: verifyButton?.disabled, buttonText: verifyButton?.textContent };
      });
      
      if (verifyButtonClicked !== true) {
        console.log('❌ Verify button issue:', verifyButtonClicked);
      }
      
      console.log('14. Waiting for registration API call...');
      
      // Wait for API response
      const registrationResponse = await page.waitForResponse(
        response => response.url().includes('/api/auth/verify-and-register'),
        { timeout: 10000 }
      ).catch(e => {
        console.log('No registration API call detected within 10 seconds');
        return null;
      });
      
      if (registrationResponse) {
        console.log('Registration API called successfully');
        await wait(2000); // Give time for any redirects
      }
      
      // Wait for navigation or alert
      await Promise.race([
        page.waitForNavigation({ waitUntil: 'networkidle2', timeout: 5000 }).catch(() => null),
        wait(5000)
      ]);
      
      // Check if we captured any alerts
      if (lastAlertText) {
        console.log('Registration alert message:', lastAlertText);
      }
      
      // Check current URL
      const currentUrl = page.url();
      console.log('Current URL:', currentUrl);
      
      // Check for success or error messages
      const pageState = await page.evaluate(() => {
        const pageText = document.body.textContent;
        const alerts = Array.from(document.querySelectorAll('.MuiAlert-root')).map(a => a.textContent);
        const isOnDashboard = window.location.pathname.includes('/chat');
        const isOnLogin = document.body.textContent.includes('Send OTP to Login');
        
        return {
          pageText: pageText.substring(0, 500),
          alerts: alerts,
          isOnDashboard: isOnDashboard,
          isOnLogin: isOnLogin,
          pathname: window.location.pathname
        };
      });
      
      console.log('Page state after registration:', {
        alerts: pageState.alerts,
        isOnDashboard: pageState.isOnDashboard,
        isOnLogin: pageState.isOnLogin,
        pathname: pageState.pathname
      });
      
      // Take final screenshot
      await page.screenshot({ 
        path: `automated-tests/registration-result-${viewportName}.png`,
        fullPage: true 
      });
      
      if (pageState.alerts.length > 0) {
        console.log('✅ Registration response received:', pageState.alerts[0]);
      } else if (pageState.isOnDashboard) {
        console.log('✅ Registration successful - redirected to dashboard!');
      } else if (pageState.isOnLogin) {
        console.log('⚠️  Registration completed - user needs to login');
      } else {
        console.log('❓ Registration status unclear');
        console.log('Page content preview:', pageState.pageText.substring(0, 200) + '...');
      }
      
      // Log API calls made
      console.log('\nAPI calls made during test:');
      apiCalls.forEach(call => {
        console.log(`- ${call.method} ${call.url}`);
        if (call.postData) {
          console.log(`  Body: ${call.postData.substring(0, 100)}...`);
        }
      });
    } else {
      console.log('❌ Failed to reach OTP screen');
      
      // Take a screenshot to see current state
      await page.screenshot({ 
        path: `automated-tests/failed-otp-${viewportName}.png`,
        fullPage: true 
      });
      
      // Check current page state
      const pageState = await page.evaluate(() => {
        const hasOTPButton = document.body.textContent.includes('Send OTP');
        const hasRegisterTab = Array.from(document.querySelectorAll('button')).some(b => b.textContent === 'Register');
        const visibleText = Array.from(document.querySelectorAll('h1, h2, h3, p')).map(el => el.textContent).filter(t => t);
        
        return {
          stillOnRegistrationForm: hasOTPButton,
          hasRegisterTab: hasRegisterTab,
          visibleHeadings: visibleText.slice(0, 5)
        };
      });
      
      console.log('Current page state:', JSON.stringify(pageState, null, 2));
    }
    
    console.log(`\n✅ ${viewportName} test completed`);
    
  } catch (error) {
    console.error(`❌ Error during ${viewportName} test:`, error);
    await page.screenshot({ 
      path: `automated-tests/error-${viewportName}.png`,
      fullPage: true 
    });
  } finally {
    await browser.close();
  }
}

// Main test runner
async function runTests() {
  console.log('Starting Registration Tests...');
  console.log('Test Phone:', TEST_PHONE);
  console.log('Test OTP:', TEST_OTP);
  
  // Test on desktop
  await testRegistration('desktop', viewports.desktop);
  
  // Wait between tests
  await wait(2000);
  
  // Test on mobile
  await testRegistration('mobile', viewports.mobile);
  
  console.log('\n========== All Tests Completed ==========');
}

// Check if server is running
async function checkServer() {
  try {
    const response = await fetch(BASE_URL);
    return response.ok;
  } catch (error) {
    return false;
  }
}

// Main execution
(async () => {
  console.log('Checking if server is running...');
  const serverRunning = await checkServer();
  
  if (!serverRunning) {
    console.error('❌ Server is not running. Please start the server with: npm run dev');
    process.exit(1);
  }
  
  console.log('✅ Server is running');
  
  // Run the tests
  await runTests();
})();