# Automated Tests

This folder contains automated end-to-end tests for the Doctor Dashboard using P<PERSON>peteer.

## Available Tests

### 1. Registration Test (`test-registration.js`)

Tests the complete registration flow:

1. **Navigation** - Navigating to the homepage
2. **Form Navigation** - Clicking the Register tab
3. **Form Filling** - Filling all registration fields:
   - Phone number
   - Full name
   - Organization/Hospital
   - License number
   - Specialization
4. **OTP Request** - Clicking Send OTP button
5. **OTP Entry** - Entering the test OTP (123456)
6. **Registration Completion** - Verifying registration success

### 2. Login Test (`test-login.js`)

Tests the complete login flow:

1. **Navigation** - Navigating to the homepage
2. **Login Form** - Entering phone number
3. **OTP Request** - Clicking Send OTP button
4. **OTP Entry** - Entering the test OTP (123456)
5. **Login Completion** - Verifying login success
6. **Dashboard Access** - Confirming user reaches dashboard

## Running Tests

### Prerequisites
1. Ensure the server is running:
   ```bash
   npm run dev
   ```

2. Ensure Puppeteer is installed:
   ```bash
   npm install puppeteer
   ```

3. For login tests, ensure the test user exists and has access:
   - Phone: `+919819304846`
   - Must be registered and approved

### Run Individual Tests
```bash
# Registration test
node automated-tests/test-registration.js

# Login test
node automated-tests/test-login.js
```

## Test Configuration

- **Test Phone**: `+919819304846` (configured test number)
- **Test OTP**: `123456` (fixed test OTP)
- **Viewports**: Tests run on both desktop (1200x800) and mobile (375x667)

## Screenshots

The tests capture screenshots at key points:

### Registration Test Screenshots:
- `registration-form-{viewport}.png` - Filled registration form
- `otp-screen-{viewport}.png` - OTP entry screen
- `registration-result-{viewport}.png` - Final result
- `failed-otp-{viewport}.png` - Error state (if OTP screen doesn't load)

### Login Test Screenshots:
- `login-form-{viewport}.png` - Login form with phone number
- `login-otp-{viewport}.png` - OTP entry screen
- `login-result-{viewport}.png` - Dashboard after successful login
- `failed-login-{viewport}.png` - Error state (if login fails)

## Test Output

Successful output looks like:
```
✅ Server is running
Starting Registration Tests...

========== Testing Registration on desktop ==========
1. Navigating to homepage...
2. Clicking Register button...
3. Filling registration form...
4. Taking screenshot of filled form...
5. Waiting for form validation...
6. Clicking Send OTP button...
7. Waiting for OTP screen...
8. OTP screen loaded successfully!
9. Entering OTP...
10. Taking screenshot of OTP screen...
11. Clicking Verify & Register button...
✅ Registration completed successfully!
```

## Troubleshooting

1. **Send OTP button disabled**: The test includes proper input handling with Puppeteer's `type()` method to ensure React state updates correctly.

2. **OTP screen not loading**: The test waits 5 seconds for the OTP screen and checks for errors. Check server logs if this fails.

3. **Registration fails**: Check that the test phone number is not already registered or rate-limited.

## Future Enhancements

- Add login flow tests
- Add negative test cases (invalid inputs, wrong OTP)
- Add performance benchmarks
- Integrate with CI/CD pipeline
- Add visual regression testing