const puppeteer = require('puppeteer');

// Test configuration
const BASE_URL = 'http://localhost:3000';
const TEST_PHONE = '+919819304846';
const TEST_OTP = '123456';

// Viewport configurations
const viewports = {
  desktop: { width: 1200, height: 800 },
  mobile: { width: 375, height: 667, isMobile: true }
};

// Utility function to wait
const wait = (ms) => new Promise(resolve => setTimeout(resolve, ms));

// Test login flow
async function testLogin(viewportName, viewport) {
  console.log(`\n========== Testing Login on ${viewportName} ==========`);
  
  const browser = await puppeteer.launch({
    headless: false, // Set to true for CI/CD
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });
  
  try {
    const page = await browser.newPage();
    await page.setViewport(viewport);
    
    // Set up alert handler
    let lastAlertText = null;
    page.on('dialog', async dialog => {
      lastAlertText = dialog.message();
      console.log('Alert detected:', lastAlertText);
      await dialog.accept();
    });
    
    // Capture console logs
    page.on('console', msg => {
      if (msg.type() === 'error') {
        console.log('Browser console error:', msg.text());
      }
    });
    
    // Monitor network requests
    const apiCalls = [];
    page.on('request', request => {
      if (request.url().includes('/api/')) {
        apiCalls.push({
          url: request.url(),
          method: request.method(),
          postData: request.postData()
        });
      }
    });
    
    page.on('response', async response => {
      if (response.url().includes('/api/auth/verify-otp')) {
        console.log('Login API response:', response.status());
        try {
          const responseData = await response.json();
          console.log('Response data:', JSON.stringify(responseData, null, 2));
        } catch (e) {
          console.log('Could not parse response as JSON');
        }
      }
    });
    
    console.log('1. Navigating to homepage...');
    await page.goto(BASE_URL, { waitUntil: 'networkidle2' });
    await wait(2000);
    
    // Make sure we're on the login tab (default)
    const loginTabActive = await page.evaluate(() => {
      const buttons = Array.from(document.querySelectorAll('button'));
      const loginButton = buttons.find(btn => btn.textContent === 'Login');
      return loginButton && loginButton.classList.contains('MuiButton-contained');
    });
    
    if (!loginTabActive) {
      console.log('2. Clicking Login tab...');
      await page.evaluate(() => {
        const buttons = Array.from(document.querySelectorAll('button'));
        const loginButton = buttons.find(btn => btn.textContent === 'Login');
        if (loginButton) {
          loginButton.click();
        }
      });
      await wait(1000);
    } else {
      console.log('2. Already on Login tab');
    }
    
    console.log('3. Entering phone number...');
    const phoneInput = await page.$('input[placeholder*="98193"]');
    if (phoneInput) {
      await phoneInput.click({ clickCount: 3 }); // Clear any existing value
      await phoneInput.type(TEST_PHONE);
    } else {
      console.log('❌ Could not find phone input');
    }
    
    await wait(1000);
    
    console.log('4. Taking screenshot of login form...');
    await page.screenshot({ 
      path: `automated-tests/login-form-${viewportName}.png`,
      fullPage: true 
    });
    
    console.log('5. Clicking Send OTP button...');
    const otpButtonClicked = await page.evaluate(() => {
      const buttons = Array.from(document.querySelectorAll('button'));
      const sendOTPButton = buttons.find(btn => 
        btn.textContent.includes('Send OTP to Login')
      );
      if (sendOTPButton && !sendOTPButton.disabled) {
        sendOTPButton.click();
        return true;
      }
      return { clicked: false, disabled: sendOTPButton?.disabled, text: sendOTPButton?.textContent };
    });
    
    if (otpButtonClicked !== true) {
      console.log('❌ Send OTP button issue:', otpButtonClicked);
    }
    
    console.log('6. Waiting for OTP screen...');
    await wait(5000); // Increased wait time
    
    // Check for any errors first
    const errorMessage = await page.evaluate(() => {
      const alerts = document.querySelectorAll('.MuiAlert-root');
      if (alerts.length > 0) {
        return Array.from(alerts).map(a => a.textContent).join(', ');
      }
      return null;
    });
    
    if (errorMessage) {
      console.log('❌ Error found:', errorMessage);
    }
    
    // Check if we're on the OTP screen
    const onOTPScreen = await page.evaluate(() => {
      return document.body.textContent.includes('Enter the 6-digit code') ||
             document.querySelector('input[maxlength="6"]') !== null;
    });
    
    if (onOTPScreen) {
      console.log('7. OTP screen loaded successfully!');
      
      console.log('8. Entering OTP...');
      // Find the OTP input and type into it
      const otpInput = await page.$('input[maxlength="6"]');
      if (otpInput) {
        await otpInput.click({ clickCount: 3 }); // Clear any existing value
        await otpInput.type(TEST_OTP);
        console.log('OTP entered successfully');
      } else {
        console.log('❌ Could not find OTP input');
      }
      
      await wait(1000);
      
      // Verify OTP was entered
      const otpValue = await page.evaluate(() => {
        const input = document.querySelector('input[maxlength="6"]');
        return input ? input.value : null;
      });
      console.log('OTP value in input:', otpValue);
      
      console.log('9. Taking screenshot of OTP screen...');
      await page.screenshot({ 
        path: `automated-tests/login-otp-${viewportName}.png`,
        fullPage: true 
      });
      
      console.log('10. Checking button state...');
      const buttonState = await page.evaluate(() => {
        const buttons = Array.from(document.querySelectorAll('button'));
        const verifyButton = buttons.find(btn => 
          btn.textContent.includes('Verify & Login')
        );
        return {
          exists: !!verifyButton,
          disabled: verifyButton?.disabled,
          text: verifyButton?.textContent,
          className: verifyButton?.className
        };
      });
      console.log('Button state:', buttonState);
      
      console.log('11. Clicking Verify & Login button...');
      const verifyButtonClicked = await page.evaluate(() => {
        const buttons = Array.from(document.querySelectorAll('button'));
        const verifyButton = buttons.find(btn => 
          btn.textContent.includes('Verify & Login')
        );
        if (verifyButton && !verifyButton.disabled) {
          verifyButton.click();
          return true;
        }
        return { clicked: false, disabled: verifyButton?.disabled, buttonText: verifyButton?.textContent };
      });
      
      if (verifyButtonClicked !== true) {
        console.log('❌ Verify button issue:', verifyButtonClicked);
      }
      
      console.log('12. Waiting for login API call...');
      
      // Wait for API response
      const loginResponse = await page.waitForResponse(
        response => response.url().includes('/api/auth/verify-otp'),
        { timeout: 10000 }
      ).catch(e => {
        console.log('No login API call detected within 10 seconds');
        return null;
      });
      
      if (loginResponse) {
        console.log('Login API called successfully');
        await wait(2000); // Give time for any redirects
      }
      
      // Wait for navigation to dashboard
      await page.waitForNavigation({ waitUntil: 'networkidle2', timeout: 10000 }).catch(() => {
        console.log('Navigation timeout - checking current state');
      });
      
      // Check current URL
      const currentUrl = page.url();
      console.log('Current URL:', currentUrl);
      
      // Check if we're on the dashboard
      const pageState = await page.evaluate(() => {
        const isOnDashboard = window.location.pathname === '/' && 
                             (document.body.textContent.includes('New Conversation') ||
                              document.querySelector('[data-testid="add-icon"]') !== null);
        const hasThreadList = document.querySelector('.MuiList-root') !== null;
        const hasChatInterface = document.querySelector('textarea[placeholder*="Type your message"]') !== null;
        const localStorage = window.localStorage;
        
        return {
          isOnDashboard: isOnDashboard,
          hasThreadList: hasThreadList,
          hasChatInterface: hasChatInterface,
          pathname: window.location.pathname,
          hasAuthToken: !!localStorage.getItem('authToken'),
          hasAccessToken: !!localStorage.getItem('access_token'),
          hasUser: !!localStorage.getItem('user')
        };
      });
      
      console.log('Page state after login:', pageState);
      
      // Take final screenshot
      await page.screenshot({ 
        path: `automated-tests/login-result-${viewportName}.png`,
        fullPage: true 
      });
      
      if (pageState.isOnDashboard && pageState.hasAuthToken) {
        console.log('✅ Login successful - user is on dashboard with auth tokens!');
        
        // Check user data
        const userData = await page.evaluate(() => {
          const userStr = localStorage.getItem('user');
          return userStr ? JSON.parse(userStr) : null;
        });
        
        if (userData) {
          console.log('User data:', {
            id: userData.id,
            phone: userData.phone,
            name: userData.name,
            access: userData.access
          });
        }
      } else if (lastAlertText) {
        console.log('⚠️  Login attempt completed with alert:', lastAlertText);
      } else {
        console.log('❓ Login status unclear');
      }
      
      // Log API calls made
      console.log('\nAPI calls made during test:');
      apiCalls.forEach(call => {
        console.log(`- ${call.method} ${call.url}`);
        if (call.postData) {
          console.log(`  Body: ${call.postData.substring(0, 100)}...`);
        }
      });
    } else {
      console.log('❌ Failed to reach OTP screen');
      
      // Take a screenshot to see current state
      await page.screenshot({ 
        path: `automated-tests/failed-login-${viewportName}.png`,
        fullPage: true 
      });
    }
    
    console.log(`\n✅ ${viewportName} test completed`);
    
  } catch (error) {
    console.error(`❌ Error during ${viewportName} test:`, error);
    await page.screenshot({ 
      path: `automated-tests/error-login-${viewportName}.png`,
      fullPage: true 
    });
  } finally {
    await browser.close();
  }
}

// Main test runner
async function runTests() {
  console.log('Starting Login Tests...');
  console.log('Test Phone:', TEST_PHONE);
  console.log('Test OTP:', TEST_OTP);
  console.log('\nNOTE: This test assumes the user is already registered and has access.');
  
  // Test on desktop
  await testLogin('desktop', viewports.desktop);
  
  // Wait between tests
  await wait(2000);
  
  // Test on mobile
  await testLogin('mobile', viewports.mobile);
  
  console.log('\n========== All Tests Completed ==========');
}

// Check if server is running
async function checkServer() {
  try {
    const response = await fetch(BASE_URL);
    return response.ok;
  } catch (error) {
    return false;
  }
}

// Main execution
(async () => {
  console.log('Checking if server is running...');
  const serverRunning = await checkServer();
  
  if (!serverRunning) {
    console.error('❌ Server is not running. Please start the server with: npm run dev');
    process.exit(1);
  }
  
  console.log('✅ Server is running');
  
  // Run the tests
  await runTests();
})();