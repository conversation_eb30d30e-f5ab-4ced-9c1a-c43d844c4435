#!/usr/bin/env node

/**
 * Doctor Dashboard Authentication API Test Suite
 * 
 * This script tests the complete authentication flow:
 * 1. Health check
 * 2. Doctor registration flow
 * 3. Admin approval flow
 * 4. Login flow
 * 5. Protected endpoints
 * 6. Token refresh
 * 7. Logout
 * 
 * Run with: node test-auth-flow.js
 */

const API_BASE = 'http://localhost:3000/api';

// Test data
const TEST_DOCTOR = {
  phone: '+**********',
  name: 'Dr. <PERSON>',
  organization: 'General Hospital',
  license_number: 'MD123456789',
  specialization: 'Internal Medicine'
};

const TEST_ADMIN = {
  phone: '+**********' // Using the admin phone from database setup
};

// Global variables to store tokens and user IDs
let doctorUserId = null;
let adminAccessToken = null;
let doctorAccessToken = null;
let doctor<PERSON>efreshToken = null;

// Helper functions
function log(message, data = null) {
  console.log(`\n📝 ${message}`);
  if (data) {
    console.log(JSON.stringify(data, null, 2));
  }
}

function logError(message, error) {
  console.log(`\n❌ ${message}`);
  console.log(JSON.stringify(error, null, 2));
}

function logSuccess(message, data = null) {
  console.log(`\n✅ ${message}`);
  if (data) {
    console.log(JSON.stringify(data, null, 2));
  }
}

async function apiCall(method, endpoint, data = null, token = null) {
  const url = `${API_BASE}${endpoint}`;
  const options = {
    method,
    headers: {
      'Content-Type': 'application/json',
    },
  };
  
  if (token) {
    options.headers['Authorization'] = `Bearer ${token}`;
  }
  
  if (data) {
    options.body = JSON.stringify(data);
  }
  
  try {
    const response = await fetch(url, options);
    const result = await response.json();
    
    return { 
      status: response.status, 
      success: response.ok,
      data: result 
    };
  } catch (error) {
    return { 
      error: error.message,
      success: false 
    };
  }
}

function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

// Test functions
async function testHealthCheck() {
  log('🏥 Testing Health Check');
  
  const response = await apiCall('GET', '/health');
  
  if (response.success && response.data.success) {
    logSuccess('Health check passed', response.data);
    return true;
  } else {
    logError('Health check failed', response);
    return false;
  }
}

async function testDoctorRegistrationFlow() {
  log('👨‍⚕️ Testing Doctor Registration Flow');
  
  // Step 1: Request OTP for doctor
  log('Step 1: Requesting OTP for doctor registration');
  const otpResponse = await apiCall('POST', '/auth/request-otp', {
    phone: TEST_DOCTOR.phone
  });
  
  if (!otpResponse.success) {
    logError('Failed to request OTP', otpResponse);
    return false;
  }
  
  logSuccess('OTP requested successfully', otpResponse.data);
  
  // Step 2: Register doctor (using mock OTP - will fail but tests validation)
  log('Step 2: Attempting doctor registration with invalid OTP (expected to fail)');
  const registerResponse = await apiCall('POST', '/auth/verify-and-register', {
    phone: TEST_DOCTOR.phone,
    otp: '123456', // Mock OTP - will fail
    name: TEST_DOCTOR.name,
    organization: TEST_DOCTOR.organization,
    license_number: TEST_DOCTOR.license_number,
    specialization: TEST_DOCTOR.specialization
  });
  
  if (registerResponse.data.code === 'OTP_INVALID') {
    logSuccess('Registration validation working correctly (OTP validation failed as expected)');
  } else {
    logError('Unexpected registration response', registerResponse);
  }
  
  // Step 3: Simulate successful registration by directly inserting into database
  log('Step 3: For testing purposes, we need to manually create a test doctor account');
  log('In a real scenario, the doctor would receive a valid OTP via SMS');
  
  return true;
}

async function testAdminFlow() {
  log('👔 Testing Admin Authentication Flow');
  
  // Step 1: Admin login - request OTP
  log('Step 1: Requesting OTP for admin login');
  const adminOtpResponse = await apiCall('POST', '/auth/login', {
    phone: TEST_ADMIN.phone
  });
  
  if (!adminOtpResponse.success) {
    logError('Failed to request admin OTP', adminOtpResponse);
    return false;
  }
  
  logSuccess('Admin OTP requested successfully', adminOtpResponse.data);
  
  // Step 2: Admin login verification (will fail with mock OTP)
  log('Step 2: Attempting admin login with mock OTP (expected to fail)');
  const adminLoginResponse = await apiCall('POST', '/auth/verify-login', {
    phone: TEST_ADMIN.phone,
    otp: '123456' // Mock OTP
  });
  
  if (adminLoginResponse.data.code === 'OTP_INVALID') {
    logSuccess('Admin login OTP validation working correctly');
  } else {
    logError('Unexpected admin login response', adminLoginResponse);
  }
  
  return true;
}

async function testProtectedEndpoints() {
  log('🔒 Testing Protected Endpoints');
  
  // Test accessing protected endpoint without token
  log('Testing /auth/me without token (should fail)');
  const noTokenResponse = await apiCall('GET', '/auth/me');
  
  if (noTokenResponse.status === 401) {
    logSuccess('Protected endpoint correctly rejects requests without token');
  } else {
    logError('Protected endpoint should reject requests without token', noTokenResponse);
  }
  
  // Test admin endpoints without token
  log('Testing admin endpoint without token (should fail)');
  const adminNoTokenResponse = await apiCall('GET', '/admin/pending-registrations');
  
  if (adminNoTokenResponse.status === 401) {
    logSuccess('Admin endpoint correctly rejects requests without token');
  } else {
    logError('Admin endpoint should reject requests without token', adminNoTokenResponse);
  }
  
  return true;
}

async function testRefreshTokenFlow() {
  log('🔄 Testing Refresh Token Flow');
  
  // Test refresh without token
  log('Testing refresh without token (should fail)');
  const noRefreshTokenResponse = await apiCall('POST', '/auth/refresh', {
    refresh_token: 'invalid-token'
  });
  
  if (noRefreshTokenResponse.status === 401) {
    logSuccess('Refresh endpoint correctly rejects invalid tokens');
  } else {
    logError('Refresh endpoint should reject invalid tokens', noRefreshTokenResponse);
  }
  
  return true;
}

async function testInputValidation() {
  log('✅ Testing Input Validation');
  
  // Test invalid phone number format
  log('Testing invalid phone number format');
  const invalidPhoneResponse = await apiCall('POST', '/auth/request-otp', {
    phone: 'invalid-phone'
  });
  
  if (invalidPhoneResponse.status === 400) {
    logSuccess('Phone number validation working correctly');
  } else {
    logError('Should reject invalid phone numbers', invalidPhoneResponse);
  }
  
  // Test missing required fields
  log('Testing missing required fields in registration');
  const missingFieldsResponse = await apiCall('POST', '/auth/verify-and-register', {
    phone: TEST_DOCTOR.phone,
    otp: '123456'
    // Missing name, organization, license_number
  });
  
  if (missingFieldsResponse.status === 400) {
    logSuccess('Required field validation working correctly');
  } else {
    logError('Should reject requests with missing required fields', missingFieldsResponse);
  }
  
  return true;
}

async function testRatelimiting() {
  log('⏱️ Testing Rate Limiting (Basic)');
  
  // Make multiple rapid requests to test rate limiting
  log('Making multiple rapid OTP requests');
  
  const promises = [];
  for (let i = 0; i < 5; i++) {
    promises.push(apiCall('POST', '/auth/request-otp', {
      phone: `+123456789${i}`
    }));
  }
  
  const responses = await Promise.all(promises);
  const successCount = responses.filter(r => r.success).length;
  
  log(`${successCount} out of 5 rapid requests succeeded`);
  logSuccess('Rate limiting test completed (basic implementation allows all for now)');
  
  return true;
}

async function testDatabaseCleanup() {
  log('🧹 Testing Database Cleanup (without admin token)');
  
  const cleanupResponse = await apiCall('POST', '/admin/cleanup');
  
  if (cleanupResponse.status === 401) {
    logSuccess('Cleanup endpoint correctly requires admin authentication');
  } else {
    logError('Cleanup endpoint should require admin authentication', cleanupResponse);
  }
  
  return true;
}

// Main test runner
async function runAllTests() {
  console.log('🚀 Starting Doctor Dashboard Authentication API Tests');
  console.log('='.repeat(60));
  
  const tests = [
    { name: 'Health Check', fn: testHealthCheck },
    { name: 'Doctor Registration Flow', fn: testDoctorRegistrationFlow },
    { name: 'Admin Flow', fn: testAdminFlow },
    { name: 'Protected Endpoints', fn: testProtectedEndpoints },
    { name: 'Refresh Token Flow', fn: testRefreshTokenFlow },
    { name: 'Input Validation', fn: testInputValidation },
    { name: 'Rate Limiting', fn: testRateLimit },
    { name: 'Database Cleanup', fn: testDatabaseCleanup }
  ];
  
  let passedTests = 0;
  let totalTests = tests.length;
  
  for (const test of tests) {
    try {
      console.log('\n' + '─'.repeat(40));
      const result = await test.fn();
      if (result) {
        passedTests++;
        logSuccess(`Test "${test.name}" PASSED`);
      } else {
        logError(`Test "${test.name}" FAILED`);
      }
    } catch (error) {
      logError(`Test "${test.name}" ERROR`, { message: error.message });
    }
    
    // Small delay between tests
    await sleep(500);
  }
  
  // Final summary
  console.log('\n' + '='.repeat(60));
  console.log('📊 TEST SUMMARY');
  console.log('='.repeat(60));
  console.log(`Total Tests: ${totalTests}`);
  console.log(`Passed: ${passedTests}`);
  console.log(`Failed: ${totalTests - passedTests}`);
  console.log(`Success Rate: ${Math.round((passedTests / totalTests) * 100)}%`);
  
  if (passedTests === totalTests) {
    console.log('\n🎉 All tests passed! The authentication API is working correctly.');
  } else {
    console.log('\n⚠️  Some tests failed. Please check the output above for details.');
  }
  
  console.log('\n📝 Note: Tests involving OTP verification will fail with mock OTP codes.');
  console.log('This is expected behavior - in production, users would receive real OTP codes via SMS.');
  console.log('\n🔧 To test the complete flow with real OTP codes:');
  console.log('1. Integrate a real SMS service (Twilio, AWS SNS, etc.)');
  console.log('2. Use the OTP codes received via SMS');
  console.log('3. Complete the registration and login flows');
}

// Check if fetch is available
async function checkEnvironment() {
  if (typeof fetch === 'undefined') {
    try {
      // Try to import fetch for Node.js
      const { default: fetch } = await import('node-fetch');
      global.fetch = fetch;
      console.log('✅ Using node-fetch for HTTP requests');
    } catch (error) {
      console.error('❌ fetch is not available. Please install node-fetch:');
      console.error('npm install node-fetch');
      process.exit(1);
    }
  }
}

// Run tests
async function main() {
  await checkEnvironment();
  await runAllTests();
}

// Execute if run directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(console.error);
} else {
  // Export for use in other modules
  module.exports = {
    runAllTests,
    testHealthCheck,
    testDoctorRegistrationFlow,
    testAdminFlow,
    testProtectedEndpoints,
    testRefreshTokenFlow,
    testInputValidation,
    testRateLimit,
    testDatabaseCleanup
  };
}