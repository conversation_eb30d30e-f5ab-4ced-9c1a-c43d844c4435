#!/bin/bash

echo "🔧 PM2 Redis Connection Fix"
echo "=========================="

# Stop the current PM2 process
echo "🛑 Stopping PM2 process..."
pm2 stop doctor-dashboard 2>/dev/null || true

# Clear PM2 logs
echo "🧹 Clearing PM2 logs..."
pm2 flush doctor-dashboard 2>/dev/null || true

# Test Redis connection
echo "🔍 Testing Redis connection..."
node test-redis-fix.js &
REDIS_TEST_PID=$!
sleep 10
kill $REDIS_TEST_PID 2>/dev/null

# Create PM2 ecosystem file with better Redis handling
echo "📝 Creating PM2 ecosystem file..."
cat > ecosystem.config.js << 'EOF'
module.exports = {
  apps: [{
    name: 'doctor-dashboard',
    script: './custom-server.js',
    instances: 1,
    exec_mode: 'fork',
    env: {
      NODE_ENV: 'production',
      PORT: 3000
    },
    error_file: './logs/pm2-error.log',
    out_file: './logs/pm2-out.log',
    log_file: './logs/pm2-combined.log',
    time: true,
    max_memory_restart: '1G',
    node_args: '--max-old-space-size=1024',
    // Restart settings for Redis connection issues
    max_restarts: 10,
    min_uptime: '10s',
    // Health check
    health_check_url: 'http://localhost:3000/health',
    // Environment variables
    env_file: '.env',
    // Graceful shutdown
    kill_timeout: 5000,
    // Restart on Redis errors
    restart_delay: 2000,
    // Log rotation
    log_type: 'json',
    merge_logs: true
  }]
};
EOF

# Create logs directory
mkdir -p logs

# Start with the new configuration
echo "🚀 Starting PM2 with Redis fix..."
pm2 start ecosystem.config.js

# Monitor for Redis errors
echo "🔍 Monitoring for Redis errors..."
pm2 logs doctor-dashboard --lines 50 | grep -E "(Redis|Socket closed|Error)" &

# Show status
echo "📊 PM2 Status:"
pm2 status

echo "✅ PM2 Redis fix applied"
echo "🔍 Monitor logs with: pm2 logs doctor-dashboard"
echo "🔍 Check health: curl http://localhost:3000/health"
echo "🔍 Check Redis health: curl http://localhost:3000/health/redis"
echo "🛑 Stop with: pm2 stop doctor-dashboard"
