#!/bin/bash

echo "🚀 Starting Python Research Service"
echo "=================================="

# Navigate to service directory
cd /Users/<USER>/doctor-dashboard/python-research-service

# Check if venv exists
if [ ! -d "venv" ]; then
    echo "❌ Virtual environment not found!"
    echo "Please run: bash setup_env.sh"
    exit 1
fi

# Activate virtual environment
echo "Activating virtual environment..."
source venv/bin/activate

# Set environment variables
echo "Setting environment variables..."
export OPENAI_API_KEY="********************************************************************************************************************************************************************"
export VECTORX_API_TOKEN="achdaxqj:atYrnbfEUQaP5qZzdvz20QhuylCubkcR:iw1"
export VECTORX_ENCRYPTION_KEY="2abb958e9d25879776e56927c63d2d26"
export VECTORX_INDEX_NAME="pubmed_collection_v1"
export LLM_MODEL_VERSION="gpt-4o-mini"

echo "Configuration:"
echo "  - VectorX Index: $VECTORX_INDEX_NAME"
echo "  - LLM Model: $LLM_MODEL_VERSION"
echo "  - API Token: ${VECTORX_API_TOKEN:0:10}..."

# Start the service
echo ""
echo "Starting FastAPI service..."
python -m uvicorn main:app --reload --host 0.0.0.0 --port 8000