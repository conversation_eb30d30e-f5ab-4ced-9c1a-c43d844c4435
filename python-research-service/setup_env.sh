#!/bin/bash

echo "🔧 Setting up Python Research Service Environment"
echo "=============================================="

# Navigate to the service directory
cd /Users/<USER>/doctor-dashboard/python-research-service

# Remove old virtual environment if exists
if [ -d "venv" ]; then
    echo "Removing old virtual environment..."
    rm -rf venv
fi

# Create new virtual environment
echo "Creating new virtual environment..."
python3 -m venv venv

# Activate virtual environment
echo "Activating virtual environment..."
source venv/bin/activate

# Upgrade pip
echo "Upgrading pip..."
pip install --upgrade pip

# Install requirements
echo "Installing requirements..."
pip install fastapi uvicorn openai sentence-transformers torch numpy pandas python-dotenv

# Install vecx with correct import path
echo "Installing VectorX client..."
pip install vecx

# Test the import
echo ""
echo "Testing VectorX import..."
python -c "from vecx.vectorx import VectorX; print('✅ VectorX import successful!')" || {
    echo "❌ VectorX import failed. Trying alternative installation..."
    pip uninstall -y vecx
    pip install --force-reinstall vecx
}

echo ""
echo "✅ Setup complete!"
echo ""
echo "To start the service, run:"
echo "  source venv/bin/activate"
echo "  export OPENAI_API_KEY='your-key'"
echo "  export VECTORX_API_TOKEN='your-token'"
echo "  export VECTORX_ENCRYPTION_KEY='your-key'"
echo "  export VECTORX_INDEX_NAME='pubmed_collection_v1'"
echo "  python -m uvicorn main:app --reload"