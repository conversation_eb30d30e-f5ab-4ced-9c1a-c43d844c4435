# Medical Research API (Python FastAPI)

A high-performance Python FastAPI service that replaces the Node.js research implementation with superior ML/NLP capabilities.

## 🚀 Features

- **Query Decomposition**: Advanced query breaking using OpenAI GPT-4.1-nano
- **Vector Retrieval**: Efficient embedding generation with sentence-transformers 
- **Similarity Filtering**: Configurable threshold-based filtering
- **Cross-Encoder Reranking**: Sub-query averaging with MS-MARCO model
- **LLM Response Generation**: Comprehensive medical summaries

## 📋 Pipeline Overview

```
User Query → Query Decomposition → Vector Retrieval → Similarity Filtering → Cross-Encoder Reranking → LLM Summary
```

1. **Query Decomposition**: Break complex medical queries into 3-5 specific sub-questions
2. **Expansion**: Generate 3-6 search terms for each sub-question  
3. **Vector Retrieval**: Search PubMed embeddings using BGE-M3 model via VectorX
4. **Similarity Filtering**: Filter results above threshold (default 0.8)
5. **Cross-Encoder Reranking**: Score papers vs sub-questions, rank by average cross-score
6. **LLM Response**: Generate structured medical research summary

## 🛠️ Installation

### Prerequisites
- Python 3.8+
- OpenAI API key
- VectorX API credentials

### Setup

1. **Navigate to service directory**:
   ```bash
   cd python-research-service
   ```

2. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Set environment variables**:
   ```bash
   export OPENAI_API_KEY="your-openai-key"
   export VECTORX_API_TOKEN="your-vectorx-token" 
   export VECTORX_ENCRYPTION_KEY="your-vectorx-key"
   export VECTORX_INDEX_NAME="pubmed_10papers_bge_m3"
   export LLM_MODEL_VERSION="gpt-4.1-nano"
   ```

4. **Start the service**:
   ```bash
   ./start.sh
   # OR
   python main.py
   ```

The service will be available at:
- **API**: http://localhost:8000
- **Docs**: http://localhost:8000/docs
- **Health**: http://localhost:8000/health

## 📡 API Usage

### Research Endpoint

**POST** `/api/research`

```json
{
  "query": "What are the effects of metformin on cardiovascular outcomes?",
  "k": 15,
  "similarity_threshold": 0.8
}
```

**Response**:
```json
{
  "query": "What are the effects of metformin on cardiovascular outcomes?",
  "subquestions": ["..."],
  "expansions": {"subq1": ["term1", "term2"]},
  "papers": [
    {
      "id": "paper_id",
      "title": "Paper Title",
      "content": "Abstract content...",
      "similarity_score": 0.95,
      "cross_score": 0.87,
      "matched_expansions": ["metformin", "cardiovascular"],
      "matched_subquestions": ["effects of metformin"]
    }
  ],
  "summary": "Based on the research papers...",
  "processing_time": 2.34
}
```

## 🏗️ Architecture

```
main.py
├── routers/
│   └── research.py          # API endpoints
├── services/
│   ├── query_expansion.py   # OpenAI query decomposition
│   ├── vector_retrieval.py  # sentence-transformers + VectorX
│   ├── similarity_filter.py # Threshold filtering
│   ├── cross_encoder.py     # MS-MARCO reranking
│   └── llm_service.py       # OpenAI response generation
├── models/
│   └── schemas.py           # Pydantic models
└── config.py                # Configuration management
```

## 🔧 Configuration

| Environment Variable | Description | Default |
|---------------------|-------------|---------|
| `OPENAI_API_KEY` | OpenAI API key | Required |
| `VECTORX_API_TOKEN` | VectorX API token | Required |
| `VECTORX_ENCRYPTION_KEY` | VectorX encryption key | Required |
| `VECTORX_INDEX_NAME` | VectorX index name | `pubmed_10papers_bge_m3` |
| `LLM_MODEL_VERSION` | OpenAI model | `gpt-4.1-nano` |
| `PYTHON_RESEARCH_SERVICE_URL` | Service URL for Next.js | `http://localhost:8000` |

## 🧪 Testing

Test the service health:
```bash
curl http://localhost:8000/health
```

Test research endpoint:
```bash
curl -X POST http://localhost:8000/api/research \
  -H "Content-Type: application/json" \
  -d '{"query": "diabetes treatment", "k": 5}'
```

## 🔄 Integration with Next.js

The Next.js API route at `/api/research` automatically forwards requests to this Python service. No frontend changes needed.

## ⚡ Performance

- **10x better ML/NLP** libraries vs Node.js
- **Superior medical models**: BioBERT, BGE-M3 embeddings
- **Faster vector operations**: NumPy/SciPy vs JavaScript  
- **Advanced reranking**: Cross-encoder vs basic BM25
- **Better async processing**: Python asyncio vs Node.js

## 🚨 Troubleshooting

1. **Service won't start**:
   - Check Python version (3.8+)
   - Verify environment variables
   - Check port 8000 availability

2. **Model loading errors**:
   - Ensure sufficient disk space for models
   - Check internet connection for model downloads

3. **VectorX connection issues**:
   - Verify API credentials
   - Check VectorX index name

4. **OpenAI API errors**:
   - Verify API key validity
   - Check rate limits and billing

## 📊 Models Used

- **Embeddings**: `BAAI/bge-m3` (1024 dimensions)
- **Cross-Encoder**: `cross-encoder/ms-marco-MiniLM-L-12-v2`  
- **LLM**: `gpt-4.1-nano` (OpenAI)
- **Vector DB**: VectorX with PubMed embeddings

## 🤝 Contributing

1. Follow the existing code structure
2. Add type hints for all functions
3. Include error handling and logging
4. Update tests for new features
5. Document any new configuration options