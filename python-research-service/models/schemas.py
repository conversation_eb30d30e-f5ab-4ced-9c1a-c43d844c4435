from pydantic import BaseModel, Field
from typing import List, Dict, Optional, Any

class ResearchRequest(BaseModel):
    query: str = Field(..., min_length=1, description="Medical research query")
    k: int = Field(default=15, ge=1, le=50, description="Number of results to return")
    similarity_threshold: float = Field(default=0.8, ge=0.0, le=1.0, description="Similarity threshold for filtering")

class PaperResult(BaseModel):
    uuid: Optional[str] = None
    pmid: Optional[str] = None
    id: str  # Fallback identifier for backward compatibility
    title: str
    abstract: str  # Primary field for abstract content
    content: str  # Keep for backward compatibility
    url: Optional[str] = None
    similarity_score: float
    cross_score: float
    matched_expansions: List[str] = []
    matched_subquestions: List[str] = []

class ResearchResponse(BaseModel):
    query: str
    subquestions: List[str]
    expansions: Dict[str, List[str]]
    papers: List[PaperResult]
    summary: str
    processing_time: float
    query_expansion_time: Optional[float] = None
    vector_search_time: Optional[float] = None
    similarity_filter_time: Optional[float] = None
    cross_encoder_time: Optional[float] = None
    llm_processing_time: Optional[float] = None

class HealthResponse(BaseModel):
    status: str = "healthy"
    version: str = "1.0.0"
    timestamp: str

class ErrorResponse(BaseModel):
    error: str
    detail: Optional[str] = None
    timestamp: str