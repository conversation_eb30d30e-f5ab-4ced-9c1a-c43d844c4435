#!/bin/bash

# Medical Research API Startup Script

echo "🚀 Starting Medical Research API (Python FastAPI)"
echo "================================================="

# Check if virtual environment exists
if [ ! -d "venv" ]; then
    echo "📦 Creating virtual environment..."
    python3 -m venv venv
fi

# Activate virtual environment
echo "🔧 Activating virtual environment..."
source venv/bin/activate

# Install dependencies
echo "📚 Installing dependencies..."
pip install -r requirements.txt

# Check environment variables
echo "🔍 Checking environment variables..."
if [ -z "$OPENAI_API_KEY" ]; then
    echo "⚠️  Warning: OPENAI_API_KEY not set"
fi

if [ -z "$VECTORX_API_TOKEN" ]; then
    echo "⚠️  Warning: VECTORX_API_TOKEN not set"
fi

if [ -z "$VECTORX_ENCRYPTION_KEY" ]; then
    echo "⚠️  Warning: VECTORX_ENCRYPTION_KEY not set"
fi

echo ""
echo "🌟 Starting FastAPI server on http://localhost:8000"
echo "📖 API Documentation: http://localhost:8000/docs"
echo "❤️  Health Check: http://localhost:8000/health"
echo ""

# Start the server
python main.py