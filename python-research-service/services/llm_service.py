from typing import List, Dict, Any
import openai
from config import config

class LLMService:
    """Service for generating final research summaries using LLM"""
    
    def __init__(self):
        if not config.OPENAI_API_KEY:
            raise ValueError("OpenAI API key not configured")
        
        self.client = openai.AsyncOpenAI(api_key=config.OPENAI_API_KEY)
    
    async def generate_response(self, query: str, subquestions: List[str], papers: List[Dict[str, Any]]) -> str:
        """Generate comprehensive medical research summary"""
        try:
            # Extract abstracts from papers
            abstracts = []
            for index, paper in enumerate(papers[:10]):  # Limit to top 10 papers to avoid token limits
                meta = paper.get("meta", {})
                content = meta.get("content", paper.get("content", ""))
                title = meta.get("title", "Untitled")
                
                # Truncate content if too long
                if len(content) > 500:
                    content = content[:500] + "..."
                
                abstracts.append(f"Paper {index + 1}: {title}\n{content}")
            
            abstracts_text = "\n\n".join(abstracts)
            subq_section = "\n".join([f"- {sq}" for sq in subquestions])
            
            prompt = f"""Original Query: {query}
---
Key Sub-questions:
{subq_section}
---
Abstracts:
{abstracts_text}
---
Summary:"""

            completion = await self.client.chat.completions.create(
                model=config.LLM_MODEL_VERSION,
                messages=[
                    {
                        "role": "system",
                        "content": """You are a highly skilled and discerning medical research summarizer for clinical decision support. Your expertise lies in extracting, synthesizing, and presenting critical information from academic literature in a clear, concise, and clinically actionable manner.

Your task is to generate an evidence-based academic summary that directly and comprehensively answers the original clinical query. This summary must integrate findings from the provided abstracts, specifically addressing the key sub-questions, and be presented in a well-structured, easy-to-digest format that facilitates quick understanding for a physician.

Strictly adhere to the following guidelines:

1. **Direct Answer & Flow:** Directly and conclusively answer the "Original Query." Synthesize information into clear, cohesive points, ensuring a proper flow of information that is easy to follow and leaves no room for confusion.
2. **Evidence-Based:** All information in the summary must be explicitly supported by the provided "Abstracts." Do not introduce external knowledge or make speculative statements.
3. **Sub-question Integration:** Implicitly or explicitly weave the answers to the "Key Sub-questions" into the summary's structure.
4. **Structured Format:** Present the summary using clear, distinct bullet points, or a table if the data is best conveyed in a tabular format.
5. **Concise Synthesis:** Synthesize findings from multiple abstracts into a unified answer, avoiding redundancy and repetition by integrating similar points and highlighting critical nuances or contradictions if they exist. Focus on the most pertinent information for the query.
6. **Academic Tone:** Maintain a formal, objective, and academic tone suitable for clinical research.

---
"""
                    },
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                temperature=0.3,
                max_tokens=2000,
            )
            
            summary = completion.choices[0].message.content
            
            if not summary or summary.strip() == prompt.strip():
                print("[LLMService] LLM returned invalid response")
                return self.generate_basic_summary(papers)
            
            return summary
            
        except Exception as e:
            print(f"[LLMService] Error generating response: {e}")
            return self.generate_basic_summary(papers)
    
    def generate_basic_summary(self, papers: List[Dict[str, Any]]) -> str:
        """Generate basic summary without LLM as fallback"""
        if not papers:
            return "No relevant papers found for your query."
        
        summary = [f"Based on {len(papers)} relevant papers found:"]
        
        for index, paper in enumerate(papers[:5]):
            meta = paper.get("meta", {})
            title = meta.get("title", "Untitled")
            content = meta.get("content", paper.get("content", ""))
            
            snippet = content[:200].strip() if content else ""
            
            summary.append(f"\n{index + 1}. **{title}**")
            if snippet:
                summary.append(f"   {snippet}...")
        
        if len(papers) > 5:
            summary.append(f"\n...and {len(papers) - 5} more papers.")
        
        return "\n".join(summary)