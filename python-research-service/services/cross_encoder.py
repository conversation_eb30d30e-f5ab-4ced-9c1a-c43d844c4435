import asyncio
from typing import List, Dict, Any, Tu<PERSON>
from sentence_transformers import CrossEncoder
from config import config
import torch

class CrossEncoderService:
    """Service for cross-encoder reranking using subquery averaging"""
    
    def __init__(self):
        print(f"[CrossEncoder] Loading cross-encoder model: {config.CROSS_ENCODER_MODEL}")
        
        # Optimize for t3.medium (2 vCPUs)
        torch.set_num_threads(2)
        print(f"[CrossEncoder] Set PyTorch threads to 2 for t3.medium")
        
        self.cross_encoder = CrossEncoder(config.CROSS_ENCODER_MODEL)
        print(f"[CrossEncoder] Model loaded successfully")
    
    def truncate_content(self, content: str, max_tokens: int = 512) -> str:
        """Truncate content to max_tokens to prevent tokenization overhead"""
        # Simple word-based truncation (approximate)
        words = content.split()
        if len(words) > max_tokens:
            return ' '.join(words[:max_tokens])
        return content
    
    def score_pairs(self, query: str, documents: List[Dict[str, Any]]) -> List[float]:
        """Score query-document pairs using cross-encoder"""
        try:
            # Prepare query-document pairs
            pairs = []
            for doc in documents:
                content = doc.get("meta", {}).get("content", doc.get("content", ""))
                # Truncate content to prevent tokenization overhead
                truncated_content = self.truncate_content(content)
                pairs.append([query, truncated_content])
            
            if not pairs:
                return []
            
            # Score all pairs at once
            scores = self.cross_encoder.predict(pairs)
            
            # Convert to list if it's a numpy array
            if hasattr(scores, 'tolist'):
                scores = scores.tolist()
            elif not isinstance(scores, list):
                scores = [float(scores)]
            
            return scores
            
        except Exception as e:
            print(f"[CrossEncoder] Error in cross-encoder scoring: {e}")
            # Return default scores if cross-encoder fails
            return [0.0] * len(documents)
    
    def score_batch_pairs(self, queries: List[str], documents: List[Dict[str, Any]]) -> Dict[str, List[float]]:
        """Score multiple query-document pairs in a single batch for maximum efficiency"""
        try:
            # Prepare all query-document pairs
            pairs = []
            query_indices = []
            
            for q_idx, query in enumerate(queries):
                for doc in documents:
                    content = doc.get("meta", {}).get("content", doc.get("content", ""))
                    # Truncate content to prevent tokenization overhead
                    truncated_content = self.truncate_content(content)
                    pairs.append([query, truncated_content])
                    query_indices.append(q_idx)
            
            if not pairs:
                return {query: [] for query in queries}
            
            print(f"[CrossEncoder] Batch scoring {len(pairs)} pairs for {len(queries)} queries and {len(documents)} documents")
            
            # Score all pairs at once - this is the key optimization
            scores = self.cross_encoder.predict(pairs)
            
            # Convert to list if it's a numpy array
            if hasattr(scores, 'tolist'):
                scores = scores.tolist()
            elif not isinstance(scores, list):
                scores = [float(scores)]
            
            # Group scores by query
            result = {query: [] for query in queries}
            docs_per_query = len(documents)
            
            for q_idx, query in enumerate(queries):
                start_idx = q_idx * docs_per_query
                end_idx = start_idx + docs_per_query
                result[query] = scores[start_idx:end_idx]
            
            return result
            
        except Exception as e:
            print(f"[CrossEncoder] Error in batch cross-encoder scoring: {e}")
            # Return default scores if cross-encoder fails
            return {query: [0.0] * len(documents) for query in queries}
    
    async def subquery_averaging_rerank(self, subqueries: List[str], candidate_docs: List[Dict[str, Any]], top_k: int = 15) -> List[Dict[str, Any]]:
        """Rerank documents using sub-query averaging method with batch processing"""
        try:
            print(f"[CrossEncoder] Starting optimized sub-query averaging rerank for {len(candidate_docs)} documents")
            
            # Use batch processing to score all subquery-document pairs at once
            batch_scores = await asyncio.get_event_loop().run_in_executor(
                None, 
                self.score_batch_pairs, 
                subqueries, 
                candidate_docs
            )
            
            # Aggregate scores for each document across all subqueries
            doc_scores_agg = {}
            
            # Build document mapping
            for index, doc in enumerate(candidate_docs):
                doc_id = (
                    doc.get("meta", {}).get("id") or 
                    doc.get("meta", {}).get("url") or 
                    f"doc_{index}"
                )
                
                if doc_id not in doc_scores_agg:
                    doc_scores_agg[doc_id] = {
                        "scores": [],
                        "doc": doc
                    }
                
                # Collect scores from all subqueries for this document
                for subquery in subqueries:
                    if subquery in batch_scores and index < len(batch_scores[subquery]):
                        doc_scores_agg[doc_id]["scores"].append(batch_scores[subquery][index])
            
            # Calculate average scores and sort
            avg_scores = []
            for doc_id, data in doc_scores_agg.items():
                if data["scores"]:
                    avg_score = sum(data["scores"]) / len(data["scores"])
                else:
                    avg_score = 0.0
                
                avg_scores.append({
                    "doc_id": doc_id,
                    "avg_score": avg_score,
                    "doc": data["doc"]
                })
            
            # Sort by average score (highest first)
            avg_scores.sort(key=lambda x: x["avg_score"], reverse=True)
            
            # Return top K documents with scores
            reranked = []
            for item in avg_scores[:top_k]:
                doc = item["doc"].copy()
                doc["cross_score"] = item["avg_score"]
                reranked.append(doc)
            
            print(f"[CrossEncoder] Optimized reranking completed - top {len(reranked)} documents")
            return reranked
            
        except Exception as e:
            print(f"[CrossEncoder] Error in optimized sub-query averaging rerank: {e}")
            # Return original documents if reranking fails
            return candidate_docs[:top_k]
    
    async def reranking(self, original_query: str, candidate_docs: List[Dict[str, Any]], top_k: int = 15, subqueries: List[str] = None) -> List[Dict[str, Any]]:
        """Main reranking function"""
        try:
            print(f"[CrossEncoder] Starting reranking for {len(candidate_docs)} documents")
            
            # Use sub-query averaging method if subqueries are provided
            if subqueries and len(subqueries) > 0:
                return await self.subquery_averaging_rerank(subqueries, candidate_docs, top_k)
            
            # Fallback to simple scoring with original query
            print("[CrossEncoder] No subqueries provided, using original query for reranking")
            return await self.subquery_averaging_rerank([original_query], candidate_docs, top_k)
            
        except Exception as e:
            print(f"[CrossEncoder] Error in reranking pipeline: {e}")
            # Return original documents if reranking fails
            return candidate_docs[:top_k]