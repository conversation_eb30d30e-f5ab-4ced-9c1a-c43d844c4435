import asyncio
import httpx
from typing import List, Dict, Any
from config import config
import logging

logger = logging.getLogger(__name__)

class EmbeddingClient:
    """HTTP client for the embedding service"""
    
    def __init__(self, base_url: str = config.EMBEDDING_MODEL_URL):
        self.base_url = base_url.rstrip('/')
        self.timeout = httpx.Timeout(30.0)
        
    async def generate_embedding(self, text: str, normalize: bool = True) -> List[float]:
        """Generate embedding for a single text"""
        try:
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                response = await client.post(
                    f"{self.base_url}/embed/single",
                    json={
                        "text": text,
                        "normalize": normalize
                    }
                )
                response.raise_for_status()
                data = response.json()
                return data["embedding"]
                
        except httpx.TimeoutException:
            logger.error(f"Timeout calling embedding service for text: {text[:100]}...")
            raise Exception("Embedding service timeout")
        except httpx.HTTPError as e:
            logger.error(f"HTTP error calling embedding service: {e}")
            raise Exception(f"Embedding service error: {e}")
        except Exception as e:
            logger.error(f"Unexpected error calling embedding service: {e}")
            raise Exception(f"Embedding service error: {e}")
    
    async def generate_batch_embeddings(self, texts: List[str], normalize: bool = True, batch_size: int = 32) -> List[List[float]]:
        """Generate embeddings for multiple texts"""
        try:
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                response = await client.post(
                    f"{self.base_url}/embed/batch",
                    json={
                        "texts": texts,
                        "normalize": normalize,
                        "batch_size": batch_size
                    }
                )
                response.raise_for_status()
                data = response.json()
                return data["embeddings"]
                
        except httpx.TimeoutException:
            logger.error(f"Timeout calling embedding service for {len(texts)} texts")
            raise Exception("Embedding service timeout")
        except httpx.HTTPError as e:
            logger.error(f"HTTP error calling embedding service: {e}")
            raise Exception(f"Embedding service error: {e}")
        except Exception as e:
            logger.error(f"Unexpected error calling embedding service: {e}")
            raise Exception(f"Embedding service error: {e}")
    
    async def health_check(self) -> bool:
        """Check if the embedding service is healthy"""
        try:
            async with httpx.AsyncClient(timeout=httpx.Timeout(5.0)) as client:
                response = await client.get(f"{self.base_url}/health")
                response.raise_for_status()
                data = response.json()
                return data.get("status") == "healthy"
        except Exception as e:
            logger.error(f"Embedding service health check failed: {e}")
            return False
