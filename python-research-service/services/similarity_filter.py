from typing import List, Dict, Any

class SimilarityFilterService:
    """Service for filtering search results based on similarity threshold"""
    
    def filter_on_threshold(self, search_result: List[Dict[str, Any]], similarity_threshold: float = 0.8) -> List[Dict[str, Any]]:
        """
        Filter search results based on similarity threshold
        
        Args:
            search_result: List of search results (dicts or objects)
            similarity_threshold: Minimum similarity score to include (default 0.8)
            
        Returns:
            Filtered list of results above threshold
        """
        results = []
        for result in search_result:
            similarity = None
            if isinstance(result, dict):
                # For dict results, check different possible similarity keys
                # Note: "similarity" field is a dict in vector_retrieval results, 
                # the actual score is in "initial_score"
                similarity = (
                    result.get("initial_score") or 
                    result.get("score") or
                    result.get("similarity")  # Last resort, in case it's a simple float
                )
            else:
                # For object results, try to get similarity attribute
                similarity = getattr(result, "similarity", None)
            
            if similarity is not None and isinstance(similarity, (int, float)) and similarity > similarity_threshold:
                results.append(result)
        
        print(f"[SimilarityFilter] Filtered {len(search_result)} results to {len(results)} above threshold {similarity_threshold}")
        return results
    
    def get_similarity_stats(self, search_result: List[Dict[str, Any]]) -> Dict[str, float]:
        """Get statistics about similarity scores in the results"""
        similarities = []
        
        for result in search_result:
            similarity = None
            if isinstance(result, dict):
                similarity = (
                    result.get("initial_score") or 
                    result.get("score") or
                    result.get("similarity")  # Last resort, in case it's a simple float
                )
            else:
                similarity = getattr(result, "similarity", None)
            
            if similarity is not None:
                similarities.append(similarity)
        
        if not similarities:
            return {"min": 0.0, "max": 0.0, "mean": 0.0, "count": 0}
        
        return {
            "min": min(similarities),
            "max": max(similarities),
            "mean": sum(similarities) / len(similarities),
            "count": len(similarities)
        }