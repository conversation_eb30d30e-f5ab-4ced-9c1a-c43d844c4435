import json
import asyncio
from typing import List, Dict
import openai
from config import config

# Stopwords for filtering
STOPWORDS = {}

def is_generic_or_stopword(exp: str) -> bool:
    """Check if expansion is too generic or composed of stopwords"""
    tokens = [word.lower() for word in exp.split() if word.isalnum()]
    if not tokens or all(token in STOPWORDS for token in tokens):
        return True
    if len(tokens) < 2:
        return True
    return False

class QueryExpansionService:
    def __init__(self):
        if not config.OPENAI_API_KEY:
            raise ValueError("OpenAI API key not configured")
        
        self.client = openai.AsyncOpenAI(api_key=config.OPENAI_API_KEY)
    
    async def decompose_query(self, query: str, max_subqs: int = config.MAX_SUBQUESTIONS) -> List[str]:
        """Decompose query into sub-questions using OpenAI"""
        system_prompt = f"""
You are a lead researcher who will be given a task to research on, and your job is to generate sub-questions based on the instructions and guidelines given below.

## Context
- You are an expert medical researcher which will be given a question or task to research on. 
- Your steps in general would be first creating subquestions/subtasks from the main question/task which are short, and more broad so as to map the space and then progressively narrow focus after seeing the results from the earlier questions.
- Never generate questions which have the answers or hints of answers to the original question otherwise it will bias the search.

## Guidelines

When presented with a medical question, first conduct a cognitive mapping exercise:

1. **Question Taxonomy**: Classify the question type:
- Etiology/risk factors
- Diagnosis/screening
- Prognosis
- Therapy/intervention
- Prevention
- Health systems/delivery
- Basic science mechanism

2. **Scope Boundary Analysis**: Explicitly define:
- What biological systems are involved vs excluded
- Which populations are of interest vs out of scope
- What time horizons matter
- Which outcomes are relevant vs tangential
- What constitutes direct vs indirect evidence

### Progressive Focusing Strategy
As good secondary research is done by starting wide and then narrowing down, to avoid missing anything and explore the landscape before missing anything. 
Generate research queries following this funnel approach:

**Broad Landscape Queries** (Round 1):
- Map the entire knowledge domain
- Identify major research streams
- Discover unexpected connections
- Surface terminology variations across specialties

**Targeted Domain Queries** (Round 2):
- Focus on high-yield areas identified in Round 1
- Explore specific patient populations
- Investigate particular interventions or exposures
- Examine specific outcome measures

**Precision Queries** (Round 3):
- Address specific evidence gaps
- Resolve contradictions found earlier
- Seek recent updates on narrow topics
- Find guidance on clinical edge cases

### Relevance Evaluation Criteria

Assess each piece of evidence against these standards:

**Direct Relevance**:
- Does it address the exact population of interest?
- Does it measure the specific outcomes that matter?
- Is the intervention/exposure comparable?
- Is the follow-up period appropriate?

**Methodological Quality**:
- Is the study design appropriate for the question?
- Are biases adequately controlled?
- Is the sample size sufficient?
- Are the statistical methods sound?

**Clinical Applicability**:
- Can findings be translated to real-world practice?
- Are effect sizes clinically meaningful?
- Do benefits outweigh harms?
- Is the evidence actionable?
"""
        user_prompt = f'''
## Round 1 Task 

{query}

## Output format

- Give the output as JSON with the following 2 keys and values as mentioned below:
1. "plan": this should contain a reasoning through the guidelines especially regarding scope boundary analysis and broad landscape queries.
2. "questions": an array of 4 questions you generate after following your plan above.'''
        try:
            completion = await self.client.chat.completions.create(
                model=config.LLM_MODEL_VERSION,
                messages=[
                    {
                        "role": "system",
                        "content": system_prompt
                    },
                    {
                        "role": "user",
                        "content": user_prompt
                    }
                ],
                temperature=0.2,
                max_tokens=500,
                response_format={"type": "json_object"}
            )
            
            text = completion.choices[0].message.content
            response = json.loads(text)
            subqs = response.get("questions", [])
            
            # Filter out empty, duplicates, and overly generic
            filtered = []
            seen = set()
            
            for s in subqs:
                s_clean = s.strip()
                if not s_clean or s_clean.lower() in seen:
                    continue
                if is_generic_or_stopword(s_clean):
                    continue
                seen.add(s_clean.lower())
                filtered.append(s_clean)
            
            return filtered if filtered else [query]
            
        except Exception as e:
            print(f"[QueryExpansion] Error in decomposition: {e}")
            return [query]
    
    async def expand_subquery(self, subquery: str, max_expansions: int = config.MAX_EXPANSIONS) -> List[str]:
        """Expand subquery into search terms using OpenAI"""
        system_prompt = f"""
You are an expert medical information retrieval specialist who transforms research questions into semantic search phrases optimized for matching against medical research paper abstracts. Your deep understanding of how medical abstracts are written allows you to predict the exact language researchers use to describe their work.

## Core Understanding

Medical abstracts follow predictable patterns and use standardized vocabulary. They typically contain:
- **Background/objective statements** with problem framing
- **Methods descriptions** with specific terminology
- **Results statements** with quantitative language
- **Conclusion statements** with clinical implications

Your task is to reverse-engineer how authors would have written about the topic in their abstracts.

## Phrase Generation Strategy

### 1. Abstract Language Patterns

Medical abstracts use specific linguistic formulations:

**For Therapeutic Studies:**
- "We investigated the efficacy of [intervention] in [population]"
- "randomized controlled trial of [drug] for [condition]"
- "[intervention] significantly improved [outcome] compared to"
- "treatment with [drug] resulted in [effect]"

**For Diagnostic Studies:**
- "diagnostic accuracy of [test] for detecting [condition]"
- "sensitivity and specificity of [method] in [population]"
- "[test] showed superior performance in identifying"
- "novel biomarker for early detection of"

**For Epidemiological Studies:**
- "prevalence of [condition] among [population]"
- "risk factors associated with [outcome]"
- "incidence rates of [disease] in [demographic]"
- "population-based cohort study of"

**For Mechanistic Studies:**
- "[molecule/gene] mediates [process] through [pathway]"
- "mechanism underlying [phenomenon] involves"
- "[intervention] inhibits [target] by [mechanism]"
- "pathway analysis revealed that"

### 2. Question Decomposition Protocol

When receiving a research question, analyze it for:

**Core Concepts:**
- What is the primary medical entity (disease, drug, procedure)?
- What is the relationship being investigated?
- What is the outcome of interest?

**Context Clues:**
- Is this about causation, association, or description?
- What study types would answer this question?
- What medical specialty would publish this?

### 3. Phrase Generation Rules

**Rule 1: Generate Multiple Formulation Variants**

For each concept, create phrases using:
- Technical terminology AND common clinical terms
- Acronyms AND full spellings
- American AND British spelling variants
- Old AND current nomenclature

Example:
- "myocardial infarction" AND "heart attack" AND "MI" AND "acute coronary syndrome"
- "diabetic nephropathy" AND "diabetic kidney disease" AND "DKD"

**Rule 2: Include Methodological Markers**

Add study design indicators that appear in abstracts:
- "double-blind"
- "prospective cohort"
- "systematic review"
- "meta-analysis"
- "case-control"
- "cross-sectional"

**Rule 3: Incorporate Statistical Language**

Include terms that signal results:
- "significantly reduced"
- "no significant difference"
- "odds ratio"
- "hazard ratio"
- "relative risk"
- "p < 0.05"
- "95% confidence interval"

**Rule 4: Add Population Specifiers**

Include demographic and clinical descriptors:
- Age groups: "elderly patients", "pediatric population", "adults aged"
- Conditions: "treatment-naive", "newly diagnosed", "refractory"
- Settings: "hospitalized patients", "community-dwelling", "intensive care"

### 4. Semantic Similarity Optimization

**Technique 1: Chunk-Based Phrases**
Create 10-20 word phrases that mirror abstract sentences:
- "To evaluate the efficacy and safety of [drug] in patients with [condition]"
- "We conducted a retrospective analysis of [population] to determine"
- "The primary endpoint was [outcome] at [timepoint]"

**Technique 2: Concept Clustering**
Group related terms that often co-occur:
- "cardiovascular outcomes" + "MACE" + "mortality" + "hospitalization"
- "tumor response" + "progression-free survival" + "overall survival"
- "cognitive function" + "MMSE" + "neuropsychological testing"

**Technique 3: Negation Handling**
Include both positive and negative formulations:
- "effective in reducing" AND "failed to reduce"
- "associated with increased" AND "no association with"
- "superior to" AND "non-inferior to" AND "inferior to"

### 5. Output Structure

For each research question, generate:

**Primary Phrases** (5-7 phrases):
- Direct formulations matching expected abstract language
- Include methodology + population + intervention/exposure + outcome

**Semantic Variants** (3-5 per primary phrase):
- Synonym substitutions
- Alternate technical terminology
- Different grammatical constructions

**Precision Boosters** (2-3 phrases):
- Highly specific technical formulations
- Exact statistical terminology
- Specialty-specific jargon

## Execution Framework

When given a medical research question:

1. **Identify Question Type**:
   - Therapeutic/Intervention
   - Diagnostic/Screening
   - Prognostic
   - Etiologic/Risk Factor
   - Mechanistic

2. **Extract Key Elements**:
   ```
   P: Population/Problem
   I: Intervention/Exposure
   C: Comparison (if applicable)
   O: Outcome
   S: Study Design (implicit or explicit)
   ```

3. **Generate Abstract-Style Phrases**:
   - Opening phrase (objective statement)
   - Methods phrase (study design + population)
   - Results phrase (outcome + effect)
   - Conclusion phrase (clinical implication)

4. **Create Semantic Variations**:
   - Technical ↔ Clinical terminology
   - Active ↔ Passive voice
   - Present ↔ Past tense
   - Specific ↔ General terms

5. **Add Context Markers**:
   - Temporal qualifiers ("long-term", "acute", "chronic")
   - Severity indicators ("mild", "moderate", "severe", "advanced")
   - Treatment line indicators ("first-line", "adjuvant", "salvage")

## Examples

**Input Question**: "What is the effectiveness of SGLT2 inhibitors in preventing heart failure in type 2 diabetes patients?"

**Output Phrases**:
1. "SGLT2 inhibitors reduced heart failure hospitalization in patients with type 2 diabetes"
2. "sodium-glucose cotransporter-2 inhibitor therapy prevents cardiac events diabetic"
3. "empagliflozin dapagliflozin canagliflozin heart failure outcomes T2DM"
4. "cardiovascular benefit SGLT2i diabetic population hazard ratio"
5. "randomized trial SGLT-2 inhibitors primary prevention heart failure"

**Input Question**: "How accurate is procalcitonin for diagnosing bacterial pneumonia in children?"

**Output Phrases**:
1. "procalcitonin diagnostic accuracy bacterial pneumonia pediatric patients"
2. "PCT sensitivity specificity differentiate viral bacterial respiratory infection children"
3. "biomarker procalcitonin distinguishing bacterial etiology childhood pneumonia"
4. "diagnostic performance serum procalcitonin pediatric community-acquired pneumonia"
5. "receiver operating characteristic curve analysis PCT bacterial pneumonia"

## Special Considerations

**For Negative Studies**: Include phrases like "no significant difference", "failed to demonstrate", "comparable outcomes"

**For Rare Diseases**: Include multiple name variants, orphan disease terminology, ICD codes

**For Drug Studies**: Include generic name, brand names, drug class, mechanism of action

**For Surgical Studies**: Include procedure name variants, surgical approach terms, anatomical descriptors

Remember: The goal is not to find papers with exact keyword matches, but to generate phrases that semantically align with how medical researchers actually write their abstracts *without* actually mentioning the answers/potential answers of the question in the phrases you generate to prevent any bias while searching. Think like an abstract author, not a search engine user.

## Output format
- Generate 5 phrases. Give your output as a JSON format with one key called "output" and a list of phrases in the value. 
"""

        try:
            completion = await self.client.chat.completions.create(
                model=config.LLM_MODEL_VERSION,
                messages=[
                    {
                        "role": "system", 
                        "content": system_prompt
                    },
                    {
                        "role": "user",
                        "content": subquery
                    }
                ],
                temperature=0.3,
                max_tokens=300,
                response_format={"type": "json_object"}
            )
            
            text = completion.choices[0].message.content
            response = json.loads(text)
            expansions = response.get("output", [])
            
            # Filter expansions
            filtered = []
            seen = set()
            
            for exp in expansions:
                exp_clean = exp.strip()
                if not exp_clean or exp_clean.lower() in seen:
                    continue
                if is_generic_or_stopword(exp_clean):
                    continue
                seen.add(exp_clean.lower())
                filtered.append(exp_clean)
            
            return filtered if filtered else [subquery]
            
        except Exception as e:
            print(f"[QueryExpansion] Error in expansion: {e}")
            return [subquery]
    
    async def query_expansion(self, query: str) -> Dict[str, List[str]]:
        """Main orchestration function for query expansion"""
        try:
            # Decompose the query into sub-questions
            subquestions = await self.decompose_query(query)
            print(f"[QueryExpansion] Sub-questions: {subquestions}")
            
            # Expand all sub-questions in parallel
            expansion_tasks = [self.expand_subquery(subq) for subq in subquestions]
            expansions_results = await asyncio.gather(*expansion_tasks, return_exceptions=True)
            
            # Build the expansion dictionary
            expansion_dict = {}
            for subq, result in zip(subquestions, expansions_results):
                if isinstance(result, Exception):
                    print(f"[QueryExpansion] Error expanding \"{subq}\": {result}")
                    expansion_dict[subq] = [subq]  # Fallback to original subquery
                else:
                    expansion_dict[subq] = result
                    print(f"[QueryExpansion] Expansions for \"{subq}\": {result}")
            
            return expansion_dict
            
        except Exception as e:
            print(f"[QueryExpansion] Error in query expansion: {e}")
            # Fallback to basic expansion
            return {query: [query]}