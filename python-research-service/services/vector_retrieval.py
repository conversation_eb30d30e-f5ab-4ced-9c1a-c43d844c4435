import asyncio
from typing import List, Dict, Any
from vecx.vectorx import VectorX
from config import config
from services.embedding_client import EmbeddingClient

class VectorRetrievalService:
    def __init__(self):
        if not config.VECTORX_API_TOKEN:
            raise ValueError("VectorX token not configured")
        
        # Initialize VectorX client
        self.vx = VectorX(token=config.VECTORX_API_TOKEN)
        self.index = self.vx.get_index(name=config.VECTORX_INDEX_NAME)
        
        # Initialize embedding client (HTTP)
        print(f"[VectorRetrieval] Using embedding client at: {config.EMBEDDING_MODEL_URL}")
        self.embedding_client = EmbeddingClient(config.EMBEDDING_MODEL_URL)
        print(f"[VectorRetrieval] Embedding client initialized successfully")
    
    async def generate_embedding(self, text: str) -> List[float]:
        """Generate embedding for text using embedding service"""
        try:
            embedding = await self.embedding_client.generate_embedding(text)
            return embedding
        except Exception as e:
            print(f"[VectorRetrieval] Error generating embedding: {e}")
            raise e
    
    async def query_vector_index(self, embedding: List[float], top_k: int = 50) -> List[Dict[str, Any]]:
        """Query the vector index with an embedding"""
        try:
            # Query the vector index
            results = await asyncio.get_event_loop().run_in_executor(
                None,
                lambda: self.index.query(
                    vector=embedding,
                    top_k=top_k
                )
            )
            return results
        except Exception as e:
            print(f"[VectorRetrieval] Error querying vector index: {e}")
            raise e

    async def vector_retrieval(self, expansion_dict: Dict[str, List[str]], top_k: int = 50) -> List[Dict[str, Any]]:
        """Main vector retrieval function with a process-wide semaphore for embedding requests"""
        try:
            all_results = []
            seen_ids = set()

            # Use a process-wide semaphore to limit concurrent embedding requests
            # This is a class attribute so all calls share the same semaphore
            if not hasattr(self, "_embedding_semaphore"):
                # Limit to 2 concurrent embedding requests
                self._embedding_semaphore = asyncio.Semaphore(6)
            semaphore = self._embedding_semaphore

            # Prepare all expansion query tasks
            query_tasks = []
            expansion_info = []  # To track which subq and expansion each task corresponds to

            # Define the embed_and_query function outside the loop to avoid late binding issues
            async def embed_and_query(subq, expansion):
                async with semaphore:
                    embedding = await self.generate_embedding(expansion)
                return await self.query_vector_index(embedding, top_k)

            # Collect all expansion queries
            for subq, expansions in expansion_dict.items():
                print(f"[VectorRetrieval] Preparing subquery: \"{subq}\" with {len(expansions)} expansions")
                for expansion in expansions:
                    try:
                        # Add coroutine to tasks
                        query_tasks.append(embed_and_query(subq, expansion))
                        expansion_info.append((subq, expansion))
                    except Exception as embedding_error:
                        print(f"[VectorRetrieval] Error preparing embedding for \"{expansion}\": {embedding_error}")
                        continue

            # Execute all queries in parallel
            print(f"[VectorRetrieval] Executing {len(query_tasks)} queries in parallel...")
            query_results = await asyncio.gather(*query_tasks, return_exceptions=True)

            # Process all results
            for idx, results in enumerate(query_results):
                if isinstance(results, Exception):
                    subq, expansion = expansion_info[idx]
                    print(f"[VectorRetrieval] Error querying for expansion \"{expansion}\": {results}")
                    continue

                subq, expansion = expansion_info[idx]

                # Process results
                for rank, item in enumerate(results):
                    similarity = item.get("score", item.get("similarity", 0))

                    # Get document identifiers
                    metadata = item.get("meta", {})

                    # Extract multiple ID fields from VectorX metadata
                    uuid = metadata.get("uuid", metadata.get("id"))
                    pmid = metadata.get("pmid", metadata.get("pubmed_id"))

                    # Use UUID as primary doc_id for deduplication, fallback to other fields
                    doc_id = (
                        uuid or 
                        pmid or
                        metadata.get("url") or 
                        metadata.get("title") or 
                        f"doc_{rank}"
                    )

                    if doc_id not in seen_ids:
                        # Debug: Print meta structure for first few results
                        if len(all_results) < 3:
                            print(f"[VectorRetrieval] DEBUG - Meta keys: {list(metadata.keys())}")
                            print(f"[VectorRetrieval] DEBUG - Sample meta: {metadata}")

                        all_results.append({
                            "meta": metadata,
                            "content": metadata.get("content", ""),
                            "initial_score": similarity,
                            "expansions": {expansion},
                            "subqs": {subq},
                            "rank": {expansion: rank + 1},
                            "similarity": {expansion: similarity}
                        })
                        seen_ids.add(doc_id)
                    else:
                        # Update existing document with new expansion info
                        existing_doc = next(
                            (doc for doc in all_results 
                             if (doc["meta"].get("uuid") or doc["meta"].get("id") or 
                                 doc["meta"].get("pmid") or doc["meta"].get("pubmed_id") or
                                 doc["meta"].get("url") or doc["meta"].get("title")) == doc_id),
                            None
                        )

                        if existing_doc:
                            existing_doc["expansions"].add(expansion)
                            existing_doc["subqs"].add(subq)
                            existing_doc["rank"][expansion] = rank + 1
                            existing_doc["similarity"][expansion] = similarity
                            # Update initial score if this similarity is higher
                            existing_doc["initial_score"] = max(existing_doc["initial_score"], similarity)

            # Sort by initial score (highest similarity across all expansions)
            all_results.sort(key=lambda x: x["initial_score"], reverse=True)

            print(f"[VectorRetrieval] Retrieved {len(all_results)} unique documents")
            return all_results

        except Exception as e:
            print(f"[VectorRetrieval] Error in vector retrieval: {e}")
            raise e