import asyncio
import httpx
from typing import List, Dict, Any
from config import config
import logging
import json
import os
import uuid
from datetime import datetime

logger = logging.getLogger(__name__)

def save_payload_json(payload: dict, prefix: str):
    """Save payload to a JSON file for debugging, with unique filename, if config.SAVE_PAYLOADS is True."""
    if getattr(config, "SAVE_PAYLOADS", False):
        try:
            os.makedirs("debug_payloads", exist_ok=True)
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")
            unique_id = uuid.uuid4().hex[:8]
            filename = f"debug_payloads/{prefix}_payload_{timestamp}_{unique_id}.json"
            with open(filename, "w", encoding="utf-8") as f:
                json.dump(payload, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.warning(f"Failed to save payload to JSON file: {e}")

class CrossEncoderClient:
    """HTTP client for the cross-encoder service"""
    
    def __init__(self, base_url: str = config.CROSS_ENCODER_SERVICE_URL):
        self.base_url = base_url.rstrip('/')
        self.timeout = httpx.Timeout(config.CROSS_ENCODER_TIMEOUT)  # Configurable timeout
        
    async def rank_single(self, query: str, documents: List[str], top_k: int = 15) -> Dict[str, Any]:
        """Rank documents for a single query"""
        payload = {
            "query": query,
            "documents": documents,
            "top_k": top_k
        }

        # Save payload to a JSON file for debugging if enabled
        save_payload_json(payload, "rank_single")

        try:
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                response = await client.post(
                    f"{self.base_url}/rank/single",
                    json=payload
                )
                response.raise_for_status()
                return response.json()
                
        except httpx.TimeoutException:
            logger.error(f"Timeout calling cross-encoder service for query: {query[:100]}...")
            raise Exception("Cross-encoder service timeout")
        except httpx.HTTPError as e:
            logger.error(f"HTTP error calling cross-encoder service: {e}")
            raise Exception(f"Cross-encoder service error: {e}")
        except Exception as e:
            logger.error(f"Unexpected error calling cross-encoder service: {e}")
            raise Exception(f"Cross-encoder service error: {e}")
    
    async def rank_batch(self, queries: List[str], documents: List[str], top_k: int = 15) -> Dict[str, Any]:
        """Rank documents for multiple queries"""
        
        # Calculate total pairs and check if we need to split the batch
        total_pairs = len(queries) * len(documents)
        MAX_BATCH_PAIRS = 5000  # Conservative limit

        payload = {
            "queries": queries,
            "documents": documents,
            "top_k": top_k
        }
        save_payload_json(payload, "rank_batch")
        
        if total_pairs > MAX_BATCH_PAIRS:
            logger.info(f"Large batch detected ({total_pairs} pairs), splitting into smaller chunks")
            return await self._rank_batch_chunked(queries, documents, top_k)
        
        try:
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                response = await client.post(
                    f"{self.base_url}/rank/batch",
                    json=payload
                )
                response.raise_for_status()
                return response.json()
                
        except httpx.TimeoutException:
            logger.error(f"Timeout calling cross-encoder service for {len(queries)} queries")
            raise Exception("Cross-encoder service timeout")
        except httpx.HTTPError as e:
            logger.error(f"HTTP error calling cross-encoder service: {e}")
            raise Exception(f"Cross-encoder service error: {e}")
        except Exception as e:
            logger.error(f"Unexpected error calling cross-encoder service: {e}")
            raise Exception(f"Cross-encoder service error: {e}")
    
    async def _rank_batch_chunked(self, queries: List[str], documents: List[str], top_k: int = 15) -> Dict[str, Any]:
        """Handle large batches by chunking them"""
        MAX_BATCH_PAIRS = 5000
        
        # Calculate chunk sizes
        max_docs_per_chunk = MAX_BATCH_PAIRS // len(queries)
        if max_docs_per_chunk < 10:  # If too many queries, limit queries instead
            max_queries_per_chunk = MAX_BATCH_PAIRS // len(documents)
            max_queries_per_chunk = max(1, max_queries_per_chunk)
            return await self._rank_by_query_chunks(queries, documents, top_k, max_queries_per_chunk)
        
        # Split documents into chunks
        doc_chunks = [documents[i:i + max_docs_per_chunk] for i in range(0, len(documents), max_docs_per_chunk)]
        
        # Process each chunk
        all_results = {}
        for query in queries:
            all_results[query] = {"scores": [], "ranked_indices": [], "top_k": top_k}
        
        for chunk_idx, doc_chunk in enumerate(doc_chunks):
            logger.info(f"Processing document chunk {chunk_idx + 1}/{len(doc_chunks)} with {len(doc_chunk)} documents")
            
            payload = {
                "queries": queries,
                "documents": doc_chunk,
                "top_k": min(top_k, len(doc_chunk))
            }
            save_payload_json(payload, f"rank_batch_chunk_{chunk_idx+1}")

            try:
                async with httpx.AsyncClient(timeout=self.timeout) as client:
                    response = await client.post(
                        f"{self.base_url}/rank/batch",
                        json=payload
                    )
                    response.raise_for_status()
                    chunk_results = response.json()
                
                # Merge results
                for query in queries:
                    if query in chunk_results["query_results"]:
                        query_result = chunk_results["query_results"][query]
                        # Adjust indices to account for chunk offset
                        offset = chunk_idx * max_docs_per_chunk
                        adjusted_indices = [idx + offset for idx in query_result["ranked_indices"]]
                        
                        all_results[query]["scores"].extend(query_result["scores"])
                        all_results[query]["ranked_indices"].extend(adjusted_indices)
                        
            except Exception as e:
                logger.error(f"Error processing chunk {chunk_idx}: {e}")
                continue
        
        # Sort and limit results for each query
        for query in queries:
            if all_results[query]["scores"]:
                # Combine scores and indices, sort by score
                combined = list(zip(all_results[query]["scores"], all_results[query]["ranked_indices"]))
                combined.sort(key=lambda x: x[0], reverse=True)
                
                # Take top_k
                top_combined = combined[:top_k]
                all_results[query]["scores"] = [score for score, _ in top_combined]
                all_results[query]["ranked_indices"] = [idx for _, idx in top_combined]
        
        return {"query_results": all_results, "processing_time": 0.0}
    
    async def _rank_by_query_chunks(self, queries: List[str], documents: List[str], top_k: int, max_queries_per_chunk: int) -> Dict[str, Any]:
        """Handle large batches by chunking queries"""
        query_chunks = [queries[i:i + max_queries_per_chunk] for i in range(0, len(queries), max_queries_per_chunk)]
        
        all_results = {}
        
        for chunk_idx, query_chunk in enumerate(query_chunks):
            logger.info(f"Processing query chunk {chunk_idx + 1}/{len(query_chunks)} with {len(query_chunk)} queries")
            
            payload = {
                "queries": query_chunk,
                "documents": documents,
                "top_k": top_k
            }
            save_payload_json(payload, f"rank_query_chunk_{chunk_idx+1}")

            try:
                async with httpx.AsyncClient(timeout=self.timeout) as client:
                    response = await client.post(
                        f"{self.base_url}/rank/batch",
                        json=payload
                    )
                    response.raise_for_status()
                    chunk_results = response.json()
                
                # Merge results
                all_results.update(chunk_results["query_results"])
                        
            except Exception as e:
                logger.error(f"Error processing query chunk {chunk_idx}: {e}")
                continue
        
        return {"query_results": all_results, "processing_time": 0.0}
    
    async def health_check(self) -> bool:
        """Check if the cross-encoder service is healthy"""
        try:
            async with httpx.AsyncClient(timeout=httpx.Timeout(5.0)) as client:
                response = await client.get(f"{self.base_url}/health")
                response.raise_for_status()
                data = response.json()
                return data.get("status") == "healthy"
        except Exception as e:
            logger.error(f"Cross-encoder service health check failed: {e}")
            return False

    async def rerank_with_subquery_averaging(self, subqueries: List[str], documents: List[Dict[str, Any]], top_k: int = 15) -> List[Dict[str, Any]]:
        """Rerank documents using sub-query averaging method"""
        try:
            logger.info(f"Reranking {len(documents)} documents with {len(subqueries)} subqueries")
            
            # Extract document contents for ranking
            doc_contents = []
            for doc in documents:
                content = doc.get("meta", {}).get("content", doc.get("content", ""))
                doc_contents.append(content)
            
            # Save payload for rerank_with_subquery_averaging if enabled
            payload = {
                "subqueries": subqueries,
                "documents": doc_contents,
                "top_k": top_k
            }
            save_payload_json(payload, "rerank_with_subquery_averaging")

            # Use batch ranking for efficiency
            ranking_result = await self.rank_batch(subqueries, doc_contents, top_k)
            
            # Process results to calculate average scores
            doc_scores_agg = {}
            
            # Build document mapping
            for index, doc in enumerate(documents):
                doc_id = (
                    doc.get("meta", {}).get("id") or 
                    doc.get("meta", {}).get("url") or 
                    f"doc_{index}"
                )
                
                if doc_id not in doc_scores_agg:
                    doc_scores_agg[doc_id] = {
                        "scores": [],
                        "doc": doc,
                        "original_index": index
                    }
                
            # Collect scores from all subqueries for each document
            for subquery in subqueries:
                if subquery in ranking_result["query_results"]:
                    subquery_result = ranking_result["query_results"][subquery]
                    # Find each document's score in the ranked results
                    if "ranked_indices" in subquery_result and "scores" in subquery_result:
                        ranked_indices = subquery_result["ranked_indices"]
                        scores = subquery_result["scores"]
                        
                        # For each document, find its score for this subquery
                        for doc_id, doc_data in doc_scores_agg.items():
                            original_index = doc_data["original_index"]
                            
                            # Find where this document appears in the ranking
                            if original_index in ranked_indices:
                                rank_position = ranked_indices.index(original_index)
                                if rank_position < len(scores):
                                    doc_data["scores"].append(scores[rank_position])
                                else:
                                    doc_data["scores"].append(0.0)
                            else:
                                # Document not in top-k, assign a low score
                                doc_data["scores"].append(0.0)
            
            # Calculate average scores and sort
            avg_scores = []
            for doc_id, data in doc_scores_agg.items():
                if data["scores"]:
                    avg_score = sum(data["scores"]) / len(data["scores"])
                else:
                    avg_score = 0.0
                
                avg_scores.append({
                    "doc_id": doc_id,
                    "avg_score": avg_score,
                    "doc": data["doc"]
                })
            
            # Sort by average score (highest first)
            avg_scores.sort(key=lambda x: x["avg_score"], reverse=True)
            
            # Return top K documents with scores
            reranked = []
            for item in avg_scores[:top_k]:
                doc = item["doc"].copy()
                doc["cross_score"] = item["avg_score"]
                reranked.append(doc)
            
            logger.info(f"Reranking completed - top {len(reranked)} documents")
            return reranked
            
        except Exception as e:
            logger.error(f"Error in subquery averaging rerank: {e}")
            # Return original documents if reranking fails
            return documents[:top_k]
