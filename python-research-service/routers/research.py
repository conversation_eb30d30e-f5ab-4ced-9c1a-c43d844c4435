import time
from datetime import datetime
from typing import List
from fastapi import APIRouter, HTTPException, status
from models.schemas import ResearchRequest, ResearchResponse, ErrorResponse
from services.query_expansion import QueryExpansionService
from services.vector_retrieval import VectorRetrievalService
from services.similarity_filter import SimilarityFilterService
from services.cross_encoder_client import CrossEncoderClient
# from services.llm_service import LLMService  # Not needed anymore
import os

router = APIRouter()

# Initialize services at startup (no models loaded, just HTTP clients)
print("[Research Router] Initializing services...")
try:
    query_expansion_service = QueryExpansionService()
    vector_retrieval_service = VectorRetrievalService()
    similarity_filter_service = SimilarityFilterService()
    
    # Initialize cross-encoder client
    cross_encoder_service_url = os.getenv("CROSS_ENCODER_SERVICE_URL", "http://localhost:8002")
    cross_encoder_client = CrossEncoderClient(cross_encoder_service_url)
    print(f"[Research Router] Cross-encoder client initialized with URL: {cross_encoder_service_url}")
    
    print("[Research Router] All services initialized successfully")
except Exception as e:
    print(f"[Research Router] Failed to initialize services: {e}")
    raise e

def get_services():
    """Return already initialized services"""
    return query_expansion_service, vector_retrieval_service, similarity_filter_service, cross_encoder_client

@router.post("/research", response_model=ResearchResponse)
async def research_endpoint(request: ResearchRequest):
    """
    Main research endpoint implementing the RAG pipeline:
    1. Query decomposition and expansion
    2. Vector retrieval
    3. Similarity filtering  
    4. Cross-encoder reranking with subquery averaging
    """
    start_time = time.time()
    timing_info = {}
    
    try:
        # Get services
        query_exp, vector_ret, sim_filter, cross_enc = get_services()
        
        print(f"[Research API] Processing query: \"{request.query}\"")
        
        # Step 1: Query Decomposition and Expansion
        print("[Research API] Step 1: Query decomposition and expansion")
        step1_start = time.time()
        expansion_data = await query_exp.query_expansion(request.query)
        timing_info['query_expansion_time'] = time.time() - step1_start
        
        if not expansion_data or len(expansion_data) == 0:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to decompose query"
            )
        
        # Step 2: Vector Retrieval for each expansion
        print("[Research API] Step 2: Vector retrieval")
        step2_start = time.time()
        retrieval_results = await vector_ret.vector_retrieval(expansion_data)
        timing_info['vector_search_time'] = time.time() - step2_start
        
        if not retrieval_results or len(retrieval_results) == 0:
            # Return empty results if no papers found
            return ResearchResponse(
                query=request.query,
                subquestions=list(expansion_data.keys()),
                expansions=expansion_data,
                papers=[],
                summary="No relevant papers found for your query.",
                processing_time=time.time() - start_time,
                **timing_info
            )
        
        # Step 3: Similarity Filtering
        print("[Research API] Step 3: Similarity filtering")
        step3_start = time.time()
        filtered_results = sim_filter.filter_on_threshold(
            retrieval_results, 
            request.similarity_threshold
        )
        timing_info['similarity_filter_time'] = time.time() - step3_start
        
        if not filtered_results:
            # If all results filtered out, use lower threshold
            print(f"[Research API] No results above threshold {request.similarity_threshold}, using all results")
            filtered_results = retrieval_results
        
        # Step 4: Cross-Encoder Reranking with Sub-query Averaging
        print("[Research API] Step 4: Cross-encoder reranking")
        step4_start = time.time()
        subquestions = list(expansion_data.keys())
        reranked_results = await cross_enc.rerank_with_subquery_averaging(
            subquestions, 
            filtered_results, 
            request.k
        )
        timing_info['cross_encoder_time'] = time.time() - step4_start
        
        # Skip LLM summary generation - return papers directly
        
        # Format response
        papers = []
        for paper in reranked_results[:request.k]:
            meta = paper.get("meta", {})
            
            # Extract multiple ID fields
            uuid = meta.get("uuid", meta.get("id"))
            pmid = meta.get("pmid", meta.get("pubmed_id"))
            
            # Generate PubMed URL if PMID is available
            pubmed_url = f"https://pubmed.ncbi.nlm.nih.gov/{pmid}" if pmid else meta.get("url", "")
            
            # Get abstract content from the 'content' field in VectorX
            abstract_content = meta.get("content", "")
            
            papers.append({
                "uuid": uuid,
                "pmid": pmid,
                "id": uuid or pmid or meta.get("url", "unknown"),  # Fallback for backward compatibility
                "title": meta.get("title", "No title"),
                "abstract": abstract_content,
                "content": abstract_content,  # Keep for backward compatibility
                "url": pubmed_url,
                "similarity_score": paper.get("initial_score", 0.0),
                "cross_score": paper.get("cross_score", 0.0),
                "matched_expansions": list(paper.get("expansions", set())),
                "matched_subquestions": list(paper.get("subqs", set()))
            })
        
        processing_time = time.time() - start_time
        print(f"[Research API] Completed in {processing_time:.2f} seconds")
        
        return ResearchResponse(
            query=request.query,
            subquestions=subquestions,
            expansions=expansion_data,
            papers=papers,
            summary="",  # No LLM summary
            processing_time=processing_time,
            **timing_info
        )
        
    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        print(f"[Research API] Unexpected error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Internal server error during research processing: {str(e)}"
        )
