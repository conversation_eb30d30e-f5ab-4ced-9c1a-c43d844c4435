import os
import sys
from datetime import datetime
from fastapi import FastAP<PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from dotenv import load_dotenv

from config import config
from models.schemas import HealthResponse, ErrorResponse
from routers import research

# Load environment variables
load_dotenv()

# Validate configuration
if not config.validate_config():
    print("❌ Configuration validation failed. Please check your environment variables.")
    sys.exit(1)

# Create FastAPI app
app = FastAPI(
    title=config.API_TITLE,
    version=config.API_VERSION,
    description=config.API_DESCRIPTION,
    docs_url="/docs",
    redoc_url="/redoc"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://127.0.0.1:3000"],  # Next.js dev server
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE"],
    allow_headers=["*"],
)

# Include routers
app.include_router(research.router, prefix="/api", tags=["research"])

@app.get("/health", response_model=HealthResponse)
async def health_check():
    """Health check endpoint"""
    return HealthResponse(
        status="healthy",
        version=config.API_VERSION,
        timestamp=datetime.utcnow().isoformat()
    )

@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "message": "Medical Research API",
        "version": config.API_VERSION,
        "docs": "/docs",
        "health": "/health"
    }

@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    """Global exception handler"""
    print(f"Global exception: {exc}")
    return ErrorResponse(
        error="Internal server error",
        detail=str(exc),
        timestamp=datetime.utcnow().isoformat()
    )

if __name__ == "__main__":
    import uvicorn
    
    print(f"🚀 Starting {config.API_TITLE} v{config.API_VERSION}")
    print(f"📚 Embedding model: {config.EMBEDDING_MODEL}")
    print(f"🔄 Cross-encoder model: {config.CROSS_ENCODER_MODEL}")
    print(f"🤖 LLM model: {config.LLM_MODEL_VERSION}")
    print(f"📊 VectorX index: {config.VECTORX_INDEX_NAME}")
    print()
    
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=False,  # Disable reload to prevent model reloading
        log_level="info"
    )