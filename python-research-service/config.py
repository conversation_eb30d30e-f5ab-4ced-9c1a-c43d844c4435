import os
from typing import Optional
from pathlib import Path
from dotenv import load_dotenv

# Load .env file from parent directory
env_path = Path(__file__).parent.parent / 'python-research-service/.env'
load_dotenv(env_path, override=True)
print(f"Loading environment variables from {env_path}")
print(f"Environment variables for EMBEDDING_MODEL_URL: {os.getenv('EMBEDDING_MODEL_URL')}")

class Config:
    # OpenAI Configuration
    OPENAI_API_KEY: Optional[str] = os.getenv("OPENAI_API_KEY")
    LLM_MODEL_VERSION: str = os.getenv("LLM_MODEL_VERSION", "gpt-4o-mini")
    
    # VectorX Configuration
    VECTORX_API_TOKEN: Optional[str] = os.getenv("VECTORX_API_TOKEN", "").strip("'")
    VECTORX_ENCRYPTION_KEY: Optional[str] = os.getenv("VECTORX_ENCRYPTION_KEY")
    VECTORX_INDEX_NAME: str = os.getenv("VECTORX_INDEX_NAME", "pubmed_collection_v1")
    
    # Model Configuration
    EMBEDDING_MODEL: str = "BAAI/bge-m3"
    CROSS_ENCODER_MODEL: str = "cross-encoder/ms-marco-MiniLM-L-12-v2"
    
    # Processing Configuration
    MAX_SUBQUESTIONS: int = 5
    MAX_EXPANSIONS: int = 6
    DEFAULT_SIMILARITY_THRESHOLD: float = 0.8
    DEFAULT_TOP_K: int = 15
    
    # Vector search configuration
    VECTOR_SEARCH_TOP_K: int = 20  # Number of results per query (reduced from 50)
    VECTOR_SEARCH_SIMILARITY_THRESHOLD: float = 0.55  # Lowered threshold
    RERANK_TOP_K: int = 10  # Final number of results after reranking
    
    # Performance optimization settings
    MAX_EXPANSIONS_PER_SUBQUERY: int = 5  # Reduced from 6
    MAX_TOTAL_DOCS_FOR_RERANKING: int = 100  # Cap total docs sent to cross-encoder
    CROSS_ENCODER_TIMEOUT: float = 30.0  # Timeout for cross-encoder service
    EMBEDDING_MODEL_URL: str = os.getenv("EMBEDDING_MODEL_URL", "http://localhost:8001")
    CROSS_ENCODER_SERVICE_URL: str = os.getenv("CROSS_SERVER_URL", "http://localhost:8002")
    
    # API Configuration
    API_TITLE: str = "Medical Research API"
    API_VERSION: str = "1.0.0"
    API_DESCRIPTION: str = "Python FastAPI service for medical research queries with RAG pipeline"
    
    @classmethod
    def validate_config(cls) -> bool:
        """Validate that required configuration is present"""
        missing = []
        
        if not cls.OPENAI_API_KEY:
            missing.append("OPENAI_API_KEY")
        if not cls.VECTORX_API_TOKEN:
            missing.append("VECTORX_API_TOKEN")
        # VECTORX_ENCRYPTION_KEY is optional
            
        if missing:
            print(f"⚠️ Missing required environment variables: {', '.join(missing)}")
            return False
            
        return True

config = Config()
