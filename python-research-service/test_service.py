import requests

# API endpoint - testing locally
url = "http://localhost:8000/api/research"

# Test query
payload = {
    "query": "dosage adjustments of ceftriaxone in patients with severe renal impairment",
    "k": 5,  # Fewer results to process faster
    "similarity_threshold": 0.65
}

# Make request
response = requests.post(url, json=payload, timeout=120)
data = response.json()

print(f"Status: {response.status_code}\n")
print(f"Papers found: {len(data['papers'])}\n")

# Check each paper's title and content
for i, paper in enumerate(data['papers'], 1):
    print(f"Paper {i}:")
    print(f"Title: {paper['title']}")
    print(f"Title length: {len(paper['title'])} characters")
    print(f"Similarity score: {paper.get('similarity_score', 'N/A')}")
    print(f"Cross score: {paper.get('cross_score', 'N/A')}")
    print(f"Content preview: {paper['content'][:200]}..." if paper['content'] else "Content: None")
    print(f"Content length: {len(paper['content'])} characters" if paper['content'] else "")
    print("-" * 80)