/**
 * Comprehensive Chat Flow Test Suite for Doctor Dashboard
 * 
 * This test suite verifies the complete chat functionality including:
 * - Sending messages
 * - Receiving AI responses
 * - Thread creation and navigation
 * - Different message types (Patient Case, Research, Quick Facts)
 * 
 * IMPORTANT CONTEXT FOR FUTURE SESSIONS:
 * - The app stays at http://localhost:3000/ after login (no /dashboard or /chat routes)
 * - Left sidebar shows threads, center shows chat, bottom has thread type buttons
 * - Test phone: +919819304846, OTP: 123456
 */

const puppeteer = require('puppeteer');
const fs = require('fs');
const path = require('path');

// Ensure directories exist
const ensureDir = (dir) => {
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
  }
};

ensureDir('test-results/screenshots/chat-flow');
ensureDir('test-results/reports');

// Test configuration
const CONFIG = {
  baseUrl: 'http://localhost:3000',
  testPhone: '+919819304846',
  testOTP: '123456',
  headless: process.env.TEST_HEADLESS !== 'false',
  slowMo: 50, // Slow down actions for better visibility
  defaultTimeout: 30000,
  aiResponseTimeout: 15000 // Time to wait for AI responses
};

// Test messages for different scenarios
const TEST_MESSAGES = {
  patientCase: {
    initial: "45-year-old male presenting with Type 2 Diabetes. HbA1c is 8.2%. Currently on Metformin 1000mg twice daily. Patient reports frequent urination and increased thirst. BP 140/90. What treatment modifications would you recommend?",
    followUp: "What about lifestyle modifications and dietary recommendations? Should we consider adding a SGLT2 inhibitor?"
  },
  research: {
    initial: "What are the latest clinical trials comparing SGLT2 inhibitors vs GLP-1 agonists for cardiovascular outcomes in Type 2 Diabetes patients?",
    followUp: "Can you provide specific data from the SUSTAIN-6 and LEADER trials?"
  },
  quickFacts: {
    initial: "What is the normal range for HbA1c and how does it correlate with average blood glucose?",
    followUp: "What are the target HbA1c levels for elderly patients with multiple comorbidities?"
  }
};

// Helper to take annotated screenshots
const screenshot = async (page, name, annotations = []) => {
  const screenshotPath = `test-results/screenshots/chat-flow/${name}.png`;
  await page.screenshot({ path: screenshotPath, fullPage: true });
  
  // Save annotations for the screenshot
  if (annotations.length > 0) {
    const annotationPath = screenshotPath.replace('.png', '-annotations.json');
    fs.writeFileSync(annotationPath, JSON.stringify({
      screenshot: name,
      timestamp: new Date().toISOString(),
      annotations: annotations
    }, null, 2));
  }
  
  console.log(`   📸 Screenshot: ${name}`);
  return screenshotPath;
};

// Helper to describe what should be visible in screenshots
const describeScreenshot = (description) => {
  console.log(`   👁️  Expected: ${description}`);
};

// Main test runner
(async () => {
  console.log('🚀 Comprehensive Chat Flow Test Suite');
  console.log('Testing message sending, AI responses, and thread navigation\n');
  console.log('📋 Test Plan: See TEST_PLAN_CHAT_FLOW.md for detailed expectations\n');
  
  const browser = await puppeteer.launch({
    headless: CONFIG.headless,
    slowMo: CONFIG.slowMo,
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });
  
  const page = await browser.newPage();
  await page.setViewport({ width: 1440, height: 900 }); // Larger viewport for better screenshots
  page.setDefaultTimeout(CONFIG.defaultTimeout);
  
  // Collect console logs and errors
  const consoleLogs = [];
  page.on('console', msg => {
    const logEntry = {
      type: msg.type(),
      text: msg.text(),
      timestamp: new Date().toISOString()
    };
    consoleLogs.push(logEntry);
    
    if (msg.type() === 'error') {
      console.log('   🔴 Console error:', msg.text());
    }
  });
  
  // Test results tracking
  const testResults = {
    startTime: new Date().toISOString(),
    tests: [],
    screenshots: [],
    consoleLogs: []
  };
  
  // Run a test step
  const runTest = async (name, testFn, expectedElements = []) => {
    console.log(`\n▶️  ${name}`);
    const testStart = Date.now();
    
    try {
      await testFn();
      console.log(`   ✅ PASSED (${Date.now() - testStart}ms)`);
      testResults.tests.push({
        name,
        status: 'passed',
        duration: Date.now() - testStart,
        expectedElements
      });
      return true;
    } catch (error) {
      console.log(`   ❌ FAILED: ${error.message}`);
      testResults.tests.push({
        name,
        status: 'failed',
        error: error.message,
        duration: Date.now() - testStart
      });
      await screenshot(page, `error-${name.replace(/\s+/g, '-').toLowerCase()}`);
      return false;
    }
  };
  
  try {
    // PHASE 1: AUTHENTICATION
    console.log('============================================================');
    console.log('📋 PHASE 1: AUTHENTICATION');
    console.log('============================================================');
    
    await runTest('Navigate to login page', async () => {
      await page.goto(CONFIG.baseUrl);
      await page.waitForSelector('input[placeholder*="98193"]');
      describeScreenshot('Login form with phone input field and "Doctor Dashboard" header');
    });
    
    await runTest('Complete login flow', async () => {
      // Enter phone
      await page.type('input[placeholder*="98193"]', CONFIG.testPhone);
      await screenshot(page, '1-phone-entered', [
        { element: 'phone input', description: 'Should show +919819304846' }
      ]);
      
      // Request OTP
      await page.click('button[type="submit"]');
      await page.waitForSelector('input[maxlength="6"]', { timeout: 10000 });
      describeScreenshot('OTP input field with 6 digit spaces');
      
      // Enter OTP
      await page.type('input[maxlength="6"]', CONFIG.testOTP);
      await screenshot(page, '2-otp-entered', [
        { element: 'OTP field', description: 'Should show 123456' }
      ]);
      
      // Submit OTP
      const buttons = await page.$$('button');
      for (const button of buttons) {
        const text = await page.evaluate(el => el.textContent, button);
        if (text.includes('Verify') || text.includes('Login')) {
          await button.click();
          break;
        }
      }
      
      // Wait for dashboard
      await new Promise(resolve => setTimeout(resolve, 3000));
      await page.waitForSelector('textarea');
      
      const url = page.url();
      if (!url.includes('localhost:3000')) {
        throw new Error('Not on expected URL after login');
      }
      
      describeScreenshot('Main dashboard with sidebar, chat area, and thread type buttons');
    });
    
    // PHASE 2: INITIAL STATE VERIFICATION
    console.log('\n============================================================');
    console.log('📋 PHASE 2: INITIAL STATE VERIFICATION');
    console.log('============================================================');
    
    await runTest('Verify dashboard layout', async () => {
      await screenshot(page, '3-dashboard-initial', [
        { element: 'sidebar', description: 'Left panel with thread list' },
        { element: 'chat area', description: 'Center with welcome message' },
        { element: 'thread buttons', description: 'Bottom buttons for Patient Case, Research, Quick Facts' }
      ]);
      
      // Check for key elements
      const hasWelcomeMessage = await page.evaluate(() => 
        document.body.textContent.includes('Hey Doc, how can I help you today?')
      );
      if (!hasWelcomeMessage) throw new Error('Welcome message not found');
      
      const hasTextarea = await page.$('textarea');
      if (!hasTextarea) throw new Error('Message textarea not found');
      
      const hasThreadButtons = await page.evaluate(() => {
        const text = document.body.textContent;
        return text.includes('Patient Case') && 
               text.includes('Research') && 
               text.includes('Quick Facts');
      });
      if (!hasThreadButtons) throw new Error('Thread type buttons not found');
      
      describeScreenshot('Empty chat with welcome message and input controls');
    });
    
    // PHASE 3: PATIENT CASE MESSAGE FLOW
    console.log('\n============================================================');
    console.log('📋 PHASE 3: PATIENT CASE MESSAGE FLOW');
    console.log('============================================================');
    
    let threadCreated = false;
    
    await runTest('Select Patient Case thread type', async () => {
      // Click Patient Case button
      const patientCaseBtn = await page.evaluateHandle(() => 
        Array.from(document.querySelectorAll('button')).find(b => 
          b.textContent.includes('Patient Case'))
      );
      
      if (!patientCaseBtn) throw new Error('Patient Case button not found');
      
      await patientCaseBtn.click();
      await new Promise(resolve => setTimeout(resolve, 500));
      
      await screenshot(page, '4-patient-case-selected', [
        { element: 'Patient Case button', description: 'Should be highlighted/active' }
      ]);
      
      describeScreenshot('Patient Case button highlighted in green');
    });
    
    await runTest('Type and send patient case message', async () => {
      const textarea = await page.$('textarea');
      if (!textarea) throw new Error('Textarea not found');
      
      // Type message
      await textarea.click();
      await page.keyboard.type(TEST_MESSAGES.patientCase.initial);
      
      await screenshot(page, '5-message-typed', [
        { element: 'textarea', description: 'Full patient case message visible' }
      ]);
      
      describeScreenshot('Textarea filled with patient case details');
      
      // Send message
      await page.keyboard.press('Enter');
      console.log('   ℹ️  Message sent, waiting for it to appear...');
      
      // Wait for message to appear in chat
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      await screenshot(page, '6-message-sent', [
        { element: 'user message', description: 'Right-aligned message bubble' },
        { element: 'loading indicator', description: 'AI thinking indicator' }
      ]);
      
      describeScreenshot('User message on right side, loading indicator visible');
      threadCreated = true;
    });
    
    await runTest('Wait for AI response', async () => {
      console.log('   ⏳ Waiting up to 15 seconds for AI response...');
      
      const startTime = Date.now();
      let messageCount = 0;
      
      // Poll for AI response
      while (Date.now() - startTime < CONFIG.aiResponseTimeout) {
        messageCount = await page.evaluate(() => {
          // Count message elements (various possible selectors)
          const messages = document.querySelectorAll(
            '[class*="message"], [class*="Message"], ' +
            '[role="article"], [data-message], ' +
            'div[class*="bubble"], p[class*="text"]'
          );
          return messages.length;
        });
        
        if (messageCount >= 2) {
          console.log('   ✅ AI response detected');
          break;
        }
        
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
      
      if (messageCount < 2) {
        throw new Error('AI response not received within timeout');
      }
      
      await screenshot(page, '7-ai-response-received', [
        { element: 'AI message', description: 'Left-aligned response with medical advice' },
        { element: 'formatting', description: 'Markdown formatting with bullets/structure' }
      ]);
      
      describeScreenshot('Two messages visible: user (right) and AI (left) with formatted medical response');
    });
    
    await runTest('Verify thread creation in sidebar', async () => {
      // Check if thread appears in sidebar
      const threadInSidebar = await page.evaluate(() => {
        const sidebarText = document.querySelector('aside, [class*="sidebar"], nav')?.textContent || 
                           document.body.textContent;
        return sidebarText.includes('45-year-old male') || 
               sidebarText.includes('Diabetes');
      });
      
      if (!threadInSidebar) {
        console.log('   ⚠️  Thread may not be visible in sidebar yet');
      }
      
      await screenshot(page, '8-thread-in-sidebar', [
        { element: 'sidebar', description: 'New thread under "Today" section' },
        { element: 'thread title', description: 'Shows beginning of patient case message' }
      ]);
      
      describeScreenshot('Sidebar shows new thread with truncated message as title');
    });
    
    await runTest('Send follow-up message', async () => {
      const textarea = await page.$('textarea');
      if (!textarea) throw new Error('Textarea not found for follow-up');
      
      await textarea.click();
      await textarea.click({ clickCount: 3 }); // Select all
      await page.keyboard.type(TEST_MESSAGES.patientCase.followUp);
      await page.keyboard.press('Enter');
      
      console.log('   ⏳ Waiting for follow-up response...');
      await new Promise(resolve => setTimeout(resolve, 5000));
      
      await screenshot(page, '9-follow-up-conversation', [
        { element: 'conversation', description: 'Four messages total (2 user, 2 AI)' }
      ]);
      
      describeScreenshot('Full conversation with initial exchange and follow-up');
    });
    
    // PHASE 4: THREAD NAVIGATION
    console.log('\n============================================================');
    console.log('📋 PHASE 4: THREAD NAVIGATION');
    console.log('============================================================');
    
    await runTest('Create new conversation', async () => {
      // Click New Conversation
      const newConvBtn = await page.evaluateHandle(() => 
        Array.from(document.querySelectorAll('button')).find(b => 
          b.textContent.includes('New Conversation'))
      );
      
      if (!newConvBtn) throw new Error('New Conversation button not found');
      
      await newConvBtn.click();
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Verify chat is cleared
      const textarea = await page.$('textarea');
      const textareaValue = await page.evaluate(el => el.value, textarea);
      
      await screenshot(page, '10-new-conversation', [
        { element: 'chat area', description: 'Should be empty with welcome message' }
      ]);
      
      describeScreenshot('Chat area cleared, ready for new conversation');
    });
    
    await runTest('Navigate back to previous thread', async () => {
      // Click on the thread in sidebar
      const threadLink = await page.evaluateHandle(() => {
        const elements = Array.from(document.querySelectorAll('div, a, button'));
        return elements.find(el => 
          el.textContent.includes('45-year-old male') || 
          el.textContent.includes('Diabetes')
        );
      });
      
      if (threadLink) {
        await threadLink.click();
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        await screenshot(page, '11-thread-restored', [
          { element: 'chat area', description: 'Previous conversation restored' }
        ]);
        
        describeScreenshot('Full conversation history visible again');
      } else {
        console.log('   ⚠️  Could not find thread in sidebar to click');
      }
    });
    
    // PHASE 5: RESEARCH MESSAGE TYPE
    console.log('\n============================================================');
    console.log('📋 PHASE 5: RESEARCH MESSAGE TYPE');
    console.log('============================================================');
    
    await runTest('Test Research thread type', async () => {
      // Start new conversation
      const newConvBtn = await page.evaluateHandle(() => 
        Array.from(document.querySelectorAll('button')).find(b => 
          b.textContent.includes('New Conversation'))
      );
      if (newConvBtn) await newConvBtn.click();
      
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Click Research button
      const researchBtn = await page.evaluateHandle(() => 
        Array.from(document.querySelectorAll('button')).find(b => 
          b.textContent === 'Research')
      );
      
      if (!researchBtn) throw new Error('Research button not found');
      await researchBtn.click();
      
      // Send research query
      const textarea = await page.$('textarea');
      await textarea.click();
      await page.keyboard.type(TEST_MESSAGES.research.initial);
      await page.keyboard.press('Enter');
      
      await new Promise(resolve => setTimeout(resolve, 7000));
      
      await screenshot(page, '12-research-response', [
        { element: 'AI response', description: 'Should include study references and data' }
      ]);
      
      describeScreenshot('Research response with clinical trial information');
    });
    
    // PHASE 6: QUICK FACTS MESSAGE TYPE
    console.log('\n============================================================');
    console.log('📋 PHASE 6: QUICK FACTS MESSAGE TYPE');
    console.log('============================================================');
    
    await runTest('Test Quick Facts thread type', async () => {
      // Start new conversation
      const newConvBtn = await page.evaluateHandle(() => 
        Array.from(document.querySelectorAll('button')).find(b => 
          b.textContent.includes('New Conversation'))
      );
      if (newConvBtn) await newConvBtn.click();
      
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Click Quick Facts button
      const quickFactsBtn = await page.evaluateHandle(() => 
        Array.from(document.querySelectorAll('button')).find(b => 
          b.textContent === 'Quick Facts')
      );
      
      if (!quickFactsBtn) throw new Error('Quick Facts button not found');
      await quickFactsBtn.click();
      
      // Send quick fact query
      const textarea = await page.$('textarea');
      await textarea.click();
      await page.keyboard.type(TEST_MESSAGES.quickFacts.initial);
      await page.keyboard.press('Enter');
      
      await new Promise(resolve => setTimeout(resolve, 5000));
      
      await screenshot(page, '13-quick-facts-response', [
        { element: 'AI response', description: 'Concise answer with specific values' }
      ]);
      
      describeScreenshot('Brief, factual response about HbA1c ranges');
    });
    
    // PHASE 7: FINAL VERIFICATION
    console.log('\n============================================================');
    console.log('📋 PHASE 7: FINAL VERIFICATION');
    console.log('============================================================');
    
    await runTest('Verify multiple threads in sidebar', async () => {
      const threadCount = await page.evaluate(() => {
        // Count threads in sidebar
        const sidebarElement = document.querySelector('aside, [class*="sidebar"], nav');
        if (!sidebarElement) return 0;
        
        // Look for thread-like elements
        const threads = sidebarElement.querySelectorAll('div[class*="thread"], a[href*="chat"], button');
        return threads.length;
      });
      
      console.log(`   ℹ️  Found ${threadCount} thread elements in sidebar`);
      
      await screenshot(page, '14-final-sidebar-state', [
        { element: 'sidebar', description: 'Multiple threads organized by time' }
      ]);
      
      describeScreenshot('Sidebar with multiple conversation threads');
    });
    
  } catch (criticalError) {
    console.error('\n💥 Critical error:', criticalError.message);
    await screenshot(page, 'critical-error');
  } finally {
    // Save test results
    testResults.endTime = new Date().toISOString();
    testResults.consoleLogs = consoleLogs;
    testResults.screenshots = fs.readdirSync('test-results/screenshots/chat-flow')
      .filter(f => f.endsWith('.png'))
      .map(f => ({
        name: f,
        path: `test-results/screenshots/chat-flow/${f}`,
        annotations: fs.existsSync(`test-results/screenshots/chat-flow/${f.replace('.png', '-annotations.json')}`) ?
          JSON.parse(fs.readFileSync(`test-results/screenshots/chat-flow/${f.replace('.png', '-annotations.json')}`)) : null
      }));
    
    // Calculate summary
    const passed = testResults.tests.filter(t => t.status === 'passed').length;
    const failed = testResults.tests.filter(t => t.status === 'failed').length;
    
    testResults.summary = {
      total: testResults.tests.length,
      passed,
      failed,
      successRate: ((passed / testResults.tests.length) * 100).toFixed(1) + '%'
    };
    
    // Save detailed report
    const reportPath = `test-results/reports/chat-flow-test-${Date.now()}.json`;
    fs.writeFileSync(reportPath, JSON.stringify(testResults, null, 2));
    
    // Print summary
    console.log('\n============================================================');
    console.log('📊 TEST SUMMARY');
    console.log('============================================================');
    console.log(`Total Tests: ${testResults.tests.length}`);
    console.log(`✅ Passed: ${passed}`);
    console.log(`❌ Failed: ${failed}`);
    console.log(`📈 Success Rate: ${testResults.summary.successRate}`);
    
    if (failed > 0) {
      console.log('\n❌ Failed Tests:');
      testResults.tests.filter(t => t.status === 'failed').forEach(test => {
        console.log(`   - ${test.name}: ${test.error}`);
      });
    }
    
    console.log(`\n📄 Detailed report saved to: ${reportPath}`);
    console.log('📸 Screenshots saved to: test-results/screenshots/chat-flow/');
    console.log('\n📋 For detailed test expectations, see: TEST_PLAN_CHAT_FLOW.md');
    
    console.log('\n✨ Chat flow test completed!');
    
    await browser.close();
  }
})();