#!/usr/bin/env node

const axios = require('axios');
const { io } = require('socket.io-client');

async function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

async function testGatekeeperFlow() {
  console.log('🧪 Testing Complete Gatekeeper Flow');
  console.log('===================================\n');

  let socket;
  
  try {
    // 1. Connect WebSocket
    console.log('1️⃣ Connecting WebSocket...');
    socket = io('http://localhost:3000', {
      transports: ['websocket'],
      autoConnect: true
    });

    await new Promise((resolve, reject) => {
      socket.on('connect', () => {
        console.log('✅ WebSocket connected:', socket.id);
        resolve();
      });
      socket.on('connect_error', reject);
      setTimeout(() => reject(new Error('Connection timeout')), 5000);
    });

    // 2. Authenticate
    console.log('\n2️⃣ Authenticating...');
    const authData = {
      token: 'test-token',
      user: {
        id: '898a9e7b-6873-4c4c-b21c-f4786ee281ad',
        userId: '898a9e7b-6873-4c4c-b21c-f4786ee281ad',
        phone: '+919819304846',
        name: 'Test User'
      }
    };

    socket.emit('authenticate', authData);

    await new Promise((resolve) => {
      socket.on('authenticated', (data) => {
        console.log('✅ Authenticated:', data);
        resolve();
      });
      socket.on('authentication_failed', (err) => {
        console.error('❌ Auth failed:', err);
        resolve();
      });
    });

    // 3. Listen for responses
    console.log('\n3️⃣ Setting up response listener...');
    socket.on('message_response', (data) => {
      console.log('📥 Received response:', JSON.stringify(data, null, 2));
    });

    // 4. Send a message first (to establish thread mapping)
    console.log('\n4️⃣ Sending initial message to establish thread...');
    const testThreadId = 'd17ec368-f130-4280-8986-ce98179f9431';
    
    socket.emit('send_message', {
      threadId: testThreadId,
      content: 'Test message to establish thread mapping',
      conversationType: 'patient-case',
      attachments: []
    });

    socket.on('message_sent', (data) => {
      console.log('✅ Message sent confirmation:', data);
    });

    socket.on('message_error', (error) => {
      console.error('❌ Message error:', error);
    });

    // Wait for message to be processed
    await sleep(2000);

    // 5. Now simulate Gatekeeper response
    console.log('\n5️⃣ Simulating Gatekeeper response...');
    const gatekeeperPayload = {
      userId: "898a9e7b-6873-4c4c-b21c-f4786ee281ad",
      messageText: encodeURIComponent("This is a test response from Gatekeeper. The thread mapping should now work correctly."),
      phone: "+919819304846"
      // Note: No dialogueId/threadId - testing the mapping
    };

    console.log('📤 Sending to /api/emit-message:', JSON.stringify(gatekeeperPayload, null, 2));

    const response = await axios.post('http://localhost:3000/api/emit-message', gatekeeperPayload, {
      headers: {
        'Content-Type': 'application/json'
      }
    });

    console.log('✅ Emit-message response:', response.data);

    // Wait to see if we receive the WebSocket message
    await sleep(3000);

    // 6. Test with explicit dialogueId
    console.log('\n6️⃣ Testing with explicit dialogueId...');
    const explicitPayload = {
      userId: "898a9e7b-6873-4c4c-b21c-f4786ee281ad",
      messageText: "This message has an explicit dialogueId",
      phone: "+919819304846",
      dialogueId: testThreadId
    };

    const response2 = await axios.post('http://localhost:3000/api/emit-message', explicitPayload);
    console.log('✅ Response with dialogueId:', response2.data);

    await sleep(2000);

  } catch (error) {
    console.error('\n❌ Test failed:', error.message);
    if (error.response) {
      console.error('Response:', error.response.data);
    }
  } finally {
    if (socket) {
      console.log('\n🔌 Disconnecting WebSocket...');
      socket.disconnect();
    }
    console.log('\n✅ Test complete');
    process.exit(0);
  }
}

// Run the test
testGatekeeperFlow();