# Thread Implementation Plan - Doctor Dashboard

## Overview
This document outlines the implementation plan for the threaded conversation system using the existing database schema with dialogues, dialogue_messages, chat_history, and chat_logs tables.

## Database Architecture

### Table Relationships
```
dialogues (threads)
├── dialogue_messages (message references)
    ├── chat_history (AI messages - raw)
    └── chat_logs (user messages - formatted)
```

### Data Flow
1. **User Input** → `chat_logs` (type: <PERSON><PERSON>AN) → `chat_history` (role: human)
2. **AI Processing** → Expert selection based on `dialogues.type`
3. **AI Response** → `chat_history` (role: assistant) → `chat_logs` (type: ASSISTANT)
4. **Thread Linking** → `dialogue_messages` connects messages to dialogues

## AI Expert Routing

### Expert Types by Dialogue Type
- **`patient-case`** → Clinical Diagnosis Expert
- **`research`** → Medical Research Expert  
- **`quick-fact`** → General Medical Knowledge Expert

### Expert Selection Logic
```javascript
const getExpertByType = (dialogueType) => {
  const experts = {
    'patient-case': 'clinical-diagnosis-expert',
    'research': 'medical-research-expert',
    'quick-fact': 'general-medical-expert'
  };
  return experts[dialogueType] || 'general-medical-expert';
};
```

## API Endpoints Implementation

### 1. Thread Management
```
POST   /api/threads/create           # Create new dialogue thread
GET    /api/threads                  # List user's threads
GET    /api/threads/:id              # Get specific thread
PUT    /api/threads/:id              # Update thread (title, type)
DELETE /api/threads/:id              # Delete thread
```

### 2. Message Management
```
POST   /api/threads/:id/messages     # Send message to thread
GET    /api/threads/:id/messages     # Get thread messages
PUT    /api/threads/:id/messages/:msgId # Edit message
DELETE /api/threads/:id/messages/:msgId # Delete message
```

### 3. Thread Operations
```
POST   /api/threads/:id/regenerate   # Regenerate last AI response
POST   /api/threads/:id/branch       # Create new thread from message
GET    /api/threads/search           # Search across threads
```

## Implementation Steps

### Phase 1: Core Thread System
1. **Thread CRUD Operations**
   - Create dialogue with title and type
   - List user's dialogues with pagination
   - Update dialogue metadata
   - Delete dialogue and cascade messages

2. **Message Storage System**
   - Store user messages in both chat_logs and chat_history
   - Store AI responses in both tables with proper formatting
   - Link messages to dialogues via dialogue_messages

3. **Expert Routing**
   - Implement expert selection based on dialogue.type
   - Route messages to appropriate AI endpoints
   - Handle expert-specific prompts and context

### Phase 2: Advanced Features
4. **Thread Context Management**
   - Maintain conversation context within threads
   - Handle context windows for long conversations
   - Implement context summarization

5. **File Attachments in Threads**
   - Store file attachments per message
   - Link attachments to specific messages in threads
   - Handle medical image analysis per expert type

6. **Thread Search & Organization**
   - Full-text search across thread messages
   - Filter threads by type, date, expert
   - Tag system for thread organization

### Phase 3: UI/UX Implementation
7. **Frontend Thread Interface**
   - Thread sidebar with conversation list
   - Thread creation modal with type selection
   - Message interface within threads
   - Thread switching and management

8. **Advanced UI Features**
   - Thread export functionality
   - Message editing and regeneration
   - Thread branching visualization
   - Collaborative features (future)

## Database Operations

### Create New Thread
```sql
-- 1. Create dialogue
INSERT INTO dialogues (id, title, type, trigger, user_id, status) 
VALUES ($1, $2, $3, 'user_initiated', $4, 'active');

-- 2. First message → chat_logs
INSERT INTO chat_logs (id, phone, type, message, user_id, session_id) 
VALUES ($1, $2, 'HUMAN', $3, $4, $5);

-- 3. First message → chat_history  
INSERT INTO chat_history (role, message, user_id, message_id, session_id)
VALUES ('human', $1, $2, $3, $4);

-- 4. Link message to dialogue
INSERT INTO dialogue_messages (id, dialogue_id, message_id_new, role)
VALUES ($1, $2, $3, 'human');
```

### Add Message to Thread
```sql
-- 1. Store user message
INSERT INTO chat_logs (id, phone, type, message, user_id, session_id) 
VALUES ($1, $2, 'HUMAN', $3, $4, $5);

INSERT INTO chat_history (role, message, user_id, message_id, session_id)
VALUES ('human', $1, $2, $3, $4);

-- 2. Store AI response
INSERT INTO chat_logs (id, phone, type, message, user_id, session_id) 
VALUES ($1, $2, 'ASSISTANT', $3, $4, $5);

INSERT INTO chat_history (role, message, user_id, message_id, session_id, expert)
VALUES ('assistant', $1, $2, $3, $4, $5);

-- 3. Link both messages to dialogue
INSERT INTO dialogue_messages (id, dialogue_id, message_id_new, role)
VALUES 
  ($1, $2, $3, 'human'),
  ($4, $2, $5, 'assistant');
```

### Retrieve Thread Messages
```sql
SELECT 
  cl.type as message_type,
  cl.message,
  cl.timestamp,
  cl.meta,
  ch.expert,
  ch.execution_time,
  dm.role,
  dm.created_at
FROM dialogue_messages dm
JOIN chat_logs cl ON dm.message_id_new = cl.message_id_new
LEFT JOIN chat_history ch ON dm.message_id_new = ch.message_id_new 
WHERE dm.dialogue_id = $1
ORDER BY dm.created_at ASC;
```

## File Attachment Strategy

### Attachment Storage per Message
- Extend `chat_logs.meta` to include attachment references
- Store file metadata: `{attachments: [{id, name, type, size, url}]}`
- Link attachments to specific messages within threads

### Expert-Specific File Processing
- **Clinical Expert**: Medical images, lab reports, patient files
- **Research Expert**: Papers, studies, clinical data
- **General Expert**: Any medical reference files

## Error Handling & Edge Cases

### Thread Management
- Handle concurrent message sending
- Manage thread deletion with active messages
- Handle expert routing failures

### Message Consistency
- Ensure chat_logs and chat_history sync
- Handle message editing and history preservation
- Manage message ordering in long threads

### Performance Considerations
- Index dialogue_messages for fast thread retrieval
- Paginate long thread message lists
- Cache frequently accessed threads

## Security & Permissions

### Thread Access Control
- Users can only access their own threads
- Admin users can view all threads (with audit logs)
- Secure message content from unauthorized access

### Data Privacy
- Encrypt sensitive medical information
- Audit trail for thread access and modifications
- Compliance with medical data regulations

## Testing Strategy

### Unit Tests
- Thread CRUD operations
- Message storage and retrieval
- Expert routing logic

### Integration Tests  
- End-to-end thread conversation flow
- File attachment handling
- Multi-user thread isolation

### Performance Tests
- Large thread message retrieval
- Concurrent thread operations
- Database query optimization

## Migration & Deployment

### Database Migrations
- Add indexes for performance
- Add constraints for data integrity
- Backup strategy for existing data

### API Versioning
- Version thread API endpoints
- Backward compatibility considerations
- Gradual rollout strategy

## Future Enhancements

### Advanced Features
- Thread sharing between doctors
- Thread templates for common cases
- AI-suggested thread titles
- Conversation analytics and insights

### Integration Possibilities
- EMR system integration
- Medical database lookups
- Collaborative diagnosis features
- Telemedicine integration

---

This implementation plan provides a comprehensive roadmap for building the threaded conversation system while leveraging the existing database schema and maintaining data consistency across all message storage tables.