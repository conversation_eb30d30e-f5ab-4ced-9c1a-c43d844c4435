#!/usr/bin/env node

const puppeteer = require('puppeteer');
const { v4: uuidv4 } = require('uuid');

async function testFullSession() {
  console.log('🧪 Starting Fresh Puppeteer Session');
  console.log('==================================\n');

  let browser;
  try {
    // 1. Launch browser
    console.log('1️⃣ Launching browser...');
    browser = await puppeteer.launch({
      headless: false,
      devtools: true,
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });
    
    const page = await browser.newPage();
    
    // Log console messages
    page.on('console', msg => {
      if (msg.type() === 'error') {
        console.log('❌ Page Error:', msg.text());
      }
    });

    // 2. Navigate to app
    console.log('2️⃣ Navigating to app...');
    await page.goto('http://localhost:3000', { waitUntil: 'networkidle2' });
    await page.screenshot({ path: 'test-1-homepage.png' });
    
    // 3. Enter phone number
    console.log('3️⃣ Entering phone number...');
    const phoneInput = await page.waitForSelector('input[type="tel"]', { timeout: 5000 });
    await phoneInput.click();
    await phoneInput.type('+************');
    await page.screenshot({ path: 'test-2-phone-entered.png' });
    
    // 4. Click submit button
    console.log('4️⃣ Requesting OTP...');
    const submitButton = await page.waitForSelector('button[type="submit"]');
    await submitButton.click();
    
    // 5. Wait for OTP field
    console.log('5️⃣ Waiting for OTP field...');
    const otpInput = await page.waitForSelector('input[placeholder*="OTP" i], input[placeholder*="code" i]', { timeout: 10000 });
    await page.screenshot({ path: 'test-3-otp-field.png' });
    
    // 6. Enter OTP
    console.log('6️⃣ Entering OTP...');
    await otpInput.click();
    await otpInput.type('123456');
    await page.screenshot({ path: 'test-4-otp-entered.png' });
    
    // 7. Submit OTP
    console.log('7️⃣ Submitting OTP...');
    const otpSubmitButton = await page.waitForSelector('button[type="submit"]');
    await otpSubmitButton.click();
    
    // 8. Wait for dashboard
    console.log('8️⃣ Waiting for dashboard...');
    await page.waitForSelector('[class*="Dashboard"], [class*="ThreadList"]', { timeout: 10000 });
    await page.screenshot({ path: 'test-5-dashboard.png' });
    console.log('✅ Logged in successfully!');
    
    // 9. Click new thread button
    console.log('9️⃣ Creating new conversation...');
    const newThreadButton = await page.waitForSelector('button:has-text("New Thread"), button:has-text("New Conversation"), [aria-label*="new" i]');
    await newThreadButton.click();
    await page.waitForTimeout(1000);
    
    // 10. Select Patient Case
    console.log('🔟 Selecting Patient Case...');
    const patientCaseOption = await page.waitForSelector('[data-value="patient-case"], [value="patient-case"], :text("Patient Case")');
    await patientCaseOption.click();
    
    // 11. Confirm thread creation
    const confirmButton = await page.waitForSelector('button:has-text("Start"), button:has-text("Create"), button:has-text("Confirm")');
    await confirmButton.click();
    await page.waitForTimeout(1000);
    
    // 12. Type message
    console.log('1️⃣1️⃣ Typing message...');
    const messageInput = await page.waitForSelector('textarea, input[type="text"][placeholder*="message" i]', { timeout: 5000 });
    await messageInput.click();
    
    const testMessage = `Patient case: 45-year-old male presenting with severe chest pain radiating to left arm. Started 2 hours ago. History of hypertension.`;
    await messageInput.type(testMessage);
    await page.screenshot({ path: 'test-6-message-typed.png' });
    
    // 13. Send message
    console.log('1️⃣2️⃣ Sending message...');
    await page.keyboard.press('Enter');
    
    // 14. Wait for response
    console.log('1️⃣3️⃣ Waiting for AI response...');
    await page.waitForTimeout(5000);
    await page.screenshot({ path: 'test-7-response-received.png' });
    
    // Check for response
    const pageContent = await page.evaluate(() => document.body.innerText);
    if (pageContent.includes('having trouble processing')) {
      console.log('⚠️  Received error response from AI');
    } else {
      console.log('✅ Received AI response');
    }
    
    console.log('\n✅ Test completed successfully!');
    console.log('📸 Screenshots saved: test-1-homepage.png through test-7-response-received.png');
    
    // Keep browser open for inspection
    console.log('\n🔍 Browser kept open for inspection. Press Ctrl+C to close.');
    await new Promise(() => {});
    
  } catch (error) {
    console.error('\n❌ Test failed:', error.message);
    if (browser) {
      const pages = await browser.pages();
      if (pages.length > 0) {
        await pages[0].screenshot({ path: 'test-error.png' });
        console.log('📸 Error screenshot saved: test-error.png');
      }
    }
  } finally {
    if (browser && process.env.CLOSE_BROWSER === 'true') {
      await browser.close();
    }
  }
}

testFullSession();