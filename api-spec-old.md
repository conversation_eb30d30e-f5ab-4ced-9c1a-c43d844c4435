# Doctor Dashboard API Specification

## Base URL
- **Staging**: `https://gatekeeper-staging.getbeyondhealth.com`
- **Tenant**: `practitioner-dashboard`

## Authentication
- **JWT Bearer Token**: For authenticated endpoints (user-specific operations)
- **Static Bearer Token**: For registration endpoint
  - Staging Token: `m}0/m9ZL`k{|Mz:Ca{7k8PF(gJV"Xz/j`

---

## 1. User Registration API

### POST `/c/practitioner-dashboard/register`

Register a new user after OTP verification. Creates user with `access: false` by default (pending approval).

**Headers:**
```
Authorization: Bearer m}0/m9ZL`k{|Mz:Ca{7k8PF(gJV"Xz/j
Content-Type: application/json
```

**Request Body:**
```json
{
  "source": "WEB",
  "phoneNumber": "+************",
  "user_role": "DOCTOR"
}
```

**Parameters:**
- `source` (string, required): Always "WEB" for web dashboard
- `phoneNumber` (string, required): User's phone number in E.164 format
- `user_role` (string, required): Always "DOCTOR" for doctor dashboard

**Response:**
```json
{
  "success": true,
  "userId": "uuid",
  "message": "User registered successfully, pending approval"
}
```

---

## 2. Get Messages by Dialogue ID

### GET `/user/practitioner-dashboard/get-chats-by-dialogueId`

Fetch message history for a specific dialogue/thread.

**Headers:**
```
Authorization: Bearer <JWT>
Content-Type: application/json
```

**Query Parameters:**
- `dialogue_id` (string, required): The dialogue/thread ID
- `limit` (integer, optional): Number of messages to fetch (default: 30)

**Example Request:**
```bash
GET /user/practitioner-dashboard/get-chats-by-dialogueId?limit=30&dialogue_id=2ea2558e-7d45-4bde-b440-5f70086ace03
```

**Response:**
```json
{
  "messages": [
    {
      "id": "message-id",
      "dialogueId": "2ea2558e-7d45-4bde-b440-5f70086ace03",
      "sender": "human|ai",
      "text": "Message content",
      "timestamp": **********,
      "attachments": []
    }
  ],
  "hasMore": false
}
```

---

## 3. Send Message API

### POST `/c/practitioner-dashboard/webhook`

Send a message (text, single file, or multiple files) to a dialogue.

**Headers:**
```
Authorization: Bearer <JWT>
Content-Type: application/json
```

### 3.1 Text Message

**Request Body:**
```json
{
  "dialogueId": "2ea2558e-7d45-4bde-b440-5f70086ace03",
  "dialogueType": "patient-case",
  "text": "Patient presents with fever and cough",
  "providerMessageId": "550e8400-e29b-41d4-a716-446655440000",
  "sender": "human",
  "source": "WEB",
  "phoneNumber": "+************",
  "timestamp": 1750816877000,
  "requestId": "660e8400-e29b-41d4-a716-************"
}
```

### 3.2 Single File Message

**Request Body:**
```json
{
  "dialogueId": "2ea2558e-7d45-4bde-b440-5f70086ace03",
  "dialogueType": "patient-case",
  "text": "Here's the patient's X-ray",
  "providerMessageId": "550e8400-e29b-41d4-a716-446655440000",
  "attachment": "https://azure-blob-url/file.png",
  "fileExtension": ".png",
  "messageType": "image",
  "sender": "human",
  "source": "WEB",
  "phoneNumber": "+************",
  "timestamp": 1750816877000,
  "requestId": "660e8400-e29b-41d4-a716-************"
}
```

### 3.3 Multiple Files Message

**Request Body:**
```json
{
  "dialogueId": "2ea2558e-7d45-4bde-b440-5f70086ace03",
  "dialogueType": "patient-case",
  "text": "Patient reports and lab results",
  "providerMessageId": "550e8400-e29b-41d4-a716-446655440000",
  "attachment": [
    {
      "url": "https://azure-blob-url/report1.png",
      "fileExtension": ".png",
      "messageType": "image"
    },
    {
      "url": "https://azure-blob-url/report2.pdf",
      "fileExtension": ".pdf",
      "messageType": "pdf"
    }
  ],
  "sender": "human",
  "source": "WEB",
  "phoneNumber": "+************",
  "timestamp": 1750816877000,
  "requestId": "660e8400-e29b-41d4-a716-************"
}
```

**Parameters:**
- `dialogueId` (string, required): Thread/conversation ID
- `dialogueType` (string, required): One of: `patient-case`, `research`, `quick-fact`
- `text` (string, optional): Message text content
- `providerMessageId` (string, required): Frontend-generated UUID for this message
- `attachment` (string/array, optional): 
  - String: URL for single file
  - Array: Array of file objects for multiple files
- `fileExtension` (string, conditional): Required for single file
- `messageType` (string, conditional): Required for single file (e.g., "image", "pdf")
- `sender` (string, required): Always "human" for user messages
- `source` (string, required): Always "WEB"
- `phoneNumber` (string, required): User's phone number
- `timestamp` (number, required): Unix timestamp in milliseconds
- `requestId` (string, required): Frontend-generated UUID for this request

**File Upload Process:**
1. Upload file to Azure Blob Storage using the upload function in `custom-server.js`
2. Get the blob URL
3. Include the URL in the message payload

---

## 4. Get All Dialogues/Threads API

### GET `/user/practitioner-dashboard/get-chats-by-dialogueId`

Get all dialogues/threads for the authenticated user.

**Headers:**
```
Authorization: Bearer <JWT>
Content-Type: application/json
```

**Query Parameters:**
- `limit` (integer, required): Number of dialogues to fetch (recommended: 100+ since no pagination)
- `dialogue_id` (string, required): Pass empty string or specific dialogue ID

**Example Request:**
```bash
GET /user/practitioner-dashboard/get-chats-by-dialogueId?limit=100&dialogue_id=
```

**Note:** No pagination implemented yet. Use high limit value to get all threads.

---

## Additional Information

### Authentication Flow
1. Request OTP via existing endpoint
2. Verify OTP - returns JWT if successful
3. For new users, call registration endpoint after OTP verification
4. Currently no way to check if user has `access=true` (being added)

### Response Delivery
- Gatekeeper sends responses via Redis Pub/Sub
- Our server subscribes to Redis channels for real-time message delivery
- No webhook endpoint needed - Redis handles the communication
- Message webhook always returns simple success response: `{ "success": true, "tenantSlug": "practitioner-dashboard" }`

### File Specifications
- **Supported Types**: Images (PNG, JPG, etc.) and PDF
- **Max Size**: 25MB per file
- **Upload Process**: Upload to Azure Blob Storage first, then send URL

### Message Types
- `text`: Text-only message (attachment = "")
- `image`: Image attachment (can include text)
- `pdf`: PDF attachment (can include text)

### Dialogue Creation
- Frontend generates UUID for new dialogue
- Send first message with new UUID
- Backend automatically creates dialogue on receiving new ID

### Timestamp Format
- Always use Unix timestamp in milliseconds

---

## API Test Results (2025-06-25)

### Working Endpoints

#### 1. Request OTP
**Endpoint:** `POST /auth/practitioner-dashboard/request-otp`
**Request:**
```json
{
  "phoneNumber": "+************"
}
```
**Response:** ✅ 200 OK
```json
{
  "requestId": "test-request-id"
}
```

#### 2. Register User
**Endpoint:** `POST /c/practitioner-dashboard/register`
**Headers:**
```
Authorization: Bearer m}0/m9ZL`k{|Mz:Ca{7k8PF(gJV"Xz/j
Content-Type: application/json
```
**Request:**
```json
{
  "source": "WEB",
  "phoneNumber": "+************",
  "user_role": "DOCTOR"
}
```
**Response:** ✅ 200 OK
```json
{
  "success": true,
  "message": "User registered successfully"
}
```

#### 3. Send Message (All Types)
**Endpoint:** `POST /c/practitioner-dashboard/webhook`

**Text Message Response:** ✅ 200 OK
```json
{
  "success": true,
  "tenantSlug": "august"
}
```

**Single File Message Response:** ✅ 200 OK
```json
{
  "success": true,
  "tenantSlug": "august"
}
```

**Multiple Files Message Response:** ✅ 200 OK
```json
{
  "success": true,
  "tenantSlug": "august"
}
```

**All Dialogue Types:** ✅ All working
- `patient-case`: `{ "success": true, "tenantSlug": "practitioner-dashboard" }`
- `research`: `{ "success": true, "tenantSlug": "practitioner-dashboard" }`
- `quick-fact`: `{ "success": true, "tenantSlug": "practitioner-dashboard" }`

### Authentication-Required Endpoints (Need Valid JWT)

#### Get All Dialogues / Get Messages by Dialogue ID
**Endpoint:** `GET /user/practitioner-dashboard/get-chats-by-dialogueId`

**Error Response:** ❌ 401 Unauthorized
```json
{
  "success": false,
  "message": "Invalid or expired JWT token",
  "details": "jwt malformed"
}
```

### Rate Limiting Headers
Gatekeeper includes rate limiting headers in responses:
```
Ratelimit-Policy: 4;w=1800
Ratelimit-Limit: 4
Ratelimit-Remaining: 2
Ratelimit-Reset: 1433
```

### Key Findings

1. **OTP Request Rate Limiting**: 4 requests per 30 minutes (1800 seconds)
2. **Message Sending**: Does not require JWT authentication
3. **Data Retrieval**: Requires valid JWT from Gatekeeper auth flow
4. **Registration**: Uses static bearer token, not JWT
5. **All message types and dialogue types are supported**

---

## API Summary

### Complete API List

1. **Authentication**
   - `POST /auth/practitioner-dashboard/request-otp` - Request OTP ✅ Working
   - `POST /auth/practitioner-dashboard/verify-otp` - Verify OTP and get JWT ⚠️ Need to test with real OTP
   - `POST /auth/practitioner-dashboard/refresh-token` - Refresh JWT token ⚠️ Need to test with real JWT

2. **User Management**
   - `POST /c/practitioner-dashboard/register` - Register new user (requires static token) ✅ Working

3. **Messaging**
   - `POST /c/practitioner-dashboard/webhook` - Send message (text/files) ✅ Working

4. **Data Retrieval** (requires JWT)
   - `GET /user/practitioner-dashboard/get-chats-by-dialogueId` - Get messages/threads ❌ Requires valid JWT

### Implementation Status

- ✅ OTP Request endpoint tested and working
- ✅ User registration endpoint tested and working
- ✅ Message sending via webhook tested for all types
- ✅ All dialogue types supported (patient-case, research, quick-fact)
- ⚠️ OTP Verify endpoint needs testing with real OTP
- ⚠️ Need real JWT from Gatekeeper for GET endpoints
- ⚠️ Redis Pub/Sub integration pending for receiving AI responses
- ⚠️ WebSocket relay to Gatekeeper pending

---

## Implementation Notes

### WebSocket Integration
- Messages are sent via WebSocket to our custom server
- Our server forwards them to Gatekeeper's webhook endpoint
- Gatekeeper sends responses via Redis Pub/Sub
- We relay responses to the client via WebSocket

### File Handling
- Files are uploaded to Azure Blob Storage first
- Blob URLs are then included in message payloads
- Multiple files are supported as an array of attachment objects

### Dialogue Types Mapping
- Frontend uses: `THREAD_TYPES.PATIENT_CASE`, `THREAD_TYPES.RESEARCH`, `THREAD_TYPES.QUICK_FACTS`
- API expects: `patient-case`, `research`, `quick-fact`

### Next Steps
1. Update custom-server.js to use real Gatekeeper APIs
2. Implement Redis Pub/Sub for receiving responses
3. Update frontend to use real dialogue IDs
4. Handle JWT refresh flow
5. Implement proper error handling