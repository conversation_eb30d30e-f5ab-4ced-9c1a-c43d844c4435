const puppeteer = require('puppeteer');

// Configuration
const BASE_URL = process.env.BASE_URL || 'http://localhost:3000';
const HEADLESS = process.env.HEADLESS !== 'false';

// Enhanced logging utility
const log = {
  info: (msg, data) => console.log(`[INFO] ${new Date().toISOString()} - ${msg}`, data || ''),
  error: (msg, data) => console.error(`[ERROR] ${new Date().toISOString()} - ${msg}`, data || ''),
  debug: (msg, data) => console.log(`[DEBUG] ${new Date().toISOString()} - ${msg}`, data || ''),
  warn: (msg, data) => console.warn(`[WARN] ${new Date().toISOString()} - ${msg}`, data || '')
};

// Test data
const testDoctor = {
  phone: '+919999999999', // Replace with a test phone number
  otp: '123456' // This will need to be obtained from your OTP system
};

async function setupPage(page) {
  // Set up console message logging
  page.on('console', msg => {
    const type = msg.type();
    const text = msg.text();
    const location = msg.location();
    
    log.debug(`Browser Console [${type}]:`, {
      text,
      url: location.url,
      lineNumber: location.lineNumber,
      columnNumber: location.columnNumber
    });
    
    // Check for JSON parsing errors
    if (text.includes('JSON') || text.includes('parse') || text.includes('SyntaxError')) {
      log.error('Potential JSON error detected in console:', text);
    }
  });

  // Set up request interception to log all network requests
  page.on('request', request => {
    const url = request.url();
    const method = request.method();
    const headers = request.headers();
    
    if (url.includes('api') || method === 'POST') {
      log.debug(`Network Request [${method}]:`, {
        url,
        headers: headers['content-type'] ? { 'content-type': headers['content-type'] } : {},
        postData: request.postData()
      });
    }
  });

  // Set up response logging
  page.on('response', async response => {
    const url = response.url();
    const status = response.status();
    const headers = response.headers();
    
    if (url.includes('api') || status >= 400) {
      log.debug(`Network Response [${status}]:`, {
        url,
        status,
        contentType: headers['content-type']
      });
      
      // Try to get response body for error responses
      if (status >= 400 || url.includes('api')) {
        try {
          const text = await response.text();
          
          // Check if response is JSON
          if (headers['content-type']?.includes('application/json')) {
            try {
              const json = JSON.parse(text);
              log.debug('Response JSON:', json);
            } catch (e) {
              log.error('Failed to parse JSON response:', {
                url,
                text: text.substring(0, 500),
                error: e.message
              });
            }
          } else {
            log.debug('Response Text:', text.substring(0, 500));
          }
        } catch (e) {
          log.warn('Could not read response body:', e.message);
        }
      }
    }
  });

  // Set up error logging
  page.on('error', error => {
    log.error('Page crashed:', error);
  });

  page.on('pageerror', error => {
    log.error('Page error:', {
      message: error.message,
      stack: error.stack
    });
    
    // Check for JSON parsing errors
    if (error.message.includes('JSON') || error.message.includes('parse')) {
      log.error('JSON parsing error detected:', error.message);
    }
  });

  // Set up request failure logging
  page.on('requestfailed', request => {
    log.error('Request failed:', {
      url: request.url(),
      method: request.method(),
      failure: request.failure()
    });
  });
}

async function testDoctorDashboard() {
  log.info('Starting Doctor Dashboard Puppeteer Test');
  
  let browser;
  let page;
  
  try {
    // Launch browser with debugging options
    browser = await puppeteer.launch({
      headless: HEADLESS,
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-accelerated-2d-canvas',
        '--no-first-run',
        '--no-zygote',
        '--disable-gpu'
      ],
      devtools: !HEADLESS // Open DevTools in non-headless mode
    });
    
    log.info('Browser launched successfully');
    
    page = await browser.newPage();
    
    // Set viewport
    await page.setViewport({ width: 1280, height: 720 });
    
    // Enable request interception
    await page.setRequestInterception(true);
    page.on('request', (request) => {
      request.continue();
    });
    
    // Set up all logging handlers
    await setupPage(page);
    
    // Test 1: Navigate to home page
    log.info('Test 1: Navigating to home page', { url: BASE_URL });
    try {
      const response = await page.goto(BASE_URL, {
        waitUntil: 'networkidle2',
        timeout: 30000
      });
      
      log.info('Navigation response:', {
        status: response.status(),
        statusText: response.statusText(),
        url: response.url()
      });
      
      // Take screenshot
      await page.screenshot({ 
        path: 'test-screenshots/home-page.png',
        fullPage: true 
      });
      log.info('Screenshot saved: home-page.png');
      
    } catch (error) {
      log.error('Failed to navigate to home page:', error);
      throw error;
    }
    
    // Test 2: Check if login form exists
    log.info('Test 2: Checking for login form');
    try {
      // Wait for either login form or dashboard (in case already logged in)
      const loginFormExists = await page.waitForSelector('input[type="tel"], input[placeholder*="phone"], input[name="phone"]', { 
        timeout: 5000 
      }).then(() => true).catch(() => false);
      
      if (loginFormExists) {
        log.info('Login form found, proceeding with authentication');
        
        // Test 3: Enter phone number
        log.info('Test 3: Entering phone number');
        const phoneInput = await page.$('input[type="tel"], input[placeholder*="phone"], input[name="phone"]');
        await phoneInput.click({ clickCount: 3 }); // Triple click to select all
        await phoneInput.type(testDoctor.phone);
        
        // Take screenshot after entering phone
        await page.screenshot({ 
          path: 'test-screenshots/phone-entered.png',
          fullPage: true 
        });
        
        // Test 4: Submit phone number
        log.info('Test 4: Submitting phone number');
        const submitButton = await page.$('button[type="submit"], button:contains("Send OTP"), button:contains("Continue")');
        
        if (submitButton) {
          // Set up promise to catch navigation or OTP field
          const navigationPromise = page.waitForNavigation({ 
            waitUntil: 'networkidle2',
            timeout: 10000 
          }).catch(() => null);
          
          await submitButton.click();
          
          // Wait for either navigation or OTP field to appear
          await Promise.race([
            navigationPromise,
            page.waitForSelector('input[type="text"][maxlength="6"], input[placeholder*="OTP"], input[name="otp"]', { timeout: 10000 })
          ]);
          
          log.info('Phone submission completed');
          
          // Take screenshot after submission
          await page.screenshot({ 
            path: 'test-screenshots/after-phone-submit.png',
            fullPage: true 
          });
          
        } else {
          log.error('Submit button not found');
        }
        
      } else {
        log.info('No login form found, might already be logged in');
        
        // Check if we're on the dashboard
        const isDashboard = await page.$('.dashboard, [data-testid="dashboard"], h1:contains("Dashboard")');
        if (isDashboard) {
          log.info('Already on dashboard');
        }
      }
      
    } catch (error) {
      log.error('Error during login process:', error);
      
      // Take error screenshot
      await page.screenshot({ 
        path: 'test-screenshots/error-state.png',
        fullPage: true 
      });
      
      // Get page content for debugging
      const pageContent = await page.content();
      log.debug('Page HTML (first 1000 chars):', pageContent.substring(0, 1000));
    }
    
    // Test 5: Check for any JSON errors in the page
    log.info('Test 5: Checking for JSON errors');
    const pageErrors = await page.evaluate(() => {
      const errors = [];
      
      // Check for any error messages in the DOM
      const errorElements = document.querySelectorAll('.error, .alert, [class*="error"], [class*="alert"]');
      errorElements.forEach(el => {
        if (el.textContent) {
          errors.push(el.textContent.trim());
        }
      });
      
      // Check for JSON in script tags
      const scriptTags = document.querySelectorAll('script[type="application/json"]');
      scriptTags.forEach((script, index) => {
        try {
          JSON.parse(script.textContent);
        } catch (e) {
          errors.push(`JSON parse error in script tag ${index}: ${e.message}`);
        }
      });
      
      return errors;
    });
    
    if (pageErrors.length > 0) {
      log.error('Page errors found:', pageErrors);
    }
    
    // Test 6: Check network tab for failed requests
    log.info('Test 6: Final summary');
    log.info('Test completed. Check the logs above for any JSON errors or failed requests.');
    
  } catch (error) {
    log.error('Test failed with error:', {
      message: error.message,
      stack: error.stack
    });
    
    // Take final error screenshot
    if (page) {
      await page.screenshot({ 
        path: 'test-screenshots/final-error.png',
        fullPage: true 
      }).catch(() => log.warn('Could not take final screenshot'));
    }
    
    throw error;
    
  } finally {
    if (browser) {
      await browser.close();
      log.info('Browser closed');
    }
  }
}

// Create screenshots directory
const fs = require('fs');
if (!fs.existsSync('test-screenshots')) {
  fs.mkdirSync('test-screenshots');
}

// Run the test
testDoctorDashboard()
  .then(() => {
    log.info('All tests completed successfully');
    process.exit(0);
  })
  .catch(error => {
    log.error('Test suite failed:', error);
    process.exit(1);
  });