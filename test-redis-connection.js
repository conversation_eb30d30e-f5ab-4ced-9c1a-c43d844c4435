#!/usr/bin/env node

/**
 * Redis Connection Test
 * Tests connection to Azure Redis Cache and Pub/Sub functionality
 */

const { createClient } = require('redis');
require('dotenv').config({ path: '.env.local' });

const REDIS_URL = process.env.REDIS_URL;
const REDIS_PASSWORD = process.env.REDIS_PASSWORD;

console.log(`
🔴 REDIS CONNECTION TEST
========================
Redis URL: ${REDIS_URL}
Password: ${REDIS_PASSWORD ? '[PROVIDED]' : '[MISSING]'}
========================
`);

async function testRedisConnection() {
  let client = null;
  let subscriber = null;
  
  try {
    console.log('📡 Creating Redis client...');
    
    // Create Redis client
    client = createClient({
      url: `rediss://${REDIS_URL}:6380`,
      password: REDIS_PASSWORD,
      socket: {
        tls: true,
        rejectUnauthorized: false
      }
    });

    // Error handling
    client.on('error', (err) => {
      console.error('❌ Redis Client Error:', err);
    });

    client.on('connect', () => {
      console.log('🔄 Redis connecting...');
    });

    client.on('ready', () => {
      console.log('✅ Redis connection ready!');
    });

    console.log('🔌 Connecting to Redis...');
    await client.connect();

    console.log('✅ Redis connected successfully!');

    // Test basic operations
    console.log('\n📝 Testing basic Redis operations...');
    
    // SET operation
    await client.set('test:key', 'Hello Redis!');
    console.log('✅ SET operation successful');
    
    // GET operation
    const value = await client.get('test:key');
    console.log('✅ GET operation successful:', value);
    
    // DELETE operation
    await client.del('test:key');
    console.log('✅ DEL operation successful');

    // Test Pub/Sub functionality
    console.log('\n📢 Testing Pub/Sub functionality...');
    
    // Create subscriber client
    subscriber = client.duplicate();
    await subscriber.connect();
    console.log('✅ Subscriber client connected');

    // Set up subscription
    let messageReceived = false;
    
    await subscriber.subscribe('test:channel', (message) => {
      console.log('📨 Received message:', message);
      messageReceived = true;
    });
    console.log('✅ Subscribed to test:channel');

    // Publish a test message
    setTimeout(async () => {
      await client.publish('test:channel', JSON.stringify({
        type: 'test',
        message: 'Hello from Publisher!',
        timestamp: new Date().toISOString()
      }));
      console.log('📤 Published test message');
    }, 1000);

    // Wait for message
    await new Promise(resolve => {
      const timeout = setTimeout(() => {
        console.log('⚠️ No message received within 5 seconds');
        resolve();
      }, 5000);
      
      const checkMessage = setInterval(() => {
        if (messageReceived) {
          console.log('✅ Pub/Sub test successful!');
          clearTimeout(timeout);
          clearInterval(checkMessage);
          resolve();
        }
      }, 100);
    });

    // Test Gatekeeper-style channels
    console.log('\n🏥 Testing Gatekeeper-style channels...');
    
    const testPhone = '+919819304846';
    const channelPattern = `gatekeeper:response:${testPhone}:*`;
    
    console.log('Channel pattern:', channelPattern);
    console.log('This is how Gatekeeper will send AI responses');

    // Clean up
    await subscriber.unsubscribe();
    console.log('✅ Unsubscribed from channels');

  } catch (error) {
    console.error('❌ Redis test failed:', error);
    
    if (error.code === 'ENOTFOUND') {
      console.log('\n💡 Troubleshooting:');
      console.log('- Check if Redis URL is correct');
      console.log('- Verify network connectivity');
      console.log('- Ensure Redis service is running');
    } else if (error.message.includes('AUTH')) {
      console.log('\n💡 Troubleshooting:');
      console.log('- Check if Redis password is correct');
      console.log('- Verify Redis AUTH is enabled');
    }
    
  } finally {
    // Clean up connections
    try {
      if (subscriber) {
        await subscriber.quit();
        console.log('🔌 Subscriber disconnected');
      }
      if (client) {
        await client.quit();
        console.log('🔌 Client disconnected');
      }
    } catch (cleanupError) {
      console.warn('⚠️ Cleanup warning:', cleanupError.message);
    }
  }
}

// Test Redis configuration
function testConfiguration() {
  console.log('\n⚙️ CONFIGURATION CHECK');
  console.log('='.repeat(30));
  
  if (!REDIS_URL) {
    console.log('❌ REDIS_URL not found in environment');
    return false;
  }
  
  if (!REDIS_PASSWORD) {
    console.log('❌ REDIS_PASSWORD not found in environment');
    return false;
  }
  
  console.log('✅ Redis URL configured:', REDIS_URL);
  console.log('✅ Redis password configured');
  
  // Validate URL format
  const urlPattern = /^[a-zA-Z0-9.-]+\.(redis\.cache\.windows\.net|redis\.windows\.net)$/;
  if (!urlPattern.test(REDIS_URL)) {
    console.log('⚠️ Redis URL format may be incorrect');
    console.log('Expected: *.redis.cache.windows.net');
  }
  
  return true;
}

// Main execution
async function main() {
  const configValid = testConfiguration();
  
  if (!configValid) {
    console.log('\n❌ Configuration issues detected. Please check .env.local file.');
    process.exit(1);
  }
  
  await testRedisConnection();
  
  console.log('\n🎉 Redis test completed!');
  console.log('\nNext steps:');
  console.log('1. If connection successful: Redis is ready for Pub/Sub');
  console.log('2. If connection failed: Check configuration and network');
  console.log('3. Update custom-server.js to subscribe to Gatekeeper channels');
}

main().catch(error => {
  console.error('💥 Test script failed:', error);
  process.exit(1);
});