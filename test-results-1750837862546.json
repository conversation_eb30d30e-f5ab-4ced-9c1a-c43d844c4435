{"timestamp": "2025-06-25T07:50:57.961Z", "tests": {"Get All Dialogues": {"success": false, "timestamp": "2025-06-25T07:50:58.457Z", "data": null, "error": {"message": "Request failed with status code 401", "status": 401, "statusText": "Unauthorized", "data": {"success": false, "message": "Invalid or expired JWT token", "details": "invalid signature"}}}, "Send Text Message": {"success": true, "timestamp": "2025-06-25T07:50:58.803Z", "data": {"success": true, "tenantSlug": "august", "sentData": {"dialogueId": "f73210a9-4ca7-4423-b7cb-857d1416f115", "providerMessageId": "2ecfc24c-9e1e-457f-ab27-3dc54fd1a698", "requestId": "7a8a2a99-951b-4a9f-a644-5d5b7dad3bb2"}}, "error": null}, "Get Messages by Dialogue ID": {"success": false, "timestamp": "2025-06-25T07:51:00.852Z", "data": null, "error": {"message": "Request failed with status code 401", "status": 401, "statusText": "Unauthorized", "data": {"success": false, "message": "Invalid or expired JWT token", "details": "invalid signature"}}}, "Send Message with Single File": {"success": true, "timestamp": "2025-06-25T07:51:01.265Z", "data": {"success": true, "tenantSlug": "august"}, "error": null}, "Send Message with Multiple Files": {"success": true, "timestamp": "2025-06-25T07:51:01.661Z", "data": {"success": true, "tenantSlug": "august"}, "error": null}, "Different Dialogue Types": {"success": true, "timestamp": "2025-06-25T07:51:02.546Z", "data": {"patient-case": {"success": true, "response": {"success": true, "tenantSlug": "august"}}, "research": {"success": true, "response": {"success": true, "tenantSlug": "august"}}, "quick-fact": {"success": true, "response": {"success": true, "tenantSlug": "august"}}}, "error": null}}}