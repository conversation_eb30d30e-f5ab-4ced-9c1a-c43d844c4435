# Medical Research Microservices Architecture

This project has been refactored from a monolithic service into a microservices architecture to optimize memory usage and performance, especially for deployment on resource-constrained instances like t3.medium (4GB RAM).

## 🏗️ Architecture Overview

The system is now split into three independent microservices:

### 1. **Embedding Service** (Port 8001)
- **Purpose**: Text embedding generation using BAAI/bge-m3
- **RAM Usage**: ~1.5GB
- **Features**: Batch processing, caching, memory optimization
- **Endpoints**: `/embed/single`, `/embed/batch`, `/health`

### 2. **Cross-Encoder Service** (Port 8002)
- **Purpose**: Document re-ranking using cross-encoder models
- **RAM Usage**: ~800MB
- **Features**: Subquery averaging, batch ranking, text truncation
- **Endpoints**: `/rank/single`, `/rank/batch`, `/health`

### 3. **Research Service** (Port 8000)
- **Purpose**: Main orchestration, query expansion, vector retrieval
- **RAM Usage**: ~300MB (no models loaded)
- **Features**: Service coordination, similarity filtering, result formatting
- **Endpoints**: `/api/research`, `/health`

## 🚀 Quick Start

### Prerequisites
```bash
# Install Docker and Docker Compose
# Make sure you have your environment variables set
```

### Environment Variables
Create a `.env` file in the root directory:
```bash
# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key_here

# VectorX Configuration
VECTORX_API_TOKEN=your_vectorx_token_here
VECTORX_ENCRYPTION_KEY=your_encryption_key_here
VECTORX_INDEX_NAME=pubmed_collection_v1

# Model Configuration (optional)
LLM_MODEL_VERSION=gpt-4o-mini
```

### Local Development
```bash
# Start all services with Docker Compose
docker-compose up -d

# Check service health
curl http://localhost:8001/health  # Embedding service
curl http://localhost:8002/health  # Cross-encoder service
curl http://localhost:8000/health  # Research service

# Test the research endpoint
curl -X POST "http://localhost:8000/api/research" \
  -H "Content-Type: application/json" \
  -d '{
    "query": "What are the effects of caffeine on sleep?",
    "k": 5,
    "similarity_threshold": 0.7
  }'
```

### Individual Service Deployment

#### Embedding Service
```bash
cd embedding-service
docker build -t embedding-service .
docker run -p 8001:8001 \
  -e EMBEDDING_MODEL=BAAI/bge-m3 \
  -e USE_FP16=true \
  -e CACHE_SIZE=1000 \
  embedding-service
```

#### Cross-Encoder Service
```bash
cd cross-encoder-service
docker build -t cross-encoder-service .
docker run -p 8002:8002 \
  -e CROSS_ENCODER_MODEL=cross-encoder/ms-marco-MiniLM-L-12-v2 \
  -e USE_FP16=true \
  -e CACHE_SIZE=500 \
  cross-encoder-service
```

#### Research Service
```bash
cd python-research-service
docker build -t research-service .
docker run -p 8000:8000 \
  -e OPENAI_API_KEY=your_key \
  -e VECTORX_API_TOKEN=your_token \
  -e EMBEDDING_SERVICE_URL=http://embedding-service:8001 \
  -e CROSS_ENCODER_SERVICE_URL=http://cross-encoder-service:8002 \
  research-service
```

## 🌐 Production Deployment

### AWS EC2 Deployment Strategy

#### Option 1: Single Instance (t3.large recommended)
```bash
# Deploy all services on one instance
# Requires at least 4-6GB RAM
docker-compose up -d
```

#### Option 2: Distributed Deployment (Recommended)
```bash
# Instance 1 (t3.medium): Research Service
docker run -d -p 8000:8000 \
  -e EMBEDDING_SERVICE_URL=http://EMBEDDING_INSTANCE_IP:8001 \
  -e CROSS_ENCODER_SERVICE_URL=http://CROSS_ENCODER_INSTANCE_IP:8002 \
  research-service

# Instance 2 (t3.large): Embedding Service  
docker run -d -p 8001:8001 embedding-service

# Instance 3 (t3.medium): Cross-Encoder Service
docker run -d -p 8002:8002 cross-encoder-service
```

### Load Balancer Configuration
```nginx
# nginx.conf example
upstream embedding_service {
    server embedding-instance-1:8001;
    server embedding-instance-2:8001;
}

upstream cross_encoder_service {
    server cross-encoder-instance-1:8002;
}

upstream research_service {
    server research-instance-1:8000;
    server research-instance-2:8000;
}
```

## 📊 Performance Optimization

### Memory Usage by Service
- **Embedding Service**: 1.5GB (model) + 300MB (overhead) = ~1.8GB
- **Cross-Encoder Service**: 800MB (model) + 200MB (overhead) = ~1GB  
- **Research Service**: 300MB (no models, just HTTP clients)

### Recommended Instance Types
- **Embedding Service**: t3.large (2 vCPU, 8GB RAM) or c5.xlarge for better performance
- **Cross-Encoder Service**: t3.medium (2 vCPU, 4GB RAM) 
- **Research Service**: t3.small (2 vCPU, 2GB RAM) or t3.medium

### Scaling Strategies
```bash
# Scale embedding service for high load
docker-compose up -d --scale embedding-service=2

# Use different instance types per service
# embedding-service: CPU/memory optimized
# cross-encoder-service: Balanced
# research-service: Network optimized
```

## 🔧 Configuration Options

### Embedding Service Environment Variables
```bash
EMBEDDING_MODEL=BAAI/bge-m3              # Model to use
USE_FP16=true                            # Use half precision
CACHE_SIZE=1000                          # LRU cache size
MAX_BATCH_SIZE=64                        # Maximum batch size
MAX_TEXT_LENGTH=8192                     # Max input length
ENABLE_MEMORY_CLEANUP=true               # Auto memory cleanup
```

### Cross-Encoder Service Environment Variables
```bash
CROSS_ENCODER_MODEL=cross-encoder/ms-marco-MiniLM-L-12-v2
USE_FP16=true
CACHE_SIZE=500
MAX_BATCH_SIZE=32
MAX_TEXT_LENGTH=512
ENABLE_MEMORY_CLEANUP=true
```

### Research Service Environment Variables
```bash
EMBEDDING_SERVICE_URL=http://localhost:8001
CROSS_ENCODER_SERVICE_URL=http://localhost:8002
OPENAI_API_KEY=your_key
VECTORX_API_TOKEN=your_token
VECTORX_INDEX_NAME=pubmed_collection_v1
```

## 🔍 Monitoring and Health Checks

### Health Check Endpoints
```bash
# Check all services
curl http://localhost:8001/health | jq
curl http://localhost:8002/health | jq  
curl http://localhost:8000/health | jq
```

### Expected Health Response
```json
{
  "status": "healthy",
  "model_loaded": true,
  "memory_usage": {
    "rss_mb": 1024.5,
    "vms_mb": 2048.0,
    "percent": 25.6
  },
  "timestamp": "2025-01-07T10:30:00"
}
```

### Monitoring with Docker
```bash
# Monitor resource usage
docker stats

# View logs
docker-compose logs -f embedding-service
docker-compose logs -f cross-encoder-service
docker-compose logs -f research-service
```

## 🐛 Troubleshooting

### Common Issues

#### 1. Service Communication Errors
```bash
# Check network connectivity
docker network ls
docker network inspect research-services

# Test service connectivity
docker exec research-service curl http://embedding-service:8001/health
```

#### 2. Memory Issues
```bash
# Check memory usage
docker exec embedding-service free -h
docker exec cross-encoder-service free -h

# Restart with more memory
docker-compose up -d --force-recreate
```

#### 3. Model Loading Failures
```bash
# Check service logs
docker-compose logs embedding-service | grep -i error
docker-compose logs cross-encoder-service | grep -i error

# Verify model downloads
docker exec embedding-service ls -la ~/.cache/huggingface/
```

### Performance Tuning

#### For t3.medium Instances
```bash
# Reduce batch sizes
-e MAX_BATCH_SIZE=16    # Instead of 32
-e CACHE_SIZE=500       # Instead of 1000

# Enable aggressive memory cleanup
-e ENABLE_MEMORY_CLEANUP=true
```

#### For Production Workloads
```bash
# Increase batch sizes for better throughput
-e MAX_BATCH_SIZE=64
-e CACHE_SIZE=2000

# Use multiple workers
docker-compose up -d --scale embedding-service=2
```

## 📈 Expected Performance Improvements

Compared to the monolithic architecture:

- **RAM Usage**: 70-80% reduction on main instance
- **Embedding Speed**: 3-5x faster with dedicated service and caching
- **Cross-Encoder Speed**: 2-3x faster with optimized batching  
- **Overall Response Time**: 50-70% improvement
- **Scalability**: Independent scaling of bottleneck services
- **Reliability**: Service failure isolation

## 🔄 Migration from Monolithic Service

1. **Stop the old service**
2. **Deploy microservices** using Docker Compose
3. **Update environment variables** to point to new services
4. **Test all endpoints** to ensure compatibility
5. **Monitor performance** and adjust resources as needed

The API interface remains exactly the same, so no client-side changes are required!

## 📝 API Documentation

All services provide interactive API documentation:
- Embedding Service: http://localhost:8001/docs
- Cross-Encoder Service: http://localhost:8002/docs  
- Research Service: http://localhost:8000/docs

## 🎯 Next Steps

1. **Deploy services** using the provided Docker files
2. **Configure load balancing** for production traffic
3. **Set up monitoring** and alerting
4. **Implement auto-scaling** based on CPU/memory metrics
5. **Add service mesh** (like Istio) for advanced traffic management

This microservices architecture provides the foundation for a highly scalable, maintainable, and cost-effective medical research system!
