#!/usr/bin/env node

/**
 * Test Redis Pub/Sub Integration
 * Simulates Gatekeeper sending AI responses via Redis
 */

const { createClient } = require('redis');
require('dotenv').config({ path: '.env.local' });

const REDIS_URL = process.env.REDIS_URL;
const REDIS_PASSWORD = process.env.REDIS_PASSWORD;
const TEST_PHONE = '+919819304846';

console.log(`
🧪 REDIS PUB/SUB INTEGRATION TEST
================================
This test simulates Gatekeeper sending AI responses via Redis Pub/Sub
Phone: ${TEST_PHONE}
================================
`);

async function testRedisIntegration() {
  let publisher = null;
  let subscriber = null;
  
  try {
    console.log('📡 Creating Redis clients...');
    
    // Create publisher (simulates Gatekeeper)
    publisher = createClient({
      url: `rediss://${REDIS_URL}:6380`,
      password: REDIS_PASSWORD,
      socket: {
        tls: true,
        rejectUnauthorized: false
      }
    });

    // Create subscriber (simulates our server)
    subscriber = createClient({
      url: `rediss://${REDIS_URL}:6380`,
      password: REDIS_PASSWORD,
      socket: {
        tls: true,
        rejectUnauthorized: false
      }
    });

    publisher.on('error', (err) => console.error('❌ Publisher Error:', err));
    subscriber.on('error', (err) => console.error('❌ Subscriber Error:', err));

    console.log('🔌 Connecting to Redis...');
    await Promise.all([publisher.connect(), subscriber.connect()]);
    console.log('✅ Redis clients connected!');

    // Test 1: Subscribe to Gatekeeper pattern
    console.log('\n📢 Test 1: Setting up Gatekeeper subscription');
    const channelPattern = 'gatekeeper:response:*';
    
    let messagesReceived = 0;
    
    await subscriber.pSubscribe(channelPattern, (message, channel) => {
      messagesReceived++;
      console.log(`📨 [${messagesReceived}] Received on channel: ${channel}`);
      
      try {
        const responseData = JSON.parse(message);
        console.log('📄 Message content:', {
          text: responseData.text?.substring(0, 50) + '...',
          messageId: responseData.messageId,
          timestamp: responseData.timestamp
        });
      } catch (parseError) {
        console.log('📄 Raw message:', message.substring(0, 100) + '...');
      }
    });
    
    console.log('✅ Subscribed to pattern:', channelPattern);

    // Test 2: Simulate Gatekeeper responses
    console.log('\n🤖 Test 2: Simulating Gatekeeper AI responses');
    
    const testCases = [
      {
        dialogueId: 'test-patient-case-1',
        dialogueType: 'patient-case',
        response: 'Based on the symptoms you described, this could be indicative of...'
      },
      {
        dialogueId: 'test-research-1',
        dialogueType: 'research',
        response: 'Recent studies have shown that...'
      },
      {
        dialogueId: 'test-quick-fact-1',
        dialogueType: 'quick-fact',
        response: 'Normal BMI range is 18.5-24.9 kg/m²'
      }
    ];

    for (const [index, testCase] of testCases.entries()) {
      const channel = `gatekeeper:response:${TEST_PHONE}:${testCase.dialogueId}`;
      const aiResponse = {
        messageId: `ai-msg-${Date.now()}-${index}`,
        text: testCase.response,
        dialogueId: testCase.dialogueId,
        dialogueType: testCase.dialogueType,
        timestamp: new Date().toISOString(),
        source: 'gatekeeper-ai'
      };

      console.log(`📤 Publishing to: ${channel}`);
      await publisher.publish(channel, JSON.stringify(aiResponse));
      
      // Wait between messages
      await new Promise(resolve => setTimeout(resolve, 1000));
    }

    // Test 3: Wait for all messages to be received
    console.log('\n⏳ Test 3: Waiting for message processing...');
    await new Promise(resolve => setTimeout(resolve, 3000));

    // Test 4: Summary
    console.log('\n📊 Test Results:');
    console.log(`Messages sent: ${testCases.length}`);
    console.log(`Messages received: ${messagesReceived}`);
    
    if (messagesReceived === testCases.length) {
      console.log('✅ All messages received successfully!');
      console.log('🎉 Redis Pub/Sub integration is working correctly');
    } else {
      console.log('⚠️ Some messages may have been lost');
      console.log('Check Redis connectivity and subscription patterns');
    }

    // Test 5: Channel format validation
    console.log('\n🔍 Test 5: Channel format validation');
    const validChannels = [
      `gatekeeper:response:${TEST_PHONE}:dialogue-123`,
      `gatekeeper:response:+1234567890:research-456`,
      `gatekeeper:response:+44987654321:quick-fact-789`
    ];

    validChannels.forEach(channel => {
      const parts = channel.split(':');
      console.log(`Channel: ${channel}`);
      console.log(`  Phone: ${parts[2]}`);
      console.log(`  Dialogue ID: ${parts[3]}`);
      console.log(`  Valid format: ${parts.length >= 4 ? '✅' : '❌'}`);
    });

  } catch (error) {
    console.error('❌ Test failed:', error);
  } finally {
    // Cleanup
    try {
      if (subscriber) {
        await subscriber.unsubscribe();
        await subscriber.quit();
        console.log('🔌 Subscriber disconnected');
      }
      if (publisher) {
        await publisher.quit();
        console.log('🔌 Publisher disconnected');
      }
    } catch (cleanupError) {
      console.warn('⚠️ Cleanup warning:', cleanupError.message);
    }
  }
}

// Main execution
async function main() {
  if (!REDIS_URL || !REDIS_PASSWORD) {
    console.log('❌ Redis configuration missing. Check .env.local file.');
    process.exit(1);
  }

  await testRedisIntegration();
  
  console.log('\n🎯 Next Steps:');
  console.log('1. Start your server: npm run dev');
  console.log('2. Connect via WebSocket from frontend');
  console.log('3. Send a message to trigger Gatekeeper webhook');
  console.log('4. Gatekeeper will publish AI response to Redis');
  console.log('5. Your server will receive and forward to WebSocket client');
  
  console.log('\n✨ Redis Pub/Sub integration test completed!');
}

main().catch(error => {
  console.error('💥 Test script failed:', error);
  process.exit(1);
});