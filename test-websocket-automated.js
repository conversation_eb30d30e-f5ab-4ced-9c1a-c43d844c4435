const puppeteer = require('puppeteer');
const { spawn } = require('child_process');

let server;
let browser;

async function startServer() {
  console.log('🚀 Starting custom server...');
  server = spawn('node', ['custom-server.js'], {
    env: { ...process.env, NODE_ENV: 'development' }
  });
  
  // Capture server output
  server.stdout.on('data', (data) => {
    console.log(`[SERVER] ${data.toString().trim()}`);
  });
  
  server.stderr.on('data', (data) => {
    console.error(`[SERVER ERROR] ${data.toString().trim()}`);
  });
  
  // Wait for server to start
  await new Promise(resolve => setTimeout(resolve, 5000));
  console.log('✅ Server should be running');
}

async function testWebSocket() {
  console.log('\n📱 Starting WebSocket test...\n');
  
  browser = await puppeteer.launch({
    headless: false,
    defaultViewport: null,
    args: ['--window-size=1200,800']
  });
  
  const page = await browser.newPage();
  
  // Enable console logging
  page.on('console', msg => {
    const text = msg.text();
    if (text.includes('🔌') || text.includes('✅') || text.includes('📤') || 
        text.includes('🚀') || text.includes('❌') || text.includes('🔑')) {
      console.log(`[BROWSER] ${text}`);
    }
  });
  
  try {
    // Navigate to login page
    console.log('📍 Navigating to http://localhost:3000');
    await page.goto('http://localhost:3000', { waitUntil: 'networkidle0' });
    
    // Take screenshot
    await page.screenshot({ path: 'websocket-test-1-login.png' });
    
    // Enter phone number
    console.log('📱 Entering phone number...');
    await page.waitForSelector('input[type="tel"]');
    await page.type('input[type="tel"]', '+919819304846');
    
    // Click send OTP
    console.log('📤 Sending OTP...');
    await page.click('button[type="submit"]');
    
    // Wait for OTP field
    await page.waitForSelector('input[placeholder*="6-digit"]', { timeout: 10000 });
    await page.screenshot({ path: 'websocket-test-2-otp.png' });
    
    // Enter OTP
    console.log('🔢 Entering OTP...');
    await page.type('input[placeholder*="6-digit"]', '123456');
    
    // Click verify
    await page.click('button[type="submit"]');
    
    // Wait for dashboard to load
    console.log('⏳ Waiting for dashboard...');
    await page.waitForSelector('textarea[placeholder*="Describe"]', { timeout: 15000 });
    await page.screenshot({ path: 'websocket-test-3-dashboard.png' });
    
    // Wait a bit for WebSocket to connect
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Type a test message
    console.log('💬 Typing test message...');
    const testMessage = 'Testing WebSocket connection - can you hear me?';
    await page.type('textarea[placeholder*="Describe"]', testMessage);
    await page.screenshot({ path: 'websocket-test-4-message-typed.png' });
    
    // Send message
    console.log('🚀 Sending message via WebSocket...');
    await page.keyboard.press('Enter');
    
    // Wait for response
    console.log('⏳ Waiting for WebSocket response...');
    await new Promise(resolve => setTimeout(resolve, 5000));
    
    // Take final screenshot
    await page.screenshot({ path: 'websocket-test-5-response.png' });
    
    // Check if response appeared
    const responseText = await page.evaluate(() => {
      const messages = document.querySelectorAll('[class*="MuiPaper-root"]');
      const lastMessage = messages[messages.length - 1];
      return lastMessage ? lastMessage.innerText : null;
    });
    
    console.log('\n📊 Test Results:');
    if (responseText && responseText.includes('WebSocket connection is working')) {
      console.log('✅ SUCCESS: WebSocket message sent and response received!');
      console.log('📨 Response:', responseText);
    } else {
      console.log('❌ FAILED: No WebSocket response received');
      console.log('Last message found:', responseText);
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    await page.screenshot({ path: 'websocket-test-error.png' });
  }
}

async function cleanup() {
  console.log('\n🧹 Cleaning up...');
  if (browser) await browser.close();
  if (server) {
    server.kill();
    console.log('🛑 Server stopped');
  }
}

// Run the test
(async () => {
  try {
    await startServer();
    await testWebSocket();
  } catch (error) {
    console.error('Test error:', error);
  } finally {
    await cleanup();
    process.exit(0);
  }
})();