const { io } = require('socket.io-client');

// Test WebSocket connection to production server
const testWebSocket = async () => {
  console.log('🔌 Testing WebSocket connection to production server...');
  
  const socket = io('http://ec2-3-111-56-167.ap-south-1.compute.amazonaws.com:3000', {
    transports: ['websocket', 'polling'],
    timeout: 10000,
    auth: {
      token: 'test-token'
    }
  });

  socket.on('connect', () => {
    console.log('✅ Connected to production WebSocket');
    console.log('🔌 Socket ID:', socket.id);
    console.log('🔌 Transport:', socket.io.engine.transport.name);
    
    // Test authentication
    socket.emit('authenticate', {
      token: 'test-token',
      user: { id: 'test-user', phone: '+919819304846' }
    });
  });

  socket.on('authenticated', (data) => {
    console.log('✅ Authentication successful:', data);
    
    // Test sending a message
    socket.emit('send_message', {
      threadId: 'test-thread-id',
      content: 'Test message from production test',
      conversationType: 'patient_case'
    });
  });

  socket.on('connect_error', (error) => {
    console.error('❌ Connection error:', error);
    console.error('❌ Error type:', error.type);
    console.error('❌ Error message:', error.message);
  });

  socket.on('disconnect', (reason) => {
    console.log('❌ Disconnected:', reason);
  });

  socket.on('error', (error) => {
    console.error('❌ Socket error:', error);
  });

  // Connect
  socket.connect();
  
  // Disconnect after 10 seconds
  setTimeout(() => {
    console.log('🔌 Disconnecting test socket...');
    socket.disconnect();
    process.exit(0);
  }, 10000);
};

testWebSocket();
