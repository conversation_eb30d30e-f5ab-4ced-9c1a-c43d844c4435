const axios = require('axios');

// Test User Registration
async function testRegistration() {
  console.log('\n👤 TEST 3: User Registration');
  console.log('=' + '='.repeat(60));
  console.log('Purpose: Register a new doctor user (or verify existing)');
  console.log('Endpoint: POST /c/practitioner-dashboard/register');
  console.log('Auth: Uses static bearer token (not JWT)');
  console.log('=' + '='.repeat(60));
  
  const url = 'https://gatekeeper-staging.getbeyondhealth.com/c/practitioner-dashboard/register';
  const staticToken = 'm}0/m9ZL`k{|Mz:Ca{7k8PF(gJV"Xz/j';
  
  const payload = {
    source: 'WEB',
    phoneNumber: '+919819304846',
    user_role: 'DOCTOR'
  };
  
  console.log('\n📤 Request:');
  console.log('URL:', url);
  console.log('Headers:');
  console.log('  Authorization: Bearer', staticToken);
  console.log('  Content-Type: application/json');
  console.log('Body:', JSON.stringify(payload, null, 2));
  
  console.log('\n📝 What this endpoint does:');
  console.log('- Creates a new user record if phone number is new');
  console.log('- Sets access: false (pending admin approval)');
  console.log('- If user exists, returns success (idempotent)');
  
  try {
    const response = await axios.post(url, payload, {
      headers: {
        'Authorization': `Bearer ${staticToken}`,
        'Content-Type': 'application/json'
      }
    });
    
    console.log('\n✅ Response Success!');
    console.log('Status:', response.status, response.statusText);
    console.log('Body:', JSON.stringify(response.data, null, 2));
    
    console.log('\n✅ Test Result: PASSED');
    console.log('- Registration endpoint is working');
    console.log('- Static token authentication successful');
    
    if (response.status === 200) {
      console.log('- User already exists (idempotent behavior)');
    } else if (response.status === 201) {
      console.log('- New user created successfully');
    }
    
  } catch (error) {
    console.log('\n❌ Response Failed!');
    console.log('Status:', error.response?.status);
    console.log('Error:', error.response?.data || error.message);
    
    if (error.response?.status === 401) {
      console.log('\n⚠️ Authentication Failed!');
      console.log('The static bearer token may be incorrect');
    } else if (error.response?.status === 409) {
      console.log('\n⚠️ User Already Exists!');
      console.log('This is expected behavior for existing users');
    }
  }
}

testRegistration();