{"timestamp": "2025-06-25T08:10:27.502Z", "baseUrl": "https://gatekeeper-staging.getbeyondhealth.com", "tenant": "practitioner-dashboard", "testPhone": "+919819304846", "apis": {"Request OTP": {"endpoint": "/auth/practitioner-dashboard/request-otp", "method": "POST", "timestamp": "2025-06-25T08:10:28.405Z", "request": {"headers": {"Content-Type": "application/json"}, "params": {}, "body": {"phoneNumber": "+919819304846"}}, "response": {"status": 200, "statusText": "OK", "data": {"requestId": "test-request-id"}, "headers": {"content-type": "application/json; charset=utf-8", "content-length": "31", "connection": "keep-alive", "date": "Wed, 25 Jun 2025 08:10:28 GMT", "etag": "W/\"1f-FTEg0uJrn0Xdq1WdYZzoE29nors\"", "access-control-allow-origin": "*", "content-security-policy": "default-src 'self';base-uri 'self';font-src 'self' https: data:;form-action 'self';frame-ancestors 'self';img-src 'self' data:;object-src 'none';script-src 'self';script-src-attr 'none';style-src 'self' https: 'unsafe-inline';upgrade-insecure-requests", "cross-origin-opener-policy": "same-origin", "cross-origin-resource-policy": "same-origin", "origin-agent-cluster": "?1", "referrer-policy": "no-referrer", "strict-transport-security": "max-age=15552000; includeSubDomains", "x-content-type-options": "nosniff", "x-dns-prefetch-control": "off", "x-download-options": "noopen", "x-frame-options": "SAMEORIGIN", "x-permitted-cross-domain-policies": "none", "x-xss-protection": "0", "ratelimit-policy": "4;w=1800", "ratelimit-limit": "4", "ratelimit-remaining": "0", "ratelimit-reset": "713", "x-cache": "Miss from cloudfront", "via": "1.1 c4c157354bcc3a6d902795d5ad7850ca.cloudfront.net (CloudFront)", "x-amz-cf-pop": "BLR50-P1", "x-amz-cf-id": "MLUKQb3PE5FufmAMYxH0aayc6tIHtemMIqEt5obzxB2IE2T3-nt18Q=="}}, "error": null, "success": true}, "Register User": {"endpoint": "/c/practitioner-dashboard/register", "method": "POST", "timestamp": "2025-06-25T08:10:31.557Z", "request": {"headers": {"Authorization": "Bearer m}0/m9ZL`k{|Mz:Ca{7k8PF(gJV\"Xz/j", "Content-Type": "application/json"}, "params": {}, "body": {"source": "WEB", "phoneNumber": "+919819304846", "user_role": "DOCTOR"}}, "response": {"status": 200, "statusText": "OK", "data": {"success": true, "message": "User registered successfully"}, "headers": {"content-type": "application/json; charset=utf-8", "content-length": "57", "connection": "keep-alive", "date": "Wed, 25 Jun 2025 08:10:31 GMT", "etag": "W/\"39-L4ChN3EFRYuIiTpWUtpbvMTfrpg\"", "access-control-allow-origin": "*", "content-security-policy": "default-src 'self';base-uri 'self';font-src 'self' https: data:;form-action 'self';frame-ancestors 'self';img-src 'self' data:;object-src 'none';script-src 'self';script-src-attr 'none';style-src 'self' https: 'unsafe-inline';upgrade-insecure-requests", "cross-origin-opener-policy": "same-origin", "cross-origin-resource-policy": "same-origin", "origin-agent-cluster": "?1", "referrer-policy": "no-referrer", "strict-transport-security": "max-age=15552000; includeSubDomains", "x-content-type-options": "nosniff", "x-dns-prefetch-control": "off", "x-download-options": "noopen", "x-frame-options": "SAMEORIGIN", "x-permitted-cross-domain-policies": "none", "x-xss-protection": "0", "x-cache": "Miss from cloudfront", "via": "1.1 c4c157354bcc3a6d902795d5ad7850ca.cloudfront.net (CloudFront)", "x-amz-cf-pop": "BLR50-P1", "x-amz-cf-id": "Rjunn-Pj9OaX8yXPHhlq_0vz_vydO3G2EDZOCGV-N0FTiRkiYE9Bmg=="}}, "error": null, "success": true}, "Get All Dialogues": {"endpoint": "/user/practitioner-dashboard/get-chats-by-dialogueId", "method": "GET", "timestamp": "2025-06-25T08:10:32.728Z", "request": {"headers": {"Authorization": "Bearer test-jwt", "Content-Type": "application/json"}, "params": {"limit": 100, "dialogue_id": ""}, "body": {}}, "response": null, "error": {"message": "Request failed with status code 401", "code": "ERR_BAD_REQUEST", "status": 401, "statusText": "Unauthorized", "data": {"success": false, "message": "Invalid or expired JWT token", "details": "jwt malformed"}}, "success": false}, "Send Text Message": {"endpoint": "/c/practitioner-dashboard/webhook", "method": "POST", "timestamp": "2025-06-25T08:10:33.998Z", "request": {"headers": {"Content-Type": "application/json"}, "params": {}, "body": {"dialogueId": "e5c2da90-ddfe-4627-be4c-1cee6c29490a", "dialogueType": "patient-case", "text": "Test patient case: 45-year-old male with chest pain", "providerMessageId": "5959a438-64bb-47d3-9bb6-3317ed33fc30", "sender": "human", "source": "WEB", "phoneNumber": "+919819304846", "timestamp": 1750839033731, "requestId": "db52f701-761c-42d3-bb58-def9b828e31c"}}, "response": {"status": 200, "statusText": "OK", "data": {"success": true, "tenantSlug": "practitioner-dashboard"}, "headers": {"content-type": "application/json; charset=utf-8", "content-length": "54", "connection": "keep-alive", "date": "Wed, 25 Jun 2025 08:10:34 GMT", "etag": "W/\"36-Ag4/1+DYEIm4mBv3udbVuJNFF9I\"", "access-control-allow-origin": "*", "content-security-policy": "default-src 'self';base-uri 'self';font-src 'self' https: data:;form-action 'self';frame-ancestors 'self';img-src 'self' data:;object-src 'none';script-src 'self';script-src-attr 'none';style-src 'self' https: 'unsafe-inline';upgrade-insecure-requests", "cross-origin-opener-policy": "same-origin", "cross-origin-resource-policy": "same-origin", "origin-agent-cluster": "?1", "referrer-policy": "no-referrer", "strict-transport-security": "max-age=15552000; includeSubDomains", "x-content-type-options": "nosniff", "x-dns-prefetch-control": "off", "x-download-options": "noopen", "x-frame-options": "SAMEORIGIN", "x-permitted-cross-domain-policies": "none", "x-xss-protection": "0", "x-cache": "Miss from cloudfront", "via": "1.1 c4c157354bcc3a6d902795d5ad7850ca.cloudfront.net (CloudFront)", "x-amz-cf-pop": "BLR50-P1", "x-amz-cf-id": "iV0oILPFgmZyjBNEXM2_ZY1GGSW2s0lqmWda2i4l0PolEu1Gmtfilw=="}}, "error": null, "success": true}, "Send Message with File": {"endpoint": "/c/practitioner-dashboard/webhook", "method": "POST", "timestamp": "2025-06-25T08:10:35.467Z", "request": {"headers": {"Content-Type": "application/json"}, "params": {}, "body": {"dialogueId": "d13d2532-1783-4c6f-8fa7-1cf6defbaf5d", "dialogueType": "patient-case", "text": "X-ray report attached", "providerMessageId": "54a8ea1b-3151-49a3-b22d-788b8c046ce7", "attachment": "https://example-blob.blob.core.windows.net/container/xray-report.png", "fileExtension": ".png", "messageType": "image", "sender": "human", "source": "WEB", "phoneNumber": "+919819304846", "timestamp": 1750839034999, "requestId": "14153bd9-00d5-48f4-9ee6-6f6d412eb4bf"}}, "response": {"status": 200, "statusText": "OK", "data": {"success": true, "tenantSlug": "practitioner-dashboard"}, "headers": {"content-type": "application/json; charset=utf-8", "content-length": "54", "connection": "keep-alive", "date": "Wed, 25 Jun 2025 08:10:35 GMT", "etag": "W/\"36-Ag4/1+DYEIm4mBv3udbVuJNFF9I\"", "access-control-allow-origin": "*", "content-security-policy": "default-src 'self';base-uri 'self';font-src 'self' https: data:;form-action 'self';frame-ancestors 'self';img-src 'self' data:;object-src 'none';script-src 'self';script-src-attr 'none';style-src 'self' https: 'unsafe-inline';upgrade-insecure-requests", "cross-origin-opener-policy": "same-origin", "cross-origin-resource-policy": "same-origin", "origin-agent-cluster": "?1", "referrer-policy": "no-referrer", "strict-transport-security": "max-age=15552000; includeSubDomains", "x-content-type-options": "nosniff", "x-dns-prefetch-control": "off", "x-download-options": "noopen", "x-frame-options": "SAMEORIGIN", "x-permitted-cross-domain-policies": "none", "x-xss-protection": "0", "x-cache": "Miss from cloudfront", "via": "1.1 c4c157354bcc3a6d902795d5ad7850ca.cloudfront.net (CloudFront)", "x-amz-cf-pop": "BLR50-P1", "x-amz-cf-id": "ztDEZnECfKv71uh4ZJ3cq4758M-nqfze_H9oWw-U7-MCkjOshH5PCQ=="}}, "error": null, "success": true}, "Send Message Multiple Files": {"endpoint": "/c/practitioner-dashboard/webhook", "method": "POST", "timestamp": "2025-06-25T08:10:36.745Z", "request": {"headers": {"Content-Type": "application/json"}, "params": {}, "body": {"dialogueId": "151f94d0-a489-4053-90af-c45aeb3e8c29", "dialogueType": "research", "text": "Multiple research papers attached", "providerMessageId": "20a6f8bf-b945-4e76-b199-11e676d0ad1d", "attachment": [{"url": "https://example-blob.blob.core.windows.net/container/paper1.pdf", "fileExtension": ".pdf", "messageType": "pdf"}, {"url": "https://example-blob.blob.core.windows.net/container/figure1.png", "fileExtension": ".png", "messageType": "image"}], "sender": "human", "source": "WEB", "phoneNumber": "+919819304846", "timestamp": 1750839036468, "requestId": "39578efd-d0d9-40dc-a7c4-78dc557be3d0"}}, "response": {"status": 200, "statusText": "OK", "data": {"success": true, "tenantSlug": "practitioner-dashboard"}, "headers": {"content-type": "application/json; charset=utf-8", "content-length": "54", "connection": "keep-alive", "date": "Wed, 25 Jun 2025 08:10:36 GMT", "etag": "W/\"36-Ag4/1+DYEIm4mBv3udbVuJNFF9I\"", "access-control-allow-origin": "*", "content-security-policy": "default-src 'self';base-uri 'self';font-src 'self' https: data:;form-action 'self';frame-ancestors 'self';img-src 'self' data:;object-src 'none';script-src 'self';script-src-attr 'none';style-src 'self' https: 'unsafe-inline';upgrade-insecure-requests", "cross-origin-opener-policy": "same-origin", "cross-origin-resource-policy": "same-origin", "origin-agent-cluster": "?1", "referrer-policy": "no-referrer", "strict-transport-security": "max-age=15552000; includeSubDomains", "x-content-type-options": "nosniff", "x-dns-prefetch-control": "off", "x-download-options": "noopen", "x-frame-options": "SAMEORIGIN", "x-permitted-cross-domain-policies": "none", "x-xss-protection": "0", "x-cache": "Miss from cloudfront", "via": "1.1 c4c157354bcc3a6d902795d5ad7850ca.cloudfront.net (CloudFront)", "x-amz-cf-pop": "BLR50-P1", "x-amz-cf-id": "r8AQ0-jfsO96ddRU1kHomwkUSZ16lBCFr9dyibmTiM_dfpbLXXnYZA=="}}, "error": null, "success": true}, "All Dialogue Types": {"patient-case": {"success": true, "response": {"success": true, "tenantSlug": "practitioner-dashboard"}}, "research": {"success": true, "response": {"success": true, "tenantSlug": "practitioner-dashboard"}}, "quick-fact": {"success": true, "response": {"success": true, "tenantSlug": "practitioner-dashboard"}}}, "Get Messages by Dialogue ID": {"endpoint": "/user/practitioner-dashboard/get-chats-by-dialogueId", "method": "GET", "timestamp": "2025-06-25T08:10:39.413Z", "request": {"headers": {"Authorization": "Bearer test-jwt", "Content-Type": "application/json"}, "params": {"limit": 30, "dialogue_id": "e5c2da90-ddfe-4627-be4c-1cee6c29490a"}, "body": {}}, "response": null, "error": {"message": "Request failed with status code 401", "code": "ERR_BAD_REQUEST", "status": 401, "statusText": "Unauthorized", "data": {"success": false, "message": "Invalid or expired JWT token", "details": "jwt malformed"}}, "success": false}}, "testDialogueId": "e5c2da90-ddfe-4627-be4c-1cee6c29490a"}