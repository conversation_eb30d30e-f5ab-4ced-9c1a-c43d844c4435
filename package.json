{"name": "doctor-dashboard", "version": "0.1.0", "private": true, "scripts": {"dev": "node custom-server.js", "build": "next build", "start": "NODE_ENV=production node custom-server.js", "lint": "next lint", "test:e2e": "./run-e2e-tests.sh", "test:e2e:auth": "./run-e2e-tests.sh -s authentication", "test:e2e:headed": "./run-e2e-tests.sh -H", "test:e2e:cleanup": "./run-e2e-tests.sh -c", "test:registration": "node automated-tests/test-registration.js"}, "dependencies": {"@azure/storage-blob": "^12.17.0", "@blocknote/core": "^0.35.0", "@blocknote/mantine": "^0.35.0", "@blocknote/react": "^0.35.0", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^7.1.2", "@mui/material": "^7.1.2", "@mui/system": "^7.2.0", "@socket.io/redis-adapter": "^8.2.1", "axios": "^1.6.5", "bcryptjs": "^3.0.2", "body-parser": "^1.20.2", "dotenv": "^16.6.1", "express": "^4.18.2", "form-data": "^4.0.4", "jsonwebtoken": "^9.0.2", "marked": "^16.1.2", "next": "15.3.4", "node-fetch": "^3.3.2", "pg": "^8.16.2", "react": "^19.0.0", "react-dom": "^19.0.0", "react-markdown": "^10.1.0", "react-phone-input-2": "^2.15.1", "redis": "^5.5.6", "rehype-raw": "^7.0.0", "remark-gfm": "^4.0.1", "socket.io": "^4.7.4", "socket.io-client": "^4.7.4", "uuid": "^11.1.0", "winston": "^3.11.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "critters": "^0.0.23", "eslint": "^9", "eslint-config-next": "15.3.4", "puppeteer": "^24.10.2", "tailwindcss": "^4", "typescript": "^5"}}