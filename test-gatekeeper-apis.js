const axios = require('axios');
const { v4: uuidv4 } = require('uuid');
const fs = require('fs');

// Configuration
const BASE_URL = 'https://gatekeeper-staging.getbeyondhealth.com';
const AUGUST_TOKEN = 'm}0/m9ZL`k{|Mz:Ca{7k8PF(gJV"Xz/j';
const TEST_PHONE = '+************';

// Test user JWT from generate-test-jwt.js
const TEST_JWT = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************************************************************._J4CfDglZYPj5r95NoHmw3RyEpK2GgenLv2p1pjTSYs';

// Test results object
const testResults = {
  timestamp: new Date().toISOString(),
  tests: {}
};

// Helper function to log results
function logResult(testName, success, data, error = null) {
  testResults.tests[testName] = {
    success,
    timestamp: new Date().toISOString(),
    data: success ? data : null,
    error: error ? {
      message: error.message,
      status: error.response?.status,
      statusText: error.response?.statusText,
      data: error.response?.data
    } : null
  };
  
  console.log(`\n${'='.repeat(60)}`);
  console.log(`Test: ${testName}`);
  console.log(`Status: ${success ? '✅ SUCCESS' : '❌ FAILED'}`);
  if (success) {
    console.log('Response:', JSON.stringify(data, null, 2));
  } else {
    console.log('Error:', error.message);
    if (error.response?.data) {
      console.log('Error Details:', JSON.stringify(error.response.data, null, 2));
    }
  }
  console.log('='.repeat(60));
}

// Test 1: User Registration
async function testUserRegistration() {
  console.log('\n🧪 Testing User Registration API...');
  
  try {
    const response = await axios.post(
      `${BASE_URL}/c/august/register`,
      {
        source: 'WEB',
        phoneNumber: TEST_PHONE,
        user_role: 'DOCTOR'
      },
      {
        headers: {
          'Authorization': `Bearer ${AUGUST_TOKEN}`,
          'Content-Type': 'application/json'
        }
      }
    );
    
    logResult('User Registration', true, response.data);
    return response.data;
  } catch (error) {
    logResult('User Registration', false, null, error);
    return null;
  }
}

// Test 2: Get All Dialogues
async function testGetAllDialogues() {
  console.log('\n🧪 Testing Get All Dialogues API...');
  
  try {
    const response = await axios.get(
      `${BASE_URL}/user/practitioner-dashboard/get-chats-by-dialogueId`,
      {
        params: {
          limit: 100,
          dialogue_id: ''
        },
        headers: {
          'Authorization': `Bearer ${TEST_JWT}`,
          'Content-Type': 'application/json'
        }
      }
    );
    
    logResult('Get All Dialogues', true, response.data);
    return response.data;
  } catch (error) {
    logResult('Get All Dialogues', false, null, error);
    return null;
  }
}

// Test 3: Send Text Message
async function testSendTextMessage() {
  console.log('\n🧪 Testing Send Text Message API...');
  
  const dialogueId = uuidv4();
  const providerMessageId = uuidv4();
  const requestId = uuidv4();
  
  try {
    const response = await axios.post(
      `${BASE_URL}/c/august/webhook`,
      {
        dialogueId: dialogueId,
        dialogueType: 'patient-case',
        text: 'Test message from API test script',
        providerMessageId: providerMessageId,
        sender: 'human',
        source: 'WEB',
        phoneNumber: TEST_PHONE,
        timestamp: Date.now(),
        requestId: requestId
      },
      {
        headers: {
          'Authorization': `Bearer ${TEST_JWT}`,
          'Content-Type': 'application/json'
        }
      }
    );
    
    logResult('Send Text Message', true, {
      ...response.data,
      sentData: { dialogueId, providerMessageId, requestId }
    });
    return { success: true, dialogueId };
  } catch (error) {
    logResult('Send Text Message', false, null, error);
    return { success: false };
  }
}

// Test 4: Get Messages by Dialogue ID
async function testGetMessagesByDialogueId(dialogueId) {
  console.log('\n🧪 Testing Get Messages by Dialogue ID API...');
  
  if (!dialogueId) {
    console.log('⚠️  No dialogue ID provided, skipping test');
    return null;
  }
  
  try {
    const response = await axios.get(
      `${BASE_URL}/user/practitioner-dashboard/get-chats-by-dialogueId`,
      {
        params: {
          limit: 30,
          dialogue_id: dialogueId
        },
        headers: {
          'Authorization': `Bearer ${TEST_JWT}`,
          'Content-Type': 'application/json'
        }
      }
    );
    
    logResult('Get Messages by Dialogue ID', true, response.data);
    return response.data;
  } catch (error) {
    logResult('Get Messages by Dialogue ID', false, null, error);
    return null;
  }
}

// Test 5: Send Message with Single File
async function testSendMessageWithSingleFile() {
  console.log('\n🧪 Testing Send Message with Single File API...');
  
  const dialogueId = uuidv4();
  const providerMessageId = uuidv4();
  const requestId = uuidv4();
  
  // Simulated Azure Blob URL (in real implementation, you'd upload first)
  const mockFileUrl = 'https://azure-blob-storage.com/test-container/test-image.png';
  
  try {
    const response = await axios.post(
      `${BASE_URL}/c/august/webhook`,
      {
        dialogueId: dialogueId,
        dialogueType: 'patient-case',
        text: 'Here is the patient X-ray',
        providerMessageId: providerMessageId,
        attachment: mockFileUrl,
        fileExtension: '.png',
        messageType: 'image',
        sender: 'human',
        source: 'WEB',
        phoneNumber: TEST_PHONE,
        timestamp: Date.now(),
        requestId: requestId
      },
      {
        headers: {
          'Authorization': `Bearer ${TEST_JWT}`,
          'Content-Type': 'application/json'
        }
      }
    );
    
    logResult('Send Message with Single File', true, response.data);
    return { success: true, dialogueId };
  } catch (error) {
    logResult('Send Message with Single File', false, null, error);
    return { success: false };
  }
}

// Test 6: Send Message with Multiple Files
async function testSendMessageWithMultipleFiles() {
  console.log('\n🧪 Testing Send Message with Multiple Files API...');
  
  const dialogueId = uuidv4();
  const providerMessageId = uuidv4();
  const requestId = uuidv4();
  
  // Simulated Azure Blob URLs
  const mockFiles = [
    {
      url: 'https://azure-blob-storage.com/test-container/report1.png',
      fileExtension: '.png',
      messageType: 'image'
    },
    {
      url: 'https://azure-blob-storage.com/test-container/report2.pdf',
      fileExtension: '.pdf',
      messageType: 'pdf'
    }
  ];
  
  try {
    const response = await axios.post(
      `${BASE_URL}/c/august/webhook`,
      {
        dialogueId: dialogueId,
        dialogueType: 'research',
        text: 'Multiple patient reports attached',
        providerMessageId: providerMessageId,
        attachment: mockFiles,
        sender: 'human',
        source: 'WEB',
        phoneNumber: TEST_PHONE,
        timestamp: Date.now(),
        requestId: requestId
      },
      {
        headers: {
          'Authorization': `Bearer ${TEST_JWT}`,
          'Content-Type': 'application/json'
        }
      }
    );
    
    logResult('Send Message with Multiple Files', true, response.data);
    return { success: true, dialogueId };
  } catch (error) {
    logResult('Send Message with Multiple Files', false, null, error);
    return { success: false };
  }
}

// Test 7: Test Different Dialogue Types
async function testDifferentDialogueTypes() {
  console.log('\n🧪 Testing Different Dialogue Types...');
  
  const dialogueTypes = ['patient-case', 'research', 'quick-fact'];
  const results = {};
  
  for (const type of dialogueTypes) {
    const dialogueId = uuidv4();
    const providerMessageId = uuidv4();
    const requestId = uuidv4();
    
    try {
      const response = await axios.post(
        `${BASE_URL}/c/august/webhook`,
        {
          dialogueId: dialogueId,
          dialogueType: type,
          text: `Test message for ${type} dialogue type`,
          providerMessageId: providerMessageId,
          sender: 'human',
          source: 'WEB',
          phoneNumber: TEST_PHONE,
          timestamp: Date.now(),
          requestId: requestId
        },
        {
          headers: {
            'Authorization': `Bearer ${TEST_JWT}`,
            'Content-Type': 'application/json'
          }
        }
      );
      
      results[type] = { success: true, response: response.data };
    } catch (error) {
      results[type] = { 
        success: false, 
        error: error.response?.data || error.message 
      };
    }
  }
  
  logResult('Different Dialogue Types', true, results);
  return results;
}

// Main test runner
async function runAllTests() {
  console.log(`
${'='.repeat(60)}
🚀 GATEKEEPER API TEST SUITE
${'='.repeat(60)}
Base URL: ${BASE_URL}
Test Phone: ${TEST_PHONE}
JWT Token: ${TEST_JWT.substring(0, 50)}...
Timestamp: ${new Date().toISOString()}
${'='.repeat(60)}
`);

  // Run tests in sequence
  // Note: Commenting out registration test as user might already exist
  // await testUserRegistration();
  
  await testGetAllDialogues();
  
  const textMessageResult = await testSendTextMessage();
  
  // Wait a bit to allow message to be processed
  if (textMessageResult.success) {
    console.log('\n⏳ Waiting 2 seconds for message to be processed...');
    await new Promise(resolve => setTimeout(resolve, 2000));
    await testGetMessagesByDialogueId(textMessageResult.dialogueId);
  }
  
  await testSendMessageWithSingleFile();
  await testSendMessageWithMultipleFiles();
  await testDifferentDialogueTypes();
  
  // Save test results
  const resultsPath = `test-results-${Date.now()}.json`;
  fs.writeFileSync(resultsPath, JSON.stringify(testResults, null, 2));
  
  console.log(`\n
${'='.repeat(60)}
📊 TEST SUMMARY
${'='.repeat(60)}
Total Tests: ${Object.keys(testResults.tests).length}
Passed: ${Object.values(testResults.tests).filter(t => t.success).length}
Failed: ${Object.values(testResults.tests).filter(t => !t.success).length}
Results saved to: ${resultsPath}
${'='.repeat(60)}
`);
}

// Run the tests
runAllTests().catch(console.error);