#!/bin/bash

echo "Testing exact curl command from logs..."
echo "======================================"

curl -X POST "https://gatekeeper-staging.getbeyondhealth.com/c/practitioner-dashboard/webhook" \
    -H "Content-Type: application/json" \
    -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************************************************************************************************************************************************.QFnZ5tkp-pZG-NRcdUAEDaOOI8RUcFH8X2C_IvVxgio" \
    -d '{
  "dialogueId": "e232c5f2-6357-4242-a103-a95352b397d9",
  "dialogueType": "patient-case",
  "text": "hello world",
  "providerMessageId": "bcb28b02-a82b-4765-9107-19472484d57d",
  "sender": "human",
  "source": "WEB",
  "phoneNumber": "+919819304846",
  "timestamp": 1750855489497,
  "requestId": "618c036b-6702-48ba-adac-4cb002543e39",
  "attachments": []
}'

echo ""
echo "======================================"
echo "Key IDs to check in database:"
echo "dialogueId: e232c5f2-6357-4242-a103-a95352b397d9"
echo "providerMessageId: bcb28b02-a82b-4765-9107-19472484d57d"
echo "requestId: 618c036b-6702-48ba-adac-4cb002543e39"