{"timestamp": "2025-06-25T08:14:04.842Z", "baseUrl": "https://gatekeeper-staging.getbeyondhealth.com", "tenant": "practitioner-dashboard", "testPhone": "+************", "apis": {"Mock Verify OTP Response": {"note": "Expected response structure", "response": {"success": true, "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...", "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...", "expiresIn": 3600, "user": {"id": "user-uuid", "phone": "+************", "role": "DOCTOR", "access": true}}}, "All Message Types": {"Text Only Message": {"success": true, "request": {"dialogueId": "aeca0a5d-d279-450b-b0c7-e07a5dc8e986", "dialogueType": "patient-case", "text": "Patient presents with fever and headache", "providerMessageId": "83f109a6-330b-4c9d-9290-4aa8bc8b74fe", "sender": "human", "source": "WEB", "phoneNumber": "+************", "timestamp": 1750839245846, "requestId": "78c324e5-a0db-42bd-bcfd-c15e8b8ca428"}, "response": {"success": true, "tenantSlug": "practitioner-dashboard"}}, "Single Image Attachment": {"success": true, "request": {"dialogueId": "97c9509c-7b40-42e9-b6a7-3b74aca060dc", "dialogueType": "patient-case", "text": "X-ray image attached", "providerMessageId": "3d3d42eb-9902-4904-9f8f-b6445d3f8442", "attachment": "https://example.blob.core.windows.net/container/xray.png", "fileExtension": ".png", "messageType": "image", "sender": "human", "source": "WEB", "phoneNumber": "+************", "timestamp": 1750839245846, "requestId": "ce782370-4c4f-427c-8f36-a950a63e0c23"}, "response": {"success": true, "tenantSlug": "practitioner-dashboard"}}, "Single PDF Attachment": {"success": true, "request": {"dialogueId": "6855202d-f0c2-42a5-b0a0-32df80bedbfc", "dialogueType": "research", "text": "Research paper attached", "providerMessageId": "ed7eee36-6ffc-46db-a8c1-5171d7a907ae", "attachment": "https://example.blob.core.windows.net/container/paper.pdf", "fileExtension": ".pdf", "messageType": "pdf", "sender": "human", "source": "WEB", "phoneNumber": "+************", "timestamp": 1750839245846, "requestId": "a96b9c87-437b-4293-bb3f-f12d77b67a3b"}, "response": {"success": true, "tenantSlug": "practitioner-dashboard"}}, "Multiple Mixed Attachments": {"success": true, "request": {"dialogueId": "a0da6625-2b1f-4706-aa06-4bc276e31601", "dialogueType": "research", "text": "Multiple files attached", "providerMessageId": "eeb08df0-8cca-4c9d-876d-7da4978d82ee", "attachment": [{"url": "https://example.blob.core.windows.net/container/image1.png", "fileExtension": ".png", "messageType": "image"}, {"url": "https://example.blob.core.windows.net/container/doc1.pdf", "fileExtension": ".pdf", "messageType": "pdf"}, {"url": "https://example.blob.core.windows.net/container/image2.jpg", "fileExtension": ".jpg", "messageType": "image"}], "sender": "human", "source": "WEB", "phoneNumber": "+************", "timestamp": 1750839245846, "requestId": "ccd9fe61-bc2d-4034-9eb4-a66820ec61ab"}, "response": {"success": true, "tenantSlug": "practitioner-dashboard"}}}, "Error Cases": {"Missing Required Fields": {"success": true, "note": "Expected to fail but succeeded", "response": {"success": true, "tenantSlug": "practitioner-dashboard"}}, "Invalid Dialogue Type": {"success": true, "note": "Expected to fail but succeeded", "response": {"success": true, "tenantSlug": "practitioner-dashboard"}}, "Invalid Phone Format": {"success": true, "note": "Expected to fail but succeeded", "response": {"success": true, "tenantSlug": "practitioner-dashboard"}}}, "Mock GET Responses": {"Get All Dialogues": {"success": true, "dialogues": [{"id": "dialogue-uuid-1", "type": "patient-case", "lastMessage": {"text": "Patient case discussion...", "timestamp": "2025-06-25T08:00:00Z", "sender": "human"}, "createdAt": "2025-06-24T10:00:00Z", "updatedAt": "2025-06-25T08:00:00Z"}, {"id": "dialogue-uuid-2", "type": "research", "lastMessage": {"text": "Research query about...", "timestamp": "2025-06-24T15:00:00Z", "sender": "ai"}, "createdAt": "2025-06-24T14:00:00Z", "updatedAt": "2025-06-24T15:00:00Z"}], "total": 2, "limit": 100}, "Get Messages by Dialogue ID": {"success": true, "dialogueId": "dialogue-uuid-1", "messages": [{"id": "msg-1", "sender": "human", "text": "45 year old male with chest pain", "timestamp": "2025-06-25T08:00:00Z", "attachments": []}, {"id": "msg-2", "sender": "ai", "text": "I'll help you assess this patient...", "timestamp": "2025-06-25T08:00:30Z", "attachments": []}], "hasMore": false, "limit": 30}}}}