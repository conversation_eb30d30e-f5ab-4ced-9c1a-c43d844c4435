#!/usr/bin/env node

const redis = require('redis');

// Configuration
const REDIS_URL = "rediss://:AlhP5MXWfxMcS7V8jHe40GyWv2BLLKjlyAzCaPPORFQ=@rate-limit-cache.redis.cache.windows.net:6380";
const TEST_PHONE_NUMBER = "+919819304846";

// ANSI color codes
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

// Helper functions
const log = {
  info: (msg) => console.log(`${colors.blue}ℹ️  ${msg}${colors.reset}`),
  success: (msg) => console.log(`${colors.green}✅ ${msg}${colors.reset}`),
  warning: (msg) => console.log(`${colors.yellow}⚠️  ${msg}${colors.reset}`),
  error: (msg) => console.log(`${colors.red}❌ ${msg}${colors.reset}`),
  header: (msg) => {
    console.log(`\n${colors.cyan}${'='.repeat(60)}${colors.reset}`);
    console.log(`${colors.cyan}${msg}${colors.reset}`);
    console.log(`${colors.cyan}${'='.repeat(60)}${colors.reset}\n`);
  }
};

// Create Redis client
async function createRedisClient() {
  const client = redis.createClient({
    url: REDIS_URL,
    socket: {
      tls: true,
      rejectUnauthorized: false
    }
  });

  client.on('error', (err) => {
    log.error(`Redis Client Error: ${err.message}`);
  });

  await client.connect();
  return client;
}

// Check rate limit status for test phone
async function checkStatus() {
  let client;
  try {
    client = await createRedisClient();
    log.success('Connected to Redis');
    
    log.header(`Rate Limit Status for ${TEST_PHONE_NUMBER}`);
    
    // Common rate limit key patterns
    const patterns = [
      `*${TEST_PHONE_NUMBER}*`,
      'otp_ip:127.0.0.1*',
      'otp_ip:::1*',
      'otp_ip:localhost*'
    ];
    
    let foundKeys = [];
    
    for (const pattern of patterns) {
      const keys = await client.keys(pattern);
      foundKeys = [...foundKeys, ...keys];
    }
    
    // Remove duplicates
    foundKeys = [...new Set(foundKeys)];
    
    if (foundKeys.length === 0) {
      log.success('No rate limit keys found - Ready for testing!');
    } else {
      log.warning(`Found ${foundKeys.length} rate limit keys:`);
      
      for (const key of foundKeys) {
        const ttl = await client.ttl(key);
        const type = await client.type(key);
        
        console.log(`\n  📌 ${key}`);
        console.log(`     Type: ${type}`);
        console.log(`     TTL: ${ttl > 0 ? `${ttl}s (${Math.ceil(ttl/60)}m)` : 'No expiration'}`);
        
        try {
          if (type === 'string') {
            const value = await client.get(key);
            console.log(`     Value: ${value}`);
          }
        } catch (e) {
          // Ignore errors reading values
        }
      }
    }
    
  } catch (error) {
    log.error(`Failed to check status: ${error.message}`);
  } finally {
    if (client) await client.quit();
  }
}

// Clear rate limits for test phone only
async function clearTestPhone() {
  let client;
  try {
    client = await createRedisClient();
    log.success('Connected to Redis');
    
    log.header(`Clearing Rate Limits for ${TEST_PHONE_NUMBER}`);
    
    // Find all keys related to test phone
    const patterns = [
      `*${TEST_PHONE_NUMBER}*`,
      'otp_ip:127.0.0.1*',
      'otp_ip:::1*',
      'otp_ip:localhost*',
      'otp_ip:192.168.*'
    ];
    
    let keysToDelete = [];
    
    for (const pattern of patterns) {
      const keys = await client.keys(pattern);
      keysToDelete = [...keysToDelete, ...keys];
    }
    
    // Remove duplicates
    keysToDelete = [...new Set(keysToDelete)];
    
    if (keysToDelete.length === 0) {
      log.info('No rate limit keys found to clear');
      return;
    }
    
    log.warning(`Found ${keysToDelete.length} keys to delete`);
    
    let deletedCount = 0;
    for (const key of keysToDelete) {
      try {
        const result = await client.del(key);
        if (result > 0) {
          deletedCount++;
          console.log(`  ✅ Deleted: ${key}`);
        }
      } catch (err) {
        console.log(`  ❌ Failed to delete ${key}: ${err.message}`);
      }
    }
    
    log.success(`Deleted ${deletedCount} rate limit keys`);
    
  } catch (error) {
    log.error(`Failed to clear rate limits: ${error.message}`);
  } finally {
    if (client) await client.quit();
  }
}

// Monitor rate limits in real-time
async function monitor() {
  let client;
  try {
    client = await createRedisClient();
    log.success('Connected to Redis - Monitoring rate limits...');
    log.info('Press Ctrl+C to stop monitoring\n');
    
    const checkInterval = setInterval(async () => {
      const timestamp = new Date().toLocaleTimeString();
      console.log(`\n[${timestamp}] Checking rate limits...`);
      
      const patterns = [`*${TEST_PHONE_NUMBER}*`];
      let foundKeys = [];
      
      for (const pattern of patterns) {
        const keys = await client.keys(pattern);
        foundKeys = [...foundKeys, ...keys];
      }
      
      if (foundKeys.length === 0) {
        console.log('  ✅ No rate limits active');
      } else {
        console.log(`  ⚠️  ${foundKeys.length} rate limit keys active`);
        for (const key of foundKeys) {
          const ttl = await client.ttl(key);
          console.log(`     - ${key} (expires in ${ttl}s)`);
        }
      }
    }, 5000); // Check every 5 seconds
    
    // Handle graceful shutdown
    process.on('SIGINT', async () => {
      clearInterval(checkInterval);
      await client.quit();
      console.log('\n\n👋 Monitoring stopped');
      process.exit(0);
    });
    
  } catch (error) {
    log.error(`Failed to start monitoring: ${error.message}`);
    if (client) await client.quit();
  }
}

// CLI interface
async function main() {
  const args = process.argv.slice(2);
  const command = args[0];
  
  console.log(`\n${colors.magenta}🔧 Rate Limit Manager for Doctor Dashboard${colors.reset}`);
  console.log(`${colors.magenta}Test Phone: ${TEST_PHONE_NUMBER}${colors.reset}\n`);
  
  switch (command) {
    case 'status':
    case 'check':
      await checkStatus();
      break;
      
    case 'clear':
    case 'reset':
      await clearTestPhone();
      console.log('\nVerifying clearance...');
      await checkStatus();
      break;
      
    case 'monitor':
    case 'watch':
      await monitor();
      break;
      
    default:
      console.log('Usage: node rate-limit-manager.js <command>\n');
      console.log('Commands:');
      console.log('  status   - Check current rate limit status');
      console.log('  clear    - Clear rate limits for test phone');
      console.log('  monitor  - Monitor rate limits in real-time\n');
      console.log('Examples:');
      console.log('  node rate-limit-manager.js status');
      console.log('  node rate-limit-manager.js clear');
      console.log('  node rate-limit-manager.js monitor\n');
  }
}

// Run if called directly
if (require.main === module) {
  main().catch(err => {
    log.error(`Fatal error: ${err.message}`);
    process.exit(1);
  });
}

module.exports = {
  checkStatus,
  clearTestPhone,
  monitor,
  TEST_PHONE_NUMBER
};
