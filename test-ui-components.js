const puppeteer = require('puppeteer');

async function testUIComponents() {
  console.log('\n🏥 Doctor Dashboard UI Components Test\n');
  console.log('This test will verify UI components and interactions.\n');
  
  const browser = await puppeteer.launch({
    headless: false,
    slowMo: 50,
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });
  
  const results = {
    passed: [],
    failed: []
  };
  
  try {
    const page = await browser.newPage();
    await page.setViewport({ width: 1440, height: 900 });
    
    // Test 1: Login Page Components
    console.log('📱 Testing Login Page Components\n');
    
    await page.goto('http://localhost:3000', { waitUntil: 'networkidle0' });
    
    // Check title and branding
    const pageTitle = await page.title();
    console.log(`Page title: ${pageTitle}`);
    results.passed.push('Page loads successfully');
    
    // Check login form elements
    const elements = await page.evaluate(() => {
      return {
        logo: !!document.querySelector('img[alt*="logo"], svg, [class*="logo"]'),
        title: document.querySelector('h1, h2, h3')?.textContent || '',
        phoneInput: !!document.querySelector('input[placeholder*="phone"], input[type="tel"]'),
        submitButton: !!document.querySelector('button[type="submit"]'),
        tabButtons: Array.from(document.querySelectorAll('button')).filter(btn => 
          btn.textContent === 'Login' || btn.textContent === 'Register'
        ).length
      };
    });
    
    console.log('Login page elements:');
    console.log(`  Logo/Icon: ${elements.logo ? '✅' : '❌'}`);
    console.log(`  Title: ${elements.title || 'Not found'}`);
    console.log(`  Phone input: ${elements.phoneInput ? '✅' : '❌'}`);
    console.log(`  Submit button: ${elements.submitButton ? '✅' : '❌'}`);
    console.log(`  Login/Register tabs: ${elements.tabButtons}`);
    
    await page.screenshot({ path: 'test-screenshots/ui-1-login-page.png' });
    
    // Test 2: Form Validation
    console.log('\n🔍 Testing Form Validation\n');
    
    // Try to submit empty form
    const submitButton = await page.$('button[type="submit"]');
    const isDisabled = await submitButton.evaluate(btn => btn.disabled);
    console.log(`Empty form submit button disabled: ${isDisabled ? '✅' : '❌'}`);
    
    if (!isDisabled) {
      await submitButton.click();
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Check for validation error
      const hasError = await page.evaluate(() => {
        return !!document.querySelector('.error, [role="alert"], .MuiFormHelperText-root');
      });
      console.log(`Validation error shown: ${hasError ? '✅' : '❌'}`);
    }
    
    // Test with invalid phone number
    const phoneInput = await page.$('input[placeholder*="98193"]');
    await phoneInput.click({ clickCount: 3 });
    await phoneInput.type('123'); // Too short
    
    await page.screenshot({ path: 'test-screenshots/ui-2-invalid-phone.png' });
    
    // Test 3: UI Responsiveness
    console.log('\n📱 Testing UI Responsiveness\n');
    
    // Test different viewport sizes
    const viewports = [
      { name: 'Mobile', width: 375, height: 667 },
      { name: 'Tablet', width: 768, height: 1024 },
      { name: 'Desktop', width: 1920, height: 1080 }
    ];
    
    for (const viewport of viewports) {
      await page.setViewport({ width: viewport.width, height: viewport.height });
      await new Promise(resolve => setTimeout(resolve, 500));
      
      const isResponsive = await page.evaluate(() => {
        const form = document.querySelector('form');
        return form && form.offsetWidth <= window.innerWidth;
      });
      
      console.log(`${viewport.name} (${viewport.width}x${viewport.height}): ${isResponsive ? '✅' : '❌'}`);
      await page.screenshot({ 
        path: `test-screenshots/ui-responsive-${viewport.name.toLowerCase()}.png` 
      });
    }
    
    // Reset to desktop size
    await page.setViewport({ width: 1440, height: 900 });
    
    // Test 4: Theme and Styling
    console.log('\n🎨 Testing Theme and Styling\n');
    
    const styles = await page.evaluate(() => {
      const button = document.querySelector('button[type="submit"]');
      const input = document.querySelector('input');
      const body = document.body;
      
      return {
        primaryColor: button ? getComputedStyle(button).backgroundColor : null,
        fontFamily: getComputedStyle(body).fontFamily,
        hasRoundedCorners: input ? parseInt(getComputedStyle(input).borderRadius) > 0 : false,
        hasShadows: Array.from(document.querySelectorAll('*')).some(el => 
          getComputedStyle(el).boxShadow !== 'none'
        )
      };
    });
    
    console.log('Theme elements:');
    console.log(`  Primary color: ${styles.primaryColor || 'Not found'}`);
    console.log(`  Font family: ${styles.fontFamily}`);
    console.log(`  Rounded corners: ${styles.hasRoundedCorners ? '✅' : '❌'}`);
    console.log(`  Box shadows: ${styles.hasShadows ? '✅' : '❌'}`);
    
    // Test 5: Registration Mode
    console.log('\n📝 Testing Registration Mode\n');
    
    // Click Register tab if available
    const registerButton = await page.evaluateHandle(() => {
      return Array.from(document.querySelectorAll('button')).find(btn => 
        btn.textContent === 'Register'
      );
    });
    
    if (registerButton && await registerButton.evaluate(el => el !== null)) {
      await registerButton.click();
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Check for additional fields
      const registrationFields = await page.evaluate(() => {
        return {
          nameField: !!document.querySelector('input[placeholder*="name" i], input[name="name"]'),
          organizationField: !!document.querySelector('input[placeholder*="organization" i], input[placeholder*="hospital" i]'),
          licenseField: !!document.querySelector('input[placeholder*="license" i], input[name*="license"]'),
          specializationField: !!document.querySelector('input[placeholder*="specialization" i]')
        };
      });
      
      console.log('Registration fields:');
      Object.entries(registrationFields).forEach(([field, exists]) => {
        console.log(`  ${field}: ${exists ? '✅' : '❌'}`);
      });
      
      await page.screenshot({ path: 'test-screenshots/ui-3-registration.png' });
      
      // Switch back to login
      const loginButton = await page.evaluateHandle(() => {
        return Array.from(document.querySelectorAll('button')).find(btn => 
          btn.textContent === 'Login'
        );
      });
      
      if (loginButton && await loginButton.evaluate(el => el !== null)) {
        await loginButton.click();
      }
    }
    
    // Test 6: OTP Request Flow (UI only)
    console.log('\n🔐 Testing OTP Request UI\n');
    
    // Enter valid phone number
    await phoneInput.click({ clickCount: 3 });
    await phoneInput.type('+919819304846');
    
    // Check if submit button is enabled
    const submitEnabled = await submitButton.evaluate(btn => !btn.disabled);
    console.log(`Submit button enabled with valid phone: ${submitEnabled ? '✅' : '❌'}`);
    
    // Click submit (this will fail due to API issue, but we can check UI behavior)
    if (submitEnabled) {
      await submitButton.click();
      console.log('Clicked submit, checking UI response...');
      
      // Wait for loading state or error
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Check for loading indicator
      const hasLoader = await page.evaluate(() => {
        return !!document.querySelector('.MuiCircularProgress-root, [role="progressbar"], .loader');
      });
      
      // Check for error message
      const errorMessage = await page.evaluate(() => {
        const error = document.querySelector('.MuiAlert-root, .error, [role="alert"]');
        return error ? error.textContent : null;
      });
      
      console.log(`Loading indicator shown: ${hasLoader ? '✅' : '❌'}`);
      if (errorMessage) {
        console.log(`Error handling works: ✅`);
        console.log(`Error message: "${errorMessage}"`);
      }
      
      await page.screenshot({ path: 'test-screenshots/ui-4-error-state.png' });
    }
    
    // Summary
    console.log('\n📊 UI Test Summary\n');
    console.log('Components tested:');
    console.log('  ✅ Login page structure');
    console.log('  ✅ Form validation');
    console.log('  ✅ Responsive design');
    console.log('  ✅ Theme and styling');
    console.log('  ✅ Registration mode');
    console.log('  ✅ Error handling UI');
    
    console.log('\n📸 Screenshots saved in test-screenshots/');
    console.log('\n⚠️  Note: Full flow testing requires working OTP service.');
    
  } catch (error) {
    console.error('\n❌ Test error:', error.message);
    results.failed.push(error.message);
  } finally {
    console.log('\n✨ UI component testing complete!');
    await browser.close();
  }
}

// Run the test
testUIComponents();