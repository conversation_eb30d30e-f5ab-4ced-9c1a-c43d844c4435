#!/bin/bash

# Doctor Dashboard API Test Script
# Tests all authentication endpoints using cURL
# Make sure your server is running: npm run dev

API_BASE="http://localhost:3000/api"
TEST_PHONE="+919819304846"  # Real phone number for testing
ADMIN_PHONE="+919819304846"  # Same for admin (will be created during testing)

echo "🚀 Doctor Dashboard API Test Suite"
echo "=================================================="
echo "Testing all authentication endpoints"
echo "Note: Now using REAL Gatekeeper OTP service!"
echo "⚠️  You need a real phone number to receive OTP codes"
echo "=================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

test_count=0
pass_count=0

# Helper function to test API endpoint
test_api() {
    local name="$1"
    local method="$2"
    local endpoint="$3"
    local data="$4"
    local expected_status="$5"
    local auth_header="$6"
    
    test_count=$((test_count + 1))
    echo -e "\n${BLUE}📝 Test $test_count: $name${NC}"
    echo "-------------------------------------------"
    
    # Build curl command
    curl_cmd="curl -s -w \"\\nHTTP_STATUS:%{http_code}\" -X $method"
    
    if [ ! -z "$auth_header" ]; then
        curl_cmd="$curl_cmd -H \"Authorization: Bearer $auth_header\""
    fi
    
    if [ ! -z "$data" ]; then
        curl_cmd="$curl_cmd -H \"Content-Type: application/json\" -d '$data'"
    fi
    
    curl_cmd="$curl_cmd \"$API_BASE$endpoint\""
    
    echo "Command: $curl_cmd"
    echo ""
    
    # Execute curl command
    response=$(eval $curl_cmd)
    http_status=$(echo "$response" | grep "HTTP_STATUS:" | cut -d: -f2)
    json_response=$(echo "$response" | sed '/HTTP_STATUS:/d')
    
    echo "Response: $json_response"
    echo "Status: $http_status"
    
    # Check if status matches expected
    if [ "$http_status" = "$expected_status" ]; then
        echo -e "${GREEN}✅ PASS - Status $http_status as expected${NC}"
        pass_count=$((pass_count + 1))
    else
        echo -e "${RED}❌ FAIL - Expected status $expected_status, got $http_status${NC}"
    fi
}

echo -e "\n${YELLOW}Starting API Tests...${NC}"

# Test 1: Health Check
test_api "Health Check" "GET" "/health" "" "200"

# Test 2: Request OTP - Valid Phone
test_api "Request OTP (Valid Phone)" "POST" "/auth/request-otp" "{\"phone\":\"$TEST_PHONE\"}" "200"

# Test 3: Request OTP - Invalid Phone
test_api "Request OTP (Invalid Phone)" "POST" "/auth/request-otp" "{\"phone\":\"invalid\"}" "400"

# Test 4: Registration - Missing Fields
test_api "Registration (Missing Fields)" "POST" "/auth/verify-and-register" "{\"phone\":\"$TEST_PHONE\",\"otp\":\"123456\"}" "400"

# Test 5: Registration - Complete but with dummy OTP (will fail with real Gatekeeper)
test_api "Registration (Test OTP - Expected to Fail)" "POST" "/auth/verify-and-register" "{\"phone\":\"$TEST_PHONE\",\"otp\":\"123456\",\"name\":\"Dr. Test\",\"organization\":\"Test Hospital\",\"license_number\":\"MD123456\"}" "400"

# Test 6: Login - Non-existent User
test_api "Login (Non-existent User)" "POST" "/auth/login" "{\"phone\":\"+**********\"}" "404"

# Test 7: Login - Existing User (Admin)
test_api "Login (Admin User)" "POST" "/auth/login" "{\"phone\":\"$ADMIN_PHONE\"}" "200"

# Test 8: Login Verification - Test OTP (will fail with real Gatekeeper)
test_api "Login Verification (Test OTP - Expected to Fail)" "POST" "/auth/verify-login" "{\"phone\":\"$ADMIN_PHONE\",\"otp\":\"123456\"}" "400"

# Test 9: Protected Endpoint - No Token
test_api "Get User Info (No Token)" "GET" "/auth/me" "" "401"

# Test 10: Protected Endpoint - Invalid Token
test_api "Get User Info (Invalid Token)" "GET" "/auth/me" "" "401" "invalid-token"

# Test 11: Admin Endpoint - No Token
test_api "Admin Pending Registrations (No Token)" "GET" "/admin/pending-registrations" "" "401"

# Test 12: Admin Review - No Token
test_api "Admin Review (No Token)" "POST" "/admin/review-registration" "{\"user_id\":\"test\",\"action\":\"approve\"}" "401"

# Test 13: Refresh Token - Invalid Token
test_api "Refresh Token (Invalid)" "POST" "/auth/refresh" "{\"refresh_token\":\"invalid\"}" "401"

# Test 14: Logout - No Token
test_api "Logout (No Token)" "POST" "/auth/logout" "" "401"

# Test 15: Database Cleanup - No Token
test_api "Database Cleanup (No Token)" "POST" "/admin/cleanup" "" "401"

# Summary
echo -e "\n${'=':=:=:=:=:=:=:=:=:=:=:=:=:=:=:=:=:=:=:=:=:=:=:=:=:=:=:=:=:=:=:=:=:=:=:=:=:=:=:=:=:=:=:=:=:=:=:=:=:=}"
echo -e "${YELLOW}📊 TEST SUMMARY${NC}"
echo "=================================================="
echo -e "Total Tests: $test_count"
echo -e "${GREEN}Passed: $pass_count${NC}"
echo -e "${RED}Failed: $((test_count - pass_count))${NC}"

if [ $pass_count -eq $test_count ]; then
    echo -e "\n${GREEN}🎉 All tests passed! Your API is working correctly.${NC}"
else
    echo -e "\n${YELLOW}⚠️  Some tests failed. This is expected for OTP-related endpoints.${NC}"
fi

echo -e "\n${BLUE}📝 Notes:${NC}"
echo "- Now using REAL Gatekeeper OTP service!"
echo "- Test OTP codes (123456) will fail - this is correct behavior"
echo "- To test complete flow: use real phone number and check SMS for OTP"
echo "- Protected endpoints correctly reject unauthorized requests"

echo -e "\n${BLUE}🔧 Testing with Real OTP:${NC}"
echo "1. Use your real phone number in the test variables"
echo "2. Check your SMS for the actual OTP code"
echo "3. Manually test endpoints with real OTP codes"
echo "4. Example: curl -X POST http://localhost:3000/api/auth/verify-and-register \\"
echo "   -H 'Content-Type: application/json' \\"
echo "   -d '{\"phone\":\"+your-real-number\",\"otp\":\"REAL_OTP_FROM_SMS\",...}'"

echo -e "\n${GREEN}✅ API testing completed!${NC}"