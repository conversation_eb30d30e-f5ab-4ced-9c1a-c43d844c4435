import os
import gc
import time
import hashlib
from datetime import datetime
from typing import List, Dict, Any, Optional
from fastapi import FastAP<PERSON>, HTTPException, status
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field
import uvicorn
from sentence_transformers import SentenceTransformer
import torch
import numpy as np
from functools import lru_cache
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Pydantic models
class EmbeddingRequest(BaseModel):
    text: str = Field(..., description="Text to embed")
    normalize: bool = Field(True, description="Whether to normalize embeddings")

class BatchEmbeddingRequest(BaseModel):
    texts: List[str] = Field(..., description="List of texts to embed")
    normalize: bool = Field(True, description="Whether to normalize embeddings")
    batch_size: int = Field(32, description="Batch size for processing")

class EmbeddingResponse(BaseModel):
    embedding: List[float] = Field(..., description="Text embedding")
    dimension: int = Field(..., description="Embedding dimension")
    processing_time: float = Field(..., description="Processing time in seconds")

class BatchEmbeddingResponse(BaseModel):
    embeddings: List[List[float]] = Field(..., description="List of embeddings")
    dimension: int = Field(..., description="Embedding dimension")
    processing_time: float = Field(..., description="Processing time in seconds")
    batch_count: int = Field(..., description="Number of texts processed")

class HealthResponse(BaseModel):
    status: str = Field(..., description="Service status")
    model_loaded: bool = Field(..., description="Whether model is loaded")
    memory_usage: Dict[str, Any] = Field(..., description="Memory usage info")
    timestamp: str = Field(..., description="Response timestamp")

# Configuration
class Config:
    EMBEDDING_MODEL = os.getenv("EMBEDDING_MODEL", "BAAI/bge-m3")
    LOCAL_MODEL_PATH = os.getenv("LOCAL_MODEL_PATH", "./models/embedding_model")
    USE_LOCAL_MODEL = os.getenv("USE_LOCAL_MODEL", "true").lower() == "true"
    USE_FP16 = os.getenv("USE_FP16", "true").lower() == "true"
    CACHE_SIZE = int(os.getenv("CACHE_SIZE", "1000"))
    MAX_BATCH_SIZE = int(os.getenv("MAX_BATCH_SIZE", "64"))
    MAX_TEXT_LENGTH = int(os.getenv("MAX_TEXT_LENGTH", "8192"))
    ENABLE_MEMORY_CLEANUP = os.getenv("ENABLE_MEMORY_CLEANUP", "true").lower() == "true"

config = Config()

# Global model instance
model = None
model_dimension = None

def get_memory_usage():
    """Get current memory usage statistics"""
    try:
        import psutil
        process = psutil.Process(os.getpid())
        memory_info = process.memory_info()
        return {
            "rss_mb": round(memory_info.rss / 1024 / 1024, 2),
            "vms_mb": round(memory_info.vms / 1024 / 1024, 2),
            "percent": round(process.memory_percent(), 2)
        }
    except ImportError:
        return {"error": "psutil not available"}

def cleanup_memory():
    """Force garbage collection and clear caches"""
    if config.ENABLE_MEMORY_CLEANUP:
        gc.collect()
        if torch.cuda.is_available():
            torch.cuda.empty_cache()

def text_to_hash(text: str) -> str:
    """Generate hash for text caching"""
    return hashlib.md5(text.encode()).hexdigest()

@lru_cache(maxsize=config.CACHE_SIZE)
def cached_encode(text_hash: str, text: str, normalize: bool) -> tuple:
    """Cached embedding generation"""
    global model
    if model is None:
        raise RuntimeError("Model not loaded")
    
    # Truncate text if too long
    if len(text) > config.MAX_TEXT_LENGTH:
        text = text[:config.MAX_TEXT_LENGTH]
    
    try:
        # Generate embedding
        embedding = model.encode(
            text,
            normalize_embeddings=normalize,
            convert_to_numpy=True,
            batch_size=1,
            show_progress_bar=False
        )
        
        # Convert to list for JSON serialization
        embedding_list = embedding.tolist()
        return embedding_list, len(embedding_list)
        
    except Exception as e:
        logger.error(f"Error generating embedding: {e}")
        raise e

def load_model():
    """Load the embedding model with optimizations"""
    global model, model_dimension
    
    # Determine model path
    if config.USE_LOCAL_MODEL and os.path.exists(config.LOCAL_MODEL_PATH):
        model_path = config.LOCAL_MODEL_PATH
        logger.info(f"Loading local embedding model from: {model_path}")
    else:
        model_path = config.EMBEDDING_MODEL
        logger.info(f"Loading embedding model from HuggingFace: {model_path}")
    
    try:
        # Load model with optimizations
        model = SentenceTransformer(model_path)
        
        # Optimize for inference
        model.eval()
        
        # Use FP16 if enabled and supported
        if config.USE_FP16 and torch.cuda.is_available():
            model = model.half()
            logger.info("Using FP16 precision")
        
        # Set number of threads for CPU inference
        torch.set_num_threads(2)  # Optimized for t3.medium
        
        # Get model dimension
        test_embedding = model.encode("test", convert_to_numpy=True)
        model_dimension = len(test_embedding)
        
        logger.info(f"Model loaded successfully. Dimension: {model_dimension}")
        logger.info(f"Memory usage after loading: {get_memory_usage()}")
        
    except Exception as e:
        logger.error(f"Failed to load model: {e}")
        raise e

# Create FastAPI app
app = FastAPI(
    title="Embedding Service",
    description="Microservice for text embedding generation",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["GET", "POST"],
    allow_headers=["*"],
)

@app.on_event("startup")
async def startup_event():
    """Initialize model on startup"""
    load_model()

@app.get("/health", response_model=HealthResponse)
async def health_check():
    """Health check endpoint"""
    return HealthResponse(
        status="healthy" if model is not None else "unhealthy",
        model_loaded=model is not None,
        memory_usage=get_memory_usage(),
        timestamp=datetime.utcnow().isoformat()
    )

@app.post("/embed/single", response_model=EmbeddingResponse)
async def embed_single(request: EmbeddingRequest):
    """Generate embedding for a single text"""
    if model is None:
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Model not loaded"
        )
    
    if not request.text or not request.text.strip():
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Text cannot be empty"
        )
    
    start_time = time.time()
    
    try:
        # Use cached embedding generation
        text_hash = text_to_hash(request.text)
        embedding, dimension = cached_encode(text_hash, request.text, request.normalize)
        
        processing_time = time.time() - start_time
        
        # Cleanup memory periodically
        if config.ENABLE_MEMORY_CLEANUP and time.time() % 10 < 1:
            cleanup_memory()
        
        return EmbeddingResponse(
            embedding=embedding,
            dimension=dimension,
            processing_time=processing_time
        )
        
    except Exception as e:
        logger.error(f"Error in single embedding: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Embedding generation failed: {str(e)}"
        )

@app.post("/embed/batch", response_model=BatchEmbeddingResponse)
async def embed_batch(request: BatchEmbeddingRequest):
    """Generate embeddings for multiple texts"""
    if model is None:
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Model not loaded"
        )
    
    if not request.texts:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Texts list cannot be empty"
        )
    
    if len(request.texts) > config.MAX_BATCH_SIZE:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Batch size too large. Maximum: {config.MAX_BATCH_SIZE}"
        )
    
    start_time = time.time()
    
    try:
        # Filter empty texts
        valid_texts = [text for text in request.texts if text and text.strip()]
        
        if not valid_texts:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="No valid texts provided"
            )
        
        # Truncate texts if too long
        truncated_texts = []
        for text in valid_texts:
            if len(text) > config.MAX_TEXT_LENGTH:
                truncated_texts.append(text[:config.MAX_TEXT_LENGTH])
            else:
                truncated_texts.append(text)
        
        # Generate embeddings in batches
        embeddings = []
        batch_size = min(request.batch_size, config.MAX_BATCH_SIZE)
        
        for i in range(0, len(truncated_texts), batch_size):
            batch_texts = truncated_texts[i:i + batch_size]
            
            batch_embeddings = model.encode(
                batch_texts,
                normalize_embeddings=request.normalize,
                convert_to_numpy=True,
                batch_size=batch_size,
                show_progress_bar=False
            )
            
            # Convert to list for JSON serialization
            if len(batch_embeddings.shape) == 1:
                # Single embedding
                embeddings.append(batch_embeddings.tolist())
            else:
                # Multiple embeddings
                embeddings.extend(batch_embeddings.tolist())
        
        processing_time = time.time() - start_time
        
        # Cleanup memory after batch processing
        if config.ENABLE_MEMORY_CLEANUP:
            cleanup_memory()
        
        return BatchEmbeddingResponse(
            embeddings=embeddings,
            dimension=model_dimension,
            processing_time=processing_time,
            batch_count=len(embeddings)
        )
        
    except Exception as e:
        logger.error(f"Error in batch embedding: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Batch embedding generation failed: {str(e)}"
        )

@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "service": "Embedding Service",
        "version": "1.0.0",
        "model": config.EMBEDDING_MODEL,
        "status": "running" if model is not None else "loading",
        "endpoints": {
            "health": "/health",
            "single_embedding": "/embed/single",
            "batch_embedding": "/embed/batch",
            "docs": "/docs"
        }
    }

if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8001,
        reload=False,
        log_level="info"
    )
