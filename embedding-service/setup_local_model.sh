#!/bin/bash
# Setup script for embedding service local model

set -e

echo "Setting up local embedding model..."

# Create virtual environment if it doesn't exist
if [ ! -d "venv" ]; then
    echo "Creating virtual environment..."
    python3 -m venv venv
fi

# Activate virtual environment
source venv/bin/activate

# Install requirements
echo "Installing requirements..."
pip install -r requirements.txt

# Download model
echo "Downloading embedding model..."
python download_model.py

echo "Local model setup complete!"
echo "Model saved to: ./models/embedding_model"
echo "To use local model, set USE_LOCAL_MODEL=true in environment"
