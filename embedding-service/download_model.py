#!/usr/bin/env python3
"""
Script to download the embedding model locally
"""
import os
import sys
from sentence_transformers import SentenceTransformer

def download_model():
    """Download the embedding model to local directory"""
    model_name = os.getenv("EMBEDDING_MODEL", "BAAI/bge-m3")
    local_model_path = "./models/embedding_model"
    
    print(f"Downloading model: {model_name}")
    print(f"Local path: {local_model_path}")
    
    # Create models directory if it doesn't exist
    os.makedirs(os.path.dirname(local_model_path), exist_ok=True)
    
    try:
        # Download and save model
        model = SentenceTransformer(model_name)
        model.save(local_model_path)
        print(f"Model successfully downloaded to {local_model_path}")
        
        # Test the model
        test_embedding = model.encode("test text")
        print(f"Model test successful. Embedding dimension: {len(test_embedding)}")
        
    except Exception as e:
        print(f"Error downloading model: {e}")
        sys.exit(1)

if __name__ == "__main__":
    download_model()
