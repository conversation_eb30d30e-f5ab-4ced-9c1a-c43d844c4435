#!/usr/bin/env node

const axios = require('axios');

async function testEmitMessage() {
  console.log('🧪 Testing /api/emit-message endpoint directly');
  console.log('==========================================\n');

  try {
    // Test the endpoint with a sample Gatekeeper response
    const testPayload = {
      userId: "898a9e7b-6873-4c4c-b21c-f4786ee281ad",
      messageText: "Hello, this is a test response from the AI assistant.",
      phone: "+919819304846"
    };

    console.log('📤 Sending test payload:', JSON.stringify(testPayload, null, 2));

    const response = await axios.post('http://localhost:3000/api/emit-message', testPayload, {
      headers: {
        'Content-Type': 'application/json'
      }
    });

    console.log('\n✅ Response status:', response.status);
    console.log('📥 Response data:', JSON.stringify(response.data, null, 2));

  } catch (error) {
    console.error('\n❌ Error:', error.response ? error.response.status : error.message);
    if (error.response) {
      console.error('Response data:', error.response.data);
      console.error('Response headers:', error.response.headers);
    }
  }

  // Also test if GET works
  console.log('\n\n🧪 Testing GET /api/emit-message');
  console.log('================================\n');

  try {
    const getResponse = await axios.get('http://localhost:3000/api/emit-message');
    console.log('✅ GET Response:', getResponse.data);
  } catch (error) {
    console.error('❌ GET Error:', error.response ? error.response.status : error.message);
  }
}

testEmitMessage();