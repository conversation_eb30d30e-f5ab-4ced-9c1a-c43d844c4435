#!/usr/bin/env node

/**
 * Real OTP Testing Script for Doctor <PERSON>board
 * 
 * This script tests the complete authentication flow with REAL Gatekeeper OTP service.
 * SMS will be sent to +918417048371 for verification.
 * 
 * Usage: node test-real-otp.js
 */

const API_BASE = 'http://localhost:3000/api';
const TEST_PHONE = '+918417048371'; // Real phone number for testing

// Test doctor data
const TEST_DOCTOR = {
  phone: TEST_PHONE,
  name: 'Dr. <PERSON>',
  organization: 'General Hospital', 
  license_number: 'MD123456789',
  specialization: 'Internal Medicine'
};

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m', 
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

// Helper functions
function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logStep(step, message) {
  log(`\n📝 Step ${step}: ${message}`, 'blue');
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

function logInfo(message) {
  log(`ℹ️  ${message}`, 'cyan');
}

// HTTP request helper
async function makeRequest(method, endpoint, data = null, headers = {}) {
  const url = `${API_BASE}${endpoint}`;
  const options = {
    method,
    headers: {
      'Content-Type': 'application/json',
      ...headers
    }
  };

  if (data) {
    options.body = JSON.stringify(data);
  }

  try {
    const response = await fetch(url, options);
    const result = await response.json();
    
    return {
      status: response.status,
      success: response.ok,
      data: result
    };
  } catch (error) {
    return {
      success: false,
      error: error.message
    };
  }
}

// Prompt user for input
function promptUser(question) {
  return new Promise((resolve) => {
    const readline = require('readline');
    const rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    });
    
    rl.question(question, (answer) => {
      rl.close();
      resolve(answer.trim());
    });
  });
}

// Wait function
function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

// Test functions
async function testHealthCheck() {
  logStep(1, 'Testing Health Check');
  
  const response = await makeRequest('GET', '/health');
  
  if (response.success && response.data.success) {
    logSuccess('Health check passed');
    logInfo(`Database connected: ${response.data.database?.connected}`);
    return true;
  } else {
    logError('Health check failed');
    console.log(JSON.stringify(response, null, 2));
    return false;
  }
}

async function testRequestOTP() {
  logStep(2, `Requesting OTP for ${TEST_PHONE}`);
  logWarning('This will send a REAL SMS to the phone number!');
  
  const proceed = await promptUser('Continue with OTP request? (y/n): ');
  if (proceed.toLowerCase() !== 'y') {
    logWarning('OTP request skipped by user');
    return false;
  }
  
  const response = await makeRequest('POST', '/auth/request-otp', {
    phone: TEST_PHONE
  });
  
  if (response.success) {
    logSuccess('OTP request successful');
    logInfo('SMS should be sent to +918417048371');
    console.log('Response:', JSON.stringify(response.data, null, 2));
    return true;
  } else {
    logError('OTP request failed');
    console.log('Error:', JSON.stringify(response, null, 2));
    return false;
  }
}

async function testRegistrationFlow() {
  logStep(3, 'Testing Doctor Registration Flow');
  
  // Get OTP from user
  log('\n🔑 Please check your SMS for the OTP code');
  const otp = await promptUser('Enter the OTP you received: ');
  
  if (!otp || otp.length !== 6) {
    logError('Invalid OTP format. Please enter a 6-digit code.');
    return false;
  }
  
  logInfo(`Testing registration with OTP: ${otp}`);
  
  const response = await makeRequest('POST', '/auth/verify-and-register', {
    phone: TEST_PHONE,
    otp: otp,
    name: TEST_DOCTOR.name,
    organization: TEST_DOCTOR.organization,
    license_number: TEST_DOCTOR.license_number,
    specialization: TEST_DOCTOR.specialization
  });
  
  if (response.success) {
    logSuccess('Registration successful!');
    logInfo('Doctor account created and pending approval');
    console.log('Response:', JSON.stringify(response.data, null, 2));
    return { success: true, userId: response.data.user_id };
  } else {
    logError('Registration failed');
    console.log('Error:', JSON.stringify(response, null, 2));
    
    if (response.data?.code === 'OTP_INVALID') {
      logWarning('OTP was invalid or expired. You may need to request a new OTP.');
    } else if (response.data?.code === 'PHONE_EXISTS') {
      logWarning('Phone number already registered. Continuing with login test...');
      return { success: true, phoneExists: true };
    }
    
    return { success: false };
  }
}

async function testLoginFlow() {
  logStep(4, 'Testing Login Flow');
  
  // Request login OTP
  logInfo('Requesting login OTP...');
  const otpResponse = await makeRequest('POST', '/auth/login', {
    phone: TEST_PHONE
  });
  
  if (!otpResponse.success) {
    if (otpResponse.status === 404) {
      logWarning('User not found. Need to complete registration first.');
      return false;
    } else if (otpResponse.status === 403) {
      logWarning('Account not approved yet. Admin approval required.');
      console.log('Response:', JSON.stringify(otpResponse.data, null, 2));
      return false;
    } else {
      logError('Login OTP request failed');
      console.log('Error:', JSON.stringify(otpResponse, null, 2));
      return false;
    }
  }
  
  logSuccess('Login OTP sent');
  
  // Get OTP for login
  const otp = await promptUser('Enter the login OTP you received: ');
  
  if (!otp || otp.length !== 6) {
    logError('Invalid OTP format');
    return false;
  }
  
  // Verify login OTP
  const loginResponse = await makeRequest('POST', '/auth/verify-login', {
    phone: TEST_PHONE,
    otp: otp
  });
  
  if (loginResponse.success) {
    logSuccess('Login successful!');
    console.log('Tokens received:', {
      access_token: loginResponse.data.access_token ? 'Present' : 'Missing',
      refresh_token: loginResponse.data.refresh_token ? 'Present' : 'Missing',
      expires_in: loginResponse.data.expires_in,
      user: loginResponse.data.user
    });
    return {
      success: true,
      accessToken: loginResponse.data.access_token,
      refreshToken: loginResponse.data.refresh_token,
      user: loginResponse.data.user
    };
  } else {
    logError('Login verification failed');
    console.log('Error:', JSON.stringify(loginResponse, null, 2));
    return { success: false };
  }
}

async function testProtectedEndpoint(accessToken) {
  logStep(5, 'Testing Protected Endpoint');
  
  if (!accessToken) {
    logWarning('No access token available, skipping protected endpoint test');
    return false;
  }
  
  const response = await makeRequest('GET', '/auth/me', null, {
    'Authorization': `Bearer ${accessToken}`
  });
  
  if (response.success) {
    logSuccess('Protected endpoint access successful');
    console.log('User info:', JSON.stringify(response.data, null, 2));
    return true;
  } else {
    logError('Protected endpoint access failed');
    console.log('Error:', JSON.stringify(response, null, 2));
    return false;
  }
}

async function testRefreshToken(refreshToken, phone) {
  logStep(6, 'Testing Token Refresh');
  
  if (!refreshToken) {
    logWarning('No refresh token available, skipping refresh test');
    return false;
  }
  
  const response = await makeRequest('POST', '/auth/refresh', {
    refresh_token: refreshToken,
    phone: phone
  });
  
  if (response.success) {
    logSuccess('Token refresh successful');
    console.log('New access token received:', {
      access_token: response.data.access_token ? 'Present' : 'Missing',
      expires_in: response.data.expires_in
    });
    return { success: true, newAccessToken: response.data.access_token };
  } else {
    logError('Token refresh failed');
    console.log('Error:', JSON.stringify(response, null, 2));
    return { success: false };
  }
}

// Main test runner
async function runCompleteTest() {
  log('🚀 Doctor Dashboard Real OTP Testing', 'cyan');
  log('=' .repeat(50), 'cyan');
  log(`📱 Testing with phone number: ${TEST_PHONE}`, 'yellow');
  log('🔔 Real SMS messages will be sent!', 'yellow');
  log('=' .repeat(50), 'cyan');
  
  let testResults = {
    healthCheck: false,
    otpRequest: false,
    registration: false,
    login: false,
    protectedEndpoint: false,
    tokenRefresh: false
  };
  
  let tokens = {};
  
  try {
    // Health check
    testResults.healthCheck = await testHealthCheck();
    if (!testResults.healthCheck) {
      logError('Health check failed. Please make sure the server is running.');
      return;
    }
    
    await sleep(1000);
    
    // Request OTP
    testResults.otpRequest = await testRequestOTP();
    if (!testResults.otpRequest) {
      logError('Cannot proceed without OTP request capability');
      return;
    }
    
    await sleep(2000);
    
    // Registration flow
    const registrationResult = await testRegistrationFlow();
    testResults.registration = registrationResult.success;
    
    if (registrationResult.phoneExists) {
      logInfo('Phone already registered, proceeding to login test');
    }
    
    await sleep(1000);
    
    // Login flow
    const loginResult = await testLoginFlow();
    testResults.login = loginResult.success;
    
    if (loginResult.success) {
      tokens.accessToken = loginResult.accessToken;
      tokens.refreshToken = loginResult.refreshToken;
    }
    
    await sleep(1000);
    
    // Protected endpoint
    testResults.protectedEndpoint = await testProtectedEndpoint(tokens.accessToken);
    
    await sleep(1000);
    
    // Token refresh
    const refreshResult = await testRefreshToken(tokens.refreshToken, TEST_PHONE);
    testResults.tokenRefresh = refreshResult.success;
    
  } catch (error) {
    logError(`Test execution error: ${error.message}`);
  }
  
  // Summary
  log('\n' + '=' .repeat(50), 'cyan');
  log('📊 TEST RESULTS SUMMARY', 'cyan');
  log('=' .repeat(50), 'cyan');
  
  const tests = [
    { name: 'Health Check', result: testResults.healthCheck },
    { name: 'OTP Request', result: testResults.otpRequest },
    { name: 'Registration', result: testResults.registration },
    { name: 'Login', result: testResults.login },
    { name: 'Protected Endpoint', result: testResults.protectedEndpoint },
    { name: 'Token Refresh', result: testResults.tokenRefresh }
  ];
  
  let passedCount = 0;
  tests.forEach(test => {
    const status = test.result ? '✅ PASS' : '❌ FAIL';
    const color = test.result ? 'green' : 'red';
    log(`${status} - ${test.name}`, color);
    if (test.result) passedCount++;
  });
  
  log(`\n📈 Success Rate: ${passedCount}/${tests.length} (${Math.round(passedCount/tests.length*100)}%)`, 'cyan');
  
  if (passedCount === tests.length) {
    log('\n🎉 All tests passed! Gatekeeper integration is working perfectly!', 'green');
  } else {
    log('\n⚠️  Some tests failed. Check the output above for details.', 'yellow');
  }
  
  log('\n📝 Notes:', 'blue');
  log('- SMS messages were sent to +918417048371', 'reset');
  log('- External tokens are stored locally for reference', 'reset');
  log('- The system uses both Gatekeeper and local authentication', 'reset');
}

// Check if fetch is available (for Node.js compatibility)
async function checkEnvironment() {
  if (typeof fetch === 'undefined') {
    try {
      const { default: fetch } = await import('node-fetch');
      global.fetch = fetch;
      logInfo('Using node-fetch for HTTP requests');
    } catch (error) {
      logError('fetch is not available. Please install node-fetch:');
      log('npm install node-fetch', 'yellow');
      process.exit(1);
    }
  }
}

// Run the tests
async function main() {
  await checkEnvironment();
  await runCompleteTest();
}

// Execute if run directly
if (require.main === module) {
  main().catch(console.error);
}

module.exports = { runCompleteTest };