# E2E Test Suite Fix Documentation

## Overview
This document details the fixes applied to the Doctor Dashboard E2E test suite to resolve failures and compatibility issues.

## Issues Fixed

### 1. Selector Issues
**Problem**: The test suite used `:has-text()` pseudo-selector which is not supported by standard querySelector.
```javascript
// BEFORE - Invalid
button:has-text("New")

// AFTER - Valid
await this.page.evaluate(() => {
  const buttons = Array.from(document.querySelectorAll('button'));
  const newButton = buttons.find(btn => 
    btn.textContent.toLowerCase().includes('new')
  );
  if (newButton) newButton.click();
});
```

### 2. Element Reference Issues
**Problem**: `evaluateHandle()` returns JSHandle objects that don't have direct DOM methods like `.click()`.
```javascript
// BEFORE - Error: .click is not a function
const element = await this.page.evaluateHandle(() => {...});
await element.click();

// AFTER - Correct
const element = await this.page.evaluateHandle(() => {...});
await element.asElement().click();

// OR use evaluate() instead for direct DOM manipulation
await this.page.evaluate(() => {
  const element = document.querySelector('...');
  if (element) element.click();
});
```

### 3. waitForTimeout Deprecation
**Problem**: `page.waitForTimeout()` is deprecated in newer Puppeteer versions.
```javascript
// BEFORE
await this.page.waitForTimeout(1000);

// AFTER
await new Promise(resolve => setTimeout(resolve, 1000));
```

### 4. Route Updates
**Problem**: Tests were trying to navigate to `/dashboard` which doesn't exist.
```javascript
// BEFORE
await this.page.goto(CONFIG.baseUrl + '/dashboard');

// AFTER
await this.page.goto(CONFIG.baseUrl + '/chat');
```

### 5. Screenshot Naming
**Problem**: Screenshot names with hyphens were being interpreted as URLs, causing false 404 errors.
```javascript
// BEFORE
`${testName}-${count}-${name}.png`  // Could look like: dashboard-navigation-1-dashboard-main.png

// AFTER
`${testName}_${count}_${sanitizedName}.png`  // Now: dashboard_navigation_1_dashboard_main.png
```

## Test Suite Structure

### Main Files
- **`e2e-test-suite.js`** - Base test framework with TestSuite class
- **`run-all-tests.js`** - Test orchestrator that runs all suites
- **`run-e2e-tests.sh`** - Shell script wrapper for npm script

### Test Suites
1. **Authentication** - Login and OTP verification
2. **Dashboard Navigation** - Layout and navigation tests
3. **Messaging** - Basic message input tests
4. **Patient Cases** - Patient case creation and chat
5. **Research** - Research queries and citations
6. **Quick Facts** - Medical calculations and drug info
7. **Error Handling** - Validation and error scenarios

## Running Tests

### Basic Usage
```bash
# Run all tests
npm run test:e2e

# Run specific suite
npm run test:e2e -- -s authentication

# Run with visible browser
npm run test:e2e -- -H

# Run with OTP
npm run test:e2e -- -o 123456
```

### Environment Variables
```bash
TEST_PHONE="+919819304846"      # Test phone number
TEST_BASE_URL="http://localhost:3000"  # Base URL
TEST_HEADLESS="false"            # Show browser
TEST_OTP="123456"                # Auto-fill OTP
```

## Common Patterns

### 1. Element Selection and Interaction
```javascript
// For simple selectors
const button = await this.page.$('button[type="submit"]');
if (button) await button.click();

// For text-based selection
await this.page.evaluate(() => {
  const elements = Array.from(document.querySelectorAll('button'));
  const targetElement = elements.find(el => 
    el.textContent.includes('Target Text')
  );
  if (targetElement) targetElement.click();
});
```

### 2. Waiting for Elements
```javascript
// Wait for selector
await this.page.waitForSelector('input[maxlength="6"]', { timeout: 10000 });

// Wait for function
await this.page.waitForFunction(() => {
  const element = document.querySelector('.message');
  return element && element.textContent.includes('Expected text');
});

// Simple delay
await new Promise(resolve => setTimeout(resolve, 2000));
```

### 3. Error Handling
```javascript
try {
  await this.page.waitForResponse(res => res.url().includes('/api/'), { timeout: 5000 });
} catch (error) {
  this.logger.warn('No API response detected');
}
```

## Debugging Tips

1. **Run tests in headed mode** to see what's happening:
   ```bash
   npm run test:e2e -- -H
   ```

2. **Check screenshots** in `test-results/screenshots/`

3. **Review logs** in `test-results/logs/`

4. **Add debug logging**:
   ```javascript
   this.logger.debug('Current URL:', this.page.url());
   this.logger.debug('Page content:', await this.page.content());
   ```

5. **Use page.evaluate() for debugging**:
   ```javascript
   const debug = await this.page.evaluate(() => {
     return {
       title: document.title,
       buttons: Array.from(document.querySelectorAll('button')).map(b => b.textContent),
       inputs: Array.from(document.querySelectorAll('input')).map(i => i.placeholder)
     };
   });
   console.log('Page state:', debug);
   ```

## Future Improvements

1. **Add retry logic** for flaky tests
2. **Implement page object model** for better maintainability
3. **Add visual regression testing** with screenshot comparison
4. **Create data-testid attributes** in the app for more stable selectors
5. **Add performance metrics** collection
6. **Implement parallel test execution** for faster runs

## Notes for Future Sessions

- Always use `+919819304846` as the test phone number
- The app uses `/` for landing page and `/chat` for main dashboard
- OTP can be provided via TEST_OTP environment variable
- Session is stored in localStorage with key 'authToken'
- The app is a Next.js application with Material-UI components