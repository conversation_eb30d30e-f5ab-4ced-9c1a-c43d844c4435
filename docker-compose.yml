version: '3.8'

services:
  # Embedding Service
  embedding-service:
    build:
      context: ./embedding-service
      dockerfile: Dockerfile
    container_name: embedding-service
    ports:
      - "8001:8001"
    environment:
      - EMBEDDING_MODEL=BAAI/bge-m3
      - USE_LOCAL_MODEL=true
      - LOCAL_MODEL_PATH=/app/models/embedding_model
      - USE_FP16=true
      - CACHE_SIZE=1000
      - MAX_BATCH_SIZE=64
      - MAX_TEXT_LENGTH=8192
      - ENABLE_MEMORY_CLEANUP=true
    volumes:
      - ./embedding-service/models:/app/models
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8001/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 2G
        reservations:
          memory: 1.5G

  # Cross-Encoder Service
  cross-encoder-service:
    build:
      context: ./cross-encoder-service
      dockerfile: Dockerfile
    container_name: cross-encoder-service
    ports:
      - "8002:8002"
    environment:
      - CROSS_ENCODER_MODEL=cross-encoder/ms-marco-MiniLM-L-12-v2
      - USE_LOCAL_MODEL=true
      - LOCAL_MODEL_PATH=/app/models/cross_encoder_model
      - USE_FP16=true
      - CACHE_SIZE=500
      - MAX_BATCH_SIZE=32
      - MAX_TEXT_LENGTH=512
      - ENABLE_MEMORY_CLEANUP=true
    volumes:
      - ./cross-encoder-service/models:/app/models
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8002/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 1G
        reservations:
          memory: 800M

  # Main Research Service
  research-service:
    build:
      context: ./python-research-service
      dockerfile: Dockerfile
    container_name: research-service
    ports:
      - "8000:8000"
    environment:
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - VECTORX_API_TOKEN=${VECTORX_API_TOKEN}
      - VECTORX_ENCRYPTION_KEY=${VECTORX_ENCRYPTION_KEY}
      - VECTORX_INDEX_NAME=${VECTORX_INDEX_NAME:-pubmed_collection_v1}
      - LLM_MODEL_VERSION=${LLM_MODEL_VERSION:-gpt-4o-mini}
      - EMBEDDING_SERVICE_URL=http://embedding-service:8001
      - CROSS_ENCODER_SERVICE_URL=http://cross-encoder-service:8002
    depends_on:
      embedding-service:
        condition: service_healthy
      cross-encoder-service:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 300M

networks:
  default:
    name: research-services
    driver: bridge

volumes:
  model-cache:
    driver: local
