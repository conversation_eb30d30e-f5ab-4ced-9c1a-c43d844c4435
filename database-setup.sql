-- Doctor Dashboard Database Setup
-- This script creates all the required tables for the authentication system

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Users table (existing structure with doctor defaults)
CREATE TABLE users
(
    id                        text not null PRIMARY KEY,
    phone                     text not null UNIQUE,
    name                      text,
    sex                       text,
    dob                       date,
    email                     text,
    weight                    real,
    height                    real,
    waist                     real,
    body_fat                  real,
    diet                      text,
    drinks                    boolean,
    hormonal_ctps             boolean   default false,
    menopausal_state          text,
    pregnancy                 boolean   default false,
    smokes                    boolean,
    sleep_sensitivity         real,
    sleep_time                real,
    wakeup_time               real,
    sleep_normalize_max_limit real,
    created_at                timestamp default now(),
    updated_at                timestamp default now(),
    source                    text,
    access                    boolean   default false,  -- false by default for doctors
    status                    text,
    report_prompt             text,
    survey_prompt             text,
    report_id                 varchar(255),
    survey_id                 varchar(255),
    role                      text      default 'doctor', -- default role is doctor
    meta                      jsonb     default '{}'::jsonb,
    first_name                varchar,
    last_name                 varchar,
    tenant_id                 varchar   default '5005'::character varying, -- tenant 5005 for doctors
    user_id                   varchar(255),
    chronic_condition         text
);

-- Verification token table (for OTP storage)
CREATE TABLE verification_token
(
    identifier text                     not null,
    expires    timestamp with time zone not null,
    token      text                     not null,
    PRIMARY KEY (identifier, token)
);

-- Web chat sessions table (for JWT storage)
CREATE TABLE web_chat_sessions
(
    id         uuid                     not null PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id    uuid                     not null,
    phone      varchar(50)              not null,
    tenant     varchar(255)             not null,
    created_at timestamp with time zone default CURRENT_TIMESTAMP,
    expires_at timestamp with time zone not null
);

-- Refresh tokens table
CREATE TABLE refresh_tokens
(
    user_id           uuid not null PRIMARY KEY,
    refresh_token_jti uuid not null UNIQUE DEFAULT uuid_generate_v4(),
    created_at        timestamp default CURRENT_TIMESTAMP
);

-- Create indexes for better performance
CREATE INDEX idx_users_phone ON users(phone);
CREATE INDEX idx_users_role ON users(role);
CREATE INDEX idx_users_access ON users(access);
CREATE INDEX idx_users_tenant_id ON users(tenant_id);
CREATE INDEX idx_verification_token_identifier ON verification_token(identifier);
CREATE INDEX idx_verification_token_expires ON verification_token(expires);
CREATE INDEX idx_web_chat_sessions_user_id ON web_chat_sessions(user_id);
CREATE INDEX idx_web_chat_sessions_expires_at ON web_chat_sessions(expires_at);
CREATE INDEX idx_refresh_tokens_user_id ON refresh_tokens(user_id);

-- Create a function to automatically update the updated_at column
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger to automatically update updated_at on users table
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Insert a sample admin user for testing (optional)
-- Password for admin would be handled through the application
INSERT INTO users (
    id, 
    phone, 
    name, 
    role, 
    access, 
    tenant_id,
    meta,
    created_at
) VALUES (
    'admin-user-001',
    '+1234567890',
    'Admin User',
    'admin',
    true,
    '5005',
    '{"admin_permissions": ["user_management", "approval_management"]}',
    CURRENT_TIMESTAMP
) ON CONFLICT (phone) DO NOTHING;

-- Print success message
SELECT 'Database setup completed successfully!' as message;