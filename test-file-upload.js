#!/usr/bin/env node

const puppeteer = require('puppeteer');
const fs = require('fs');
const path = require('path');

async function testFileUpload() {
  console.log('🧪 Testing File Upload Functionality');
  console.log('=====================================\n');

  let browser;
  try {
    // Create test PDF file
    const testPDF = Buffer.from('%PDF-1.4\n1 0 obj<</Type/Catalog/Pages 2 0 R>>endobj\n2 0 obj<</Type/Pages/Kids[3 0 R]/Count 1>>endobj\n3 0 obj<</Type/Page/Parent 2 0 R/Resources<</Font<</F1 4 0 R>>>>/MediaBox[0 0 612 792]/Contents 5 0 R>>endobj\n4 0 obj<</Type/Font/Subtype/Type1/BaseFont/Helvetica>>endobj\n5 0 obj<</Length 44>>stream\nBT /F1 12 Tf 100 700 Td (Test Document) Tj ET\nendstream\nendobj\nxref\n0 6\n0000000000 65535 f \n0000000009 00000 n \n0000000058 00000 n \n0000000115 00000 n \n0000000267 00000 n \n0000000338 00000 n \ntrailer<</Size 6/Root 1 0 R>>\nstartxref\n438\n%%EOF');
    
    const testFilePath = path.join(__dirname, 'test-file.pdf');
    fs.writeFileSync(testFilePath, testPDF);
    console.log('✅ Created test PDF:', testFilePath);

    browser = await puppeteer.launch({ 
      headless: false,
      devtools: true,
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });
    
    const page = await browser.newPage();
    
    // Capture console logs
    page.on('console', msg => {
      const text = msg.text();
      if (text.includes('FileAttachment') || 
          text.includes('originalFile') || 
          text.includes('WebSocket') ||
          text.includes('Missing') ||
          text.includes('error')) {
        console.log(`[CONSOLE] ${msg.type()}: ${text}`);
      }
    });
    
    page.on('pageerror', error => {
      console.error('❌ Page Error:', error.message);
    });

    // Navigate to app
    console.log('\n1️⃣ Navigating to app...');
    await page.goto('http://localhost:3000', { waitUntil: 'networkidle2' });
    
    // Set authentication token directly (skip login flow)
    console.log('2️⃣ Setting authentication...');
    await page.evaluate(() => {
      localStorage.setItem('authToken', 'test-token');
      localStorage.setItem('access_token', 'test-token');
      localStorage.setItem('user', JSON.stringify({ 
        id: 'test-user-123', 
        name: 'Test User',
        phone: '+919819304846'
      }));
    });
    
    // Reload to apply auth
    await page.reload({ waitUntil: 'networkidle2' });
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Navigate to a chat thread
    console.log('3️⃣ Navigating to chat thread...');
    await page.goto('http://localhost:3000/chat/test-file-upload-thread', { waitUntil: 'networkidle2' });
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // Look for file attachment button
    console.log('\n4️⃣ Looking for attachment button...');
    
    // Method 1: Try clicking the AttachFile icon button
    const attachmentButton = await page.$('button svg[data-testid="AttachFileIcon"]');
    if (!attachmentButton) {
      // Method 2: Try finding by class or aria-label
      const found = await page.evaluate(() => {
        const buttons = Array.from(document.querySelectorAll('button'));
        const attachButton = buttons.find(btn => {
          const svg = btn.querySelector('svg');
          const ariaLabel = btn.getAttribute('aria-label') || '';
          const title = btn.getAttribute('title') || '';
          return (svg && svg.innerHTML.includes('AttachFile')) || 
                 ariaLabel.toLowerCase().includes('attach') ||
                 title.toLowerCase().includes('attach');
        });
        if (attachButton) {
          attachButton.click();
          return true;
        }
        return false;
      });
      
      if (found) {
        console.log('✅ Found and clicked attachment button');
      } else {
        console.log('❌ Could not find attachment button');
        
        // Debug: Log all buttons
        const buttonInfo = await page.evaluate(() => {
          return Array.from(document.querySelectorAll('button')).map(btn => ({
            text: btn.textContent,
            className: btn.className,
            hasIcon: !!btn.querySelector('svg'),
            ariaLabel: btn.getAttribute('aria-label')
          }));
        });
        console.log('Available buttons:', buttonInfo);
      }
    } else {
      await attachmentButton.click();
      console.log('✅ Clicked attachment button (Method 1)');
    }
    
    // Wait for file input to be ready
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Find file input
    console.log('\n5️⃣ Looking for file input...');
    const fileInput = await page.$('input[type="file"]');
    
    if (fileInput) {
      console.log('✅ Found file input, uploading file...');
      
      // Upload the file
      await fileInput.uploadFile(testFilePath);
      console.log('✅ File selected');
      
      // Wait for upload to process
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      // Check if file appears in the UI
      const filePreview = await page.evaluate(() => {
        // Look for file preview elements
        const previews = Array.from(document.querySelectorAll('[title*="test-file.pdf"]'));
        return previews.length > 0;
      });
      
      if (filePreview) {
        console.log('✅ File preview appeared in UI');
      } else {
        console.log('⚠️  File preview not found in UI');
      }
      
      // Try to send a message with the attachment
      console.log('\n6️⃣ Sending message with attachment...');
      
      // Type a message
      const messageInput = await page.$('textarea[placeholder*="Type"]');
      if (messageInput) {
        await messageInput.type('Test message with attachment');
        console.log('✅ Typed message');
        
        // Find and click send button
        const sendButton = await page.evaluate(() => {
          const buttons = Array.from(document.querySelectorAll('button'));
          const send = buttons.find(btn => {
            const svg = btn.querySelector('svg');
            return svg && (svg.innerHTML.includes('Send') || btn.getAttribute('aria-label')?.includes('Send'));
          });
          if (send) {
            send.click();
            return true;
          }
          return false;
        });
        
        if (sendButton) {
          console.log('✅ Clicked send button');
          await new Promise(resolve => setTimeout(resolve, 3000));
          
          // Check console for any errors
          console.log('\n7️⃣ Checking for errors...');
          
          // Take screenshot
          await page.screenshot({ path: 'file-upload-test-result.png' });
          console.log('📸 Screenshot saved: file-upload-test-result.png');
        } else {
          console.log('❌ Could not find send button');
        }
      } else {
        console.log('❌ Could not find message input');
      }
      
    } else {
      console.log('❌ No file input found');
      
      // Debug: Check page structure
      const debugInfo = await page.evaluate(() => {
        return {
          hasFileInput: !!document.querySelector('input[type="file"]'),
          hasChatInterface: !!document.querySelector('[class*="ChatInterface"]'),
          hasAttachButton: !!document.querySelector('button svg[class*="AttachFile"]'),
          url: window.location.href
        };
      });
      console.log('Debug info:', debugInfo);
    }
    
    // Clean up test file
    if (fs.existsSync(testFilePath)) {
      fs.unlinkSync(testFilePath);
      console.log('\n✅ Cleaned up test file');
    }
    
  } catch (error) {
    console.error('\n❌ Test failed:', error.message);
    console.error(error.stack);
  } finally {
    if (browser) {
      console.log('\n🔄 Keeping browser open for inspection...');
      console.log('Press Ctrl+C to close.');
      await new Promise(() => {}); // Keep browser open
    }
  }
}

// Run the test
testFileUpload();