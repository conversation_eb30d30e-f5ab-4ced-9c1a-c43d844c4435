# Doctor Dashboard API Testing Guide

## 🚀 REAL OTP Integration Complete!

**Important**: The authentication system now uses **real Gatekeeper OTP service** instead of mock codes. You'll receive actual SMS messages with OTP codes.

## 🧪 Available Test Scripts

I've updated the test scripts to work with the real Gatekeeper API:

### 1. **Simple cURL Script** ⭐ (Recommended)
```bash
./test-api-curl.sh
```
- **Best for**: Quick testing and CI/CD pipelines
- **Requirements**: Just bash and curl (pre-installed on macOS/Linux)
- **Features**: Colorized output, comprehensive test coverage
- **Output**: Clear pass/fail status for each endpoint

### 2. **Node.js Test Script**
```bash
node test-api-simple.js
```
- **Best for**: Detailed testing and debugging
- **Requirements**: Node.js (no additional packages needed)
- **Features**: Detailed response logging, error analysis
- **Output**: JSON responses and detailed error messages

### 3. **Advanced Flow Test**
```bash
node test-auth-flow.js
```
- **Best for**: Complete authentication flow testing
- **Requirements**: Node.js with node-fetch (if needed)
- **Features**: Simulates real user workflows
- **Output**: Step-by-step flow validation

## 🚀 Quick Start

### Prerequisites
1. **Start your server**:
   ```bash
   npm run dev
   ```
   Make sure it's running on `http://localhost:3000`

2. **Verify database connection**:
   ```bash
   psql -d doctor_dashboard -c "SELECT COUNT(*) FROM users;"
   ```

### Run the Tests

**Option 1: Quick Test (Recommended)**
```bash
./test-api-curl.sh
```

**Option 2: Detailed Test**
```bash
node test-api-simple.js
```

## 📋 What Gets Tested

### ✅ Authentication Endpoints
- **POST /api/auth/request-otp** - Phone verification
- **POST /api/auth/verify-and-register** - Doctor registration
- **POST /api/auth/login** - Login initiation
- **POST /api/auth/verify-login** - Login completion
- **POST /api/auth/refresh** - Token refresh
- **GET /api/auth/me** - Get current user
- **POST /api/auth/logout** - Logout

### ✅ Admin Endpoints
- **GET /api/admin/pending-registrations** - List pending doctors
- **POST /api/admin/review-registration** - Approve/reject doctors
- **POST /api/admin/cleanup** - Database cleanup

### ✅ System Endpoints
- **GET /api/health** - Health check and system status

### ✅ Security & Validation Tests
- **Input Validation**: Invalid phone numbers, missing fields
- **Authentication**: Protected endpoints without tokens
- **Authorization**: Admin endpoints with user tokens
- **Token Validation**: Invalid/expired token handling
- **Error Handling**: Proper error codes and messages

## 📊 Expected Test Results

### 🟢 Tests That Should PASS
- Health check
- Input validation (rejecting invalid data)
- Authentication requirements (401 errors for missing tokens)
- Phone number format validation
- Required field validation
- Admin authorization requirements

### 🟡 Tests That Should FAIL (Expected)
- OTP verification with mock codes
- Registration with invalid OTP
- Login with invalid OTP

**Why test OTP codes fail**: The tests use dummy OTP codes (`123456`) which are rejected by the real Gatekeeper service. This is **correct behavior** - you now receive **real OTP codes via SMS**!

## 🔧 Sample Test Output

```bash
📝 Test 1: Health Check
-------------------------------------------
✅ PASS - Status 200 as expected

📝 Test 2: Request OTP (Valid Phone)
-------------------------------------------
✅ PASS - Status 200 as expected

📝 Test 3: Registration (Invalid OTP)
-------------------------------------------
✅ PASS - Status 400 as expected (OTP validation working)
```

## 🧪 Manual Testing Examples

### Test Health Check
```bash
curl -X GET http://localhost:3000/api/health
```

### Test OTP Request (Real SMS!)
```bash
curl -X POST http://localhost:3000/api/auth/request-otp \
  -H "Content-Type: application/json" \
  -d '{"phone":"+919819304846"}'
```
**Note**: Use your real phone number - you'll receive an actual SMS!

### Test Protected Endpoint
```bash
curl -X GET http://localhost:3000/api/auth/me \
  -H "Authorization: Bearer invalid-token"
```

### Test Registration (with Real OTP)
```bash
# Step 1: Request OTP first
curl -X POST http://localhost:3000/api/auth/request-otp \
  -H "Content-Type: application/json" \
  -d '{"phone":"+919819304846"}'

# Step 2: Check your SMS and use the real OTP code
curl -X POST http://localhost:3000/api/auth/verify-and-register \
  -H "Content-Type: application/json" \
  -d '{
    "phone":"+919819304846",
    "otp":"REAL_OTP_FROM_SMS",
    "name":"Dr. Test User",
    "organization":"Test Hospital",
    "license_number":"MD123456",
    "specialization":"Internal Medicine"
  }'
```
**Important**: Replace `REAL_OTP_FROM_SMS` with the actual OTP code you receive!

## 🔍 Debugging Failed Tests

### 1. Check Server Status
```bash
curl -X GET http://localhost:3000/api/health
```

### 2. Check Database Connection
```bash
psql -d doctor_dashboard -c "\dt"
```

### 3. Check Environment Variables
```bash
cat .env.local
```

### 4. Check Server Logs
Look at your `npm run dev` terminal for error messages.

### 5. Test Individual Endpoints
Use the manual testing examples above to isolate issues.

## 📝 Complete Test Flow (When SMS is Integrated)

Once you integrate a real SMS service, you can test the complete flow:

### 1. Doctor Registration Flow
```bash
# Step 1: Request OTP
curl -X POST http://localhost:3000/api/auth/request-otp \
  -H "Content-Type: application/json" \
  -d '{"phone":"+**********"}'

# Step 2: Check SMS for OTP code
# Step 3: Register with real OTP
curl -X POST http://localhost:3000/api/auth/verify-and-register \
  -H "Content-Type: application/json" \
  -d '{
    "phone":"+**********",
    "otp":"REAL_OTP_FROM_SMS",
    "name":"Dr. Real User",
    "organization":"Real Hospital",
    "license_number":"MD789012"
  }'
```

### 2. Admin Approval Flow
```bash
# Step 1: Admin logs in with OTP
# Step 2: Get pending registrations
curl -X GET http://localhost:3000/api/admin/pending-registrations \
  -H "Authorization: Bearer ADMIN_ACCESS_TOKEN"

# Step 3: Approve registration
curl -X POST http://localhost:3000/api/admin/review-registration \
  -H "Authorization: Bearer ADMIN_ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "user_id":"dr-uuid-here",
    "action":"approve",
    "notes":"Verified license with state board"
  }'
```

### 3. Doctor Login Flow
```bash
# Step 1: Request login OTP
curl -X POST http://localhost:3000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"phone":"+**********"}'

# Step 2: Verify OTP and get tokens
curl -X POST http://localhost:3000/api/auth/verify-login \
  -H "Content-Type: application/json" \
  -d '{"phone":"+**********","otp":"REAL_OTP_FROM_SMS"}'

# Step 3: Use access token for authenticated requests
curl -X GET http://localhost:3000/api/auth/me \
  -H "Authorization: Bearer ACCESS_TOKEN_FROM_STEP_2"
```

## 🎯 Success Criteria

Your API is working correctly if:

✅ **Health check returns 200** with database connection info  
✅ **Input validation rejects invalid data** with 400 status codes  
✅ **Authentication is required** for protected endpoints (401 errors)  
✅ **Admin authorization works** for admin endpoints (403 errors for non-admins)  
✅ **OTP validation fails with mock codes** (400 errors - this is correct!)  
✅ **All endpoints return proper JSON responses**  
✅ **Error messages are informative** and include proper error codes  

## 🔄 Next Steps

1. **Integrate SMS Service**: Replace the mock SMS with Twilio/AWS SNS
2. **Test Real Flow**: Use actual phone numbers and OTP codes
3. **Frontend Integration**: Connect these APIs to your React components
4. **Production Setup**: Configure environment variables and security
5. **Monitoring**: Add logging and error tracking

Your authentication API is fully functional and ready for integration! 🎉