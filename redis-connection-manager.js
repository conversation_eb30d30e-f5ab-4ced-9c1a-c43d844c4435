require('dotenv').config({ path: '.env' });
const { createClient } = require('redis');

class RedisConnectionManager {
  constructor() {
    this.clients = new Map();
    this.isShuttingDown = false;
  }

  async createClient(name, config = {}) {
    const clientConfig = {
      url: `rediss://${process.env.REDIS_URL}:6380`,
      password: process.env.REDIS_PASSWORD,
      socket: {
        tls: true,
        rejectUnauthorized: false,
        connectTimeout: 30000,
        commandTimeout: 10000,
        reconnectDelay: 2000,
        retryDelayOnFailover: 2000,
        maxRetriesPerRequest: 5,
        keepAlive: true,
        noDelay: true,
        pingInterval: 30000,
        lazyConnect: true,
      },
      ...config
    };

    const client = createClient(clientConfig);
    
    // Add event listeners
    client.on('error', (err) => {
      console.log(`❌ Redis ${name} Client Error:`, err.message);
      if (!this.isShuttingDown) {
        this.scheduleReconnect(name);
      }
    });
    
    client.on('connect', () => {
      console.log(`✅ Redis ${name} Client connected`);
    });
    
    client.on('ready', () => {
      console.log(`✅ Redis ${name} Client ready`);
    });
    
    client.on('end', () => {
      console.log(`⚠️  Redis ${name} Client connection ended`);
    });
    
    client.on('reconnecting', () => {
      console.log(`🔄 Redis ${name} Client reconnecting...`);
    });

    this.clients.set(name, client);
    return client;
  }

  async scheduleReconnect(name) {
    console.log(`🔄 Scheduling reconnect for ${name} in 5 seconds...`);
    setTimeout(async () => {
      try {
        const client = this.clients.get(name);
        if (client && !client.isOpen && !this.isShuttingDown) {
          console.log(`🔄 Attempting to reconnect ${name}...`);
          await client.connect();
          console.log(`✅ ${name} reconnected successfully`);
        }
      } catch (error) {
        console.log(`❌ Failed to reconnect ${name}:`, error.message);
      }
    }, 5000);
  }

  async connectAll() {
    console.log('🔌 Starting Redis connection manager...');
    
    try {
      // Create all clients
      const pubClient = await this.createClient('pub');
      const subClient = await this.createClient('sub');
      const gatekeeperClient = await this.createClient('gatekeeper');
      
      // Connect all clients
      await Promise.all([
        pubClient.connect(),
        subClient.connect(),
        gatekeeperClient.connect()
      ]);
      
      console.log('✅ All Redis clients connected successfully');
      
      // Start health monitoring
      this.startHealthMonitoring();
      
      return { pubClient, subClient, gatekeeperClient };
    } catch (error) {
      console.log('❌ Failed to connect Redis clients:', error.message);
      throw error;
    }
  }

  startHealthMonitoring() {
    console.log('🔍 Starting Redis health monitoring...');
    
    setInterval(async () => {
      for (const [name, client] of this.clients) {
        try {
          if (client.isOpen) {
            await client.ping();
            console.log(`✅ ${name} health check passed`);
          } else {
            console.log(`⚠️  ${name} is disconnected`);
          }
        } catch (error) {
          console.log(`❌ ${name} health check failed:`, error.message);
        }
      }
    }, 30000); // Check every 30 seconds
  }

  async shutdown() {
    console.log('🛑 Shutting down Redis connection manager...');
    this.isShuttingDown = true;
    
    const shutdownPromises = [];
    for (const [name, client] of this.clients) {
      shutdownPromises.push(
        client.quit().catch(err => {
          console.log(`Warning: Failed to cleanly close ${name}:`, err.message);
        })
      );
    }
    
    await Promise.all(shutdownPromises);
    console.log('✅ Redis connection manager shut down');
  }
}

// CLI interface
if (require.main === module) {
  const manager = new RedisConnectionManager();
  
  // Handle graceful shutdown
  process.on('SIGINT', async () => {
    console.log('\n🛑 Received SIGINT, shutting down gracefully...');
    await manager.shutdown();
    process.exit(0);
  });
  
  process.on('SIGTERM', async () => {
    console.log('\n🛑 Received SIGTERM, shutting down gracefully...');
    await manager.shutdown();
    process.exit(0);
  });
  
  // Start the manager
  manager.connectAll()
    .then(() => {
      console.log('✅ Redis connection manager started successfully');
      console.log('Press Ctrl+C to stop');
    })
    .catch(error => {
      console.error('❌ Failed to start Redis connection manager:', error);
      process.exit(1);
    });
}

module.exports = RedisConnectionManager;
