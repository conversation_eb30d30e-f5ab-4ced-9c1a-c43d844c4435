const puppeteer = require('puppeteer');

// Configuration
const BASE_URL = process.env.BASE_URL || 'http://localhost:3000';
const HEADLESS = process.env.HEADLESS !== 'false';
const TEST_PHONE = process.env.TEST_PHONE || '+919819304846';

// Enhanced logging
const log = {
  info: (msg, data) => console.log(`[INFO] ${msg}`, data || ''),
  error: (msg, data) => console.log(`[ERROR] ${msg}`, data || ''),
  success: (msg, data) => console.log(`[SUCCESS] ${msg}`, data || ''),
  api: (msg, data) => console.log(`[API] ${msg}`, data || ''),
  json: (msg, data) => console.log(`[JSON ERROR] ${msg}`, data || ''),
  warn: (msg, data) => console.log(`[WARN] ${msg}`, data || '')
};

// Track all API interactions
const apiLog = {
  requests: [],
  responses: [],
  jsonErrors: []
};

async function monitorAPICalls(page) {
  // Enable request interception
  await page.setRequestInterception(true);
  
  // Monitor requests
  page.on('request', request => {
    const url = request.url();
    const method = request.method();
    
    // Continue all requests
    request.continue();
    
    // Log API requests
    if (url.includes('/api/')) {
      const requestData = {
        url,
        method,
        headers: request.headers(),
        postData: request.postData(),
        timestamp: new Date().toISOString()
      };
      
      apiLog.requests.push(requestData);
      log.api(`Request: ${method} ${url}`);
      
      // Try to parse POST data
      if (requestData.postData) {
        try {
          const parsed = JSON.parse(requestData.postData);
          log.info('Request Body:', parsed);
        } catch (e) {
          log.json('Invalid JSON in request', {
            url,
            error: e.message,
            body: requestData.postData
          });
          apiLog.jsonErrors.push({
            type: 'request',
            url,
            error: e.message,
            body: requestData.postData
          });
        }
      }
    }
  });
  
  // Monitor responses
  page.on('response', async response => {
    const url = response.url();
    const status = response.status();
    
    if (url.includes('/api/')) {
      const responseData = {
        url,
        status,
        statusText: response.statusText(),
        headers: response.headers(),
        timestamp: new Date().toISOString()
      };
      
      try {
        const text = await response.text();
        responseData.body = text;
        
        log.api(`Response: ${status} ${url}`);
        
        // Try to parse response
        if (text) {
          try {
            const parsed = JSON.parse(text);
            responseData.parsed = parsed;
            log.info('Response Body:', parsed);
            
            // Check for errors
            if (parsed.error || parsed.message || status >= 400) {
              log.error('API Error Response:', parsed);
            }
          } catch (e) {
            log.json('Invalid JSON in response', {
              url,
              status,
              error: e.message,
              body: text.substring(0, 200)
            });
            apiLog.jsonErrors.push({
              type: 'response',
              url,
              status,
              error: e.message,
              body: text
            });
          }
        }
      } catch (e) {
        log.error('Could not read response body', { url, error: e.message });
      }
      
      apiLog.responses.push(responseData);
    }
  });
  
  // Monitor console for JSON errors
  page.on('console', msg => {
    const text = msg.text();
    if (msg.type() === 'error' || text.includes('JSON') || text.includes('parse')) {
      log.error('Console Error:', text);
      if (text.includes('JSON') || text.includes('parse')) {
        apiLog.jsonErrors.push({
          type: 'console',
          message: text,
          timestamp: new Date().toISOString()
        });
      }
    }
  });
  
  // Monitor page errors
  page.on('pageerror', error => {
    log.error('Page Error:', error.message);
    if (error.message.includes('JSON') || error.message.includes('parse')) {
      apiLog.jsonErrors.push({
        type: 'page_error',
        message: error.message,
        stack: error.stack,
        timestamp: new Date().toISOString()
      });
    }
  });
}

async function testDoctorDashboard() {
  log.info('Starting Doctor Dashboard API Monitor Test');
  
  const browser = await puppeteer.launch({
    headless: HEADLESS,
    args: ['--no-sandbox', '--disable-setuid-sandbox'],
    devtools: !HEADLESS
  });
  
  try {
    const page = await browser.newPage();
    await page.setViewport({ width: 1440, height: 900 });
    
    // Set up API monitoring
    await monitorAPICalls(page);
    
    // Navigate to the application
    log.info('Navigating to:', BASE_URL);
    await page.goto(BASE_URL, {
      waitUntil: ['networkidle0', 'domcontentloaded'],
      timeout: 30000
    });
    
    log.success('Page loaded successfully');
    
    // Wait for the page to be fully loaded
    await page.waitForSelector('.MuiTextField-root', { timeout: 10000 });
    
    // Find and fill the phone input
    log.info('Looking for phone input...');
    const phoneInput = await page.$('input[placeholder="+91 98193 04846"]');
    if (!phoneInput) {
      throw new Error('Phone input not found');
    }
    
    // Click and clear the input
    await phoneInput.click({ clickCount: 3 });
    await page.keyboard.press('Backspace');
    
    // Type the phone number
    await phoneInput.type(TEST_PHONE, { delay: 50 });
    log.success('Phone number entered:', TEST_PHONE);
    
    // Take screenshot
    await page.screenshot({ path: 'test-screenshots/phone-entered.png' });
    
    // Find the submit button
    log.info('Looking for submit button...');
    const submitButton = await page.evaluateHandle(() => {
      const buttons = Array.from(document.querySelectorAll('button'));
      return buttons.find(btn => 
        btn.textContent.includes('Send OTP') || 
        btn.textContent.includes('Login')
      );
    });
    
    if (!submitButton || !await submitButton.evaluate(el => el !== null)) {
      throw new Error('Submit button not found');
    }
    
    // Click the submit button and wait for response
    log.info('Clicking submit button...');
    
    // Set up response waiting before clicking
    const responsePromise = page.waitForResponse(
      response => response.url().includes('/api/') && response.request().method() === 'POST',
      { timeout: 10000 }
    ).catch(err => {
      log.error('No API response received within timeout');
      return null;
    });
    
    // Click the button
    await submitButton.click();
    log.info('Button clicked, waiting for API response...');
    
    // Wait for the response
    const apiResponse = await responsePromise;
    
    if (apiResponse) {
      log.success('API call detected!');
    } else {
      log.warn('No API call detected after button click');
    }
    
    // Wait a bit for any async operations
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // Take final screenshot
    await page.screenshot({ path: 'test-screenshots/after-api-call.png' });
    
    // Check for any error messages on the page
    const errorMessages = await page.evaluate(() => {
      const errors = [];
      document.querySelectorAll('.MuiAlert-root, .error, [role="alert"]').forEach(el => {
        if (el.textContent) {
          errors.push(el.textContent.trim());
        }
      });
      return errors;
    });
    
    if (errorMessages.length > 0) {
      log.error('Error messages found on page:', errorMessages);
    }
    
    // Check if OTP field appeared
    const otpField = await page.$('input[maxlength="6"]');
    if (otpField) {
      log.success('OTP field appeared - login flow progressed successfully');
    }
    
  } catch (error) {
    log.error('Test error:', error.message);
    throw error;
  } finally {
    // Print summary
    console.log('\n========== TEST SUMMARY ==========');
    console.log(`API Requests: ${apiLog.requests.length}`);
    console.log(`API Responses: ${apiLog.responses.length}`);
    console.log(`JSON Errors: ${apiLog.jsonErrors.length}`);
    
    if (apiLog.jsonErrors.length > 0) {
      console.log('\n========== JSON ERRORS ==========');
      apiLog.jsonErrors.forEach((error, index) => {
        console.log(`\nError ${index + 1}:`);
        console.log(error);
      });
    }
    
    if (apiLog.requests.length > 0) {
      console.log('\n========== API REQUESTS ==========');
      apiLog.requests.forEach(req => {
        console.log(`\n${req.method} ${req.url}`);
        if (req.postData) {
          console.log('Body:', req.postData);
        }
      });
    }
    
    if (apiLog.responses.length > 0) {
      console.log('\n========== API RESPONSES ==========');
      apiLog.responses.forEach(res => {
        console.log(`\n${res.status} ${res.url}`);
        if (res.body) {
          console.log('Body:', res.body);
        }
      });
    }
    
    await browser.close();
  }
}

// Create screenshots directory
const fs = require('fs');
if (!fs.existsSync('test-screenshots')) {
  fs.mkdirSync('test-screenshots');
}

// Run the test
testDoctorDashboard()
  .then(() => {
    if (apiLog.jsonErrors.length > 0) {
      console.log(`\nTest completed with ${apiLog.jsonErrors.length} JSON errors`);
      process.exit(1);
    } else {
      console.log('\nTest completed successfully');
      process.exit(0);
    }
  })
  .catch(error => {
    console.error('\nTest failed:', error);
    process.exit(1);
  });