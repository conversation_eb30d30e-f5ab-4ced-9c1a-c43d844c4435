const puppeteer = require('puppeteer');

// This test demonstrates the dashboard features using a mock authentication approach
// In a real scenario, you would use TEST_OTP environment variable

(async () => {
  console.log('🚀 Doctor Dashboard Feature Demo\n');
  console.log('This demo shows all dashboard features in action');
  console.log('=' .repeat(60) + '\n');
  
  const browser = await puppeteer.launch({
    headless: false, // Show browser for demo
    args: ['--no-sandbox', '--disable-setuid-sandbox'],
    slowMo: 50 // Slow down for visibility
  });
  
  const page = await browser.newPage();
  await page.setViewport({ width: 1280, height: 800 });
  
  try {
    // Step 1: Navigate to the app
    console.log('1️⃣ Navigating to Doctor Dashboard...');
    await page.goto('http://localhost:3000');
    await page.waitForSelector('h1');
    
    // Step 2: Demonstrate the login form
    console.log('2️⃣ Demonstrating login form...');
    const phoneInput = await page.$('input[placeholder*="98193"]');
    
    // Show form validation
    console.log('   • Testing form validation with invalid number');
    await phoneInput.type('123');
    await new Promise(resolve => setTimeout(resolve, 1000);
    
    console.log('   • Clearing and entering valid number');
    await phoneInput.click({ clickCount: 3 });
    await phoneInput.type('+************');
    await new Promise(resolve => setTimeout(resolve, 1000);
    
    // Step 3: Submit for OTP
    console.log('3️⃣ Requesting OTP...');
    await page.click('button[type="submit"]');
    
    // Wait for OTP screen
    await page.waitForSelector('input[maxlength="6"], [class*="error"]', { timeout: 10000 });
    
    const hasError = await page.$('[class*="error"]');
    if (hasError) {
      const errorText = await page.evaluate(el => el.textContent, hasError);
      console.log('   ⚠️  ' + errorText);
      
      if (errorText.includes('rate limit')) {
        console.log('\n❌ Rate limited. Cannot proceed with demo.');
        console.log('\nTo see the full dashboard demo:');
        console.log('1. Wait for rate limit to expire');
        console.log('2. Get the OTP sent to +************');
        console.log('3. Run: TEST_OTP="123456" node comprehensive-dashboard-test.js');
        await browser.close();
        return;
      }
    }
    
    // Check if OTP field appeared
    const hasOtpField = await page.$('input[maxlength="6"]');
    if (hasOtpField) {
      console.log('   ✅ OTP screen displayed successfully!');
      console.log('\n📱 OTP has been sent to +************');
      console.log('\nTo continue testing the dashboard:');
      console.log('1. Check the SMS on the phone');
      console.log('2. Run the comprehensive test with the OTP:');
      console.log('   TEST_OTP="123456" node comprehensive-dashboard-test.js');
      
      // Take screenshots of what we can access
      console.log('\n📸 Taking screenshots of accessible features...');
      
      // Screenshot OTP screen
      await page.screenshot({ path: 'test-results/screenshots/demo-otp-screen.png' });
      console.log('   • OTP entry screen');
      
      // Try "Use Different Number" flow
      const diffNumberBtn = await page.evaluate(() => {
        return Array.from(document.querySelectorAll('button, a')).find(el => 
          el.textContent.includes('Use Different Number')
        );
      });
      
      if (diffNumberBtn) {
        await page.evaluate(() => {
          const btn = Array.from(document.querySelectorAll('button, a')).find(el => 
            el.textContent.includes('Use Different Number')
          );
          if (btn) btn.click();
        });
        
        await new Promise(resolve => setTimeout(resolve, 1000);
        await page.screenshot({ path: 'test-results/screenshots/demo-back-to-login.png' });
        console.log('   • Login screen (via "Use Different Number")');
      }
      
      // Demonstrate responsive design
      console.log('\n4️⃣ Testing responsive design...');
      
      const viewports = [
        { name: 'Mobile', width: 375, height: 667 },
        { name: 'Tablet', width: 768, height: 1024 },
        { name: 'Desktop', width: 1280, height: 800 }
      ];
      
      for (const viewport of viewports) {
        await page.setViewport({ width: viewport.width, height: viewport.height });
        await page.screenshot({ 
          path: `test-results/screenshots/demo-responsive-${viewport.name.toLowerCase()}.png` 
        });
        console.log(`   • ${viewport.name} view (${viewport.width}x${viewport.height})`);
        await new Promise(resolve => setTimeout(resolve, 500));
      }
    }
    
    console.log('\n✨ Demo completed!');
    console.log('\n📊 What the full test suite covers:');
    console.log('• ✅ Complete authentication flow');
    console.log('• 📋 All thread types (Patient Cases, Research, Quick Facts)');
    console.log('• 💬 Message sending and AI responses');
    console.log('• 📎 File attachments');
    console.log('• 🔍 Search functionality');
    console.log('• 🗑️ Thread management (delete, archive)');
    console.log('• 📊 Markdown and table rendering');
    console.log('• 👤 User profile and logout');
    console.log('• 📱 Responsive design across devices');
    console.log('• ♿ Accessibility features');
    
    console.log('\n🔐 Authentication Required:');
    console.log('The dashboard requires authentication to access its features.');
    console.log('Please use the OTP sent to test the complete functionality.');
    
  } catch (error) {
    console.error('\n❌ Error:', error.message);
  } finally {
    console.log('\n🎬 Closing browser in 5 seconds...');
    await new Promise(resolve => setTimeout(resolve, 5000);
    await browser.close();
  }
})();