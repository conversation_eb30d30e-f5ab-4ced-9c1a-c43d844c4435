# Doctor Dashboard - Project Memory

## Overview
This is a medical dashboard application for doctors with AI-powered features including patient case management, medical research, and quick facts.

## Key Information

### Testing Phone Number
**ALWAYS use: +919819304846**
Never use any other number unless explicitly instructed.

### Rate Limit Management
To clear rate limits for testing:
```bash
# Check current rate limit status
node rate-limit-manager.js status

# Clear rate limits for test phone
node rate-limit-manager.js clear

# Monitor rate limits in real-time
node rate-limit-manager.js monitor
```

**IMPORTANT**: The rate limit manager ONLY operates on the test phone number (+919819304846) and local IP addresses. It's safe to use and won't affect other users.

### Architecture
- **Frontend**: Next.js 15.3.4 with Turbopack, Material-UI
- **Backend**: Next.js API routes
- **Database**: PostgreSQL
- **Authentication**: OTP-based via Gatekeeper API
- **State Management**: React hooks and context

### Project Structure
```
doctor-dashboard/
├── src/
│   ├── app/              # Next.js app directory
│   │   ├── api/         # API routes
│   │   └── components/  # React components
│   ├── lib/             # Utility functions
│   └── contexts/        # React contexts
├── public/              # Static assets
└── test files/          # E2E tests
```

### Key Features
1. **Authentication**: Phone-based OTP system
2. **Dashboard**: Thread-based conversations
3. **Patient Cases**: Clinical case management
4. **Research**: Evidence-based medical research
5. **Quick Facts**: Quick medical information lookup
6. **File Attachments**: Support for medical documents

### API Integration
- **Gatekeeper API**: https://gatekeeper-staging.getbeyondhealth.com
- **Tenant**: `practitioner-dashboard` 
- **Parameter**: Uses `phoneNumber` (not `phone`)
- **Rate Limiting**: Enforced by Gatekeeper (4 OTP requests per 30 minutes)

### API Documentation
**Complete API specification is available in `api-spec.md`**
- All endpoints tested and documented with request/response formats
- Authentication flow with real JWT tokens
- Message sending (text, single file, multiple files)
- All dialogue types supported (patient-case, research, quick-fact)
- Data retrieval endpoints (requires JWT)
- Rate limiting and error responses
- Updated with `access` field for user approval status

### Database Schema
- `users`: Doctor profiles
- `threads`: Conversation threads
- `messages`: Individual messages
- `attachments`: File metadata
- `verification_token`: OTP tokens
- `web_chat_sessions`: Session management
- `refresh_tokens`: JWT refresh tokens

### Known Issues & Solutions
1. **Gatekeeper Parameter Mismatch**: Fixed by accepting both `phone` and `phoneNumber`
2. **Rate Limiting**: Wait 5-10 minutes between excessive OTP requests
3. **404 on /dashboard**: App uses `/` as main route (SPA)

### E2E Testing Framework

#### Overview
Comprehensive Puppeteer-based testing framework with multiple test suites covering all features.

#### Running Tests
```bash
# Run all tests
npm run test:e2e

# Run specific suite
npm run test:e2e -- -s patient-cases
npm run test:e2e -- -s research
npm run test:e2e -- -s quick-facts

# Run in headed mode (see browser)
npm run test:e2e -- -H

# Skip Redis clearing
npm run test:e2e -- -r
```

#### Test Suites
1. **authentication**: Login/logout flows (100% passing - 4/4 tests)
2. **dashboard-navigation**: UI navigation (100% passing - 4/4 tests)
3. **messaging**: Message functionality (100% passing - 4/4 tests)
4. **patient-cases**: Patient case features (100% passing - 8/8 tests)
5. **research**: Research queries (100% passing - 8/8 tests)
6. **quick-facts**: Quick facts feature (90% passing - 9/10 tests)
7. **error-handling**: Error scenarios

#### Test Configuration
- Base URL: http://localhost:3000
- Test Phone: +919819304846
- Screenshots: test-results/screenshots/
- Reports: test-results/comprehensive-report-*.json

#### Common Commands
```bash
# Run with custom phone (NOT RECOMMENDED)
TEST_PHONE="+919819304846" npm run test:e2e

# Run with OTP automation
TEST_OTP="123456" npm run test:e2e

# Verbose logging
./run-e2e-tests.sh -v

# Cleanup old test results
./run-e2e-tests.sh -c
```

#### Recent Test Updates (2025-06-23)
1. **Simplified medical queries** - Tests use simpler, more reliable queries
2. **Modal handling** - Tests close any open modals before sending messages  
3. **Improved AI response detection** - Tests check full page text content
4. **Redis clearing** - Automatically clears rate limits before tests and every 3 test suites

### Automated Registration Test

#### Overview
Standalone Puppeteer test script for testing the full registration flow on both desktop and mobile viewports.

#### Location
`/Users/<USER>/projects/doctor-dashboard/automated-tests/test-registration.js`

#### Running the Test
```bash
# Prerequisites: Server must be running
npm run dev

# Clear rate limits if needed
node rate-limit-manager.js clear

# Run the test
node automated-tests/test-registration.js
```

#### Test Configuration
- **Test Phone**: +919819304846
- **Test OTP**: 123456
- **Viewports Tested**: Desktop (1920x1080) and Mobile (375x667)

#### What It Tests
1. **Phone number input validation**: Ensures proper E.164 format
2. **OTP request flow**: Monitors network requests to Gatekeeper API
3. **OTP verification**: Submits OTP and verifies JWT token response
4. **Registration form**: Fills all required fields (name, email, specialization, etc.)
5. **Dashboard access**: Verifies successful navigation after registration
6. **Mobile responsiveness**: Runs same flow on mobile viewport

#### Key Technical Details
- **Input Method**: Uses Puppeteer's `type()` method for proper React state updates
- **Network Monitoring**: Intercepts and logs all API requests/responses
- **Alert Handling**: Automatically accepts browser alerts
- **Error Handling**: Comprehensive try-catch with detailed error messages
- **Screenshots**: Takes screenshots on failure for debugging

#### Troubleshooting
- **Rate Limit Errors**: Run `node rate-limit-manager.js clear` before testing
- **Connection Errors**: Ensure server is running on http://localhost:3000
- **OTP Issues**: Check that test OTP (123456) is configured in environment
- **Timeout Errors**: Increase wait times in the script if needed

### Development Commands
```bash
# Start development server
npm run dev

# Run in production mode
npm run build && npm start

# Database migrations
node dialogue-migration.sql

# Check logs
tail -f dev.log
```

### Important Files
- `/src/app/api/auth/request-otp/route.js`: OTP request handler
- `/src/lib/auth-utils.js`: Authentication utilities
- `/src/lib/api.js`: Frontend API client
- `/src/app/components/ThreadList.js`: Main dashboard component
- `E2E_TESTING_FRAMEWORK.md`: Complete testing documentation

### Security Notes
- JWT tokens stored in localStorage
- Phone validation: `/^\+[1-9]\d{1,14}$/`
- CORS enabled for API routes
- Environment variables in .env.local

### Debugging Tips
1. Check `dev.log` for server-side errors
2. Browser console for client-side errors
3. Network tab for API failures
4. Screenshots in test-results/ for E2E failures

Remember: This is a medical application - maintain high standards for security, reliability, and user experience.

## Development Guidelines

### IMPORTANT: When fixing issues:
- Make minimal changes only
- Don't refactor working code
- Preserve all existing functionality
- Ask before making broad changes

### UUID Requirements
requestId, dialogueId, userId should always be UUIDs and nothing else.

### Authentication Requirements
NEVER bypass authentication. All downstream functionality depends on proper authentication flow. Always use the real login process with valid credentials.

### Request Logging Commands

Monitor API requests and responses using these commands:

```bash
# Follow all requests in real-time
tail -f /Users/<USER>/projects/doctor-dashboard/requests.log

# Follow only incoming HTTP requests (API calls to our server)
cd /Users/<USER>/projects/doctor-dashboard && node tail-logs.js INCOMING_HTTP -f

# Follow only outgoing HTTP requests (calls to Gatekeeper)
cd /Users/<USER>/projects/doctor-dashboard && node tail-logs.js OUTGOING_HTTP -f

# Follow WebSocket incoming events
cd /Users/<USER>/projects/doctor-dashboard && node tail-logs.js WEBSOCKET_IN -f

# Follow WebSocket outgoing events
cd /Users/<USER>/projects/doctor-dashboard && node tail-logs.js WEBSOCKET_OUT -f

# Follow all HTTP traffic (both incoming and outgoing)
tail -f /Users/<USER>/projects/doctor-dashboard/requests.log | grep -E "TYPE: (INCOMING|OUTGOING)_HTTP" -A 20

# Follow only Gatekeeper webhook calls with their responses
tail -f /Users/<USER>/projects/doctor-dashboard/requests.log | grep -E "gatekeeper.*webhook" -A 15

# Follow emit-message calls from Gatekeeper
tail -f /Users/<USER>/projects/doctor-dashboard/requests.log | grep -E "/api/emit-message" -A 20
```

## Memory: Planning Methodology

### Planning Guidelines
- **IMPORTANT: When asked to plan something, always think deeply about the plan, refer to existing documentation you've created, the /docs folder, .claude.md, /claude-docs folder, readme.md and anything else you think would help. Then generate a plan with minimal changes as required. Always get approval on the plan before implementing**