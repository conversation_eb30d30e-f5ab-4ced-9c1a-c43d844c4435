#!/usr/bin/env node

import fetch from 'node-fetch';

// Gatekeeper API configuration
const GATEKEEPER_BASE_URL = 'https://gatekeeper-staging.getbeyondhealth.com/auth/august';
const TEST_PHONE = '+919819304846';

// Colors for output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

async function testGatekeeperAPI() {
  log('\n🔐 Testing Gatekeeper API Directly\n', 'blue');
  log(`Base URL: ${GATEKEEPER_BASE_URL}`, 'cyan');
  log(`Test Phone: ${TEST_PHONE}`, 'cyan');
  
  // Test 1: Check if API is reachable
  log('\n1️⃣ Testing API Connectivity...', 'yellow');
  
  try {
    const healthCheck = await fetch(GATEKEEPER_BASE_URL, {
      method: 'GET',
      timeout: 10000
    });
    
    log(`API Base Response: ${healthCheck.status} ${healthCheck.statusText}`, 
        healthCheck.ok ? 'green' : 'red');
  } catch (error) {
    log(`Failed to reach API: ${error.message}`, 'red');
  }
  
  // Test 2: Request OTP
  log('\n2️⃣ Testing OTP Request Endpoint...', 'yellow');
  
  try {
    const requestBody = { phoneNumber: TEST_PHONE };
    log(`Request Body: ${JSON.stringify(requestBody)}`, 'cyan');
    
    const startTime = Date.now();
    const response = await fetch(`${GATEKEEPER_BASE_URL}/request-otp`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'Doctor-Dashboard-Test/1.0'
      },
      body: JSON.stringify(requestBody),
      timeout: 30000
    });
    
    const responseTime = Date.now() - startTime;
    log(`Response Time: ${responseTime}ms`, 'cyan');
    log(`Response Status: ${response.status} ${response.statusText}`, 
        response.ok ? 'green' : 'red');
    
    // Get response headers
    log('\nResponse Headers:', 'cyan');
    const headers = {};
    response.headers.forEach((value, key) => {
      headers[key] = value;
      console.log(`  ${key}: ${value}`);
    });
    
    // Get response body
    const contentType = response.headers.get('content-type');
    let responseBody;
    
    if (contentType && contentType.includes('application/json')) {
      try {
        responseBody = await response.json();
        log('\nResponse Body (JSON):', 'cyan');
        console.log(JSON.stringify(responseBody, null, 2));
      } catch (e) {
        log('\nFailed to parse JSON response:', 'red');
        responseBody = await response.text();
        console.log(responseBody);
      }
    } else {
      responseBody = await response.text();
      log('\nResponse Body (Text):', 'cyan');
      console.log(responseBody);
    }
    
    // Analyze the response
    if (response.ok) {
      log('\n✅ OTP Request Successful!', 'green');
      if (responseBody.otp_id) {
        log(`OTP ID: ${responseBody.otp_id}`, 'green');
      }
    } else {
      log('\n❌ OTP Request Failed!', 'red');
      if (responseBody.message) {
        log(`Error Message: ${responseBody.message}`, 'red');
      }
      if (responseBody.error) {
        log(`Error Details: ${responseBody.error}`, 'red');
      }
    }
    
  } catch (error) {
    log(`\n❌ Request Failed with Error:`, 'red');
    log(`Error Type: ${error.constructor.name}`, 'red');
    log(`Error Message: ${error.message}`, 'red');
    if (error.stack) {
      log('\nStack Trace:', 'red');
      console.log(error.stack);
    }
  }
  
  // Test 3: Test with different phone formats
  log('\n3️⃣ Testing Different Phone Number Formats...', 'yellow');
  
  const phoneFormats = [
    '+919819304846',      // With country code
    '919819304846',       // Without +
    '9819304846',         // Without country code
    '+91-9819304846',     // With dash
    '+91 9819304846',     // With space
  ];
  
  for (const phone of phoneFormats) {
    try {
      log(`\nTesting format: ${phone}`, 'cyan');
      
      const response = await fetch(`${GATEKEEPER_BASE_URL}/request-otp`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ phoneNumber: phone }),
        timeout: 5000
      });
      
      const result = await response.json().catch(() => ({}));
      
      if (response.ok) {
        log(`  ✅ Accepted`, 'green');
      } else {
        log(`  ❌ Rejected: ${result.message || response.statusText}`, 'red');
      }
    } catch (error) {
      log(`  ❌ Error: ${error.message}`, 'red');
    }
  }
  
  // Test 4: Check required fields
  log('\n4️⃣ Testing Required Fields...', 'yellow');
  
  const testCases = [
    { body: {}, name: 'Empty body' },
    { body: { phone: TEST_PHONE }, name: 'Using "phone" instead of "phoneNumber"' },
    { body: { phoneNumber: '' }, name: 'Empty phone number' },
    { body: { phoneNumber: '123' }, name: 'Invalid phone number' },
    { body: { phoneNumber: TEST_PHONE, extra: 'field' }, name: 'Extra fields' }
  ];
  
  for (const testCase of testCases) {
    try {
      log(`\nTesting: ${testCase.name}`, 'cyan');
      log(`Body: ${JSON.stringify(testCase.body)}`, 'cyan');
      
      const response = await fetch(`${GATEKEEPER_BASE_URL}/request-otp`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(testCase.body),
        timeout: 5000
      });
      
      const result = await response.json().catch(() => ({ error: 'Invalid JSON' }));
      
      if (response.ok) {
        log(`  ✅ Accepted`, 'green');
      } else {
        log(`  ❌ Rejected (${response.status}): ${result.message || result.error || response.statusText}`, 'red');
      }
    } catch (error) {
      log(`  ❌ Error: ${error.message}`, 'red');
    }
  }
  
  // Test 5: Check API documentation or other endpoints
  log('\n5️⃣ Checking Other Endpoints...', 'yellow');
  
  const endpoints = [
    { path: '/', method: 'GET', name: 'Root' },
    { path: '/health', method: 'GET', name: 'Health Check' },
    { path: '/status', method: 'GET', name: 'Status' },
    { path: '/verify-otp', method: 'GET', name: 'Verify OTP (GET)' },
    { path: '/verify-otp', method: 'POST', name: 'Verify OTP (POST)' }
  ];
  
  for (const endpoint of endpoints) {
    try {
      log(`\nChecking ${endpoint.name}: ${endpoint.method} ${endpoint.path}`, 'cyan');
      
      const response = await fetch(`${GATEKEEPER_BASE_URL}${endpoint.path}`, {
        method: endpoint.method,
        headers: {
          'Content-Type': 'application/json',
        },
        timeout: 5000
      });
      
      log(`  Status: ${response.status} ${response.statusText}`, 
          response.status < 500 ? 'green' : 'red');
      
    } catch (error) {
      log(`  Error: ${error.message}`, 'red');
    }
  }
  
  log('\n✨ Gatekeeper API Test Complete!\n', 'blue');
  
  // Summary
  log('Summary:', 'yellow');
  log('- The Gatekeeper API endpoint is configured correctly', 'cyan');
  log('- Check the response body above for specific error details', 'cyan');
  log('- Common issues: API key required, rate limiting, phone format validation', 'cyan');
}

// Run the test
testGatekeeperAPI();