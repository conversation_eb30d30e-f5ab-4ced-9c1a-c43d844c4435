#!/usr/bin/env node

const axios = require('axios');
const { io } = require('socket.io-client');

async function testWithFreshAuth() {
  console.log('🧪 Testing with Fresh Authentication');
  console.log('===================================\n');

  try {
    // 1. Request OTP
    console.log('1️⃣ Requesting OTP...');
    const otpResponse = await axios.post('http://localhost:3000/api/auth/request-otp', {
      phoneNumber: '+919819304846'
    });
    console.log('✅ OTP requested:', otpResponse.data);

    // 2. Verify OTP (using test OTP)
    console.log('\n2️⃣ Verifying OTP...');
    const verifyResponse = await axios.post('http://localhost:3000/api/auth/verify-login', {
      phone: '+919819304846',
      otp: '123456'
    });
    
    const { access_token, user } = verifyResponse.data;
    console.log('✅ Authenticated successfully');
    console.log('User ID:', user.id);
    console.log('Token received:', access_token ? 'Yes' : 'No');

    // 3. Connect WebSocket with fresh token
    console.log('\n3️⃣ Connecting WebSocket...');
    const socket = io('http://localhost:3000', {
      transports: ['websocket']
    });

    await new Promise((resolve) => {
      socket.on('connect', () => {
        console.log('✅ Connected:', socket.id);
        resolve();
      });
    });

    // 4. Authenticate with fresh token
    socket.emit('authenticate', {
      token: access_token,
      user: user
    });

    await new Promise((resolve) => {
      socket.on('authenticated', () => {
        console.log('✅ WebSocket authenticated');
        resolve();
      });
    });

    // 5. Send a real message
    const { v4: uuidv4 } = require('uuid');
    const threadId = uuidv4();
    console.log('\n4️⃣ Sending patient case message...');
    console.log('Thread ID:', threadId);
    
    socket.emit('send_message', {
      threadId: threadId,
      content: 'I have a patient with persistent headache for 3 days. No fever, but complaining of photophobia.',
      conversationType: 'patient-case',
      attachments: []
    });

    // Listen for responses
    socket.on('message_sent', (data) => {
      console.log('✅ Message sent:', data);
    });

    socket.on('message_response', (data) => {
      console.log('\n📥 AI Response received:');
      console.log('Content:', data.response?.content);
      console.log('Thread ID:', data.threadId);
    });

    socket.on('message_error', (error) => {
      console.error('❌ Message error:', error);
    });

    // Wait for response
    console.log('\n⏳ Waiting for AI response...');
    await new Promise(resolve => setTimeout(resolve, 10000));

    socket.disconnect();
    
    // Check server logs
    console.log('\n📋 Checking server logs for emit-message...\n');
    const { execSync } = require('child_process');
    try {
      const logs = execSync(`grep -A 5 "Received emit-message" server.log | tail -20`, { encoding: 'utf8' });
      console.log(logs);
    } catch (e) {
      console.log('No recent emit-message calls found');
    }

  } catch (error) {
    console.error('❌ Error:', error.response?.data || error.message);
  }
}

testWithFreshAuth();