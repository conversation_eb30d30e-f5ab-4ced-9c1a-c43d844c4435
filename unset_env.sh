#!/bin/bash

# Check if .env file exists
if [ ! -f .env ]; then
    echo ".env file not found!"
    exit 1
fi

# Read each line in .env file
while IFS='=' read -r key _ || [ -n "$key" ]; do
    # Skip empty lines and comments
    if [[ -n "$key" && "$key" != "#"* ]]; then
        unset "$key"
        echo "Unset: $key"
    fi
done < .env

echo "All environment variables from .env are unset."


# source unset_env.sh