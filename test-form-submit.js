const puppeteer = require('puppeteer');

async function testFormSubmit() {
  console.log('Testing form submission directly...');
  
  const browser = await puppeteer.launch({
    headless: false, // Run in visible mode to see what's happening
    args: ['--no-sandbox', '--disable-setuid-sandbox'],
    devtools: true
  });
  
  try {
    const page = await browser.newPage();
    
    // Enable request logging
    page.on('request', request => {
      if (request.method() === 'POST' || request.url().includes('/api/')) {
        console.log(`[REQUEST] ${request.method()} ${request.url()}`);
        if (request.postData()) {
          console.log('[BODY]', request.postData());
        }
      }
    });
    
    page.on('response', response => {
      if (response.request().method() === 'POST' || response.url().includes('/api/')) {
        console.log(`[RESPONSE] ${response.status()} ${response.url()}`);
      }
    });
    
    page.on('console', msg => {
      if (msg.type() === 'error') {
        console.log('[CONSOLE ERROR]', msg.text());
      }
    });
    
    await page.goto('http://localhost:3000', { waitUntil: 'networkidle0' });
    console.log('Page loaded');
    
    // Wait for form to be ready
    await page.waitForSelector('input[placeholder*="98193"]');
    
    // Try different approaches to submit the form
    console.log('\n1. Trying to fill and submit form...');
    
    // Fill the phone number
    await page.type('input[placeholder*="98193"]', '+919819304846');
    console.log('Phone number entered');
    
    // Try to find and submit the form
    const formSubmitted = await page.evaluate(() => {
      // Find the form element
      const forms = document.querySelectorAll('form');
      console.log('Forms found:', forms.length);
      
      if (forms.length > 0) {
        // Try to submit the first form
        forms[0].submit();
        return true;
      }
      
      // If no form, try to find the submit button and click it
      const submitButton = Array.from(document.querySelectorAll('button')).find(btn => 
        btn.textContent.includes('Send OTP') || btn.type === 'submit'
      );
      
      if (submitButton) {
        console.log('Submit button found:', submitButton.textContent);
        submitButton.click();
        return true;
      }
      
      return false;
    });
    
    console.log('Form submission attempted:', formSubmitted);
    
    // Wait for any network activity
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // Check if anything changed
    const currentState = await page.evaluate(() => {
      return {
        url: window.location.href,
        hasOtpField: !!document.querySelector('input[maxlength="6"]'),
        phoneValue: document.querySelector('input[placeholder*="98193"]')?.value || '',
        buttonText: Array.from(document.querySelectorAll('button')).map(b => b.textContent.trim())
      };
    });
    
    console.log('\nCurrent state:', currentState);
    
    // Try pressing Enter in the phone field
    console.log('\n2. Trying to press Enter in phone field...');
    await page.focus('input[placeholder*="98193"]');
    await page.keyboard.press('Enter');
    
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Final check
    const finalState = await page.evaluate(() => {
      // Check for any validation errors
      const errors = [];
      document.querySelectorAll('.MuiFormHelperText-root, .error, [role="alert"]').forEach(el => {
        if (el.textContent) errors.push(el.textContent.trim());
      });
      
      return {
        hasOtpField: !!document.querySelector('input[maxlength="6"]'),
        errors,
        formCount: document.querySelectorAll('form').length,
        inputCount: document.querySelectorAll('input').length
      };
    });
    
    console.log('\nFinal state:', finalState);
    
    // Keep browser open for 10 seconds to observe
    console.log('\nKeeping browser open for observation...');
    await new Promise(resolve => setTimeout(resolve, 10000));
    
  } catch (error) {
    console.error('Test error:', error);
  } finally {
    await browser.close();
  }
}

testFormSubmit();