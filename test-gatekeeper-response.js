#!/usr/bin/env node

const axios = require('axios');

async function testGatekeeperResponse() {
  console.log('🧪 Testing Gatekeeper Response Format');
  console.log('====================================\n');

  try {
    // This simulates the exact format Gatekeeper sends
    const gatekeeperPayload = {
      userId: "898a9e7b-6873-4c4c-b21c-f4786ee281ad",
      messageText: "I%E2%80%99m%20having%20trouble%20processing%20your%20request%20right%20now%2C%20but%20I%E2%80%99ll%20respond%20soon.%20Thank%20you%20for%20your%20patience!",
      phone: "+919819304846"
    };

    console.log('📤 Sending Gatekeeper format payload to /api/emit-message');
    console.log('Payload:', JSON.stringify(gatekeeperPayload, null, 2));
    console.log('\nDecoded message:', decodeURIComponent(gatekeeperPayload.messageText));
    console.log('\n');

    const response = await axios.post('http://localhost:3000/api/emit-message', gatekeeperPayload, {
      headers: {
        'Content-Type': 'application/json'
      }
    });

    console.log('✅ Response received:');
    console.log('Status:', response.status);
    console.log('Data:', JSON.stringify(response.data, null, 2));

    // Test with a different message
    console.log('\n\n📤 Testing with a different message...');
    const payload2 = {
      userId: "898a9e7b-6873-4c4c-b21c-f4786ee281ad",
      messageText: encodeURIComponent("Based on your symptoms, I recommend scheduling an appointment with your primary care physician."),
      phone: "+919819304846"
    };

    const response2 = await axios.post('http://localhost:3000/api/emit-message', payload2);
    console.log('✅ Second response:', response2.data);

    // Test with explicit dialogueId
    console.log('\n\n📤 Testing with explicit dialogueId...');
    const payload3 = {
      userId: "898a9e7b-6873-4c4c-b21c-f4786ee281ad",
      messageText: "This message includes a dialogue ID",
      phone: "+919819304846",
      dialogueId: "test-dialogue-123"
    };

    const response3 = await axios.post('http://localhost:3000/api/emit-message', payload3);
    console.log('✅ Third response:', response3.data);

  } catch (error) {
    console.error('\n❌ Error:', error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);
    }
  }
}

testGatekeeperResponse();