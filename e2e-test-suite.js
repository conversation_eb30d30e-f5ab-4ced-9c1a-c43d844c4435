const puppeteer = require('puppeteer');
const fs = require('fs');
const path = require('path');

// Configuration
const CONFIG = {
  baseUrl: 'http://localhost:3000',
  testPhone: '+919819304846',
  headless: process.env.HEADLESS !== 'false',
  slowMo: 50, // Slow down actions for reliability
  timeout: 30000,
  testOTP: '123456' // You'll need to provide the actual OTP
};

// Test data
const TEST_DOCTOR = {
  phone: CONFIG.testPhone,
  name: 'Dr. Test User',
  organization: 'Test Hospital',
  license_number: 'TEST123456',
  specialization: 'General Medicine'
};

// Create test directories
const TEST_DIRS = ['test-results', 'test-results/screenshots', 'test-results/logs'];
TEST_DIRS.forEach(dir => {
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
  }
});

// Test results tracking
const testResults = {
  totalTests: 0,
  passed: 0,
  failed: 0,
  errors: [],
  startTime: new Date(),
  tests: []
};

// Logging utilities
class Logger {
  constructor(testName) {
    this.testName = testName;
    this.logFile = `test-results/logs/${testName}-${new Date().toISOString().replace(/:/g, '-')}.log`;
    this.logStream = fs.createWriteStream(this.logFile, { flags: 'a' });
  }

  log(level, message, data = null) {
    const timestamp = new Date().toISOString();
    const logEntry = {
      timestamp,
      level,
      test: this.testName,
      message,
      data
    };
    
    // Console output with colors
    const colors = {
      INFO: '\x1b[36m',
      SUCCESS: '\x1b[32m',
      ERROR: '\x1b[31m',
      WARN: '\x1b[33m',
      DEBUG: '\x1b[90m'
    };
    
    console.log(`${colors[level] || ''}[${level}] ${message}\x1b[0m`, data || '');
    
    // File output
    this.logStream.write(JSON.stringify(logEntry) + '\n');
  }

  info(message, data) { this.log('INFO', message, data); }
  success(message, data) { this.log('SUCCESS', message, data); }
  error(message, data) { this.log('ERROR', message, data); }
  warn(message, data) { this.log('WARN', message, data); }
  debug(message, data) { this.log('DEBUG', message, data); }

  close() {
    this.logStream.end();
  }
}

// Test utilities
class TestUtils {
  constructor(page, logger) {
    this.page = page;
    this.logger = logger;
    this.screenshotCount = 0;
  }

  async screenshot(name) {
    this.screenshotCount++;
    const filename = `test-results/screenshots/${this.logger.testName}-${this.screenshotCount}-${name}.png`;
    await this.page.screenshot({ path: filename, fullPage: true });
    this.logger.debug(`Screenshot saved: ${filename}`);
    return filename;
  }

  async waitAndClick(selector, description) {
    this.logger.debug(`Waiting for ${description}: ${selector}`);
    await this.page.waitForSelector(selector, { timeout: CONFIG.timeout });
    await this.page.click(selector);
    this.logger.debug(`Clicked ${description}`);
  }

  async waitAndType(selector, text, description) {
    this.logger.debug(`Typing in ${description}: ${selector}`);
    await this.page.waitForSelector(selector, { timeout: CONFIG.timeout });
    await this.page.click(selector, { clickCount: 3 });
    await this.page.type(selector, text);
    this.logger.debug(`Typed "${text}" in ${description}`);
  }

  async waitForNavigation(options = {}) {
    await this.page.waitForNavigation({ 
      waitUntil: 'networkidle0', 
      timeout: CONFIG.timeout,
      ...options 
    });
  }

  async checkElement(selector, shouldExist = true) {
    try {
      await this.page.waitForSelector(selector, { timeout: 3000 });
      return shouldExist;
    } catch (e) {
      return !shouldExist;
    }
  }

  async getElementText(selector) {
    await this.page.waitForSelector(selector);
    return await this.page.$eval(selector, el => el.textContent.trim());
  }

  async getInputValue(selector) {
    await this.page.waitForSelector(selector);
    return await this.page.$eval(selector, el => el.value);
  }

  async waitForTimeout(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  async ensureAuthenticated() {
    // Check if we're already logged in by looking for the chat interface
    const isLoggedIn = await this.page.evaluate(() => {
      // Check for elements that indicate we're in the chat interface
      return !!(document.querySelector('textarea') || 
                document.querySelector('[placeholder*="message"]') ||
                document.querySelector('[placeholder*="ask"]'));
    });

    if (isLoggedIn) {
      this.logger.debug('Already authenticated');
      return true;
    }

    // Check if we're on the login page
    const onLoginPage = await this.checkElement('input[placeholder*="98193"]');
    
    if (!onLoginPage) {
      // Navigate to home page to start login flow
      await this.page.goto(CONFIG.baseUrl);
      await this.page.waitForSelector('input[placeholder*="98193"]', { timeout: 5000 });
    }

    try {
      this.logger.info('Starting authentication flow');
      
      // Enter phone number
      await this.waitAndType('input[placeholder*="98193"]', CONFIG.testPhone, 'phone input');
      
      // Submit phone number
      await this.page.click('button[type="submit"]');
      
      // Wait for OTP field
      await this.page.waitForSelector('input[maxlength="6"]', { timeout: 10000 });
      
      // Enter OTP
      await this.waitAndType('input[maxlength="6"]', CONFIG.testOTP, 'OTP input');
      
      // Submit OTP
      await this.page.keyboard.press('Enter');
      
      // Wait for chat interface to appear (no page navigation in SPA)
      await this.page.waitForFunction(() => {
        return document.querySelector('textarea') || 
               document.querySelector('[placeholder*="message"]') ||
               document.querySelector('.message-input') ||
               !document.querySelector('input[placeholder*="98193"]');
      }, { timeout: 10000 });
      
      await this.waitForTimeout(1000); // Give UI time to settle
      
      // Verify we're logged in
      const loggedIn = await this.page.evaluate(() => {
        return !document.querySelector('input[placeholder*="98193"]');
      });
      
      if (loggedIn) {
        this.logger.success('Authentication successful');
        return true;
      } else {
        throw new Error('Authentication failed - still on login page');
      }
    } catch (error) {
      this.logger.error('Authentication failed:', error.message);
      return false;
    }
  }
}

// Test Suite Base Class
class TestSuite {
  constructor(name) {
    this.name = name;
    this.logger = new Logger(name);
    this.browser = null;
    this.page = null;
    this.utils = null;
    this.testResult = {
      name,
      passed: true,
      errors: [],
      duration: 0,
      steps: []
    };
  }

  async setup() {
    this.logger.info(`Setting up test suite: ${this.name}`);
    
    this.browser = await puppeteer.launch({
      headless: CONFIG.headless,
      slowMo: CONFIG.slowMo,
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });
    
    this.page = await this.browser.newPage();
    await this.page.setViewport({ width: 1440, height: 900 });
    
    // Set up error monitoring
    this.page.on('console', msg => {
      if (msg.type() === 'error') {
        this.logger.error('Console error:', msg.text());
      }
    });
    
    this.page.on('pageerror', error => {
      this.logger.error('Page error:', error.message);
    });
    
    // Monitor API calls
    this.page.on('response', response => {
      if (response.url().includes('/api/')) {
        this.logger.debug(`API Response: ${response.status()} ${response.url()}`);
      }
    });
    
    this.utils = new TestUtils(this.page, this.logger);
    
    this.logger.success('Test suite setup complete');
  }

  async teardown() {
    this.logger.info('Tearing down test suite');
    
    if (this.browser) {
      await this.browser.close();
    }
    
    this.logger.close();
  }

  async runTest(testName, testFn) {
    const startTime = Date.now();
    const step = {
      name: testName,
      passed: false,
      error: null,
      duration: 0
    };
    
    try {
      this.logger.info(`Running test: ${testName}`);
      await testFn();
      step.passed = true;
      this.logger.success(`Test passed: ${testName}`);
    } catch (error) {
      step.passed = false;
      step.error = error.message;
      this.testResult.passed = false;
      this.testResult.errors.push({
        test: testName,
        error: error.message
      });
      this.logger.error(`Test failed: ${testName}`, error.message);
      
      // Take error screenshot
      await this.utils.screenshot(`error-${testName.replace(/\s+/g, '-')}`);
    }
    
    step.duration = Date.now() - startTime;
    this.testResult.steps.push(step);
  }

  async run() {
    const startTime = Date.now();
    
    try {
      await this.setup();
      await this.execute();
    } catch (error) {
      this.testResult.passed = false;
      this.testResult.errors.push({
        test: 'setup',
        error: error.message
      });
    } finally {
      await this.teardown();
      this.testResult.duration = Date.now() - startTime;
      return this.testResult;
    }
  }

  async execute() {
    // Override in subclasses
    throw new Error('execute() must be implemented in subclass');
  }
}

// Authentication Test Suite
class AuthenticationTestSuite extends TestSuite {
  constructor() {
    super('authentication');
  }

  async execute() {
    // Test 1: Navigate to home page
    await this.runTest('Navigate to home page', async () => {
      await this.page.goto(CONFIG.baseUrl);
      await this.utils.screenshot('home-page');
      
      const title = await this.page.title();
      if (!title.includes('Doctor Dashboard')) {
        throw new Error(`Unexpected title: ${title}`);
      }
    });

    // Test 2: Check login form
    await this.runTest('Check login form elements', async () => {
      await this.utils.checkElement('form');
      await this.utils.checkElement('input[placeholder*="98193"]');
      await this.utils.checkElement('button[type="submit"]');
      
      const buttonText = await this.utils.getElementText('button[type="submit"]');
      if (!buttonText.includes('Send OTP')) {
        throw new Error(`Unexpected button text: ${buttonText}`);
      }
    });

    // Test 3: Enter phone number and request OTP
    await this.runTest('Request OTP', async () => {
      await this.utils.waitAndType('input[placeholder*="98193"]', CONFIG.testPhone, 'phone input');
      await this.utils.screenshot('phone-entered');
      
      // Click submit and wait for OTP field
      const [response] = await Promise.all([
        this.page.waitForResponse(res => res.url().includes('/api/auth/request-otp')),
        this.page.click('button[type="submit"]')
      ]);
      
      if (response.status() !== 200) {
        throw new Error(`OTP request failed with status: ${response.status()}`);
      }
      
      // Wait for OTP field to appear
      await this.page.waitForSelector('input[maxlength="6"]', { timeout: 10000 });
      await this.utils.screenshot('otp-field-visible');
    });

    // Test 4: Enter OTP (manual step - need actual OTP)
    await this.runTest('Enter OTP', async () => {
      this.logger.warn('Manual step: Enter the OTP received on phone');
      
      // For automated testing, you would need to:
      // 1. Use a test OTP if your backend supports it
      // 2. Or integrate with an SMS receiving service
      // 3. Or manually input the OTP
      
      // For now, we'll simulate entering an OTP
      if (process.env.TEST_OTP) {
        await this.utils.waitAndType('input[maxlength="6"]', process.env.TEST_OTP, 'OTP input');
        await this.utils.screenshot('otp-entered');
        
        // Submit OTP
        await Promise.all([
          this.page.waitForNavigation(),
          this.page.keyboard.press('Enter')
        ]);
      } else {
        this.logger.warn('Skipping OTP entry - set TEST_OTP environment variable to automate');
      }
    });
  }
}

// Dashboard Navigation Test Suite
class DashboardTestSuite extends TestSuite {
  constructor() {
    super('dashboard-navigation');
  }

  async execute() {
    // Ensure we're authenticated first
    await this.runTest('Authenticate', async () => {
      const authenticated = await this.utils.ensureAuthenticated();
      if (!authenticated) {
        throw new Error('Failed to authenticate');
      }
    });
    
    await this.runTest('Check dashboard layout', async () => {
      // We should already be on the chat page after authentication
      await this.utils.screenshot('dashboard-main');
      
      // Check main navigation elements
      await this.utils.checkElement('nav'); // Navigation bar
      await this.utils.checkElement('aside'); // Sidebar
    });

    await this.runTest('Check sidebar elements', async () => {
      // First check if we're on the login page
      const onLoginPage = await this.utils.checkElement('input[placeholder*="98193"]');
      if (onLoginPage) {
        this.logger.warn('On login page - skipping sidebar check');
        return;
      }
      
      // Check for thread types
      const threadTypes = ['Patient Cases', 'Research', 'Quick Facts'];
      
      // In the new UI, these might be in a different location or format
      const foundTypes = await this.page.evaluate((types) => {
        const pageText = document.body.textContent || '';
        return types.filter(type => pageText.includes(type));
      }, threadTypes);
      
      this.logger.info(`Found thread types: ${foundTypes.join(', ')}`);
      
      // Just warn if not all are found, don't fail
      if (foundTypes.length < threadTypes.length) {
        this.logger.warn(`Not all thread types found. Missing: ${threadTypes.filter(t => !foundTypes.includes(t)).join(', ')}`);
      }
    });

    await this.runTest('Test new thread creation', async () => {
      // Look for new thread button - using proper selectors
      const newThreadButton = await this.page.$('button[aria-label*="new" i]');
      if (newThreadButton) {
        await newThreadButton.click();
        await this.utils.screenshot('new-thread-dialog');
        
        // Close dialog if opened
        await this.page.keyboard.press('Escape');
      } else {
        // Try finding by text content
        const clicked = await this.page.evaluate(() => {
          const buttons = Array.from(document.querySelectorAll('button'));
          const newButton = buttons.find(btn => 
            btn.textContent.toLowerCase().includes('new')
          );
          if (newButton) {
            newButton.click();
            return true;
          }
          return false;
        });
        
        if (clicked) {
          await this.utils.screenshot('new-thread-dialog');
          await this.page.keyboard.press('Escape');
        }
      }
    });
  }
}

// Message and Chat Test Suite
class MessageTestSuite extends TestSuite {
  constructor() {
    super('messaging');
  }

  async execute() {
    // Ensure we're authenticated first
    await this.runTest('Authenticate', async () => {
      const authenticated = await this.utils.ensureAuthenticated();
      if (!authenticated) {
        throw new Error('Failed to authenticate');
      }
    });
    
    await this.runTest('Test message input', async () => {
      
      // Find message input
      let messageInput = await this.page.$('textarea[placeholder*="message" i], input[placeholder*="message" i], textarea[placeholder*="ask" i], textarea[placeholder*="type" i]');
      if (!messageInput) {
        // Try a more general search
        messageInput = await this.page.$('textarea');
        if (!messageInput) {
          throw new Error('Message input not found');
        }
        this.logger.info('Found generic textarea element');
      }
      
      // Type a test message
      await messageInput.type('Test message from automated test');
      await this.utils.screenshot('message-typed');
    });

    await this.runTest('Test file attachment', async () => {
      // Look for file attachment button
      const attachButton = await this.page.$('button[aria-label*="attach" i], button[title*="attach" i]');
      if (attachButton) {
        await attachButton.click();
        await this.utils.screenshot('attachment-dialog');
        
        // Close if dialog opened
        await this.page.keyboard.press('Escape');
      } else {
        this.logger.warn('File attachment button not found');
      }
    });

    await this.runTest('Test markdown rendering', async () => {
      // Check if messages support markdown
      const markdownElements = await this.page.$$('pre, code, table');
      this.logger.info(`Found ${markdownElements.length} potential markdown elements`);
    });
  }
}

// Main Test Runner
class E2ETestRunner {
  constructor() {
    this.testSuites = [
      new AuthenticationTestSuite(),
      new DashboardTestSuite(),
      new MessageTestSuite()
    ];
  }

  async run() {
    console.log('\n🚀 Starting E2E Test Suite for Doctor Dashboard\n');
    console.log(`Configuration:`);
    console.log(`  - Base URL: ${CONFIG.baseUrl}`);
    console.log(`  - Test Phone: ${CONFIG.testPhone}`);
    console.log(`  - Headless: ${CONFIG.headless}`);
    console.log('\n' + '='.repeat(60) + '\n');

    for (const suite of this.testSuites) {
      console.log(`\n📋 Running Test Suite: ${suite.name}`);
      console.log('-'.repeat(40));
      
      const result = await suite.run();
      testResults.tests.push(result);
      testResults.totalTests += result.steps.length;
      
      if (result.passed) {
        testResults.passed += result.steps.filter(s => s.passed).length;
        testResults.failed += result.steps.filter(s => !s.passed).length;
      } else {
        testResults.failed += result.steps.length;
        testResults.errors.push(...result.errors);
      }
      
      // Summary for this suite
      console.log(`\nSuite Results:`);
      console.log(`  ✓ Passed: ${result.steps.filter(s => s.passed).length}`);
      console.log(`  ✗ Failed: ${result.steps.filter(s => !s.passed).length}`);
      
      if (result.errors.length > 0) {
        console.log(`\n  Errors:`);
        result.errors.forEach(err => {
          console.log(`    - ${err.test}: ${err.error}`);
        });
      }
    }

    // Generate final report
    this.generateReport();
  }

  generateReport() {
    testResults.endTime = new Date();
    testResults.duration = testResults.endTime - testResults.startTime;

    console.log('\n' + '='.repeat(60));
    console.log('📊 FINAL TEST REPORT');
    console.log('='.repeat(60) + '\n');
    
    console.log(`Total Test Suites: ${this.testSuites.length}`);
    console.log(`Total Tests: ${testResults.totalTests}`);
    console.log(`✅ Passed: ${testResults.passed}`);
    console.log(`❌ Failed: ${testResults.failed}`);
    console.log(`⏱️  Duration: ${(testResults.duration / 1000).toFixed(2)}s`);
    
    if (testResults.errors.length > 0) {
      console.log('\n🔴 Errors:');
      testResults.errors.forEach((err, i) => {
        console.log(`${i + 1}. ${err.test}: ${err.error}`);
      });
    }

    // Save detailed report
    const reportPath = `test-results/report-${new Date().toISOString().replace(/:/g, '-')}.json`;
    fs.writeFileSync(reportPath, JSON.stringify(testResults, null, 2));
    console.log(`\n📄 Detailed report saved to: ${reportPath}`);
    
    // Exit with appropriate code
    process.exit(testResults.failed > 0 ? 1 : 0);
  }
}

// Run the tests
if (require.main === module) {
  const runner = new E2ETestRunner();
  
  // Handle graceful shutdown
  process.on('SIGINT', () => {
    console.log('\n\n⚠️  Tests interrupted by user');
    process.exit(1);
  });
  
  runner.run().catch(error => {
    console.error('\n💥 Fatal error running tests:', error);
    process.exit(1);
  });
}

module.exports = { E2ETestRunner, TestSuite, CONFIG };