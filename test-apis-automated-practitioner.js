const axios = require('axios');
const fs = require('fs');
const { v4: uuidv4 } = require('uuid');

// Configuration
const BASE_URL = 'https://gatekeeper-staging.getbeyondhealth.com';
const TENANT = 'practitioner-dashboard'; // Updated tenant slug
const AUGUST_TOKEN = 'm}0/m9ZL`k{|Mz:Ca{7k8PF(gJV"Xz/j';
const TEST_PHONE = '+************';

// Test state
let testResults = {
  timestamp: new Date().toISOString(),
  baseUrl: BASE_URL,
  tenant: TENANT,
  testPhone: TEST_PHONE,
  apis: {}
};

// Load existing auth if available
let authData = {};
if (fs.existsSync('gatekeeper-auth.json')) {
  authData = JSON.parse(fs.readFileSync('gatekeeper-auth.json', 'utf8'));
  console.log('✅ Loaded existing auth tokens from gatekeeper-auth.json');
}

// Helper to log and save results
function logResult(apiName, endpoint, method, request, response, error = null) {
  const result = {
    endpoint,
    method,
    timestamp: new Date().toISOString(),
    request: {
      headers: request.headers || {},
      params: request.params || {},
      body: request.body || {}
    },
    response: response ? {
      status: response.status,
      statusText: response.statusText,
      data: response.data,
      headers: response.headers
    } : null,
    error: error ? {
      message: error.message,
      code: error.code,
      status: error.response?.status,
      statusText: error.response?.statusText,
      data: error.response?.data
    } : null,
    success: !error
  };

  testResults.apis[apiName] = result;

  console.log(`\n${'='.repeat(70)}`);
  console.log(`API: ${apiName}`);
  console.log(`Method: ${method} ${endpoint}`);
  console.log(`Status: ${!error ? '✅ SUCCESS' : '❌ FAILED'}`);
  
  if (!error) {
    console.log(`Response Status: ${response.status} ${response.statusText}`);
    console.log('Response Data:', JSON.stringify(response.data, null, 2));
  } else {
    console.log('Error:', error.message);
    if (error.response?.data) {
      console.log('Error Response:', JSON.stringify(error.response.data, null, 2));
    }
  }
  console.log('='.repeat(70));
}

// Test 1: Request OTP
async function test1_RequestOTP() {
  const endpoint = `/auth/${TENANT}/request-otp`;
  const request = {
    headers: { 'Content-Type': 'application/json' },
    body: { phoneNumber: TEST_PHONE }
  };

  try {
    const response = await axios.post(
      `${BASE_URL}${endpoint}`,
      request.body,
      { headers: request.headers }
    );
    
    logResult('Request OTP', endpoint, 'POST', request, response);
    return true;
  } catch (error) {
    logResult('Request OTP', endpoint, 'POST', request, null, error);
    return false;
  }
}

// Test 2: Register User
async function test2_RegisterUser() {
  const endpoint = `/c/${TENANT}/register`;
  const request = {
    headers: {
      'Authorization': `Bearer ${AUGUST_TOKEN}`,
      'Content-Type': 'application/json'
    },
    body: {
      source: 'WEB',
      phoneNumber: TEST_PHONE,
      user_role: 'DOCTOR'
    }
  };

  try {
    const response = await axios.post(
      `${BASE_URL}${endpoint}`,
      request.body,
      { headers: request.headers }
    );
    
    logResult('Register User', endpoint, 'POST', request, response);
    return true;
  } catch (error) {
    logResult('Register User', endpoint, 'POST', request, null, error);
    // 409 Conflict means user already exists, which is ok
    return error.response?.status === 409;
  }
}

// Test 3: Get All Dialogues (will fail without real JWT)
async function test3_GetAllDialogues() {
  const endpoint = '/user/practitioner-dashboard/get-chats-by-dialogueId';
  const request = {
    headers: {
      'Authorization': `Bearer ${authData.accessToken || 'test-jwt'}`,
      'Content-Type': 'application/json'
    },
    params: {
      limit: 100,
      dialogue_id: ''
    }
  };

  try {
    const response = await axios.get(
      `${BASE_URL}${endpoint}`,
      {
        params: request.params,
        headers: request.headers
      }
    );
    
    logResult('Get All Dialogues', endpoint, 'GET', request, response);
    return true;
  } catch (error) {
    logResult('Get All Dialogues', endpoint, 'GET', request, null, error);
    return false;
  }
}

// Test 4: Send Text Message
async function test4_SendTextMessage() {
  const endpoint = `/c/${TENANT}/webhook`;
  const dialogueId = uuidv4();
  const request = {
    headers: {
      'Authorization': authData.accessToken ? `Bearer ${authData.accessToken}` : undefined,
      'Content-Type': 'application/json'
    },
    body: {
      dialogueId: dialogueId,
      dialogueType: 'patient-case',
      text: 'Test patient case: 45-year-old male with chest pain',
      providerMessageId: uuidv4(),
      sender: 'human',
      source: 'WEB',
      phoneNumber: TEST_PHONE,
      timestamp: Date.now(),
      requestId: uuidv4()
    }
  };

  try {
    const response = await axios.post(
      `${BASE_URL}${endpoint}`,
      request.body,
      { headers: request.headers }
    );
    
    logResult('Send Text Message', endpoint, 'POST', request, response);
    
    // Store dialogue ID for later tests
    testResults.testDialogueId = dialogueId;
    
    return true;
  } catch (error) {
    logResult('Send Text Message', endpoint, 'POST', request, null, error);
    return false;
  }
}

// Test 5: Send Message with Single File
async function test5_SendMessageWithFile() {
  const endpoint = `/c/${TENANT}/webhook`;
  const request = {
    headers: {
      'Authorization': authData.accessToken ? `Bearer ${authData.accessToken}` : undefined,
      'Content-Type': 'application/json'
    },
    body: {
      dialogueId: uuidv4(),
      dialogueType: 'patient-case',
      text: 'X-ray report attached',
      providerMessageId: uuidv4(),
      attachment: 'https://example-blob.blob.core.windows.net/container/xray-report.png',
      fileExtension: '.png',
      messageType: 'image',
      sender: 'human',
      source: 'WEB',
      phoneNumber: TEST_PHONE,
      timestamp: Date.now(),
      requestId: uuidv4()
    }
  };

  try {
    const response = await axios.post(
      `${BASE_URL}${endpoint}`,
      request.body,
      { headers: request.headers }
    );
    
    logResult('Send Message with File', endpoint, 'POST', request, response);
    return true;
  } catch (error) {
    logResult('Send Message with File', endpoint, 'POST', request, null, error);
    return false;
  }
}

// Test 6: Send Message with Multiple Files
async function test6_SendMessageMultipleFiles() {
  const endpoint = `/c/${TENANT}/webhook`;
  const request = {
    headers: {
      'Authorization': authData.accessToken ? `Bearer ${authData.accessToken}` : undefined,
      'Content-Type': 'application/json'
    },
    body: {
      dialogueId: uuidv4(),
      dialogueType: 'research',
      text: 'Multiple research papers attached',
      providerMessageId: uuidv4(),
      attachment: [
        {
          url: 'https://example-blob.blob.core.windows.net/container/paper1.pdf',
          fileExtension: '.pdf',
          messageType: 'pdf'
        },
        {
          url: 'https://example-blob.blob.core.windows.net/container/figure1.png',
          fileExtension: '.png',
          messageType: 'image'
        }
      ],
      sender: 'human',
      source: 'WEB',
      phoneNumber: TEST_PHONE,
      timestamp: Date.now(),
      requestId: uuidv4()
    }
  };

  try {
    const response = await axios.post(
      `${BASE_URL}${endpoint}`,
      request.body,
      { headers: request.headers }
    );
    
    logResult('Send Message Multiple Files', endpoint, 'POST', request, response);
    return true;
  } catch (error) {
    logResult('Send Message Multiple Files', endpoint, 'POST', request, null, error);
    return false;
  }
}

// Test 7: Test all dialogue types
async function test7_AllDialogueTypes() {
  const endpoint = `/c/${TENANT}/webhook`;
  const dialogueTypes = ['patient-case', 'research', 'quick-fact'];
  const results = {};

  for (const type of dialogueTypes) {
    const request = {
      headers: {
        'Authorization': authData.accessToken ? `Bearer ${authData.accessToken}` : undefined,
        'Content-Type': 'application/json'
      },
      body: {
        dialogueId: uuidv4(),
        dialogueType: type,
        text: `Test message for ${type} dialogue type`,
        providerMessageId: uuidv4(),
        sender: 'human',
        source: 'WEB',
        phoneNumber: TEST_PHONE,
        timestamp: Date.now(),
        requestId: uuidv4()
      }
    };

    try {
      const response = await axios.post(
        `${BASE_URL}${endpoint}`,
        request.body,
        { headers: request.headers }
      );
      
      results[type] = {
        success: true,
        response: response.data
      };
    } catch (error) {
      results[type] = {
        success: false,
        error: error.response?.data || error.message
      };
    }
  }

  console.log(`\n${'='.repeat(70)}`);
  console.log('Test All Dialogue Types');
  console.log('Results:', JSON.stringify(results, null, 2));
  console.log('='.repeat(70));

  testResults.apis['All Dialogue Types'] = results;
  return Object.values(results).every(r => r.success);
}

// Test 8: Get Messages by Dialogue ID (will fail without real JWT)
async function test8_GetMessagesByDialogueId() {
  const endpoint = '/user/practitioner-dashboard/get-chats-by-dialogueId';
  const dialogueId = testResults.testDialogueId || 'test-dialogue-id';
  
  const request = {
    headers: {
      'Authorization': `Bearer ${authData.accessToken || 'test-jwt'}`,
      'Content-Type': 'application/json'
    },
    params: {
      limit: 30,
      dialogue_id: dialogueId
    }
  };

  try {
    const response = await axios.get(
      `${BASE_URL}${endpoint}`,
      {
        params: request.params,
        headers: request.headers
      }
    );
    
    logResult('Get Messages by Dialogue ID', endpoint, 'GET', request, response);
    return true;
  } catch (error) {
    logResult('Get Messages by Dialogue ID', endpoint, 'GET', request, null, error);
    return false;
  }
}

// Main test runner
async function runAllTests() {
  console.log(`
${'='.repeat(70)}
🧪 GATEKEEPER API AUTOMATED TEST SUITE
${'='.repeat(70)}
Base URL: ${BASE_URL}
Tenant: ${TENANT}
Test Phone: ${TEST_PHONE}
Timestamp: ${new Date().toISOString()}
Auth Available: ${!!authData.accessToken}
${'='.repeat(70)}
`);

  const tests = [
    { name: 'Request OTP', fn: test1_RequestOTP },
    { name: 'Register User', fn: test2_RegisterUser },
    { name: 'Get All Dialogues', fn: test3_GetAllDialogues },
    { name: 'Send Text Message', fn: test4_SendTextMessage },
    { name: 'Send Message with File', fn: test5_SendMessageWithFile },
    { name: 'Send Message Multiple Files', fn: test6_SendMessageMultipleFiles },
    { name: 'All Dialogue Types', fn: test7_AllDialogueTypes },
    { name: 'Get Messages by Dialogue ID', fn: test8_GetMessagesByDialogueId }
  ];

  const summary = {
    total: tests.length,
    passed: 0,
    failed: 0
  };

  for (const test of tests) {
    console.log(`\n🧪 Running: ${test.name}...`);
    const success = await test.fn();
    
    if (success) {
      summary.passed++;
    } else {
      summary.failed++;
    }
    
    // Small delay between tests
    await new Promise(resolve => setTimeout(resolve, 1000));
  }

  // Save results
  const resultsFile = `api-test-results-practitioner-${Date.now()}.json`;
  fs.writeFileSync(resultsFile, JSON.stringify(testResults, null, 2));

  console.log(`

${'='.repeat(70)}
📊 TEST SUMMARY
${'='.repeat(70)}
Total Tests: ${summary.total}
Passed: ${summary.passed}
Failed: ${summary.failed}
Results saved to: ${resultsFile}
${'='.repeat(70)}
`);

  return testResults;
}

// Run tests
runAllTests()
  .then(results => {
    console.log('\n✅ Test suite completed');
    process.exit(0);
  })
  .catch(error => {
    console.error('\n❌ Test suite failed:', error);
    process.exit(1);
  });