const axios = require('axios');
const { v4: uuidv4 } = require('uuid');

async function testWithCurrentJWT() {
  console.log('🔐 Testing with current JWT from browser session...');
  console.log('====================================================\n');
  
  // Use the JWT from the recent browser login
  const jwt = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************************************************************************************************************************************************.QFnZ5tkp-pZG-NRcdUAEDaOOI8RUcFH8X2C_IvVxgio";
  
  // Use the exact same format as the working Node.js script
  const webhookPayload = {
    dialogueId: uuidv4(),
    dialogueType: 'patient-case',
    text: 'Test message with current JWT - working format',
    providerMessageId: uuidv4(),
    sender: 'human',
    source: 'WEB',
    phoneNumber: '+************',
    timestamp: Date.now(),
    requestId: uuidv4()
    // Note: No attachments array - same as working script
  };

  console.log('📤 Sending payload (working format):');
  console.log(JSON.stringify(webhookPayload, null, 2));
  console.log('\n');

  try {
    const response = await axios.post(
      'https://gatekeeper-staging.getbeyondhealth.com/c/practitioner-dashboard/webhook',
      webhookPayload,
      {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${jwt}`
        }
      }
    );
    
    console.log('✅ Response:');
    console.log('Status:', response.status);
    console.log('Data:', JSON.stringify(response.data, null, 2));
    console.log('\n');
    
    console.log('📝 Key IDs for database check:');
    console.log('==============================');
    console.log(`dialogueId: ${webhookPayload.dialogueId}`);
    console.log(`providerMessageId: ${webhookPayload.providerMessageId}`);
    console.log(`requestId: ${webhookPayload.requestId}`);
    console.log(`timestamp: ${webhookPayload.timestamp}`);
    
  } catch (error) {
    console.error('❌ Request failed:');
    console.error('Status:', error.response?.status);
    console.error('Data:', error.response?.data);
    console.error('Message:', error.message);
  }
}

testWithCurrentJWT();