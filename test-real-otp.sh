#!/bin/bash

# Real OTP Testing Script for Doctor <PERSON>
# This script tests the complete authentication flow with REAL Gatekeeper OTP service
# SMS will be sent to +918417048371 for verification

API_BASE="http://localhost:3000/api"
TEST_PHONE="+918417048371"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

echo -e "${CYAN}🚀 Doctor Dashboard Real OTP Testing${NC}"
echo -e "${CYAN}====================================================${NC}"
echo -e "${YELLOW}📱 Testing with phone number: $TEST_PHONE${NC}"
echo -e "${YELLOW}🔔 Real SMS messages will be sent!${NC}"
echo -e "${CYAN}====================================================${NC}"

# Helper function to test API endpoint
test_api() {
    local name="$1"
    local method="$2"
    local endpoint="$3"
    local data="$4"
    local auth_header="$5"
    
    echo -e "\n${BLUE}📝 $name${NC}"
    echo "-------------------------------------------"
    
    # Build curl command
    curl_cmd="curl -s -w \"\\nHTTP_STATUS:%{http_code}\" -X $method"
    
    if [ ! -z "$auth_header" ]; then
        curl_cmd="$curl_cmd -H \"Authorization: Bearer $auth_header\""
    fi
    
    if [ ! -z "$data" ]; then
        curl_cmd="$curl_cmd -H \"Content-Type: application/json\" -d '$data'"
    fi
    
    curl_cmd="$curl_cmd \"$API_BASE$endpoint\""
    
    echo "Command: $curl_cmd"
    echo ""
    
    # Execute curl command
    response=$(eval $curl_cmd)
    http_status=$(echo "$response" | grep "HTTP_STATUS:" | cut -d: -f2)
    json_response=$(echo "$response" | sed '/HTTP_STATUS:/d')
    
    echo "Response: $json_response"
    echo "Status: $http_status"
    
    # Return the response for further processing
    echo "$json_response"
}

# Function to prompt user for input
prompt_user() {
    local question="$1"
    echo -e "\n${YELLOW}$question${NC}"
    read -r response
    echo "$response"
}

echo -e "\n${BLUE}Step 1: Health Check${NC}"
health_response=$(test_api "Health Check" "GET" "/health" "")

echo -e "\n${BLUE}Step 2: Request OTP${NC}"
echo -e "${YELLOW}⚠️  This will send a REAL SMS to $TEST_PHONE${NC}"
proceed=$(prompt_user "Continue with OTP request? (y/n):")

if [ "$proceed" != "y" ] && [ "$proceed" != "Y" ]; then
    echo -e "${YELLOW}Test cancelled by user${NC}"
    exit 0
fi

echo -e "\n${CYAN}Requesting OTP...${NC}"
otp_response=$(test_api "Request OTP" "POST" "/auth/request-otp" "{\"phone\":\"$TEST_PHONE\"}")

echo -e "\n${BLUE}Step 3: Registration Test${NC}"
echo -e "${CYAN}🔑 Please check your SMS for the OTP code${NC}"
otp_code=$(prompt_user "Enter the OTP you received:")

if [ ${#otp_code} -ne 6 ]; then
    echo -e "${RED}❌ Invalid OTP format. Please enter a 6-digit code.${NC}"
    exit 1
fi

echo -e "\n${CYAN}Testing registration with OTP: $otp_code${NC}"
registration_data="{\"phone\":\"$TEST_PHONE\",\"otp\":\"$otp_code\",\"name\":\"Dr. Test User\",\"organization\":\"Test Hospital\",\"license_number\":\"MD123456\",\"specialization\":\"Internal Medicine\"}"
registration_response=$(test_api "Doctor Registration" "POST" "/auth/verify-and-register" "$registration_data")

# Check if registration was successful or if phone already exists
if echo "$registration_response" | grep -q "PHONE_EXISTS"; then
    echo -e "${YELLOW}📝 Phone already registered, proceeding to login test${NC}"
elif echo "$registration_response" | grep -q "success.*true"; then
    echo -e "${GREEN}✅ Registration successful!${NC}"
else
    echo -e "${RED}❌ Registration failed${NC}"
    echo -e "${YELLOW}This might be due to an invalid/expired OTP${NC}"
fi

echo -e "\n${BLUE}Step 4: Login Test${NC}"
echo -e "${CYAN}Requesting login OTP...${NC}"
login_otp_response=$(test_api "Login OTP Request" "POST" "/auth/login" "{\"phone\":\"$TEST_PHONE\"}")

if echo "$login_otp_response" | grep -q "pending.*approval"; then
    echo -e "${YELLOW}⚠️  Account pending approval. Admin needs to approve the registration first.${NC}"
    echo -e "${CYAN}You can test admin endpoints to approve the account.${NC}"
elif echo "$login_otp_response" | grep -q "success.*true"; then
    echo -e "${GREEN}✅ Login OTP sent${NC}"
    
    login_otp=$(prompt_user "Enter the login OTP you received:")
    
    if [ ${#login_otp} -eq 6 ]; then
        login_data="{\"phone\":\"$TEST_PHONE\",\"otp\":\"$login_otp\"}"
        login_response=$(test_api "Login Verification" "POST" "/auth/verify-login" "$login_data")
        
        if echo "$login_response" | grep -q "access_token"; then
            echo -e "${GREEN}✅ Login successful! Tokens received.${NC}"
            # Extract access token for testing protected endpoints
            access_token=$(echo "$login_response" | grep -o '"access_token":"[^"]*"' | cut -d'"' -f4)
            
            if [ ! -z "$access_token" ]; then
                echo -e "\n${BLUE}Step 5: Testing Protected Endpoint${NC}"
                protected_response=$(test_api "Get User Info" "GET" "/auth/me" "" "$access_token")
                
                if echo "$protected_response" | grep -q "success.*true"; then
                    echo -e "${GREEN}✅ Protected endpoint access successful${NC}"
                else
                    echo -e "${RED}❌ Protected endpoint access failed${NC}"
                fi
            fi
        else
            echo -e "${RED}❌ Login verification failed${NC}"
        fi
    else
        echo -e "${RED}❌ Invalid login OTP format${NC}"
    fi
else
    echo -e "${RED}❌ Login OTP request failed${NC}"
fi

echo -e "\n${CYAN}====================================================${NC}"
echo -e "${CYAN}📊 TEST COMPLETED${NC}"
echo -e "${CYAN}====================================================${NC}"

echo -e "\n${BLUE}📝 Notes:${NC}"
echo "- SMS messages were sent to $TEST_PHONE"
echo "- This tests the real Gatekeeper OTP integration"
echo "- External tokens are stored locally for reference"
echo "- Admin approval may be required for new registrations"

echo -e "\n${BLUE}🔧 Next Steps:${NC}"
echo "1. If account needs approval, use admin endpoints to approve"
echo "2. Test the complete flow from frontend integration"
echo "3. Verify all OTP codes you receive are working correctly"

echo -e "\n${GREEN}✅ Real OTP testing completed!${NC}"