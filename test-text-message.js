#!/usr/bin/env node

const puppeteer = require('puppeteer');

async function testTextMessage() {
  console.log('🧪 Testing Text Message Display');
  console.log('==============================\n');

  let browser;
  try {
    browser = await puppeteer.launch({ 
      headless: false,
      devtools: true,
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });
    
    const page = await browser.newPage();
    
    // Capture console logs
    page.on('console', msg => {
      const text = msg.text();
      if (text.includes('optimistic') || 
          text.includes('message') || 
          text.includes('WebSocket') ||
          text.includes('error')) {
        console.log(`[CONSOLE] ${msg.type()}: ${text}`);
      }
    });
    
    page.on('pageerror', error => {
      console.error('❌ Page Error:', error.message);
    });

    // Navigate to app
    console.log('1️⃣ Navigating to app...');
    await page.goto('http://localhost:3000', { waitUntil: 'networkidle2' });
    
    // Set authentication directly
    console.log('2️⃣ Setting authentication...');
    await page.evaluate(() => {
      localStorage.setItem('authToken', 'test-token');
      localStorage.setItem('access_token', 'test-token');
      localStorage.setItem('user', JSON.stringify({ 
        id: 'test-user-123', 
        name: 'Test User',
        phone: '+919819304846'
      }));
    });
    
    // Reload to apply auth
    await page.reload({ waitUntil: 'networkidle2' });
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Type a message directly
    console.log('\n3️⃣ Typing message...');
    const messageText = 'Test message at ' + new Date().toLocaleTimeString();
    
    // Find and click in textarea
    const textarea = await page.$('textarea');
    if (textarea) {
      await textarea.click();
      await textarea.type(messageText);
      console.log('✅ Message typed:', messageText);
      
      // Check if message appears in UI
      console.log('\n4️⃣ Checking for optimistic UI update...');
      
      // Press Enter to send
      await page.keyboard.press('Enter');
      
      // Wait a bit
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Check if message appears
      const messageFound = await page.evaluate((msgText) => {
        const elements = Array.from(document.querySelectorAll('*'));
        return elements.some(el => el.textContent && el.textContent.includes(msgText));
      }, messageText);
      
      console.log('Message visible in UI:', messageFound ? '✅ Yes' : '❌ No');
      
      // Take screenshot
      await page.screenshot({ path: 'test-text-message-result.png' });
      console.log('📸 Screenshot saved: test-text-message-result.png');
      
      // Check for any elements with message content
      const messageElements = await page.evaluate(() => {
        const messages = Array.from(document.querySelectorAll('[class*="MuiBox-root"]'));
        return messages
          .map(el => el.textContent)
          .filter(text => text && text.length > 0 && text.length < 200);
      });
      
      console.log('\n📋 Found text elements:', messageElements.slice(-5));
      
    } else {
      console.log('❌ No textarea found');
    }
    
  } catch (error) {
    console.error('\n❌ Test failed:', error.message);
  } finally {
    if (browser) {
      console.log('\n🔄 Keeping browser open. Press Ctrl+C to close.');
      await new Promise(() => {});
    }
  }
}

testTextMessage();