const redis = require('redis');

// Redis connection string
const REDIS_URL = "rediss://:AlhP5MXWfxMcS7V8jHe40GyWv2BLLKjlyAzCaPPORFQ=@rate-limit-cache.redis.cache.windows.net:6380";

// IMPORTANT: Only use the authorized testing phone number
const TEST_PHONE_NUMBER = "+919819304846";

// Safety check to ensure we never accidentally clear other phone numbers
function isTestPhoneNumber(phone) {
  return phone === TEST_PHONE_NUMBER;
}

async function clearTestPhoneRateLimit() {
  let client;
  
  try {
    // Create Redis client
    client = redis.createClient({
      url: REDIS_URL,
      socket: {
        tls: true,
        rejectUnauthorized: false
      }
    });

    // Handle connection events
    client.on('error', (err) => {
      console.error('Redis Client Error:', err);
    });

    // Connect to Redis
    await client.connect();
    console.log('✅ Connected to Redis');
    console.log(`🔍 Looking for rate limit keys for test phone: ${TEST_PHONE_NUMBER}`);

    // Possible key patterns for the test phone number
    const keyPatterns = [
      `otp_phone:${TEST_PHONE_NUMBER}`,
      `otp_phone:${TEST_PHONE_NUMBER}:*`,
      `rate_limit:phone:${TEST_PHONE_NUMBER}`,
      `rate_limit:phone:${TEST_PHONE_NUMBER}:*`,
      `rl:phone:${TEST_PHONE_NUMBER}`,
      `rl:phone:${TEST_PHONE_NUMBER}:*`,
      `*:${TEST_PHONE_NUMBER}:*otp*`,
      `*:${TEST_PHONE_NUMBER}:*rate*`
    ];

    let keysToDelete = [];

    // Search for keys matching our patterns
    for (const pattern of keyPatterns) {
      try {
        const keys = await client.keys(pattern);
        if (keys.length > 0) {
          console.log(`📌 Found ${keys.length} keys matching pattern: ${pattern}`);
          
          // Double-check each key contains our test phone number
          const safeKeys = keys.filter(key => key.includes(TEST_PHONE_NUMBER));
          keysToDelete = [...keysToDelete, ...safeKeys];
        }
      } catch (err) {
        console.warn(`⚠️  Could not search pattern ${pattern}:`, err.message);
      }
    }

    // Remove duplicates
    keysToDelete = [...new Set(keysToDelete)];

    if (keysToDelete.length === 0) {
      console.log('ℹ️  No rate limit keys found for the test phone number');
      
      // Try to find any keys that might contain the phone number
      console.log('🔍 Searching for any keys containing the test phone number...');
      const allKeys = await client.keys('*');
      const phoneKeys = allKeys.filter(key => key.includes(TEST_PHONE_NUMBER));
      
      if (phoneKeys.length > 0) {
        console.log(`📌 Found ${phoneKeys.length} keys containing the test phone:`);
        phoneKeys.forEach(key => console.log(`   - ${key}`));
        keysToDelete = phoneKeys;
      } else {
        console.log('ℹ️  No keys found containing the test phone number');
        return;
      }
    }

    // Show what we're about to delete
    console.log(`\n🗑️  Preparing to delete ${keysToDelete.length} keys:`);
    keysToDelete.forEach(key => console.log(`   - ${key}`));

    // Confirm before deletion
    console.log(`\n⚠️  This will ONLY delete rate limit keys for: ${TEST_PHONE_NUMBER}`);
    
    // Delete the keys
    console.log('\n🔄 Deleting keys...');
    let deletedCount = 0;
    
    for (const key of keysToDelete) {
      try {
        const result = await client.del(key);
        if (result > 0) {
          deletedCount++;
          console.log(`   ✅ Deleted: ${key}`);
        } else {
          console.log(`   ⚠️  Key not found or already deleted: ${key}`);
        }
      } catch (err) {
        console.error(`   ❌ Failed to delete ${key}:`, err.message);
      }
    }

    console.log(`\n✅ Successfully deleted ${deletedCount} rate limit keys for ${TEST_PHONE_NUMBER}`);

    // Also try to clear IP-based rate limits if we're running locally
    console.log('\n🔍 Checking for IP-based rate limits...');
    const ipPatterns = [
      'otp_ip:127.0.0.1*',
      'otp_ip:::1*',
      'otp_ip:localhost*',
      'rate_limit:ip:127.0.0.1*',
      'rate_limit:ip:::1*'
    ];

    for (const pattern of ipPatterns) {
      try {
        const keys = await client.keys(pattern);
        if (keys.length > 0) {
          console.log(`📌 Found ${keys.length} IP rate limit keys`);
          for (const key of keys) {
            await client.del(key);
            console.log(`   ✅ Deleted IP limit: ${key}`);
          }
        }
      } catch (err) {
        console.warn(`⚠️  Could not clear IP pattern ${pattern}:`, err.message);
      }
    }

  } catch (error) {
    console.error('❌ Error clearing rate limits:', error);
  } finally {
    // Close the connection
    if (client) {
      await client.quit();
      console.log('\n🔌 Redis connection closed');
    }
  }
}

// Function to check current rate limit status
async function checkRateLimitStatus() {
  let client;
  
  try {
    client = redis.createClient({
      url: REDIS_URL,
      socket: {
        tls: true,
        rejectUnauthorized: false
      }
    });

    await client.connect();
    console.log('✅ Connected to Redis');
    console.log(`🔍 Checking rate limit status for: ${TEST_PHONE_NUMBER}\n`);

    // Search for all keys containing the test phone
    const allKeys = await client.keys('*');
    const phoneKeys = allKeys.filter(key => key.includes(TEST_PHONE_NUMBER));

    if (phoneKeys.length === 0) {
      console.log('✅ No rate limit keys found - phone is not rate limited!');
    } else {
      console.log(`⚠️  Found ${phoneKeys.length} rate limit keys:`);
      
      for (const key of phoneKeys) {
        try {
          const ttl = await client.ttl(key);
          const type = await client.type(key);
          let value = 'N/A';
          
          if (type === 'string') {
            value = await client.get(key);
          } else if (type === 'hash') {
            value = await client.hGetAll(key);
          }
          
          console.log(`\n📌 Key: ${key}`);
          console.log(`   Type: ${type}`);
          console.log(`   TTL: ${ttl > 0 ? `${ttl} seconds (${Math.ceil(ttl/60)} minutes)` : 'No expiration'}`);
          console.log(`   Value: ${JSON.stringify(value)}`);
        } catch (err) {
          console.error(`   ❌ Error reading key ${key}:`, err.message);
        }
      }
    }

  } catch (error) {
    console.error('❌ Error checking rate limit status:', error);
  } finally {
    if (client) {
      await client.quit();
      console.log('\n🔌 Redis connection closed');
    }
  }
}

// Safety wrapper to ensure we only operate on test phone
async function safelyClearTestPhoneRateLimit() {
  console.log('🚀 Rate Limit Clearer for Test Phone Number');
  console.log('=' .repeat(50));
  console.log(`📱 Test Phone: ${TEST_PHONE_NUMBER}`);
  console.log('⚠️  This script ONLY clears rate limits for the test phone');
  console.log('=' .repeat(50) + '\n');

  // First check the status
  await checkRateLimitStatus();
  
  console.log('\n' + '=' .repeat(50));
  console.log('🧹 Clearing rate limits...');
  console.log('=' .repeat(50) + '\n');
  
  // Then clear the rate limits
  await clearTestPhoneRateLimit();
  
  console.log('\n' + '=' .repeat(50));
  console.log('📊 Verifying clearance...');
  console.log('=' .repeat(50) + '\n');
  
  // Verify they were cleared
  await checkRateLimitStatus();
}

// Run the script
if (require.main === module) {
  // Check if redis package is installed
  try {
    require.resolve('redis');
  } catch(e) {
    console.error('❌ Redis package not installed. Please run: npm install redis');
    process.exit(1);
  }

  safelyClearTestPhoneRateLimit()
    .then(() => {
      console.log('\n✨ Operation completed!');
      process.exit(0);
    })
    .catch(err => {
      console.error('\n❌ Operation failed:', err);
      process.exit(1);
    });
}

module.exports = { 
  clearTestPhoneRateLimit,
  checkRateLimitStatus,
  TEST_PHONE_NUMBER
};