# Local Models Setup Guide

This guide explains how to set up and use local models for the embedding and cross-encoder services instead of downloading them from Hugging Face at runtime.

## Benefits of Local Models

1. **Faster startup**: No need to download models on each container start
2. **Offline capability**: Services work without internet connection
3. **Consistent performance**: No dependency on Hugging Face API availability
4. **Reduced bandwidth**: Models downloaded once and reused

## Quick Setup

Run the master setup script to download all models:

```bash
chmod +x setup_local_models.sh
./setup_local_models.sh
```

This will:
- Download the embedding model (BAAI/bge-m3) to `embedding-service/models/`
- Download the cross-encoder model (cross-encoder/ms-marco-MiniLM-L-12-v2) to `cross-encoder-service/models/`

## Individual Service Setup

### Embedding Service

```bash
cd embedding-service
chmod +x setup_local_model.sh
./setup_local_model.sh
```

### Cross-Encoder Service

```bash
cd cross-encoder-service
chmod +x setup_local_model.sh
./setup_local_model.sh
```

## Model Information

### Embedding Model (BAAI/bge-m3)
- **Size**: ~2.3GB
- **Purpose**: Text embedding generation
- **Local path**: `embedding-service/models/embedding_model/`

### Cross-Encoder Model (cross-encoder/ms-marco-MiniLM-L-12-v2)
- **Size**: ~90MB
- **Purpose**: Query-document relevance scoring
- **Local path**: `cross-encoder-service/models/cross_encoder_model/`

## Environment Variables

Both services support these environment variables:

- `USE_LOCAL_MODEL`: Set to `true` to use local models (default: `true`)
- `LOCAL_MODEL_PATH`: Path to local model directory
- `EMBEDDING_MODEL` / `CROSS_ENCODER_MODEL`: Fallback Hugging Face model name

## Docker Usage

### Option 1: Use Pre-downloaded Models (Recommended)

1. Download models locally first:
   ```bash
   ./setup_local_models.sh
   ```

2. Start services with docker-compose:
   ```bash
   docker-compose up embedding-service cross-encoder-service
   ```

### Option 2: Download Models in Docker Build

Uncomment the download lines in the Dockerfiles to download models during build:

```dockerfile
# Uncomment these lines in Dockerfile:
COPY download_model.py .
RUN python download_model.py
```

Then build:
```bash
docker-compose build embedding-service cross-encoder-service
```

## Manual Model Download

You can also download models manually using Python:

```python
# For embedding model
from sentence_transformers import SentenceTransformer
model = SentenceTransformer("BAAI/bge-m3")
model.save("./embedding-service/models/embedding_model")

# For cross-encoder model
from sentence_transformers import CrossEncoder
model = CrossEncoder("cross-encoder/ms-marco-MiniLM-L-12-v2")
model.save("./cross-encoder-service/models/cross_encoder_model")
```

## Troubleshooting

### Model Not Found Error
- Ensure models are downloaded: `./setup_local_models.sh`
- Check model paths exist:
  - `embedding-service/models/embedding_model/`
  - `cross-encoder-service/models/cross_encoder_model/`

### Permission Issues
- Make setup scripts executable: `chmod +x *.sh`
- Ensure Docker has access to model directories

### Large Model Size
- Embedding model is ~2.3GB - ensure sufficient disk space
- Consider using .dockerignore to exclude models from build context if needed

### Fallback Behavior
- If local models aren't found, services will fallback to downloading from Hugging Face
- Set `USE_LOCAL_MODEL=false` to force Hugging Face download

## File Structure

```
├── embedding-service/
│   ├── models/
│   │   └── embedding_model/          # Local embedding model
│   ├── download_model.py             # Model download script
│   ├── setup_local_model.sh          # Setup script
│   └── main.py                       # Updated service code
├── cross-encoder-service/
│   ├── models/
│   │   └── cross_encoder_model/      # Local cross-encoder model
│   ├── download_model.py             # Model download script
│   ├── setup_local_model.sh          # Setup script
│   └── main.py                       # Updated service code
├── setup_local_models.sh             # Master setup script
└── docker-compose.yml                # Updated with local model config
```

## Performance Notes

- Local models typically load 2-3x faster than downloading from Hugging Face
- First-time download may take 10-30 minutes depending on internet speed
- Models are cached and reused across container restarts
- Memory usage remains the same as before
