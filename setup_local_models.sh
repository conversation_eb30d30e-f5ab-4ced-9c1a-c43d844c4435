#!/bin/bash
# Master setup script for downloading models locally for both services

set -e

echo "=== Setting up local models for embedding and cross-encoder services ==="

# Setup embedding service model
echo ""
echo "1. Setting up embedding service local model..."
cd embedding-service
chmod +x setup_local_model.sh
./setup_local_model.sh
cd ..

# Setup cross-encoder service model
echo ""
echo "2. Setting up cross-encoder service local model..."
cd cross-encoder-service
chmod +x setup_local_model.sh
./setup_local_model.sh
cd ..

echo ""
echo "=== Local model setup complete! ==="
echo ""
echo "Models downloaded to:"
echo "  - embedding-service/models/embedding_model"
echo "  - cross-encoder-service/models/cross_encoder_model"
echo ""
echo "To use local models:"
echo "  - Set USE_LOCAL_MODEL=true in environment"
echo "  - Or use the updated Docker containers"
echo ""
echo "Model sizes:"
echo "  - Embedding model (BAAI/bge-m3): ~2.3GB"
echo "  - Cross-encoder model (cross-encoder/ms-marco-MiniLM-L-12-v2): ~90MB"
