#!/bin/bash

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

GATEKEEPER_URL="https://gatekeeper-staging.getbeyondhealth.com/auth/august"
TEST_PHONE="+91**********"

echo -e "${BLUE}🔐 Testing Gatekeeper API with cURL${NC}\n"
echo -e "${CYAN}Base URL: $GATEKEEPER_URL${NC}"
echo -e "${CYAN}Test Phone: $TEST_PHONE${NC}\n"

# Test 1: Basic connectivity
echo -e "${YELLOW}1️⃣ Testing API Connectivity...${NC}"
echo -e "${CYAN}Command: curl -I \"$GATEKEEPER_URL\"${NC}\n"

curl -I "$GATEKEEPER_URL" 2>/dev/null | head -n 5
echo ""

# Test 2: Request OTP with proper JSON
echo -e "${YELLOW}2️⃣ Testing OTP Request Endpoint...${NC}"
echo -e "${CYAN}Command: curl -X POST \"$GATEKEEPER_URL/request-otp\" \\
  -H \"Content-Type: application/json\" \\
  -d '{\"phoneNumber\":\"$TEST_PHONE\"}'${NC}\n"

echo "Response:"
response=$(curl -s -w "\n\nHTTP Status: %{http_code}\nResponse Time: %{time_total}s\n" \
  -X POST "$GATEKEEPER_URL/request-otp" \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d "{\"phoneNumber\":\"$TEST_PHONE\"}")

echo "$response"

# Extract just the JSON part
json_response=$(echo "$response" | sed -n '1,/^HTTP Status:/p' | sed '$d')

# Try to parse and format the JSON
if command -v jq &> /dev/null; then
    echo -e "\n${CYAN}Formatted JSON Response:${NC}"
    echo "$json_response" | jq '.' 2>/dev/null || echo "$json_response"
else
    echo -e "\n${CYAN}Raw Response:${NC}"
    echo "$json_response"
fi

# Test 3: Verbose request to see all headers
echo -e "\n${YELLOW}3️⃣ Detailed Request with Headers...${NC}"
echo -e "${CYAN}Making verbose request to see all details...${NC}\n"

curl -v -X POST "$GATEKEEPER_URL/request-otp" \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -H "User-Agent: Doctor-Dashboard-Test/1.0" \
  -d "{\"phoneNumber\":\"$TEST_PHONE\"}" 2>&1 | grep -E "(< |> |{|})"

# Test 4: Try different variations
echo -e "\n${YELLOW}4️⃣ Testing Different Request Variations...${NC}"

# Test with phone instead of phoneNumber
echo -e "\n${CYAN}Test: Using 'phone' instead of 'phoneNumber'${NC}"
curl -s -X POST "$GATEKEEPER_URL/request-otp" \
  -H "Content-Type: application/json" \
  -d '{"phone":"'$TEST_PHONE'"}'

# Test with mobile_number
echo -e "\n\n${CYAN}Test: Using 'mobile_number'${NC}"
curl -s -X POST "$GATEKEEPER_URL/request-otp" \
  -H "Content-Type: application/json" \
  -d '{"mobile_number":"'$TEST_PHONE'"}'

# Test with just the number without country code
echo -e "\n\n${CYAN}Test: Without country code${NC}"
curl -s -X POST "$GATEKEEPER_URL/request-otp" \
  -H "Content-Type: application/json" \
  -d '{"phoneNumber":"**********"}'

# Test 5: Check other endpoints
echo -e "\n\n${YELLOW}5️⃣ Checking Other Endpoints...${NC}"

endpoints=(
    "GET:/"
    "GET:/health"
    "GET:/api/health"
    "GET:/verify-otp"
    "OPTIONS:/request-otp"
)

for endpoint in "${endpoints[@]}"; do
    IFS=':' read -r method path <<< "$endpoint"
    echo -e "\n${CYAN}Testing $method $GATEKEEPER_URL$path${NC}"
    curl -s -o /dev/null -w "Status: %{http_code}\n" -X "$method" "$GATEKEEPER_URL$path"
done

echo -e "\n${BLUE}✨ Gatekeeper API Test Complete!${NC}\n"