const jwt = require('jsonwebtoken');

// Generate a test JWT token
const testUser = {
  id: "test-user-123",
  phone: "+919819304846",
  name: "Test Doctor",
  role: "doctor",
  tenant_id: "5005",
  specialization: "Internal Medicine",
  organization: "Test Hospital",
  license_number: "MD123456"
};

// Use a test secret (in production, use process.env.JWT_SECRET)
const secret = process.env.JWT_SECRET || 'test-jwt-secret-for-development';

// Generate token with 24 hour expiry
const token = jwt.sign(
  {
    id: testUser.id,
    phone: testUser.phone,
    role: testUser.role,
    tenant_id: testUser.tenant_id
  },
  secret,
  { 
    expiresIn: '24h',
    issuer: 'doctor-dashboard',
    audience: 'doctor-dashboard-api'
  }
);

// Also generate a refresh token
const refreshToken = jwt.sign(
  {
    id: testUser.id,
    phone: testUser.phone,
    type: 'refresh'
  },
  secret,
  { 
    expiresIn: '7d',
    issuer: 'doctor-dashboard'
  }
);

console.log('Test JWT Generated Successfully!\n');
console.log('=================================');
console.log('User Details:');
console.log('=================================');
console.log('ID:', testUser.id);
console.log('Phone:', testUser.phone);
console.log('Name:', testUser.name);
console.log('Role:', testUser.role);
console.log('\n=================================');
console.log('Access Token (JWT):');
console.log('=================================');
console.log(token);
console.log('\n=================================');
console.log('Refresh Token:');
console.log('=================================');
console.log(refreshToken);
console.log('\n=================================');
console.log('Token Details:');
console.log('=================================');

// Decode to show expiry
const decoded = jwt.decode(token);
console.log('Expires at:', new Date(decoded.exp * 1000).toLocaleString());
console.log('Issued at:', new Date(decoded.iat * 1000).toLocaleString());

console.log('\n=================================');
console.log('How to use:');
console.log('=================================');
console.log('1. Copy the access token above');
console.log('2. Use it in API requests with header:');
console.log('   Authorization: Bearer ' + token.substring(0, 20) + '...');
console.log('\n3. Or set in browser console:');
console.log("   localStorage.setItem('access_token', '<paste-token-here>');");
console.log("   localStorage.setItem('authToken', '<paste-token-here>');");
console.log("   localStorage.setItem('user', '" + JSON.stringify(testUser) + "');");
console.log("   window.location.reload();");