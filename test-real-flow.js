const puppeteer = require('puppeteer');
const readline = require('readline');

// Create readline interface for OTP input
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

function askQuestion(question) {
  return new Promise((resolve) => {
    rl.question(question, (answer) => {
      resolve(answer);
    });
  });
}

async function testCompleteFlow() {
  console.log('\n🏥 Doctor Dashboard Complete Flow Test\n');
  console.log('This test will walk through the entire application.');
  console.log('You will need to enter the real OTP when prompted.\n');
  
  const browser = await puppeteer.launch({
    headless: false, // Show browser for manual OTP entry
    slowMo: 100,
    args: ['--no-sandbox', '--disable-setuid-sandbox'],
    defaultViewport: { width: 1440, height: 900 }
  });
  
  try {
    const page = await browser.newPage();
    
    // Monitor console and API calls
    page.on('console', msg => {
      if (msg.type() === 'error') {
        console.log('❌ Console error:', msg.text());
      }
    });
    
    page.on('response', response => {
      if (response.url().includes('/api/')) {
        console.log(`📡 API: ${response.status()} ${response.url()}`);
      }
    });
    
    // Phase 1: Login
    console.log('📱 Phase 1: Authentication\n');
    
    console.log('1. Navigating to application...');
    await page.goto('http://localhost:3000', { waitUntil: 'networkidle0' });
    await page.screenshot({ path: 'test-screenshots/flow-1-home.png' });
    
    console.log('2. Entering phone number...');
    await page.type('input[placeholder*="98193"]', '+************');
    await page.screenshot({ path: 'test-screenshots/flow-2-phone.png' });
    
    console.log('3. Requesting OTP...');
    await page.click('button[type="submit"]');
    
    // Wait for OTP field
    await page.waitForSelector('input[maxlength="6"]', { timeout: 10000 });
    console.log('✅ OTP screen loaded');
    await page.screenshot({ path: 'test-screenshots/flow-3-otp-screen.png' });
    
    // Get OTP from user
    const otp = await askQuestion('\n📲 Please enter the OTP you received: ');
    
    console.log('4. Entering OTP...');
    await page.type('input[maxlength="6"]', otp);
    await page.screenshot({ path: 'test-screenshots/flow-4-otp-entered.png' });
    
    console.log('5. Submitting OTP...');
    await page.click('button[type="submit"]');
    
    // Wait for dashboard to load
    console.log('Waiting for dashboard to load...');
    await page.waitForTimeout(3000);
    
    // Check if we're logged in by looking for dashboard elements
    const isDashboard = await page.evaluate(() => {
      return document.body.textContent.includes('Patient Cases') || 
             document.body.textContent.includes('Research') ||
             document.body.textContent.includes('Quick Facts');
    });
    
    if (!isDashboard) {
      console.log('❌ Login failed. Please check OTP and try again.');
      await page.screenshot({ path: 'test-screenshots/flow-error-login.png' });
      return;
    }
    
    console.log('✅ Successfully logged in!\n');
    await page.screenshot({ path: 'test-screenshots/flow-5-dashboard.png' });
    
    // Phase 2: Test Dashboard Features
    console.log('🏠 Phase 2: Dashboard Features\n');
    
    // Check layout
    console.log('Checking dashboard layout...');
    const elements = await page.evaluate(() => {
      return {
        sidebar: !!document.querySelector('aside, [class*="sidebar"], [class*="drawer"]'),
        mainContent: !!document.querySelector('main, [class*="main"]'),
        messageInput: !!document.querySelector('textarea'),
        threadTypes: ['Patient Cases', 'Research', 'Quick Facts'].filter(type => 
          document.body.textContent.includes(type)
        )
      };
    });
    
    console.log('Dashboard elements:');
    console.log(`  Sidebar: ${elements.sidebar ? '✅' : '❌'}`);
    console.log(`  Main content: ${elements.mainContent ? '✅' : '❌'}`);
    console.log(`  Message input: ${elements.messageInput ? '✅' : '❌'}`);
    console.log(`  Thread types: ${elements.threadTypes.join(', ') || 'None found'}`);
    
    // Phase 3: Test Messaging
    console.log('\n💬 Phase 3: Testing Messaging\n');
    
    const messageInput = await page.$('textarea');
    if (messageInput) {
      console.log('1. Sending a test message...');
      await messageInput.type('What is the normal blood pressure range for adults?');
      await page.screenshot({ path: 'test-screenshots/flow-6-message-typed.png' });
      
      // Send message
      await page.keyboard.press('Enter');
      console.log('Message sent, waiting for response...');
      
      // Wait for AI response
      await page.waitForTimeout(5000);
      await page.screenshot({ path: 'test-screenshots/flow-7-message-response.png' });
      
      // Count messages
      const messageCount = await page.evaluate(() => {
        const messages = document.querySelectorAll('[role="article"], .message, [class*="message"]');
        return messages.length;
      });
      
      console.log(`✅ Conversation has ${messageCount} messages`);
    }
    
    // Phase 4: Test Patient Cases
    console.log('\n🏥 Phase 4: Testing Patient Cases\n');
    
    // Click on Patient Cases if available
    const patientCasesButton = await page.evaluateHandle(() => {
      const buttons = Array.from(document.querySelectorAll('button, a, div[role="button"]'));
      return buttons.find(el => el.textContent.includes('Patient Cases'));
    });
    
    if (patientCasesButton && await patientCasesButton.evaluate(el => el !== null)) {
      console.log('1. Navigating to Patient Cases...');
      await patientCasesButton.click();
      await page.waitForTimeout(2000);
      await page.screenshot({ path: 'test-screenshots/flow-8-patient-cases.png' });
      
      // Try to create a new case
      const newButton = await page.evaluateHandle(() => {
        const buttons = Array.from(document.querySelectorAll('button'));
        return buttons.find(btn => 
          btn.textContent.toLowerCase().includes('new') || 
          btn.textContent.toLowerCase().includes('create')
        );
      });
      
      if (newButton && await newButton.evaluate(el => el !== null)) {
        console.log('2. Creating new patient case...');
        await newButton.click();
        await page.waitForTimeout(1000);
        await page.screenshot({ path: 'test-screenshots/flow-9-new-case.png' });
      }
    }
    
    // Phase 5: Test Research
    console.log('\n🔬 Phase 5: Testing Research\n');
    
    const researchButton = await page.evaluateHandle(() => {
      const buttons = Array.from(document.querySelectorAll('button, a, div[role="button"]'));
      return buttons.find(el => el.textContent === 'Research');
    });
    
    if (researchButton && await researchButton.evaluate(el => el !== null)) {
      console.log('1. Navigating to Research...');
      await researchButton.click();
      await page.waitForTimeout(2000);
      await page.screenshot({ path: 'test-screenshots/flow-10-research.png' });
      
      // Send a research query
      const input = await page.$('textarea');
      if (input) {
        console.log('2. Sending research query...');
        await input.click({ clickCount: 3 });
        await input.type('Latest advances in CRISPR gene therapy for sickle cell disease');
        await page.keyboard.press('Enter');
        
        await page.waitForTimeout(5000);
        await page.screenshot({ path: 'test-screenshots/flow-11-research-response.png' });
      }
    }
    
    // Phase 6: Test Quick Facts
    console.log('\n💊 Phase 6: Testing Quick Facts\n');
    
    const quickFactsButton = await page.evaluateHandle(() => {
      const buttons = Array.from(document.querySelectorAll('button, a, div[role="button"]'));
      return buttons.find(el => el.textContent === 'Quick Facts');
    });
    
    if (quickFactsButton && await quickFactsButton.evaluate(el => el !== null)) {
      console.log('1. Navigating to Quick Facts...');
      await quickFactsButton.click();
      await page.waitForTimeout(2000);
      await page.screenshot({ path: 'test-screenshots/flow-12-quick-facts.png' });
      
      // Test quick fact queries
      const input = await page.$('textarea');
      if (input) {
        console.log('2. Testing drug dosage query...');
        await input.click({ clickCount: 3 });
        await input.type('Metformin dosage for type 2 diabetes');
        await page.keyboard.press('Enter');
        await page.waitForTimeout(3000);
        await page.screenshot({ path: 'test-screenshots/flow-13-drug-dosage.png' });
        
        console.log('3. Testing lab value query...');
        await input.click({ clickCount: 3 });
        await input.type('Normal HbA1c range');
        await page.keyboard.press('Enter');
        await page.waitForTimeout(3000);
        await page.screenshot({ path: 'test-screenshots/flow-14-lab-value.png' });
      }
    }
    
    // Final summary
    console.log('\n📊 Test Summary\n');
    console.log('✅ Authentication flow tested');
    console.log('✅ Dashboard navigation tested');
    console.log('✅ Message sending tested');
    console.log('✅ Thread types tested');
    console.log('✅ Screenshots captured for each step');
    
    console.log('\n📸 Screenshots saved in test-screenshots/');
    console.log('\n✨ Test complete! Browser will remain open for manual exploration.');
    console.log('Press Ctrl+C to exit when done.\n');
    
    // Keep browser open for manual testing
    await new Promise(() => {}); // This will keep the process running
    
  } catch (error) {
    console.error('\n❌ Test error:', error.message);
    await page.screenshot({ path: 'test-screenshots/flow-error.png' });
  } finally {
    rl.close();
  }
}

// Handle Ctrl+C gracefully
process.on('SIGINT', () => {
  console.log('\n\n👋 Test terminated by user');
  process.exit(0);
});

// Run the test
testCompleteFlow();