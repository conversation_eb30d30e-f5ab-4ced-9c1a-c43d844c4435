/**
 * Simple Doctor Dashboard API Test Script
 * 
 * Tests all authentication endpoints with curl-like functionality
 * Run with: node test-api-simple.js
 */

const http = require('http');
const https = require('https');
const { URL } = require('url');

const API_BASE = 'http://localhost:3000/api';

// Test data
const TEST_DOCTOR = {
  phone: '+**********',
  name: 'Dr. <PERSON>',
  organization: 'General Hospital',
  license_number: 'MD123456789',
  specialization: 'Internal Medicine'
};

const TEST_ADMIN_PHONE = '+**********';

// Simple HTTP client
function makeRequest(url, options = {}) {
  return new Promise((resolve, reject) => {
    const parsedUrl = new URL(url);
    const requestOptions = {
      hostname: parsedUrl.hostname,
      port: parsedUrl.port,
      path: parsedUrl.pathname,
      method: options.method || 'GET',
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      }
    };

    const client = parsedUrl.protocol === 'https:' ? https : http;
    
    const req = client.request(requestOptions, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          const parsedData = JSON.parse(data);
          resolve({
            status: res.statusCode,
            success: res.statusCode >= 200 && res.statusCode < 300,
            data: parsedData,
            headers: res.headers
          });
        } catch (error) {
          resolve({
            status: res.statusCode,
            success: false,
            data: data,
            error: 'Failed to parse JSON response'
          });
        }
      });
    });
    
    req.on('error', (error) => {
      reject({
        success: false,
        error: error.message
      });
    });
    
    if (options.body) {
      req.write(JSON.stringify(options.body));
    }
    
    req.end();
  });
}

// Helper functions
function log(message, data = null) {
  console.log(`\n📝 ${message}`);
  if (data) {
    console.log(JSON.stringify(data, null, 2));
  }
}

function logError(message, error) {
  console.log(`\n❌ ${message}`);
  console.log(JSON.stringify(error, null, 2));
}

function logSuccess(message, data = null) {
  console.log(`\n✅ ${message}`);
  if (data) {
    console.log(JSON.stringify(data, null, 2));
  }
}

function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

// Test functions
async function testHealthCheck() {
  log('🏥 Testing Health Check');
  
  try {
    const response = await makeRequest(`${API_BASE}/health`);
    
    if (response.success && response.data.success) {
      logSuccess('Health check passed');
      log('Database Status:', {
        connected: response.data.database?.connected,
        version: response.data.database?.version?.substring(0, 50) + '...'
      });
      return true;
    } else {
      logError('Health check failed', response);
      return false;
    }
  } catch (error) {
    logError('Health check error', error);
    return false;
  }
}

async function testRequestOTP() {
  log('📱 Testing Request OTP');
  
  try {
    // Test valid phone number
    const response = await makeRequest(`${API_BASE}/auth/request-otp`, {
      method: 'POST',
      body: { phone: TEST_DOCTOR.phone }
    });
    
    if (response.success) {
      logSuccess('OTP request successful', response.data);
    } else {
      logError('OTP request failed', response);
    }
    
    // Test invalid phone number
    log('Testing invalid phone number format');
    const invalidResponse = await makeRequest(`${API_BASE}/auth/request-otp`, {
      method: 'POST',
      body: { phone: 'invalid' }
    });
    
    if (invalidResponse.status === 400) {
      logSuccess('Invalid phone validation working correctly');
    } else {
      logError('Should reject invalid phone numbers', invalidResponse);
    }
    
    return true;
  } catch (error) {
    logError('Request OTP error', error);
    return false;
  }
}

async function testRegistration() {
  log('👨‍⚕️ Testing Doctor Registration');
  
  try {
    // Test with missing fields
    log('Testing registration with missing fields');
    const missingFieldsResponse = await makeRequest(`${API_BASE}/auth/verify-and-register`, {
      method: 'POST',
      body: {
        phone: TEST_DOCTOR.phone,
        otp: '123456'
        // Missing required fields
      }
    });
    
    if (missingFieldsResponse.status === 400) {
      logSuccess('Missing fields validation working correctly');
    } else {
      logError('Should reject missing required fields', missingFieldsResponse);
    }
    
    // Test with all fields but invalid OTP
    log('Testing registration with invalid OTP (expected to fail)');
    const response = await makeRequest(`${API_BASE}/auth/verify-and-register`, {
      method: 'POST',
      body: {
        phone: TEST_DOCTOR.phone,
        otp: '123456', // Invalid OTP
        name: TEST_DOCTOR.name,
        organization: TEST_DOCTOR.organization,
        license_number: TEST_DOCTOR.license_number,
        specialization: TEST_DOCTOR.specialization
      }
    });
    
    if (response.data?.code === 'OTP_INVALID') {
      logSuccess('OTP validation working correctly');
    } else {
      logError('Expected OTP validation to fail', response);
    }
    
    return true;
  } catch (error) {
    logError('Registration test error', error);
    return false;
  }
}

async function testLogin() {
  log('🔐 Testing Login Flow');
  
  try {
    // Test login for non-existent user
    log('Testing login for non-existent user');
    const nonExistentResponse = await makeRequest(`${API_BASE}/auth/login`, {
      method: 'POST',
      body: { phone: '+**********' }
    });
    
    if (nonExistentResponse.status === 404) {
      logSuccess('Non-existent user validation working correctly');
    } else {
      logError('Should reject non-existent users', nonExistentResponse);
    }
    
    // Test login for admin (existing user)
    log('Testing login for admin user');
    const adminLoginResponse = await makeRequest(`${API_BASE}/auth/login`, {
      method: 'POST',
      body: { phone: TEST_ADMIN_PHONE }
    });
    
    if (adminLoginResponse.success) {
      logSuccess('Admin login OTP request successful', adminLoginResponse.data);
    } else {
      logError('Admin login failed', adminLoginResponse);
    }
    
    // Test login verification with invalid OTP
    log('Testing login verification with invalid OTP');
    const verifyResponse = await makeRequest(`${API_BASE}/auth/verify-login`, {
      method: 'POST',
      body: {
        phone: TEST_ADMIN_PHONE,
        otp: '123456' // Invalid OTP
      }
    });
    
    if (verifyResponse.data?.code === 'OTP_INVALID') {
      logSuccess('Login OTP validation working correctly');
    } else {
      logError('Expected login OTP validation to fail', verifyResponse);
    }
    
    return true;
  } catch (error) {
    logError('Login test error', error);
    return false;
  }
}

async function testProtectedEndpoints() {
  log('🔒 Testing Protected Endpoints');
  
  try {
    // Test /auth/me without token
    log('Testing /auth/me without token');
    const meResponse = await makeRequest(`${API_BASE}/auth/me`);
    
    if (meResponse.status === 401) {
      logSuccess('Protected endpoint correctly rejects requests without token');
    } else {
      logError('Should reject requests without token', meResponse);
    }
    
    // Test with invalid token
    log('Testing /auth/me with invalid token');
    const invalidTokenResponse = await makeRequest(`${API_BASE}/auth/me`, {
      headers: { 'Authorization': 'Bearer invalid-token' }
    });
    
    if (invalidTokenResponse.status === 401) {
      logSuccess('Protected endpoint correctly rejects invalid tokens');
    } else {
      logError('Should reject invalid tokens', invalidTokenResponse);
    }
    
    return true;
  } catch (error) {
    logError('Protected endpoints test error', error);
    return false;
  }
}

async function testAdminEndpoints() {
  log('👔 Testing Admin Endpoints');
  
  try {
    // Test pending registrations without token
    log('Testing admin endpoint without token');
    const pendingResponse = await makeRequest(`${API_BASE}/admin/pending-registrations`);
    
    if (pendingResponse.status === 401) {
      logSuccess('Admin endpoint correctly rejects requests without token');
    } else {
      logError('Should reject admin requests without token', pendingResponse);
    }
    
    // Test review registration without token
    log('Testing review registration without token');
    const reviewResponse = await makeRequest(`${API_BASE}/admin/review-registration`, {
      method: 'POST',
      body: {
        user_id: 'test-user',
        action: 'approve',
        notes: 'Test approval'
      }
    });
    
    if (reviewResponse.status === 401) {
      logSuccess('Admin review endpoint correctly rejects requests without token');
    } else {
      logError('Should reject admin review requests without token', reviewResponse);
    }
    
    return true;
  } catch (error) {
    logError('Admin endpoints test error', error);
    return false;
  }
}

async function testRefreshToken() {
  log('🔄 Testing Refresh Token');
  
  try {
    // Test refresh without token
    const response = await makeRequest(`${API_BASE}/auth/refresh`, {
      method: 'POST',
      body: { refresh_token: 'invalid-token' }
    });
    
    if (response.status === 401) {
      logSuccess('Refresh endpoint correctly rejects invalid tokens');
    } else {
      logError('Should reject invalid refresh tokens', response);
    }
    
    return true;
  } catch (error) {
    logError('Refresh token test error', error);
    return false;
  }
}

async function testLogout() {
  log('🚪 Testing Logout');
  
  try {
    // Test logout without token
    const response = await makeRequest(`${API_BASE}/auth/logout`, {
      method: 'POST'
    });
    
    if (response.status === 401) {
      logSuccess('Logout endpoint correctly requires authentication');
    } else {
      logError('Should require authentication for logout', response);
    }
    
    return true;
  } catch (error) {
    logError('Logout test error', error);
    return false;
  }
}

// Main test runner
async function runAllTests() {
  console.log('🚀 Doctor Dashboard API Test Suite');
  console.log('='.repeat(60));
  console.log('This script tests all authentication endpoints');
  console.log('Note: OTP-based tests will fail with mock codes (expected behavior)');
  console.log('='.repeat(60));
  
  const tests = [
    { name: 'Health Check', fn: testHealthCheck },
    { name: 'Request OTP', fn: testRequestOTP },
    { name: 'Registration', fn: testRegistration },
    { name: 'Login', fn: testLogin },
    { name: 'Protected Endpoints', fn: testProtectedEndpoints },
    { name: 'Admin Endpoints', fn: testAdminEndpoints },
    { name: 'Refresh Token', fn: testRefreshToken },
    { name: 'Logout', fn: testLogout }
  ];
  
  let passedTests = 0;
  const totalTests = tests.length;
  
  for (const test of tests) {
    try {
      console.log('\n' + '─'.repeat(50));
      const result = await test.fn();
      if (result) {
        passedTests++;
        logSuccess(`✅ ${test.name} - PASSED`);
      } else {
        logError(`❌ ${test.name} - FAILED`);
      }
    } catch (error) {
      logError(`💥 ${test.name} - ERROR`, { message: error.message });
    }
    
    await sleep(100); // Small delay between tests
  }
  
  // Summary
  console.log('\n' + '='.repeat(60));
  console.log('📊 TEST RESULTS SUMMARY');
  console.log('='.repeat(60));
  console.log(`✅ Passed: ${passedTests}/${totalTests}`);
  console.log(`❌ Failed: ${totalTests - passedTests}/${totalTests}`);
  console.log(`📈 Success Rate: ${Math.round((passedTests / totalTests) * 100)}%`);
  
  if (passedTests === totalTests) {
    console.log('\n🎉 All tests passed! Your authentication API is working correctly.');
    console.log('\n📝 Next steps:');
    console.log('1. Integrate real SMS service for OTP delivery');
    console.log('2. Test complete flow with real OTP codes');
    console.log('3. Integrate with your frontend authentication');
  } else {
    console.log('\n⚠️  Some tests failed. Check the output above for details.');
  }
  
  console.log('\n🔧 To test with real authentication flow:');
  console.log('1. Make sure your server is running: npm run dev');
  console.log('2. Complete the SMS integration');
  console.log('3. Use real phone numbers and OTP codes');
}

// Run the tests
if (require.main === module) {
  runAllTests().catch(console.error);
}

module.exports = { runAllTests };