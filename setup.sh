#!/bin/bash

# Doctor Dashboard - Local Development Setup Script
# For macOS only
# This script sets up PostgreSQL database and environment for the Doctor Dashboard

set -e  # Exit on any error

echo "🏥 Doctor Dashboard - Local Development Setup"
echo "=============================================="
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Helper functions
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check if running on macOS
if [[ "$OSTYPE" != "darwin"* ]]; then
    log_error "This script is designed for macOS only."
    exit 1
fi

log_info "Checking system requirements..."

# Check if Homebrew is installed
if ! command -v brew &> /dev/null; then
    log_error "Homebrew is not installed. Please install it first:"
    echo "Visit: https://brew.sh/"
    exit 1
fi

log_success "Homebrew is installed"

# Check if PostgreSQL is installed
if ! command -v psql &> /dev/null; then
    log_info "PostgreSQL not found. Installing PostgreSQL 14..."
    brew install postgresql@14
    brew services start postgresql@14
    
    # Add PostgreSQL to PATH
    echo 'export PATH="/opt/homebrew/opt/postgresql@14/bin:$PATH"' >> ~/.zshrc
    export PATH="/opt/homebrew/opt/postgresql@14/bin:$PATH"
    
    log_success "PostgreSQL 14 installed and started"
else
    log_success "PostgreSQL is installed"
    
    # Start PostgreSQL service if not running
    if ! brew services list | grep postgresql | grep started &> /dev/null; then
        log_info "Starting PostgreSQL service..."
        brew services start postgresql@14 || brew services start postgresql
    fi
fi

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    log_error "Node.js is not installed. Please install Node.js 18+ first:"
    echo "Visit: https://nodejs.org/"
    exit 1
fi

# Check Node.js version
NODE_VERSION=$(node -v | sed 's/v//')
REQUIRED_VERSION="18.0.0"

if [ "$(printf '%s\n' "$REQUIRED_VERSION" "$NODE_VERSION" | sort -V | head -n1)" != "$REQUIRED_VERSION" ]; then
    log_error "Node.js version $NODE_VERSION is too old. Please install Node.js 18 or higher."
    exit 1
fi

log_success "Node.js version $NODE_VERSION is compatible"

# Get current username for PostgreSQL
USERNAME=$(whoami)

log_info "Setting up PostgreSQL database..."

# Create database if it doesn't exist
if ! psql -lqt | cut -d \| -f 1 | grep -qw doctor_dashboard; then
    log_info "Creating database 'doctor_dashboard'..."
    createdb doctor_dashboard
    log_success "Database 'doctor_dashboard' created"
else
    log_success "Database 'doctor_dashboard' already exists"
fi

# Check if database-setup.sql exists
if [ ! -f "database-setup.sql" ]; then
    log_error "database-setup.sql file not found in current directory"
    log_info "Make sure you're running this script from the project root"
    exit 1
fi

# Run database setup
log_info "Setting up database schema..."
psql -d doctor_dashboard -f database-setup.sql > /dev/null 2>&1

if [ $? -eq 0 ]; then
    log_success "Database schema created successfully"
else
    log_warning "Database schema setup encountered issues (might already exist)"
fi

# Create .env.local if it doesn't exist
if [ ! -f ".env.local" ]; then
    log_info "Creating .env.local file..."
    
    cat > .env.local << EOF
# Database Configuration
DATABASE_URL=postgresql://$USERNAME@localhost:5432/doctor_dashboard
DATABASE_SSL=false

# JWT Configuration
JWT_ACCESS_SECRET=doctor-dashboard-access-secret-key-super-secure-2024
JWT_REFRESH_SECRET=doctor-dashboard-refresh-secret-key-super-secure-2024
JWT_ACCESS_EXPIRES_IN=1h
JWT_REFRESH_EXPIRES_IN=7d

# Development Configuration
NODE_ENV=development
EOF
    
    log_success ".env.local file created"
else
    log_success ".env.local file already exists"
fi

# Install dependencies if node_modules doesn't exist
if [ ! -d "node_modules" ]; then
    log_info "Installing Node.js dependencies..."
    npm install
    log_success "Dependencies installed"
else
    log_success "Node.js dependencies already installed"
fi

# Test database connection
log_info "Testing database connection..."
DB_TEST=$(psql -d doctor_dashboard -c "SELECT 'Database connection successful' as status;" -t 2>/dev/null | xargs)

if [ "$DB_TEST" = "Database connection successful" ]; then
    log_success "Database connection test passed"
else
    log_error "Database connection test failed"
    exit 1
fi

# Check if admin user exists, if not create one
ADMIN_EXISTS=$(psql -d doctor_dashboard -c "SELECT COUNT(*) FROM users WHERE role = 'admin';" -t 2>/dev/null | xargs)

if [ "$ADMIN_EXISTS" = "0" ]; then
    log_info "Creating default admin user..."
    psql -d doctor_dashboard -c "
        INSERT INTO users (id, phone, name, role, access, tenant_id, meta, created_at, updated_at) 
        VALUES (
            'admin-1', 
            '+1234567890', 
            'Admin User', 
            'admin', 
            true, 
            '5005', 
            '{\"organization\": \"System Admin\"}', 
            NOW(), 
            NOW()
        );" > /dev/null 2>&1
    
    log_success "Default admin user created (+1234567890)"
else
    log_success "Admin user already exists"
fi

echo ""
echo "🎉 Setup completed successfully!"
echo ""
echo "📋 Next steps:"
echo "1. Start the development server:"
echo "   ${GREEN}npm run dev${NC}"
echo ""
echo "2. Open your browser and go to:"
echo "   ${BLUE}http://localhost:3000${NC}"
echo ""
echo "3. Test the application:"
echo "   • Register: Use any real phone number"
echo "   • Test Login: Use +919819304846 with OTP: 123456"
echo "   • Admin Login: Use +1234567890"
echo ""
echo "📚 Useful commands:"
echo "• Database shell: ${YELLOW}psql -d doctor_dashboard${NC}"
echo "• View logs: Check console output when running npm run dev"
echo "• Test API: Use the test scripts in the project"
echo ""
echo "🔧 Database details:"
echo "• Database: doctor_dashboard"
echo "• User: $USERNAME"
echo "• Host: localhost"
echo "• Port: 5432"
echo ""
echo "⚠️  Note: This setup uses Gatekeeper staging for OTP delivery"
echo "Real SMS messages will be sent to phone numbers you test with!"
echo ""
log_success "Doctor Dashboard is ready for development! 🚀"