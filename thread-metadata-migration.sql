\n-- Thread Metadata Migration\n-- Adds tables to store thread information and metadata\n\n-- Enable UUID extension if not already enabled\nCREATE EXTENSION IF NOT EXISTS \"uuid-ossp\";\n\n-- Threads table to store thread metadata\nCREATE TABLE IF NOT EXISTS threads (\n    id varchar(255) PRIMARY KEY,  -- Uses dialogue ID from Gatekeeper\n    user_id text NOT NULL,        -- References users.id\n    title text,\n    type varchar(50) NOT NULL,    -- 'patient-case', 'research', 'quick-facts'\n    status varchar(50) DEFAULT 'active', -- 'active', 'completed', 'archived'\n    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,\n    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,\n    last_message_at timestamp with time zone,\n    metadata jsonb DEFAULT '{}'::jsonb,\n    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE\n);\n\n-- Messages table to store message history (optional but useful)\nCREATE TABLE IF NOT EXISTS messages (\n    id varchar(255) PRIMARY KEY,\n    thread_id varchar(255) NOT NULL,\n    sender varchar(20) NOT NULL,  -- 'user' or 'ai'\n    content text,\n    attachments jsonb DEFAULT '[]'::jsonb,\n    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,\n    metadata jsonb DEFAULT '{}'::jsonb,\n    FOREIGN KEY (thread_id) REFERENCES threads(id) ON DELETE CASCADE\n);\n\n-- Attachments table to store file metadata\nCREATE TABLE IF NOT EXISTS attachments (\n    id uuid PRIMARY KEY DEFAULT uuid_generate_v4(),\n    message_id varchar(255),\n    thread_id varchar(255) NOT NULL,\n    user_id text NOT NULL,\n    filename text NOT NULL,\n    original_filename text NOT NULL,\n    file_size bigint,\n    file_type varchar(100),\n    file_category varchar(50), -- 'image', 'pdf', 'document', etc.\n    storage_url text,\n    storage_path text,\n    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,\n    FOREIGN KEY (message_id) REFERENCES messages(id) ON DELETE SET NULL,\n    FOREIGN KEY (thread_id) REFERENCES threads(id) ON DELETE CASCADE,\n    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE\n);\n\n-- Create indexes for better performance\nCREATE INDEX IF NOT EXISTS idx_threads_user_id ON threads(user_id);\nCREATE INDEX IF NOT EXISTS idx_threads_type ON threads(type);\nCREATE INDEX IF NOT EXISTS idx_threads_status ON threads(status);\nCREATE INDEX IF NOT EXISTS idx_threads_created_at ON threads(created_at);\nCREATE INDEX IF NOT EXISTS idx_threads_updated_at ON threads(updated_at);\nCREATE INDEX IF NOT EXISTS idx_threads_last_message_at ON threads(last_message_at);\n\nCREATE INDEX IF NOT EXISTS idx_messages_thread_id ON messages(thread_id);\nCREATE INDEX IF NOT EXISTS idx_messages_sender ON messages(sender);\nCREATE INDEX IF NOT EXISTS idx_messages_created_at ON messages(created_at);\n\nCREATE INDEX IF NOT EXISTS idx_attachments_message_id ON attachments(message_id);\nCREATE INDEX IF NOT EXISTS idx_attachments_thread_id ON attachments(thread_id);\nCREATE INDEX IF NOT EXISTS idx_attachments_user_id ON attachments(user_id);\nCREATE INDEX IF NOT EXISTS idx_attachments_created_at ON attachments(created_at);\n\n-- Create trigger to automatically update updated_at on threads table\nCREATE OR REPLACE FUNCTION update_threads_updated_at()\nRETURNS TRIGGER AS $$\nBEGIN\n    NEW.updated_at = CURRENT_TIMESTAMP;\n    RETURN NEW;\nEND;\n$$ language 'plpgsql';\n\nCREATE TRIGGER IF NOT EXISTS update_threads_updated_at \n    BEFORE UPDATE ON threads\n    FOR EACH ROW EXECUTE FUNCTION update_threads_updated_at();\n\n-- Function to update thread's last_message_at when messages are added\nCREATE OR REPLACE FUNCTION update_thread_last_message()\nRETURNS TRIGGER AS $$\nBEGIN\n    UPDATE threads \n    SET last_message_at = NEW.created_at,\n        updated_at = CURRENT_TIMESTAMP\n    WHERE id = NEW.thread_id;\n    RETURN NEW;\nEND;\n$$ language 'plpgsql';\n\nCREATE TRIGGER IF NOT EXISTS update_thread_last_message \n    AFTER INSERT ON messages\n    FOR EACH ROW EXECUTE FUNCTION update_thread_last_message();\n\nSELECT 'Thread metadata migration completed successfully!' as message;\n
