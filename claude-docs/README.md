# Claude Documentation for Doctor Dashboard E2E Tests

This folder contains comprehensive documentation for running and maintaining the E2E test suite.

## 📚 Documentation Index

### 🚀 Getting Started
1. **[FRESH_START_CHECKLIST.md](./FRESH_START_CHECKLIST.md)** - Start here! Step-by-step checklist for running tests
2. **[QUICK_REFERENCE_CARD.md](./QUICK_REFERENCE_CARD.md)** - Essential commands and quick fixes

### 🔧 Detailed Guides
3. **[E2E_TEST_SUITE_COMPLETE_GUIDE.md](./E2E_TEST_SUITE_COMPLETE_GUIDE.md)** - Comprehensive guide covering everything
4. **[RATE_LIMITING_GUIDE.md](./RATE_LIMITING_GUIDE.md)** - Deep dive into rate limiting issues and Redis management
5. **[TEST_PATTERNS_AND_SELECTORS.md](./TEST_PATTERNS_AND_SELECTORS.md)** - Working patterns, selectors, and code examples

### 🐛 Troubleshooting
6. **[COMMON_TEST_FAILURES_AND_FIXES.md](./COMMON_TEST_FAILURES_AND_FIXES.md)** - Specific errors and their solutions
7. **[MODAL_HANDLING_ISSUES.md](./MODAL_HANDLING_ISSUES.md)** - How we solved the modal problem

### 📖 History & Context
8. **[TEST_EVOLUTION_HISTORY.md](./TEST_EVOLUTION_HISTORY.md)** - How the tests evolved and why

## 🎯 Key Information

### Test Phone Number
```
+************
```
**Never use any other number!**

### Test OTP
```
123456
```

### Critical Command
```bash
# ALWAYS run before tests
node rate-limit-manager.js clear
```

## 📊 Current Test Status

| Test Suite | Status | Tests Passing |
|------------|--------|---------------|
| Authentication | ✅ | 4/4 (100%) |
| Dashboard Navigation | ✅ | 4/4 (100%) |
| Messaging | ✅ | 4/4 (100%) |
| Patient Cases | ✅ | 8/8 (100%) |
| Research | ✅ | 8/8 (100%) |
| Quick Facts | ⚠️ | 9/10 (90%) |

## 🔑 Key Discoveries

1. **No separate pages** - Everything happens at `/` (root)
2. **Thread-based system** - Message content determines thread type
3. **Modal blocks everything** - Just close it with Escape
4. **AI needs time** - Always wait 5+ seconds
5. **Check page text** - Don't rely on specific elements

## 💡 Quick Tips

1. **Rate limiting is the #1 issue** - Always clear Redis first
2. **Run one suite when debugging** - Start with authentication
3. **Use headed mode to see issues** - Add `-H` flag
4. **Close modals, don't fill them** - Press Escape
5. **Be patient with AI** - 5+ second waits

## 🚨 Emergency Procedure

If everything is failing:

```bash
# 1. Stop everything
pkill -f node

# 2. Clear rate limits
node rate-limit-manager.js clear

# 3. Start fresh
npm run dev  # Terminal 1
npm run test:e2e -- -s authentication  # Terminal 2

# 4. If still failing, wait 5 minutes
```

## 📝 Message Templates

These messages trigger specific thread types:

**Patient Case:**
```
Patient Case: 45-year-old male presenting with Type 2 Diabetes. 
HbA1c is 9.5. Research everything about hba1c levels and treatment options.
```

**Research:**
```
Research: Latest treatments for Type 2 Diabetes
```

**Quick Facts:**
```
Quick: Normal blood pressure range for adults
```

## 🛠 Maintenance Notes

- Tests are in `/e2e-test-suites/` directory
- Screenshots saved to `/test-results/screenshots/`
- Reports saved to `/test-results/comprehensive-report-*.json`
- Base test class: `e2e-test-suite.js`
- Test runner: `run-all-tests.js`
- Shell script: `run-e2e-tests.sh`

## 📞 Support

When you need help:
1. Check the error in [COMMON_TEST_FAILURES_AND_FIXES.md](./COMMON_TEST_FAILURES_AND_FIXES.md)
2. Review the patterns in [TEST_PATTERNS_AND_SELECTORS.md](./TEST_PATTERNS_AND_SELECTORS.md)
3. Understand the architecture in [E2E_TEST_SUITE_COMPLETE_GUIDE.md](./E2E_TEST_SUITE_COMPLETE_GUIDE.md)

---

**Remember**: The tests work reliably when you follow the guides. Most issues are rate limiting or timing related!