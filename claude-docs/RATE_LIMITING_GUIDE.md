# Rate Limiting Guide for E2E Tests

## Overview
Rate limiting is the #1 cause of E2E test failures. This guide covers everything about handling rate limits.

## The Problem

### What Happens
1. Gatekeeper API limits OTP requests (likely 3-5 per hour)
2. Each test suite requires authentication
3. Running all tests = 7 authentications
4. Tests fail with: `Error: Too many OTP requests. Please try again later.`

### Error Messages
```json
{
  "success": false,
  "error": "Too many OTP requests. Please try again later.",
  "code": "RATE_LIMITED"
}
```

## The Solution: Redis Rate Limit Manager

### Location
```
/Users/<USER>/projects/doctor-dashboard/rate-limit-manager.js
```

### Basic Usage
```bash
# Check current rate limit status
node rate-limit-manager.js status

# Clear all rate limits for test phone
node rate-limit-manager.js clear

# Monitor in real-time
node rate-limit-manager.js monitor
```

### What It Does
1. Connects to Azure Redis Cache
2. Finds keys matching the test phone (+************)
3. Deletes rate limit keys safely
4. Only affects test phone - never production users

## Integration with E2E Tests

### Automatic Clearing
The test runner (`run-all-tests.js`) automatically:
1. Clears rate limits before starting tests
2. Clears again after every 3 test suites
3. Tracks authentication count

### Manual Override
```bash
# Skip Redis clearing if needed
npm run test:e2e -- -r
```

### In Test Code
```javascript
async clearRateLimits() {
  try {
    console.log('\n🔄 Clearing Redis rate limits...');
    execSync('node rate-limit-manager.js clear', { stdio: 'inherit' });
    this.authTestCount = 0;
    return true;
  } catch (error) {
    console.warn('⚠️  Could not clear rate limits:', error.message);
    return false;
  }
}
```

## Redis Connection Details

### Configuration
- Host: Stored in environment variables
- Port: 6380 (SSL)
- Password: In .env file
- SSL: Required for Azure Redis

### Key Patterns
Rate limit keys follow patterns like:
- `otp_request:phone:+************`
- `otp_phone:+************`
- `otp_ip:127.0.0.1`

## Troubleshooting

### 1. Redis Connection Failed
```bash
Error: Redis connection failed
```
**Solution**: Check .env file has correct Redis credentials

### 2. No Keys Found
```bash
ℹ️  No rate limit keys found to clear
```
**This is OK!** Means no rate limits are active.

### 3. Permission Denied
```bash
Error: EACCES: permission denied
```
**Solution**: Make script executable:
```bash
chmod +x rate-limit-manager.js
```

### 4. Still Getting Rate Limited
**Wait Time**: If clearing doesn't work, wait 5-10 minutes
**Check API**: Gatekeeper may have server-side limits not in Redis

## Best Practices

### 1. Always Clear Before Tests
```bash
# Good practice
node rate-limit-manager.js clear && npm run test:e2e
```

### 2. Use Test Phone Only
```javascript
// Always use this number
const TEST_PHONE = '+************';
```

### 3. Monitor During Tests
In another terminal:
```bash
node rate-limit-manager.js monitor
```

### 4. Space Out Authentications
The test runner automatically spaces out auth-heavy tests:
```javascript
if (i > 0 && i % this.maxAuthBeforeClear === 0) {
  await this.clearRateLimits();
  await new Promise(resolve => setTimeout(resolve, 2000));
}
```

## Emergency Procedures

### If Everything is Rate Limited

1. **Stop all tests**
   ```bash
   # Kill any running tests
   pkill -f "node.*test"
   ```

2. **Clear Redis completely**
   ```bash
   node rate-limit-manager.js clear
   ```

3. **Wait 5 minutes**
   Some limits may be time-based

4. **Test single suite first**
   ```bash
   npm run test:e2e -- -s authentication
   ```

5. **If still failing**
   - Check Gatekeeper API status
   - Verify test phone number
   - Check network connectivity
   - Review error logs

## Integration with CI/CD

### GitHub Actions Example
```yaml
- name: Clear Rate Limits
  run: node rate-limit-manager.js clear
  
- name: Run E2E Tests
  run: npm run test:e2e
  env:
    TEST_PHONE: ${{ secrets.TEST_PHONE }}
    TEST_OTP: ${{ secrets.TEST_OTP }}
```

## Summary

**Key Points**:
1. Rate limiting is managed through Redis
2. Always clear before running tests
3. Test runner handles it automatically
4. Only affects test phone number
5. Safe to use in any environment

**Remember**: When in doubt, run `node rate-limit-manager.js clear`!