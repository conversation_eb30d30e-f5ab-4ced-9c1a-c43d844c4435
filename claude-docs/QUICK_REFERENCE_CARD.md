# E2E Test Suite Quick Reference Card

## Essential Commands

### Before Running Tests
```bash
# 1. Start the app
npm run dev

# 2. Clear rate limits (ALWAYS DO THIS!)
node rate-limit-manager.js clear
```

### Running Tests
```bash
# All tests
npm run test:e2e

# Specific suite
npm run test:e2e -- -s patient-cases
npm run test:e2e -- -s research  
npm run test:e2e -- -s quick-facts

# See browser (headed mode)
npm run test:e2e -- -H

# With cleanup
npm run test:e2e -- -c
```

### Debugging
```bash
# Check rate limit status
node rate-limit-manager.js status

# See screenshots
ls -la test-results/screenshots/

# Check test report
cat test-results/comprehensive-report-*.json | jq
```

## Critical Information

### Test Phone Number
```
+919819304846
```
**NEVER use any other number!**

### Test OTP
```
123456
```

### Base URL
```
http://localhost:3000
```

## Quick Fixes

### "Too many OTP requests"
```bash
node rate-limit-manager.js clear
# Wait 2 minutes
npm run test:e2e
```

### "Cannot find textarea"
```javascript
// Modal might be open - close it
await this.page.keyboard.press('Escape');
await this.utils.waitForTimeout(1000);
```

### "AI response not found"
```javascript
// Wait longer
await this.utils.waitForTimeout(5000); // 5 seconds

// Check broader
const pageText = document.body.textContent?.toLowerCase() || '';
```

### Test hanging/timeout
```bash
# Kill all Node processes
pkill -f node

# Start fresh
node rate-limit-manager.js clear
npm run test:e2e -- -s authentication  # Test one suite
```

## File Locations

### Test Files
```
e2e-test-suites/
├── patient-case-tests.js
├── research-tests.js
├── quick-facts-tests.js
└── error-handling-tests.js
```

### Key Files
```
e2e-test-suite.js          # Base test class
run-all-tests.js           # Test runner
run-e2e-tests.sh           # Shell script
rate-limit-manager.js      # Redis cleaner
```

### Results
```
test-results/
├── screenshots/           # Debug screenshots
└── comprehensive-report-*.json
```

## Test Status (Current)
- ✅ Authentication: 100% (4/4)
- ✅ Dashboard: 100% (4/4)
- ✅ Messaging: 100% (4/4)
- ✅ Patient Cases: 100% (8/8)
- ✅ Research: 100% (8/8)
- ⚠️ Quick Facts: 90% (9/10)

## Emergency Checklist

If everything is failing:

1. ✓ Is app running? (`http://localhost:3000`)
2. ✓ Did you clear Redis? (`node rate-limit-manager.js clear`)
3. ✓ Using correct phone? (`+919819304846`)
4. ✓ Any modals open? (Press Escape)
5. ✓ Wait 5 minutes and try again

## Message Templates

**Patient Case:**
```
Patient Case: 45-year-old male presenting with Type 2 Diabetes. 
HbA1c is 9.5. Research everything about hba1c levels and treatment options.
```

**Research:**
```
Research: Latest treatments for Type 2 Diabetes
```

**Quick Facts:**
```
Quick: Normal blood pressure range for adults
```

## Pro Tips

1. **Always clear Redis first** - It's the #1 issue
2. **Run one suite** when debugging
3. **Use headed mode** to see what's happening
4. **Check screenshots** in test-results/
5. **Close modals** with Escape key
6. **Wait 5+ seconds** for AI responses
7. **Check page text**, not elements

## Need More Help?

See detailed guides in this folder:
- `E2E_TEST_SUITE_COMPLETE_GUIDE.md` - Full documentation
- `RATE_LIMITING_GUIDE.md` - Redis and rate limits
- `MODAL_HANDLING_ISSUES.md` - Modal problems
- `TEST_PATTERNS_AND_SELECTORS.md` - What works/doesn't