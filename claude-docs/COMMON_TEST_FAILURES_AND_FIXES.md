# Common Test Failures and Fixes

## 1. Rate Limiting Failures

### Error
```
Request OTP error: Error: Too many OTP requests. Please try again later.
Status: 429
Code: RATE_LIMITED
```

### Fix
```bash
# Immediate fix
node rate-limit-manager.js clear

# If still failing, wait 5-10 minutes
sleep 300 && node rate-limit-manager.js clear
```

### Prevention
- Test runner auto-clears every 3 suites
- Always use test phone: +919819304846

---

## 2. Authentication Failures

### Error
```
Failed to find phone input
Cannot read properties of null (reading 'click')
```

### Fix
Check the selector:
```javascript
// Correct selector
const phoneInput = await this.page.$('input[placeholder*="98193"]');

// NOT these
// input[type="tel"]  - doesn't work
// input[name="phone"] - doesn't exist
```

### Error
```
OTP field not found after requesting OTP
```

### Fix
Wait for the field to appear:
```javascript
await this.page.waitForSelector('input[maxlength="6"]', { 
  timeout: 10000 
});
```

---

## 3. Modal Handling Failures

### Error
```
Node is either not clickable or not an Element
Could not find Start Conversation button
```

### Fix
Just close the modal:
```javascript
await this.page.keyboard.press('Escape');
await this.utils.waitForTimeout(1000);
```

### Error
```
Message input not found
```

### Fix
Modal might be blocking - close it first:
```javascript
// Close any modal
const modalPresent = await this.page.evaluate(() => {
  const modal = document.querySelector('[role="dialog"]');
  return modal && modal.offsetParent !== null;
});

if (modalPresent) {
  await this.page.keyboard.press('Escape');
}
```

---

## 4. AI Response Detection Failures

### Error
```
AI response does not contain expected medical information
Expected both user message and AI response to be visible
```

### Fix
1. Wait longer:
```javascript
await this.utils.waitForTimeout(5000); // 5 seconds minimum
```

2. Check broader content:
```javascript
const hasResponse = await this.page.evaluate(() => {
  const pageText = document.body.textContent?.toLowerCase() || '';
  return pageText.includes('treatment') || 
         pageText.includes('diabetes') ||
         pageText.includes('approach');
});
```

---

## 5. Navigation Failures

### Error
```
TimeoutError: Navigation timeout of 30000 ms exceeded
```

### Fix
Don't use waitForNavigation for SPAs:
```javascript
// Wrong
await this.page.waitForNavigation();

// Right
await this.page.waitForSelector('textarea', { timeout: 10000 });
```

---

## 6. Selector Failures

### Error
```
Error: :has-text() is not a valid selector
```

### Fix
Use evaluate instead:
```javascript
// Wrong
await this.page.$('button:has-text("Login")');

// Right
await this.page.evaluate(() => {
  const buttons = Array.from(document.querySelectorAll('button'));
  const btn = buttons.find(b => b.textContent?.includes('Login'));
  if (btn) btn.click();
});
```

---

## 7. Thread Detection Failures

### Error
```
Thread not found in sidebar
Thread not marked as [Type]
```

### Fix
1. Wait for sidebar to update:
```javascript
await this.utils.waitForTimeout(2000);
```

2. Check multiple selectors:
```javascript
const sidebar = document.querySelector('aside') ||
                document.querySelector('[class*="sidebar"]') ||
                document.querySelector('nav');
```

---

## 8. File Attachment Failures

### Error
```
File attachment feature not found
```

### Fix
Feature might not be visible until hovering:
```javascript
// Look for multiple possible selectors
const attachButton = await this.page.$(
  'button[aria-label*="attach" i], ' +
  'button[title*="attach" i], ' +
  'label[for*="file" i], ' +
  'button[class*="attach"]'
);
```

---

## 9. Response Speed Test Failures

### Error
```
Waiting failed: 10000ms exceeded
```

### Fix
Make the test more forgiving:
```javascript
try {
  await this.page.waitForFunction(() => {
    const pageText = document.body.textContent?.toLowerCase() || '';
    return pageText.includes('sodium');
  }, { timeout: 10000 });
} catch (e) {
  this.logger.warn('Response timeout - AI may be slow');
  // Don't fail the test
}
```

---

## 10. Console Error (Non-Critical)

### Error
```
Console error: Received `%s` for a non-boolean attribute `%s`.
```

### Fix
This is a React warning, not a test failure. Ignore it or filter console:
```javascript
this.page.on('console', (msg) => {
  if (!msg.text().includes('non-boolean attribute')) {
    console.log(`Console ${msg.type()}: ${msg.text()}`);
  }
});
```

---

## General Debugging Steps

1. **Run in headed mode**
   ```bash
   npm run test:e2e -- -H
   ```

2. **Check screenshots**
   ```bash
   ls -la test-results/screenshots/
   open test-results/screenshots/  # macOS
   ```

3. **Run single test**
   ```bash
   npm run test:e2e -- -s authentication
   ```

4. **Add debug logs**
   ```javascript
   console.log('Current URL:', await this.page.url());
   console.log('Page title:', await this.page.title());
   ```

5. **Take manual screenshot**
   ```javascript
   await this.utils.screenshot('debug-point');
   ```

## If All Else Fails

1. **Clear everything**
   ```bash
   pkill -f node
   node rate-limit-manager.js clear
   rm -rf test-results/*
   ```

2. **Start fresh**
   ```bash
   npm run dev  # In one terminal
   npm run test:e2e -- -s authentication  # In another
   ```

3. **Check basics**
   - Is app running on http://localhost:3000?
   - Can you login manually?
   - Is Redis accessible?
   - Network issues?

Remember: Most failures are due to rate limiting or timing issues!