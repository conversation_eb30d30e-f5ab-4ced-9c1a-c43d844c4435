# Fresh Start Checklist for E2E Tests

Use this checklist when starting a new session or after a break.

## Pre-Flight Check

### 1. Environment Setup
- [ ] Terminal 1: `npm run dev` (app running on http://localhost:3000)
- [ ] Terminal 2: Ready for test commands
- [ ] Check app loads: `curl http://localhost:3000` (should return HTML)

### 2. Clean Slate
```bash
# Kill any hanging processes
pkill -f "node.*test"

# Clear old results
rm -rf test-results/screenshots/*

# Clear rate limits (CRITICAL!)
node rate-limit-manager.js clear
```

### 3. Verify Test Phone
```bash
# Check it's set correctly
echo "Test phone: +919819304846"
echo "Test OTP: 123456"
```

## First Test Run

### Start Small
```bash
# Test authentication only
npm run test:e2e -- -s authentication

# Expected output:
# ✓ Passed: 4
# ✗ Failed: 0
```

### Check Results
```bash
# See screenshots
ls test-results/screenshots/

# If on macOS
open test-results/screenshots/
```

## If Authentication Passes

### Run Core Suites
```bash
# These should all pass 100%
npm run test:e2e -- -s messaging
npm run test:e2e -- -s patient-cases
npm run test:e2e -- -s research
```

### Run All Tests
```bash
# Only after individual suites pass
npm run test:e2e
```

## Common First-Run Issues

### 1. "Too many OTP requests"
```bash
# You forgot to clear Redis!
node rate-limit-manager.js clear
# Wait 2 minutes
# Try again
```

### 2. "Cannot find textarea"
```bash
# Modal might be open
# Add to test: await this.page.keyboard.press('Escape');
```

### 3. No screenshots generated
```bash
# Check the directory exists
mkdir -p test-results/screenshots
```

### 4. All tests timeout
```bash
# App might not be running
curl http://localhost:3000  # Should return HTML
npm run dev  # Start if needed
```

## Quick Validation Test

Run this in a new file to check everything works:

```javascript
// test-check.js
const puppeteer = require('puppeteer');

(async () => {
  const browser = await puppeteer.launch({ headless: false });
  const page = await browser.newPage();
  
  // Go to app
  await page.goto('http://localhost:3000');
  console.log('✓ App loaded');
  
  // Check for phone input
  const phoneInput = await page.$('input[placeholder*="98193"]');
  console.log('✓ Phone input found:', !!phoneInput);
  
  // Type phone
  if (phoneInput) {
    await phoneInput.type('+919819304846');
    console.log('✓ Phone typed');
  }
  
  await page.screenshot({ path: 'test-check.png' });
  console.log('✓ Screenshot saved');
  
  await browser.close();
})();
```

Run with: `node test-check.js`

## Environment Variables Check

```bash
# Should have these set
echo $TEST_PHONE     # Should be empty or +919819304846
echo $TEST_OTP       # Should be empty or 123456
echo $TEST_HEADLESS  # Should be empty or true
```

## Final Checklist Before Full Run

- [ ] App is running and accessible
- [ ] Redis rate limits cleared
- [ ] No modals open in browser
- [ ] Test phone number confirmed (+919819304846)
- [ ] At least one individual suite passed
- [ ] Screenshots directory exists
- [ ] No hanging test processes

## Ready to Run!

```bash
# You're ready for the full suite
npm run test:e2e

# Or specific suites
npm run test:e2e -- -s patient-cases
npm run test:e2e -- -s research
npm run test:e2e -- -s quick-facts
```

## Expected Results
- Authentication: 100% (4/4)
- Dashboard Navigation: 100% (4/4)
- Messaging: 100% (4/4)
- Patient Cases: 100% (8/8)
- Research: 100% (8/8)
- Quick Facts: 90%+ (9/10 or 10/10)

## Success! 🎉

If you see these results, the test suite is working correctly.

## Need Help?

Check these files in order:
1. `QUICK_REFERENCE_CARD.md` - Quick commands
2. `COMMON_TEST_FAILURES_AND_FIXES.md` - Specific errors
3. `RATE_LIMITING_GUIDE.md` - If getting 429 errors
4. `E2E_TEST_SUITE_COMPLETE_GUIDE.md` - Full details