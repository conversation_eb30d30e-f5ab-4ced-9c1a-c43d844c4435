# Test Patterns and Selectors That Work

## Critical Selectors

### Authentication Flow
```javascript
// Phone input - this exact selector works!
const phoneInput = await this.page.$('input[placeholder*="98193"]');

// OTP input - appears after phone submission
const otpInput = await this.page.$('input[maxlength="6"]');

// Login button (if needed)
const loginButton = await this.page.$('button[type="submit"]');
```

### Chat Interface
```javascript
// Message input textarea
const messageInput = await this.page.$('textarea');

// Alternative selectors for message input
'textarea[placeholder*="message" i]'
'textarea[placeholder*="ask" i]'
'textarea[placeholder*="type" i]'

// Send button (usually not needed - Enter key works)
'button[type="submit"]'
'button[aria-label*="send" i]'
```

### Sidebar Elements
```javascript
// Sidebar container
'aside'
'[class*="sidebar"]'
'nav'
'[class*="thread-list"]'

// New conversation button
'button[class*="new"]'
'button[aria-label*="new"]'
'button:has-text("+")'  // Note: Use evaluate for this
```

### Modal Elements
```javascript
// Modal detection
'[role="dialog"]'
'.modal'
'[class*="modal"]'

// Modal buttons
'button:has-text("Cancel")'   // Use evaluate
'button:has-text("Start")'     // Use evaluate
```

## Working Patterns

### 1. Text Search Pattern
Instead of complex selectors, search text content:

```javascript
const hasContent = await this.page.evaluate(() => {
  const pageText = document.body.textContent?.toLowerCase() || '';
  return pageText.includes('diabetes') || 
         pageText.includes('treatment');
});
```

### 2. Button Click Pattern
Always use evaluate for button clicks:

```javascript
const clicked = await this.page.evaluate(() => {
  const buttons = Array.from(document.querySelectorAll('button'));
  const targetBtn = buttons.find(btn => 
    btn.textContent?.toLowerCase().includes('new conversation')
  );
  if (targetBtn) {
    targetBtn.click();
    return true;
  }
  return false;
});
```

### 3. Wait Pattern for SPA
Don't use waitForNavigation, use:

```javascript
// Wait for specific element
await this.page.waitForSelector('textarea', { timeout: 10000 });

// Wait for URL change
await this.page.waitForFunction(
  () => window.location.pathname === '/',
  { timeout: 10000 }
);

// Wait for content change
await this.page.waitForFunction(() => {
  return document.body.textContent?.includes('expected text');
});
```

### 4. Authentication Check Pattern
```javascript
async ensureAuthenticated() {
  // Check if we're already logged in
  const isLoggedIn = await this.page.evaluate(() => {
    return !!(
      document.querySelector('textarea') || 
      document.querySelector('[placeholder*="message"]') ||
      document.querySelector('[placeholder*="ask"]')
    );
  });
  
  if (!isLoggedIn) {
    // Do login flow
    await this.authenticate();
  }
}
```

### 5. Message Send Pattern
```javascript
// Type and send message
await messageInput.click();
await messageInput.type('Your message here');
await this.page.keyboard.press('Enter');

// Wait for response
await this.utils.waitForTimeout(5000); // 5 seconds for AI
```

### 6. Thread Detection Pattern
```javascript
const threadInfo = await this.page.evaluate(() => {
  const sidebar = document.querySelector('aside, [class*="sidebar"]');
  if (!sidebar) return { found: false };
  
  const sidebarText = sidebar.textContent || '';
  
  return {
    found: sidebarText.includes('45-year-old male'),
    hasType: sidebarText.includes('Patient Case'),
    sidebarContent: sidebarText.substring(0, 200)
  };
});
```

## What Doesn't Work

### 1. :has-text() Selector
```javascript
// DON'T DO THIS
await this.page.$('button:has-text("Login")');

// DO THIS INSTEAD
await this.page.evaluate(() => {
  const buttons = Array.from(document.querySelectorAll('button'));
  const loginBtn = buttons.find(btn => 
    btn.textContent?.includes('Login')
  );
  if (loginBtn) loginBtn.click();
});
```

### 2. waitForNavigation in SPA
```javascript
// DON'T DO THIS
await Promise.all([
  this.page.click('button'),
  this.page.waitForNavigation()
]);

// DO THIS INSTEAD
await this.page.click('button');
await this.page.waitForSelector('textarea');
```

### 3. Direct .click() on ElementHandle
```javascript
// DON'T DO THIS
const handle = await this.page.evaluateHandle(() => 
  document.querySelector('button')
);
await handle.click(); // Error!

// DO THIS INSTEAD
const button = await this.page.$('button');
await button.click();
```

## Message Type Patterns

### Patient Case Message
```javascript
const patientCase = `Patient Case: 45-year-old male presenting with Type 2 Diabetes. 
HbA1c is 9.5. Research everything about hba1c levels and treatment options.`;
```

### Research Message
```javascript
const researchQuery = `Research: Latest treatments for Type 2 Diabetes`;
```

### Quick Facts Message
```javascript
const quickQuery = `Quick: Normal blood pressure range for adults`;
```

## Debugging Patterns

### 1. Screenshot at Key Points
```javascript
await this.utils.screenshot('step-name');
// Saves to: test-results/screenshots/suite-name-N-step-name.png
```

### 2. Log Current State
```javascript
const pageInfo = await this.page.evaluate(() => ({
  url: window.location.href,
  title: document.title,
  hasTextarea: !!document.querySelector('textarea'),
  bodyText: document.body.textContent?.substring(0, 200)
}));
console.log('Page state:', pageInfo);
```

### 3. Wait and Check Pattern
```javascript
// Try multiple selectors
const messageInput = await this.page.$('textarea') ||
                    await this.page.$('[placeholder*="message"]') ||
                    await this.page.$('[contenteditable="true"]');

if (!messageInput) {
  throw new Error('Could not find message input');
}
```

## Summary

**Key Rules**:
1. Use `evaluate()` for complex selections
2. Check page text content for AI responses
3. Don't use `:has-text()` selector
4. Don't use `waitForNavigation` for SPA
5. Always wait 5+ seconds for AI responses
6. Take screenshots for debugging
7. Use Escape key to close modals

**Most Reliable Pattern**:
```javascript
// Close any modals
await this.page.keyboard.press('Escape');

// Find and use textarea
const textarea = await this.page.$('textarea');
await textarea.click();
await textarea.type('message');
await this.page.keyboard.press('Enter');

// Wait for response
await this.utils.waitForTimeout(5000);

// Check response
const hasResponse = await this.page.evaluate(() => 
  document.body.textContent?.includes('expected')
);
```