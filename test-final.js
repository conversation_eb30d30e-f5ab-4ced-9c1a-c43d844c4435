const puppeteer = require('puppeteer');

async function testDoctorDashboard() {
  console.log('Doctor Dashboard Test - Final Version\n');
  
  const results = {
    apiCalls: [],
    jsonErrors: [],
    testPassed: false
  };
  
  const browser = await puppeteer.launch({
    headless: true,
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });
  
  try {
    const page = await browser.newPage();
    
    // Monitor console for errors
    page.on('console', msg => {
      const text = msg.text();
      if (msg.type() === 'error' || text.includes('JSON.parse')) {
        console.log(`[CONSOLE ERROR] ${text}`);
        if (text.includes('JSON')) {
          results.jsonErrors.push({
            type: 'console',
            message: text
          });
        }
      }
    });
    
    // Monitor all responses for JSON errors
    page.on('response', async response => {
      const url = response.url();
      const status = response.status();
      
      if (url.includes('/api/')) {
        console.log(`[API] ${response.request().method()} ${url} - Status: ${status}`);
        
        results.apiCalls.push({
          url,
          method: response.request().method(),
          status
        });
        
        // Try to parse JSON response
        try {
          const text = await response.text();
          if (text) {
            try {
              JSON.parse(text);
              console.log('[API] Valid JSON response');
            } catch (e) {
              console.log('[JSON ERROR] Invalid JSON in API response:', e.message);
              results.jsonErrors.push({
                type: 'api_response',
                url,
                status,
                error: e.message,
                body: text.substring(0, 200)
              });
            }
          }
        } catch (e) {
          console.log('[API] Could not read response body');
        }
      }
    });
    
    // Navigate to the app
    console.log('1. Navigating to http://localhost:3000...');
    await page.goto('http://localhost:3000', { waitUntil: 'networkidle0' });
    console.log('   ✓ Page loaded\n');
    
    // Wait for form
    await page.waitForSelector('form');
    
    // Fill phone number
    console.log('2. Filling phone number...');
    await page.type('input[placeholder*="98193"]', '+919819304846');
    console.log('   ✓ Phone number entered\n');
    
    // Click submit button
    console.log('3. Clicking submit button...');
    const submitButton = await page.$('button[type="submit"]');
    if (!submitButton) {
      throw new Error('Submit button not found');
    }
    
    // Click and wait for response
    const [response] = await Promise.all([
      page.waitForResponse(res => res.url().includes('/api/auth/request-otp'), { timeout: 10000 }),
      submitButton.click()
    ]);
    
    console.log('   ✓ Button clicked and API response received\n');
    
    // Check if OTP field appeared
    console.log('4. Checking for OTP field...');
    try {
      await page.waitForSelector('input[maxlength="6"]', { timeout: 5000 });
      console.log('   ✓ OTP field appeared - Test passed!\n');
      results.testPassed = true;
    } catch (e) {
      console.log('   ✗ OTP field did not appear\n');
      
      // Check for errors
      const errors = await page.evaluate(() => {
        return Array.from(document.querySelectorAll('.error, [role="alert"]'))
          .map(el => el.textContent.trim())
          .filter(text => text.length > 0);
      });
      
      if (errors.length > 0) {
        console.log('   Errors found on page:', errors);
      }
    }
    
  } catch (error) {
    console.error('\nTest error:', error.message);
  } finally {
    // Print summary
    console.log('\n========== TEST SUMMARY ==========');
    console.log(`API Calls Made: ${results.apiCalls.length}`);
    console.log(`JSON Errors: ${results.jsonErrors.length}`);
    console.log(`Test Result: ${results.testPassed ? 'PASSED' : 'FAILED'}`);
    
    if (results.apiCalls.length > 0) {
      console.log('\nAPI Calls:');
      results.apiCalls.forEach(call => {
        console.log(`  - ${call.method} ${call.url} (${call.status})`);
      });
    }
    
    if (results.jsonErrors.length > 0) {
      console.log('\nJSON Errors Found:');
      results.jsonErrors.forEach(error => {
        console.log(`  - ${error.type}: ${error.message || error.error}`);
        if (error.body) {
          console.log(`    Body: ${error.body}`);
        }
      });
    }
    
    await browser.close();
  }
}

testDoctorDashboard();