{"totalTests": 0, "passed": 0, "failed": 0, "errors": [{"test": "Test response speed", "error": "Waiting failed: 10000ms exceeded"}], "tests": [{"name": "quick-facts", "passed": false, "errors": [{"test": "Test response speed", "error": "Waiting failed: 10000ms exceeded"}], "duration": 71469, "steps": [{"name": "Authenticate", "passed": true, "error": null, "duration": 12175}, {"name": "Handle modal if present", "passed": true, "error": null, "duration": 54}, {"name": "Test drug dosage query", "passed": true, "error": null, "duration": 6435}, {"name": "Check drug dosage response", "passed": true, "error": null, "duration": 10257}, {"name": "Test medical calculation", "passed": true, "error": null, "duration": 7715}, {"name": "Test lab value interpretation", "passed": true, "error": null, "duration": 10333}, {"name": "Test quick reference", "passed": true, "error": null, "duration": 8528}, {"name": "Check response formatting", "passed": true, "error": null, "duration": 225}, {"name": "Check quick facts thread in sidebar", "passed": true, "error": null, "duration": 52}, {"name": "Test response speed", "passed": false, "error": "Waiting failed: 10000ms exceeded", "duration": 14435}]}], "startTime": "2025-06-23T14:32:38.550Z", "endTime": "2025-06-23T14:33:50.021Z", "duration": 71471}