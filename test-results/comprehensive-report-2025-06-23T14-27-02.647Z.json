{"totalTests": 0, "passed": 0, "failed": 0, "errors": [{"test": "Create new patient case conversation", "error": "Could not find Start Conversation button"}, {"test": "Check AI response", "error": "AI response does not contain expected medical information"}, {"test": "Check message history", "error": "Expected both user message and AI response to be visible"}], "tests": [{"name": "patient-cases", "passed": false, "errors": [{"test": "Create new patient case conversation", "error": "Could not find Start Conversation button"}, {"test": "Check AI response", "error": "AI response does not contain expected medical information"}, {"test": "Check message history", "error": "Expected both user message and AI response to be visible"}], "duration": 61000, "steps": [{"name": "Authenticate", "passed": true, "error": null, "duration": 12208}, {"name": "Create new patient case conversation", "passed": false, "error": "Could not find Start Conversation button", "duration": 3089}, {"name": "Send patient case message", "passed": true, "error": null, "duration": 21244}, {"name": "Check AI response", "passed": false, "error": "AI response does not contain expected medical information", "duration": 10324}, {"name": "Check message history", "passed": false, "error": "Expected both user message and AI response to be visible", "duration": 180}, {"name": "Check thread in sidebar", "passed": true, "error": null, "duration": 200}, {"name": "Send follow-up message", "passed": true, "error": null, "duration": 10197}, {"name": "Test file attachment", "passed": true, "error": null, "duration": 2134}]}], "startTime": "2025-06-23T14:26:01.646Z", "endTime": "2025-06-23T14:27:02.647Z", "duration": 61001}