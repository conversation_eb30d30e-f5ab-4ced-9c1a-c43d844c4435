{"totalTests": 0, "passed": 0, "failed": 0, "errors": [{"test": "Check AI response", "error": "AI response does not contain expected medical information"}, {"test": "Check message history", "error": "Expected both user message and AI response to be visible"}], "tests": [{"name": "patient-cases", "passed": false, "errors": [{"test": "Check AI response", "error": "AI response does not contain expected medical information"}, {"test": "Check message history", "error": "Expected both user message and AI response to be visible"}], "duration": 48820, "steps": [{"name": "Authenticate", "passed": true, "error": null, "duration": 12383}, {"name": "Create new patient case conversation", "passed": true, "error": null, "duration": 1334}, {"name": "Send patient case message", "passed": true, "error": null, "duration": 13333}, {"name": "Check AI response", "passed": false, "error": "AI response does not contain expected medical information", "duration": 7327}, {"name": "Check message history", "passed": false, "error": "Expected both user message and AI response to be visible", "duration": 192}, {"name": "Check thread in sidebar", "passed": true, "error": null, "duration": 181}, {"name": "Send follow-up message", "passed": true, "error": null, "duration": 10110}, {"name": "Test file attachment", "passed": true, "error": null, "duration": 2129}]}], "startTime": "2025-06-23T14:20:45.157Z", "endTime": "2025-06-23T14:21:33.980Z", "duration": 48823}