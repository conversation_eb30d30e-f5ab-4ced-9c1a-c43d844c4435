{"totalTests": 0, "passed": 0, "failed": 0, "errors": [{"test": "Check AI response", "error": "AI response does not contain expected medical information"}, {"test": "Check message history", "error": "Expected both user message and AI response to be visible"}], "tests": [{"name": "patient-cases", "passed": false, "errors": [{"test": "Check AI response", "error": "AI response does not contain expected medical information"}, {"test": "Check message history", "error": "Expected both user message and AI response to be visible"}], "duration": 55112, "steps": [{"name": "Authenticate", "passed": true, "error": null, "duration": 12224}, {"name": "Create new patient case conversation", "passed": true, "error": null, "duration": 1351}, {"name": "Send patient case message", "passed": true, "error": null, "duration": 20096}, {"name": "Check AI response", "passed": false, "error": "AI response does not contain expected medical information", "duration": 7331}, {"name": "Check message history", "passed": false, "error": "Expected both user message and AI response to be visible", "duration": 213}, {"name": "Check thread in sidebar", "passed": true, "error": null, "duration": 188}, {"name": "Send follow-up message", "passed": true, "error": null, "duration": 10128}, {"name": "Test file attachment", "passed": true, "error": null, "duration": 2128}]}], "startTime": "2025-06-23T14:10:00.473Z", "endTime": "2025-06-23T14:10:55.587Z", "duration": 55114}