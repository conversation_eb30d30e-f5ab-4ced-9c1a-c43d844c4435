{"totalTests": 0, "passed": 0, "failed": 0, "errors": [{"test": "Check sidebar elements", "error": "Thread type \"Patient Cases\" not found in sidebar"}, {"test": "Test new thread creation", "error": "SyntaxError: Failed to execute 'querySelector' on 'Document': 'button[aria-label*=\"new\" i], button:has-text(\"New\")' is not a valid selector."}, {"test": "Test message input", "error": "Message input not found"}, {"test": "Send patient case message", "error": "Message input not found"}, {"test": "Check AI response", "error": "Protocol error (Page.captureScreenshot): Session closed. Most likely the page has been closed."}, {"test": "setup", "error": "Protocol error (Page.captureScreenshot): Session closed. Most likely the page has been closed."}, {"test": "Navigate to Research section", "error": "Cannot read properties of null (reading 'click')"}, {"test": "Create research thread", "error": "Cannot read properties of null (reading 'click')"}, {"test": "Navigate to Quick Facts section", "error": "Cannot read properties of null (reading 'click')"}, {"test": "Test network error handling", "error": "Navigation timeout of 30000 ms exceeded"}, {"test": "setup", "error": "Protocol error (Page.captureScreenshot): Cannot take screenshot with 0 width."}], "tests": [{"name": "authentication", "passed": true, "errors": [], "duration": 9298, "steps": [{"name": "Navigate to home page", "passed": true, "error": null, "duration": 481}, {"name": "Check login form elements", "passed": true, "error": null, "duration": 1953}, {"name": "Request OTP", "passed": true, "error": null, "duration": 5248}, {"name": "Enter OTP", "passed": true, "error": null, "duration": 1}]}, {"name": "dashboard-navigation", "passed": false, "errors": [{"test": "Check sidebar elements", "error": "Thread type \"Patient Cases\" not found in sidebar"}, {"test": "Test new thread creation", "error": "SyntaxError: Failed to execute 'querySelector' on 'Document': 'button[aria-label*=\"new\" i], button:has-text(\"New\")' is not a valid selector."}], "duration": 7958, "steps": [{"name": "Check dashboard layout", "passed": true, "error": null, "duration": 6400}, {"name": "Check sidebar elements", "passed": false, "error": "Thread type \"Patient Cases\" not found in sidebar", "duration": 224}, {"name": "Test new thread creation", "passed": false, "error": "SyntaxError: Failed to execute 'querySelector' on 'Document': 'button[aria-label*=\"new\" i], button:has-text(\"New\")' is not a valid selector.", "duration": 339}]}, {"name": "messaging", "passed": false, "errors": [{"test": "Test message input", "error": "Message input not found"}], "duration": 2359, "steps": [{"name": "Test message input", "passed": false, "error": "Message input not found", "duration": 674}, {"name": "Test file attachment", "passed": true, "error": null, "duration": 50}, {"name": "Test markdown rendering", "passed": true, "error": null, "duration": 215}]}, {"name": "patient-cases", "passed": false, "errors": [{"test": "Send patient case message", "error": "Message input not found"}, {"test": "Check AI response", "error": "Protocol error (Page.captureScreenshot): Session closed. Most likely the page has been closed."}, {"test": "setup", "error": "Protocol error (Page.captureScreenshot): Session closed. Most likely the page has been closed."}], "duration": 12264, "steps": [{"name": "Create new patient case", "passed": true, "error": null, "duration": 103}, {"name": "Send patient case message", "passed": false, "error": "Message input not found", "duration": 586}]}, {"name": "research", "passed": false, "errors": [{"test": "Navigate to Research section", "error": "Cannot read properties of null (reading 'click')"}, {"test": "Create research thread", "error": "Cannot read properties of null (reading 'click')"}], "duration": 3088, "steps": [{"name": "Navigate to Research section", "passed": false, "error": "Cannot read properties of null (reading 'click')", "duration": 194}, {"name": "Create research thread", "passed": false, "error": "Cannot read properties of null (reading 'click')", "duration": 213}, {"name": "Ask research question", "passed": true, "error": null, "duration": 475}, {"name": "Check for citations", "passed": true, "error": null, "duration": 54}, {"name": "Check for data tables/visualizations", "passed": true, "error": null, "duration": 428}, {"name": "Test research export options", "passed": true, "error": null, "duration": 213}]}, {"name": "quick-facts", "passed": false, "errors": [{"test": "Navigate to Quick Facts section", "error": "Cannot read properties of null (reading 'click')"}], "duration": 2142, "steps": [{"name": "Navigate to Quick Facts section", "passed": false, "error": "Cannot read properties of null (reading 'click')", "duration": 193}, {"name": "Test drug dosage query", "passed": true, "error": null, "duration": 473}, {"name": "Test medical calculation", "passed": true, "error": null, "duration": 53}, {"name": "Test lab value interpretation", "passed": true, "error": null, "duration": 53}, {"name": "Check response formatting", "passed": true, "error": null, "duration": 55}, {"name": "Test response speed", "passed": true, "error": null, "duration": 54}]}, {"name": "error-handling", "passed": false, "errors": [{"test": "Test network error handling", "error": "Navigation timeout of 30000 ms exceeded"}, {"test": "setup", "error": "Protocol error (Page.captureScreenshot): Cannot take screenshot with 0 width."}], "duration": 37562, "steps": [{"name": "Test invalid phone number", "passed": true, "error": null, "duration": 4559}, {"name": "Test empty form submission", "passed": true, "error": null, "duration": 1988}]}], "startTime": "2025-06-23T13:17:21.512Z", "endTime": "2025-06-23T13:18:48.193Z", "duration": 86681}