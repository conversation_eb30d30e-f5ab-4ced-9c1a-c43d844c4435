{"totalTests": 0, "passed": 0, "failed": 0, "errors": [{"test": "Create new patient case conversation", "error": "Node is either not clickable or not an Element"}, {"test": "Check AI response", "error": "AI response does not contain expected medical information"}, {"test": "Check message history", "error": "Expected both user message and AI response to be visible"}], "tests": [{"name": "patient-cases", "passed": false, "errors": [{"test": "Create new patient case conversation", "error": "Node is either not clickable or not an Element"}, {"test": "Check AI response", "error": "AI response does not contain expected medical information"}, {"test": "Check message history", "error": "Expected both user message and AI response to be visible"}], "duration": 60838, "steps": [{"name": "Authenticate", "passed": true, "error": null, "duration": 12280}, {"name": "Create new patient case conversation", "passed": false, "error": "Node is either not clickable or not an Element", "duration": 2460}, {"name": "Send patient case message", "passed": true, "error": null, "duration": 21258}, {"name": "Check AI response", "passed": false, "error": "AI response does not contain expected medical information", "duration": 10395}, {"name": "Check message history", "passed": false, "error": "Expected both user message and AI response to be visible", "duration": 191}, {"name": "Check thread in sidebar", "passed": true, "error": null, "duration": 208}, {"name": "Send follow-up message", "passed": true, "error": null, "duration": 10480}, {"name": "Test file attachment", "passed": true, "error": null, "duration": 2123}]}], "startTime": "2025-06-23T14:24:22.106Z", "endTime": "2025-06-23T14:25:22.946Z", "duration": 60840}