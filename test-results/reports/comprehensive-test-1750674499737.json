{"timestamp": "2025-06-23T10:28:19.736Z", "config": {"baseUrl": "http://localhost:3000", "testPhone": "+919819304846", "testOTP": "123456", "headless": true, "defaultTimeout": 30000}, "results": {"passed": 7, "failed": 7, "tests": [{"name": "Navigate to login page", "status": "passed"}, {"name": "Enter phone number", "status": "passed"}, {"name": "Request OTP", "status": "passed"}, {"name": "Enter and submit OTP", "status": "passed"}, {"name": "Check dashboard layout", "status": "failed", "error": "SyntaxError: Failed to execute 'querySelector' on 'Document': 'button:has-text(\"New Conversation\"), button[class*=\"new\"]' is not a valid selector."}, {"name": "Create new Patient Case thread", "status": "passed"}, {"name": "Send message and wait for response", "status": "failed", "error": "AI response not received"}, {"name": "Send follow-up message", "status": "failed", "error": "Cannot read properties of null (reading 'click')"}, {"name": "Create new conversation", "status": "failed", "error": "newConvBtn.click is not a function"}, {"name": "Switch to Research type", "status": "failed", "error": "researchBtn.click is not a function"}, {"name": "Switch to Quick Facts type", "status": "failed", "error": "quickFactsBtn.click is not a function"}, {"name": "Navigate between threads", "status": "passed"}, {"name": "Check file attachment button", "status": "passed"}, {"name": "Access user menu and logout", "status": "failed", "error": "userMenu.click is not a function"}]}, "summary": {"total": 14, "passed": 7, "failed": 7, "successRate": "50.0%"}}