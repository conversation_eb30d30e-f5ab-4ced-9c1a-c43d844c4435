{"timestamp": "2025-06-23T10:13:37.060Z", "config": {"baseUrl": "http://localhost:3000", "testPhone": "+919819304846", "testOTP": "123456", "headless": true, "defaultTimeout": 30000}, "suites": {"Authentication": {"tests": [{"name": "Navigate to login page", "status": "passed", "duration": 665}, {"name": "Enter phone number", "status": "passed", "duration": 200}, {"name": "Submit OTP request", "status": "passed", "duration": 1331}, {"name": "Enter OTP", "status": "passed", "duration": 132}, {"name": "Submit OTP and login", "status": "failed", "error": "Waiting for selector `button:has-text(\"Verify\"), button[type=\"submit\"]` failed: Waiting failed: 30000ms exceeded", "duration": 30096}], "startTime": 1750673618224}}, "summary": {"totalTests": 5, "passed": 4, "failed": 1, "skipped": 0}}