{"timestamp": "2025-06-23T10:25:30.786Z", "config": {"baseUrl": "http://localhost:3000", "testPhone": "+919819304846", "testOTP": "123456", "headless": true, "defaultTimeout": 30000}, "suites": {"Authentication": {"tests": [{"name": "Navigate to login page", "status": "passed", "duration": 308}, {"name": "Enter phone number", "status": "passed", "duration": 512}, {"name": "Submit OTP request", "status": "passed", "duration": 1520}, {"name": "Enter OTP", "status": "passed", "duration": 146}, {"name": "Submit OTP and login", "status": "passed", "duration": 595}], "startTime": 1750674331834, "duration": 3082}, "Dashboard Navigation": {"tests": [{"name": "Check dashboard layout", "status": "passed", "duration": 84}, {"name": "Check thread type tabs", "status": "failed", "error": "Thread type \"Patient Cases\" tab not found", "duration": 84}], "startTime": 1750674334916}}, "summary": {"totalTests": 7, "passed": 6, "failed": 1, "skipped": 0}}