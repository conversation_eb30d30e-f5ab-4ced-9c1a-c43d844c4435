{"timestamp": "2025-06-23T07:57:49.129Z", "tests": [{"name": "Navigate to home page", "status": "passed", "duration": 192}, {"name": "Enter phone number", "status": "passed", "duration": 247}, {"name": "Submit OTP request", "status": "passed", "duration": 847}, {"name": "Check OTP input formatting", "status": "passed", "duration": 4}, {"name": "Test \"Use Different Number\" link", "status": "failed", "error": "SyntaxError: Failed to execute 'querySelector' on 'Document': 'button:has-text(\"Use Different Number\"), a:has-text(\"Use Different Number\")' is not a valid selector.", "duration": 78}, {"name": "Navigate back to login", "status": "passed", "duration": 156}, {"name": "Test invalid phone validation", "status": "passed", "duration": 52}, {"name": "Measure page load performance", "status": "passed", "duration": 421}, {"name": "Test responsive design", "status": "passed", "duration": 289}, {"name": "Check ARIA labels", "status": "passed", "duration": 1}, {"name": "Check form labels", "status": "passed", "duration": 1}], "totalPassed": 10, "totalFailed": 1}