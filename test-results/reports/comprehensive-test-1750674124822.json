{"timestamp": "2025-06-23T10:21:53.353Z", "config": {"baseUrl": "http://localhost:3000", "testPhone": "+919819304846", "testOTP": "123456", "headless": true, "defaultTimeout": 30000}, "suites": {"Authentication": {"tests": [{"name": "Navigate to login page", "status": "passed", "duration": 232}, {"name": "Enter phone number", "status": "passed", "duration": 209}, {"name": "Submit OTP request", "status": "failed", "error": "Waiting for selector `input[maxlength=\"6\"], [class*=\"error\"]` failed: Waiting failed: 10000ms exceeded", "duration": 10138}], "startTime": 1750674114195}}, "summary": {"totalTests": 3, "passed": 2, "failed": 1, "skipped": 0}}