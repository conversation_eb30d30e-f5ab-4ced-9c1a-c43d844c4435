{"timestamp": "2025-06-23T10:15:00.346Z", "config": {"baseUrl": "http://localhost:3000", "testPhone": "+919819304846", "testOTP": "123456", "headless": true, "defaultTimeout": 30000}, "suites": {"Authentication": {"tests": [{"name": "Navigate to login page", "status": "passed", "duration": 600}, {"name": "Enter phone number", "status": "passed", "duration": 1505}, {"name": "Submit OTP request", "status": "passed", "duration": 1102}, {"name": "Enter OTP", "status": "passed", "duration": 196}, {"name": "Submit OTP and login", "status": "failed", "error": "Navigation timeout of 30000 ms exceeded", "duration": 30639}], "startTime": 1750673701233}}, "summary": {"totalTests": 5, "passed": 4, "failed": 1, "skipped": 0}}