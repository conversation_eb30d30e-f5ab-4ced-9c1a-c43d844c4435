{"startTime": "2025-06-23T10:40:00.919Z", "tests": [{"name": "Navigate to login page", "status": "passed", "duration": 969, "expectedElements": []}, {"name": "Complete login flow", "status": "passed", "duration": 9424, "expectedElements": []}, {"name": "Verify dashboard layout", "status": "passed", "duration": 485, "expectedElements": []}, {"name": "Select Patient Case thread type", "status": "passed", "duration": 1120, "expectedElements": []}, {"name": "Type and send patient case message", "status": "passed", "duration": 30263, "expectedElements": []}, {"name": "Wait for AI response", "status": "failed", "error": "AI response not received within timeout", "duration": 15813}, {"name": "Verify thread creation in sidebar", "status": "passed", "duration": 186, "expectedElements": []}, {"name": "Send follow-up message", "status": "failed", "error": "Textarea not found for follow-up", "duration": 52}, {"name": "Create new conversation", "status": "failed", "error": "newConvBtn.click is not a function", "duration": 52}, {"name": "Navigate back to previous thread", "status": "failed", "error": "threadLink.click is not a function", "duration": 51}, {"name": "Test Research thread type", "status": "failed", "error": "newConvBtn.click is not a function", "duration": 53}, {"name": "Test Quick Facts thread type", "status": "failed", "error": "newConvBtn.click is not a function", "duration": 51}, {"name": "Verify multiple threads in sidebar", "status": "passed", "duration": 193, "expectedElements": []}], "screenshots": [{"name": "1-phone-entered.png", "path": "test-results/screenshots/chat-flow/1-phone-entered.png", "annotations": {"screenshot": "1-phone-entered", "timestamp": "2025-06-23T10:40:04.038Z", "annotations": [{"element": "phone input", "description": "Should show +919819304846"}]}}, {"name": "14-final-sidebar-state.png", "path": "test-results/screenshots/chat-flow/14-final-sidebar-state.png", "annotations": {"screenshot": "14-final-sidebar-state", "timestamp": "2025-06-23T10:41:00.469Z", "annotations": [{"element": "sidebar", "description": "Multiple threads organized by time"}]}}, {"name": "2-otp-entered.png", "path": "test-results/screenshots/chat-flow/2-otp-entered.png", "annotations": {"screenshot": "2-otp-entered", "timestamp": "2025-06-23T10:40:07.018Z", "annotations": [{"element": "OTP field", "description": "Should show 123456"}]}}, {"name": "3-dashboard-initial.png", "path": "test-results/screenshots/chat-flow/3-dashboard-initial.png", "annotations": {"screenshot": "3-dashboard-initial", "timestamp": "2025-06-23T10:40:11.488Z", "annotations": [{"element": "sidebar", "description": "Left panel with thread list"}, {"element": "chat area", "description": "Center with welcome message"}, {"element": "thread buttons", "description": "Bottom buttons for Patient Case, Research, Quick Facts"}]}}, {"name": "4-patient-case-selected.png", "path": "test-results/screenshots/chat-flow/4-patient-case-selected.png", "annotations": {"screenshot": "4-patient-case-selected", "timestamp": "2025-06-23T10:40:12.919Z", "annotations": [{"element": "Patient Case button", "description": "Should be highlighted/active"}]}}, {"name": "5-message-typed.png", "path": "test-results/screenshots/chat-flow/5-message-typed.png", "annotations": {"screenshot": "5-message-typed", "timestamp": "2025-06-23T10:40:40.907Z", "annotations": [{"element": "textarea", "description": "Full patient case message visible"}]}}, {"name": "6-message-sent.png", "path": "test-results/screenshots/chat-flow/6-message-sent.png", "annotations": {"screenshot": "6-message-sent", "timestamp": "2025-06-23T10:40:43.183Z", "annotations": [{"element": "user message", "description": "Right-aligned message bubble"}, {"element": "loading indicator", "description": "AI thinking indicator"}]}}, {"name": "8-thread-in-sidebar.png", "path": "test-results/screenshots/chat-flow/8-thread-in-sidebar.png", "annotations": {"screenshot": "8-thread-in-sidebar", "timestamp": "2025-06-23T10:40:59.318Z", "annotations": [{"element": "sidebar", "description": "New thread under \"Today\" section"}, {"element": "thread title", "description": "Shows beginning of patient case message"}]}}, {"name": "error-create-new-conversation.png", "path": "test-results/screenshots/chat-flow/error-create-new-conversation.png", "annotations": null}, {"name": "error-navigate-back-to-previous-thread.png", "path": "test-results/screenshots/chat-flow/error-navigate-back-to-previous-thread.png", "annotations": null}, {"name": "error-send-follow-up-message.png", "path": "test-results/screenshots/chat-flow/error-send-follow-up-message.png", "annotations": null}, {"name": "error-test-quick-facts-thread-type.png", "path": "test-results/screenshots/chat-flow/error-test-quick-facts-thread-type.png", "annotations": null}, {"name": "error-test-research-thread-type.png", "path": "test-results/screenshots/chat-flow/error-test-research-thread-type.png", "annotations": null}, {"name": "error-wait-for-ai-response.png", "path": "test-results/screenshots/chat-flow/error-wait-for-ai-response.png", "annotations": null}], "consoleLogs": [{"type": "info", "text": "%cDownload the React DevTools for a better development experience: https://react.dev/link/react-devtools font-weight:bold", "timestamp": "2025-06-23T10:40:01.228Z"}, {"type": "log", "text": "OTP sent via: gatekeeper", "timestamp": "2025-06-23T10:40:05.623Z"}, {"type": "error", "text": "Received `%s` for a non-boolean attribute `%s`.\n\nIf you want to write it to the DOM, pass a string instead: %s=\"%s\" or %s={value.toString()}. true button button true button", "timestamp": "2025-06-23T10:40:09.310Z"}, {"type": "log", "text": "[Fast Refresh] rebuilding", "timestamp": "2025-06-23T10:40:09.425Z"}, {"type": "log", "text": "[Fast Refresh] done in 166ms", "timestamp": "2025-06-23T10:40:09.489Z"}], "endTime": "2025-06-23T10:41:00.469Z", "summary": {"total": 13, "passed": 7, "failed": 6, "successRate": "53.8%"}}