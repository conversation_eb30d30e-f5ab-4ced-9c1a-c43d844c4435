{"totalTests": 0, "passed": 0, "failed": 0, "errors": [{"test": "Request OTP", "error": "Timed out after waiting 30000ms"}, {"test": "Check message history", "error": "Expected both user message and AI response to be visible"}, {"test": "Test invalid phone number", "error": "No error message shown for invalid phone number"}, {"test": "Test network error handling", "error": "Navigation timeout of 30000 ms exceeded"}, {"test": "setup", "error": "Protocol error (Page.captureScreenshot): Cannot take screenshot with 0 width."}], "tests": [{"name": "authentication", "passed": false, "errors": [{"test": "Request OTP", "error": "Timed out after waiting 30000ms"}], "duration": 37330, "steps": [{"name": "Navigate to home page", "passed": true, "error": null, "duration": 636}, {"name": "Check login form elements", "passed": true, "error": null, "duration": 1933}, {"name": "Request OTP", "passed": false, "error": "Timed out after waiting 30000ms", "duration": 33118}, {"name": "Enter OTP", "passed": true, "error": null, "duration": 0}]}, {"name": "dashboard-navigation", "passed": true, "errors": [], "duration": 20489, "steps": [{"name": "Authenticate", "passed": true, "error": null, "duration": 12500}, {"name": "Check dashboard layout", "passed": true, "error": null, "duration": 3504}, {"name": "Check sidebar elements", "passed": true, "error": null, "duration": 3053}, {"name": "Test new thread creation", "passed": true, "error": null, "duration": 433}]}, {"name": "messaging", "passed": true, "errors": [], "duration": 19214, "steps": [{"name": "Authenticate", "passed": true, "error": null, "duration": 12615}, {"name": "Test message input", "passed": true, "error": null, "duration": 4442}, {"name": "Test file attachment", "passed": true, "error": null, "duration": 903}, {"name": "Test markdown rendering", "passed": true, "error": null, "duration": 210}]}, {"name": "patient-cases", "passed": false, "errors": [{"test": "Check message history", "error": "Expected both user message and AI response to be visible"}], "duration": 54388, "steps": [{"name": "Authenticate", "passed": true, "error": null, "duration": 12819}, {"name": "Handle new conversation modal", "passed": true, "error": null, "duration": 54}, {"name": "Send patient case message", "passed": true, "error": null, "duration": 18311}, {"name": "Check AI response", "passed": true, "error": null, "duration": 10186}, {"name": "Check message history", "passed": false, "error": "Expected both user message and AI response to be visible", "duration": 176}, {"name": "Check thread in sidebar", "passed": true, "error": null, "duration": 218}, {"name": "Send follow-up message", "passed": true, "error": null, "duration": 9452}, {"name": "Test file attachment", "passed": true, "error": null, "duration": 2081}]}, {"name": "research", "passed": true, "errors": [], "duration": 40601, "steps": [{"name": "Authenticate", "passed": true, "error": null, "duration": 12645}, {"name": "Handle modal if present", "passed": true, "error": null, "duration": 52}, {"name": "Ask research question", "passed": true, "error": null, "duration": 6575}, {"name": "Check research response", "passed": true, "error": null, "duration": 8228}, {"name": "Check for citations", "passed": true, "error": null, "duration": 52}, {"name": "Ask follow-up research question", "passed": true, "error": null, "duration": 11944}, {"name": "Check for data presentation", "passed": true, "error": null, "duration": 53}, {"name": "Check research thread in sidebar", "passed": true, "error": null, "duration": 52}]}, {"name": "quick-facts", "passed": true, "errors": [], "duration": 68158, "steps": [{"name": "Authenticate", "passed": true, "error": null, "duration": 12533}, {"name": "Handle modal if present", "passed": true, "error": null, "duration": 53}, {"name": "Test drug dosage query", "passed": true, "error": null, "duration": 6389}, {"name": "Check drug dosage response", "passed": true, "error": null, "duration": 10239}, {"name": "Test medical calculation", "passed": true, "error": null, "duration": 7063}, {"name": "Test lab value interpretation", "passed": true, "error": null, "duration": 9251}, {"name": "Test quick reference", "passed": true, "error": null, "duration": 7645}, {"name": "Check response formatting", "passed": true, "error": null, "duration": 217}, {"name": "Check quick facts thread in sidebar", "passed": true, "error": null, "duration": 53}, {"name": "Test response speed", "passed": true, "error": null, "duration": 13668}]}, {"name": "error-handling", "passed": false, "errors": [{"test": "Test invalid phone number", "error": "No error message shown for invalid phone number"}, {"test": "Test network error handling", "error": "Navigation timeout of 30000 ms exceeded"}, {"test": "setup", "error": "Protocol error (Page.captureScreenshot): Cannot take screenshot with 0 width."}], "duration": 37687, "steps": [{"name": "Test invalid phone number", "passed": false, "error": "No error message shown for invalid phone number", "duration": 4615}, {"name": "Test empty form submission", "passed": true, "error": null, "duration": 1830}]}], "startTime": "2025-06-25T10:44:23.193Z", "endTime": "2025-06-25T10:49:21.268Z", "duration": 298075}