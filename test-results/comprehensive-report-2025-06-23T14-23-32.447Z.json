{"totalTests": 0, "passed": 0, "failed": 0, "errors": [{"test": "Check AI response", "error": "AI response does not contain expected medical information"}, {"test": "Check message history", "error": "Expected both user message and AI response to be visible"}], "tests": [{"name": "patient-cases", "passed": false, "errors": [{"test": "Check AI response", "error": "AI response does not contain expected medical information"}, {"test": "Check message history", "error": "Expected both user message and AI response to be visible"}], "duration": 55045, "steps": [{"name": "Authenticate", "passed": true, "error": null, "duration": 12322}, {"name": "Create new patient case conversation", "passed": true, "error": null, "duration": 1840}, {"name": "Send patient case message", "passed": true, "error": null, "duration": 16223}, {"name": "Check AI response", "passed": false, "error": "AI response does not contain expected medical information", "duration": 10361}, {"name": "Check message history", "passed": false, "error": "Expected both user message and AI response to be visible", "duration": 150}, {"name": "Check thread in sidebar", "passed": true, "error": null, "duration": 168}, {"name": "Send follow-up message", "passed": true, "error": null, "duration": 10166}, {"name": "Test file attachment", "passed": true, "error": null, "duration": 2114}]}], "startTime": "2025-06-23T14:22:37.400Z", "endTime": "2025-06-23T14:23:32.447Z", "duration": 55047}