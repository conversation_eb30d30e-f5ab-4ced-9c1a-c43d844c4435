{"totalTests": 0, "passed": 0, "failed": 0, "errors": [{"test": "Check AI response", "error": "AI response does not contain expected medical information"}], "tests": [{"name": "patient-cases", "passed": false, "errors": [{"test": "Check AI response", "error": "AI response does not contain expected medical information"}], "duration": 54846, "steps": [{"name": "Authenticate", "passed": true, "error": null, "duration": 12118}, {"name": "Handle new conversation modal", "passed": true, "error": null, "duration": 54}, {"name": "Send patient case message", "passed": true, "error": null, "duration": 18133}, {"name": "Check AI response", "passed": false, "error": "AI response does not contain expected medical information", "duration": 10387}, {"name": "Check message history", "passed": true, "error": null, "duration": 52}, {"name": "Check thread in sidebar", "passed": true, "error": null, "duration": 199}, {"name": "Send follow-up message", "passed": true, "error": null, "duration": 10445}, {"name": "Test file attachment", "passed": true, "error": null, "duration": 2096}]}], "startTime": "2025-06-23T14:28:02.096Z", "endTime": "2025-06-23T14:28:56.944Z", "duration": 54848}