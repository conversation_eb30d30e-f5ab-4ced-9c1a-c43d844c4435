{"totalTests": 0, "passed": 0, "failed": 0, "errors": [{"test": "Check message history", "error": "Expected both user message and AI response to be visible"}], "tests": [{"name": "patient-cases", "passed": false, "errors": [{"test": "Check message history", "error": "Expected both user message and AI response to be visible"}], "duration": 54493, "steps": [{"name": "Authenticate", "passed": true, "error": null, "duration": 12893}, {"name": "Handle new conversation modal", "passed": true, "error": null, "duration": 53}, {"name": "Send patient case message", "passed": true, "error": null, "duration": 18220}, {"name": "Check AI response", "passed": true, "error": null, "duration": 10201}, {"name": "Check message history", "passed": false, "error": "Expected both user message and AI response to be visible", "duration": 207}, {"name": "Check thread in sidebar", "passed": true, "error": null, "duration": 187}, {"name": "Send follow-up message", "passed": true, "error": null, "duration": 9408}, {"name": "Test file attachment", "passed": true, "error": null, "duration": 2088}]}], "startTime": "2025-06-25T10:09:13.597Z", "endTime": "2025-06-25T10:10:08.091Z", "duration": 54494}