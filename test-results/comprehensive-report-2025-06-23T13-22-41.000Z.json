{"totalTests": 0, "passed": 0, "failed": 0, "errors": [{"test": "Test message input", "error": "Message input not found"}, {"test": "Send patient case message", "error": "Message input not found"}, {"test": "Check AI response", "error": "AI response does not contain expected medical information"}, {"test": "Check message history", "error": "Expected both user message and AI response to be visible"}, {"test": "Navigate to Research section", "error": "Research section link not found"}, {"test": "Navigate to Quick Facts section", "error": "Quick Facts section link not found"}, {"test": "Test network error handling", "error": "Navigation timeout of 30000 ms exceeded"}, {"test": "setup", "error": "Protocol error (Page.captureScreenshot): Cannot take screenshot with 0 width."}], "tests": [{"name": "authentication", "passed": true, "errors": [], "duration": 9238, "steps": [{"name": "Navigate to home page", "passed": true, "error": null, "duration": 428}, {"name": "Check login form elements", "passed": true, "error": null, "duration": 1961}, {"name": "Request OTP", "passed": true, "error": null, "duration": 5143}, {"name": "Enter OTP", "passed": true, "error": null, "duration": 1}]}, {"name": "dashboard-navigation", "passed": true, "errors": [], "duration": 10863, "steps": [{"name": "Check dashboard layout", "passed": true, "error": null, "duration": 6367}, {"name": "Check sidebar elements", "passed": true, "error": null, "duration": 3057}, {"name": "Test new thread creation", "passed": true, "error": null, "duration": 263}]}, {"name": "messaging", "passed": false, "errors": [{"test": "Test message input", "error": "Message input not found"}], "duration": 4695, "steps": [{"name": "Test message input", "passed": false, "error": "Message input not found", "duration": 3407}, {"name": "Test file attachment", "passed": true, "error": null, "duration": 52}, {"name": "Test markdown rendering", "passed": true, "error": null, "duration": 215}]}, {"name": "patient-cases", "passed": false, "errors": [{"test": "Send patient case message", "error": "Message input not found"}, {"test": "Check AI response", "error": "AI response does not contain expected medical information"}, {"test": "Check message history", "error": "Expected both user message and AI response to be visible"}], "duration": 12489, "steps": [{"name": "Create new patient case", "passed": true, "error": null, "duration": 104}, {"name": "Send patient case message", "passed": false, "error": "Message input not found", "duration": 617}, {"name": "Check AI response", "passed": false, "error": "AI response does not contain expected medical information", "duration": 10352}, {"name": "Check message history", "passed": false, "error": "Expected both user message and AI response to be visible", "duration": 247}, {"name": "Check thread in sidebar", "passed": true, "error": null, "duration": 51}, {"name": "Test file attachment", "passed": true, "error": null, "duration": 53}]}, {"name": "research", "passed": false, "errors": [{"test": "Navigate to Research section", "error": "Research section link not found"}], "duration": 2646, "steps": [{"name": "Navigate to Research section", "passed": false, "error": "Research section link not found", "duration": 149}, {"name": "Create research thread", "passed": true, "error": null, "duration": 52}, {"name": "Ask research question", "passed": true, "error": null, "duration": 478}, {"name": "Check for citations", "passed": true, "error": null, "duration": 54}, {"name": "Check for data tables/visualizations", "passed": true, "error": null, "duration": 421}, {"name": "Test research export options", "passed": true, "error": null, "duration": 222}]}, {"name": "quick-facts", "passed": false, "errors": [{"test": "Navigate to Quick Facts section", "error": "Quick Facts section link not found"}], "duration": 2122, "steps": [{"name": "Navigate to Quick Facts section", "passed": false, "error": "Quick Facts section link not found", "duration": 175}, {"name": "Test drug dosage query", "passed": true, "error": null, "duration": 471}, {"name": "Test medical calculation", "passed": true, "error": null, "duration": 54}, {"name": "Test lab value interpretation", "passed": true, "error": null, "duration": 61}, {"name": "Check response formatting", "passed": true, "error": null, "duration": 55}, {"name": "Test response speed", "passed": true, "error": null, "duration": 53}]}, {"name": "error-handling", "passed": false, "errors": [{"test": "Test network error handling", "error": "Navigation timeout of 30000 ms exceeded"}, {"test": "setup", "error": "Protocol error (Page.captureScreenshot): Cannot take screenshot with 0 width."}], "duration": 37029, "steps": [{"name": "Test invalid phone number", "passed": true, "error": null, "duration": 4463}, {"name": "Test empty form submission", "passed": true, "error": null, "duration": 1585}]}], "startTime": "2025-06-23T13:21:09.908Z", "endTime": "2025-06-23T13:22:40.999Z", "duration": 91091}