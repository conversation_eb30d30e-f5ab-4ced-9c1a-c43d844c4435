{"totalTests": 0, "passed": 0, "failed": 0, "errors": [{"test": "Check AI response", "error": "AI response does not contain expected medical information"}, {"test": "Check message history", "error": "Expected both user message and AI response to be visible"}, {"test": "Check research response", "error": "AI response does not contain expected research information"}, {"test": "Check drug dosage response", "error": "Response does not contain dosage information"}, {"test": "Test response speed", "error": "Waiting failed: 10000ms exceeded"}, {"test": "Test network error handling", "error": "Navigation timeout of 30000 ms exceeded"}, {"test": "setup", "error": "Protocol error (Page.captureScreenshot): Cannot take screenshot with 0 width."}], "tests": [{"name": "authentication", "passed": true, "errors": [], "duration": 8663, "steps": [{"name": "Navigate to home page", "passed": true, "error": null, "duration": 408}, {"name": "Check login form elements", "passed": true, "error": null, "duration": 1949}, {"name": "Request OTP", "passed": true, "error": null, "duration": 4857}, {"name": "Enter OTP", "passed": true, "error": null, "duration": 1}]}, {"name": "dashboard-navigation", "passed": true, "errors": [], "duration": 20093, "steps": [{"name": "Authenticate", "passed": true, "error": null, "duration": 12296}, {"name": "Check dashboard layout", "passed": true, "error": null, "duration": 3431}, {"name": "Check sidebar elements", "passed": true, "error": null, "duration": 3055}, {"name": "Test new thread creation", "passed": true, "error": null, "duration": 421}]}, {"name": "messaging", "passed": true, "errors": [], "duration": 18702, "steps": [{"name": "Authenticate", "passed": true, "error": null, "duration": 12217}, {"name": "Test message input", "passed": true, "error": null, "duration": 4323}, {"name": "Test file attachment", "passed": true, "error": null, "duration": 924}, {"name": "Test markdown rendering", "passed": true, "error": null, "duration": 212}]}, {"name": "patient-cases", "passed": false, "errors": [{"test": "Check AI response", "error": "AI response does not contain expected medical information"}, {"test": "Check message history", "error": "Expected both user message and AI response to be visible"}], "duration": 53914, "steps": [{"name": "Authenticate", "passed": true, "error": null, "duration": 11977}, {"name": "Create new patient case conversation", "passed": true, "error": null, "duration": 1328}, {"name": "Send patient case message", "passed": true, "error": null, "duration": 19612}, {"name": "Check AI response", "passed": false, "error": "AI response does not contain expected medical information", "duration": 7358}, {"name": "Check message history", "passed": false, "error": "Expected both user message and AI response to be visible", "duration": 202}, {"name": "Check thread in sidebar", "passed": true, "error": null, "duration": 178}, {"name": "Send follow-up message", "passed": true, "error": null, "duration": 10199}, {"name": "Test file attachment", "passed": true, "error": null, "duration": 2126}]}, {"name": "research", "passed": false, "errors": [{"test": "Check research response", "error": "AI response does not contain expected research information"}], "duration": 52286, "steps": [{"name": "Authenticate", "passed": true, "error": null, "duration": 12038}, {"name": "Create research conversation", "passed": true, "error": null, "duration": 1094}, {"name": "Ask research question", "passed": true, "error": null, "duration": 17487}, {"name": "Check research response", "passed": false, "error": "AI response does not contain expected research information", "duration": 8343}, {"name": "Check for citations", "passed": true, "error": null, "duration": 52}, {"name": "Ask follow-up research question", "passed": true, "error": null, "duration": 12066}, {"name": "Check for data presentation", "passed": true, "error": null, "duration": 53}, {"name": "Check research thread in sidebar", "passed": true, "error": null, "duration": 52}]}, {"name": "quick-facts", "passed": false, "errors": [{"test": "Check drug dosage response", "error": "Response does not contain dosage information"}, {"test": "Test response speed", "error": "Waiting failed: 10000ms exceeded"}], "duration": 68992, "steps": [{"name": "Authenticate", "passed": true, "error": null, "duration": 12090}, {"name": "Create quick facts conversation", "passed": true, "error": null, "duration": 1082}, {"name": "Test drug dosage query", "passed": true, "error": null, "duration": 9420}, {"name": "Check drug dosage response", "passed": false, "error": "Response does not contain dosage information", "duration": 7358}, {"name": "Test medical calculation", "passed": true, "error": null, "duration": 7058}, {"name": "Test lab value interpretation", "passed": true, "error": null, "duration": 9253}, {"name": "Test quick reference", "passed": true, "error": null, "duration": 7636}, {"name": "Check response formatting", "passed": true, "error": null, "duration": 243}, {"name": "Check quick facts thread in sidebar", "passed": true, "error": null, "duration": 54}, {"name": "Test response speed", "passed": false, "error": "Waiting failed: 10000ms exceeded", "duration": 13757}]}, {"name": "error-handling", "passed": false, "errors": [{"test": "Test network error handling", "error": "Navigation timeout of 30000 ms exceeded"}, {"test": "setup", "error": "Protocol error (Page.captureScreenshot): Cannot take screenshot with 0 width."}], "duration": 37298, "steps": [{"name": "Test invalid phone number", "passed": true, "error": null, "duration": 4452}, {"name": "Test empty form submission", "passed": true, "error": null, "duration": 1573}]}], "startTime": "2025-06-23T14:01:35.880Z", "endTime": "2025-06-23T14:06:13.308Z", "duration": 277428}