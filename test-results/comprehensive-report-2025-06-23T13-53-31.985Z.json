{"totalTests": 0, "passed": 0, "failed": 0, "errors": [{"test": "Authenticate", "error": "Failed to authenticate"}, {"test": "Send patient case message", "error": "Message input not found"}, {"test": "Check AI response", "error": "AI response does not contain expected medical information"}, {"test": "Check message history", "error": "Expected both user message and AI response to be visible"}, {"test": "Send follow-up message", "error": "Message input not found for follow-up"}, {"test": "Authenticate", "error": "Failed to authenticate"}, {"test": "Ask research question", "error": "Message input not found"}, {"test": "Check research response", "error": "AI response does not contain expected research information"}, {"test": "Ask follow-up research question", "error": "Message input not found for follow-up"}, {"test": "Authenticate", "error": "Failed to authenticate"}, {"test": "Test drug dosage query", "error": "Message input not found"}, {"test": "Check drug dosage response", "error": "Response does not contain dosage information"}, {"test": "Test medical calculation", "error": "Message input not found"}, {"test": "Test lab value interpretation", "error": "Message input not found"}, {"test": "Test quick reference", "error": "Message input not found"}, {"test": "Test response speed", "error": "Message input not found"}, {"test": "Test network error handling", "error": "Navigation timeout of 30000 ms exceeded"}, {"test": "setup", "error": "Protocol error (Page.captureScreenshot): Cannot take screenshot with 0 width."}], "tests": [{"name": "authentication", "passed": true, "errors": [], "duration": 9084, "steps": [{"name": "Navigate to home page", "passed": true, "error": null, "duration": 475}, {"name": "Check login form elements", "passed": true, "error": null, "duration": 1963}, {"name": "Request OTP", "passed": true, "error": null, "duration": 4907}, {"name": "Enter OTP", "passed": true, "error": null, "duration": 0}]}, {"name": "dashboard-navigation", "passed": true, "errors": [], "duration": 20626, "steps": [{"name": "Authenticate", "passed": true, "error": null, "duration": 12756}, {"name": "Check dashboard layout", "passed": true, "error": null, "duration": 3483}, {"name": "Check sidebar elements", "passed": true, "error": null, "duration": 3057}, {"name": "Test new thread creation", "passed": true, "error": null, "duration": 433}]}, {"name": "messaging", "passed": true, "errors": [], "duration": 18887, "steps": [{"name": "Authenticate", "passed": true, "error": null, "duration": 12239}, {"name": "Test message input", "passed": true, "error": null, "duration": 4403}, {"name": "Test file attachment", "passed": true, "error": null, "duration": 943}, {"name": "Test markdown rendering", "passed": true, "error": null, "duration": 211}]}, {"name": "patient-cases", "passed": false, "errors": [{"test": "Authenticate", "error": "Failed to authenticate"}, {"test": "Send patient case message", "error": "Message input not found"}, {"test": "Check AI response", "error": "AI response does not contain expected medical information"}, {"test": "Check message history", "error": "Expected both user message and AI response to be visible"}, {"test": "Send follow-up message", "error": "Message input not found for follow-up"}], "duration": 26864, "steps": [{"name": "Authenticate", "passed": false, "error": "Failed to authenticate", "duration": 17789}, {"name": "Create new patient case conversation", "passed": true, "error": null, "duration": 104}, {"name": "Send patient case message", "passed": false, "error": "Message input not found", "duration": 154}, {"name": "Check AI response", "passed": false, "error": "AI response does not contain expected medical information", "duration": 7317}, {"name": "Check message history", "passed": false, "error": "Expected both user message and AI response to be visible", "duration": 167}, {"name": "Check thread in sidebar", "passed": true, "error": null, "duration": 52}, {"name": "Send follow-up message", "passed": false, "error": "Message input not found for follow-up", "duration": 191}, {"name": "Test file attachment", "passed": true, "error": null, "duration": 53}]}, {"name": "research", "passed": false, "errors": [{"test": "Authenticate", "error": "Failed to authenticate"}, {"test": "Ask research question", "error": "Message input not found"}, {"test": "Check research response", "error": "AI response does not contain expected research information"}, {"test": "Ask follow-up research question", "error": "Message input not found for follow-up"}], "duration": 27853, "steps": [{"name": "Authenticate", "passed": false, "error": "Failed to authenticate", "duration": 17783}, {"name": "Create research conversation", "passed": true, "error": null, "duration": 53}, {"name": "Ask research question", "passed": false, "error": "Message input not found", "duration": 176}, {"name": "Check research response", "passed": false, "error": "AI response does not contain expected research information", "duration": 8373}, {"name": "Check for citations", "passed": true, "error": null, "duration": 54}, {"name": "Ask follow-up research question", "passed": false, "error": "Message input not found for follow-up", "duration": 201}, {"name": "Check for data presentation", "passed": true, "error": null, "duration": 54}, {"name": "Check research thread in sidebar", "passed": true, "error": null, "duration": 51}]}, {"name": "quick-facts", "passed": false, "errors": [{"test": "Authenticate", "error": "Failed to authenticate"}, {"test": "Test drug dosage query", "error": "Message input not found"}, {"test": "Check drug dosage response", "error": "Response does not contain dosage information"}, {"test": "Test medical calculation", "error": "Message input not found"}, {"test": "Test lab value interpretation", "error": "Message input not found"}, {"test": "Test quick reference", "error": "Message input not found"}, {"test": "Test response speed", "error": "Message input not found"}], "duration": 27508, "steps": [{"name": "Authenticate", "passed": false, "error": "Failed to authenticate", "duration": 17793}, {"name": "Create quick facts conversation", "passed": true, "error": null, "duration": 52}, {"name": "Test drug dosage query", "passed": false, "error": "Message input not found", "duration": 199}, {"name": "Check drug dosage response", "passed": false, "error": "Response does not contain dosage information", "duration": 7348}, {"name": "Test medical calculation", "passed": false, "error": "Message input not found", "duration": 202}, {"name": "Test lab value interpretation", "passed": false, "error": "Message input not found", "duration": 203}, {"name": "Test quick reference", "passed": false, "error": "Message input not found", "duration": 176}, {"name": "Check response formatting", "passed": true, "error": null, "duration": 187}, {"name": "Check quick facts thread in sidebar", "passed": true, "error": null, "duration": 53}, {"name": "Test response speed", "passed": false, "error": "Message input not found", "duration": 188}]}, {"name": "error-handling", "passed": false, "errors": [{"test": "Test network error handling", "error": "Navigation timeout of 30000 ms exceeded"}, {"test": "setup", "error": "Protocol error (Page.captureScreenshot): Cannot take screenshot with 0 width."}], "duration": 37254, "steps": [{"name": "Test invalid phone number", "passed": true, "error": null, "duration": 4442}, {"name": "Test empty form submission", "passed": true, "error": null, "duration": 1581}]}], "startTime": "2025-06-23T13:50:31.898Z", "endTime": "2025-06-23T13:53:31.985Z", "duration": 180087}