#!/usr/bin/env node

const puppeteer = require('puppeteer');
const fs = require('fs');
const path = require('path');

async function debugFileUpload() {
  console.log('🔍 Debugging File Upload Issue');
  console.log('==============================\n');

  let browser;
  try {
    browser = await puppeteer.launch({ 
      headless: false,
      devtools: true,
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });
    
    const page = await browser.newPage();
    
    // Capture ALL console logs
    const consoleLogs = [];
    page.on('console', msg => {
      const text = msg.text();
      consoleLogs.push({ type: msg.type(), text });
      console.log(`[${msg.type().toUpperCase()}]`, text);
    });
    
    page.on('pageerror', error => {
      console.error('❌ Page Error:', error.message);
    });
    
    // Create a simple test PDF
    const testPDF = `%PDF-1.4
1 0 obj<</Type/Catalog/Pages 2 0 R>>endobj
2 0 obj<</Type/Pages/Kids[3 0 R]/Count 1>>endobj
xref 0 3
0000000000 65535 f 
0000000010 00000 n 
0000000053 00000 n 
trailer<</Size 3/Root 1 0 R>>
startxref 109
%%EOF`;

    const testFilePath = '/tmp/test-debug.pdf';
    fs.writeFileSync(testFilePath, testPDF);
    console.log('📄 Created test file:', testFilePath);

    // Navigate and login
    console.log('\n1️⃣ Navigating to app...');
    await page.goto('http://localhost:3000', { waitUntil: 'networkidle2' });
    
    // Login process
    console.log('2️⃣ Logging in...');
    await page.type('input[placeholder*="98193 04846"]', '+919819304846');
    await page.click('button[type="submit"]');
    
    // Wait for OTP screen
    await page.waitForSelector('input[type="text"]', { timeout: 5000 });
    await page.evaluate(() => {
      const otpInput = document.querySelector('input[type="text"]');
      if (otpInput) {
        otpInput.value = '123456';
        otpInput.dispatchEvent(new Event('input', { bubbles: true }));
        otpInput.dispatchEvent(new Event('change', { bubbles: true }));
      }
    });
    
    // Click verify
    await page.evaluate(() => {
      const buttons = Array.from(document.querySelectorAll('button'));
      const verifyButton = buttons.find(btn => btn.textContent.includes('Verify'));
      if (verifyButton) verifyButton.click();
    });
    
    // Wait for navigation and authentication
    console.log('3️⃣ Waiting for authentication...');
    await page.waitForTimeout(5000);
    
    // Check if we're authenticated
    const isAuthenticated = await page.evaluate(() => {
      return !!localStorage.getItem('access_token');
    });
    console.log('Authentication status:', isAuthenticated);
    
    // Try to find chat interface
    console.log('\n4️⃣ Looking for chat interface...');
    
    // Check current URL
    const currentUrl = await page.url();
    console.log('Current URL:', currentUrl);
    
    // If not on chat page, navigate to one
    if (!currentUrl.includes('/chat/')) {
      console.log('Navigating to a chat thread...');
      await page.goto('http://localhost:3000/chat/test-thread-123', { waitUntil: 'networkidle2' });
      await page.waitForTimeout(2000);
    }
    
    // Look for file upload elements
    console.log('\n5️⃣ Searching for file upload elements...');
    
    // Method 1: Direct file input
    const fileInputs = await page.$$('input[type="file"]');
    console.log('Found file inputs:', fileInputs.length);
    
    // Method 2: Look for attachment buttons
    const attachmentButtons = await page.evaluate(() => {
      const selectors = [
        '[aria-label*="attach"]',
        '[title*="attach"]',
        '[class*="attach"]',
        'button svg[data-testid*="AttachFile"]',
        'button:has(svg[class*="attach"])'
      ];
      
      const found = [];
      for (const selector of selectors) {
        try {
          const elements = document.querySelectorAll(selector);
          if (elements.length > 0) {
            found.push({ selector, count: elements.length });
          }
        } catch (e) {
          // Ignore invalid selectors
        }
      }
      return found;
    });
    
    console.log('Attachment button search results:', attachmentButtons);
    
    // Method 3: Look for any clickable attachment element
    const attachmentElements = await page.evaluate(() => {
      const elements = Array.from(document.querySelectorAll('*'));
      return elements
        .filter(el => {
          const text = el.textContent || '';
          const className = el.className || '';
          const ariaLabel = el.getAttribute('aria-label') || '';
          return (
            text.toLowerCase().includes('attach') ||
            className.toLowerCase().includes('attach') ||
            ariaLabel.toLowerCase().includes('attach')
          );
        })
        .map(el => ({
          tag: el.tagName,
          text: el.textContent?.trim().substring(0, 50),
          className: el.className,
          id: el.id
        }));
    });
    
    console.log('\nElements with "attach":', attachmentElements);
    
    // Try to click an attachment button
    if (attachmentElements.length > 0) {
      console.log('\n6️⃣ Trying to click attachment button...');
      
      const clicked = await page.evaluate(() => {
        const elements = Array.from(document.querySelectorAll('*'));
        const attachElement = elements.find(el => {
          const className = el.className || '';
          return className.toLowerCase().includes('attach') && 
                 (el.tagName === 'BUTTON' || el.tagName === 'ICONBUTTON' || el.onclick);
        });
        
        if (attachElement) {
          attachElement.click();
          return true;
        }
        return false;
      });
      
      console.log('Clicked attachment element:', clicked);
      
      if (clicked) {
        await page.waitForTimeout(1000);
        
        // Check for file input again
        const newFileInputs = await page.$$('input[type="file"]');
        console.log('File inputs after click:', newFileInputs.length);
        
        if (newFileInputs.length > 0) {
          console.log('\n7️⃣ Uploading file...');
          await newFileInputs[0].uploadFile(testFilePath);
          console.log('File uploaded!');
          
          await page.waitForTimeout(2000);
          
          // Check console logs for errors
          const recentLogs = consoleLogs.slice(-10);
          console.log('\nRecent console logs:');
          recentLogs.forEach(log => {
            console.log(`[${log.type}] ${log.text}`);
          });
        }
      }
    }
    
    // Take screenshot
    await page.screenshot({ path: 'debug-file-upload-final.png' });
    console.log('\n📸 Screenshot saved: debug-file-upload-final.png');
    
    // Print all collected console logs
    console.log('\n📋 All Console Logs:');
    consoleLogs.forEach((log, i) => {
      if (log.text.includes('FileAttachment') || 
          log.text.includes('originalFile') || 
          log.text.includes('error') ||
          log.text.includes('Missing')) {
        console.log(`${i}: [${log.type}] ${log.text}`);
      }
    });
    
    // Evaluate the current state
    const debugInfo = await page.evaluate(() => {
      return {
        hasFileInput: !!document.querySelector('input[type="file"]'),
        hasChatInterface: !!document.querySelector('[class*="ChatInterface"]'),
        hasMessageInput: !!document.querySelector('textarea, input[type="text"][placeholder*="message"]'),
        localStorage: {
          hasToken: !!localStorage.getItem('access_token'),
          hasUser: !!localStorage.getItem('user')
        }
      };
    });
    
    console.log('\n🔍 Debug Info:', debugInfo);
    
    // Clean up
    if (fs.existsSync(testFilePath)) {
      fs.unlinkSync(testFilePath);
    }
    
  } catch (error) {
    console.error('❌ Debug failed:', error.message);
    console.error(error.stack);
  } finally {
    console.log('\n🔄 Keeping browser open for manual inspection...');
    console.log('Press Ctrl+C to close when done.');
    
    // Keep browser open for manual debugging
    await new Promise(() => {});
  }
}

if (require.main === module) {
  debugFileUpload();
}

module.exports = { debugFileUpload };