const puppeteer = require('puppeteer');

// Configuration
const BASE_URL = process.env.BASE_URL || 'http://localhost:3000';
const HEADLESS = process.env.HEADLESS !== 'false';
const TEST_PHONE = process.env.TEST_PHONE || '+919819304846';

// Colored logging
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

const log = {
  info: (msg, data) => console.log(`${colors.blue}[INFO]${colors.reset} ${msg}`, data || ''),
  error: (msg, data) => console.log(`${colors.red}[ERROR]${colors.reset} ${msg}`, data || ''),
  success: (msg, data) => console.log(`${colors.green}[SUCCESS]${colors.reset} ${msg}`, data || ''),
  warn: (msg, data) => console.log(`${colors.yellow}[WARN]${colors.reset} ${msg}`, data || ''),
  json: (msg, data) => console.log(`${colors.magenta}[JSON ERROR]${colors.reset} ${msg}`, data || ''),
  debug: (msg, data) => console.log(`${colors.cyan}[DEBUG]${colors.reset} ${msg}`, data || '')
};

// Track all JSON errors
const jsonErrors = [];
const apiCalls = {
  requests: [],
  responses: []
};

async function setupJSONErrorDetection(page) {
  // Override JSON.parse to catch all parsing errors
  await page.evaluateOnNewDocument(() => {
    const originalParse = JSON.parse;
    JSON.parse = function(...args) {
      try {
        return originalParse.apply(this, args);
      } catch (error) {
        console.error('JSON.parse error caught:', {
          error: error.message,
          input: args[0] ? args[0].substring(0, 200) : 'undefined',
          stack: error.stack
        });
        throw error;
      }
    };
  });

  // Monitor console for errors
  page.on('console', async msg => {
    const text = msg.text();
    
    if (msg.type() === 'error' || text.includes('JSON') || text.includes('parse')) {
      const args = [];
      for (const arg of msg.args()) {
        try {
          args.push(await arg.jsonValue());
        } catch (e) {
          args.push('[Unable to serialize]');
        }
      }
      
      const errorInfo = {
        type: msg.type(),
        text,
        args,
        location: msg.location(),
        timestamp: new Date().toISOString()
      };
      
      jsonErrors.push(errorInfo);
      log.json('Console JSON/Parse Error', errorInfo);
    }
  });

  // Monitor page errors
  page.on('pageerror', error => {
    if (error.message.includes('JSON') || error.message.includes('parse')) {
      const errorInfo = {
        message: error.message,
        stack: error.stack,
        timestamp: new Date().toISOString()
      };
      jsonErrors.push(errorInfo);
      log.json('Page JavaScript Error', errorInfo);
    }
  });

  // Monitor network requests
  page.on('request', request => {
    const url = request.url();
    if (url.includes('/api/')) {
      const requestInfo = {
        url,
        method: request.method(),
        headers: request.headers(),
        postData: request.postData(),
        timestamp: new Date().toISOString()
      };
      
      apiCalls.requests.push(requestInfo);
      
      // Try to parse POST data as JSON
      if (requestInfo.postData) {
        try {
          const parsed = JSON.parse(requestInfo.postData);
          log.debug('API Request JSON', { url, data: parsed });
        } catch (e) {
          log.json('Invalid JSON in request body', {
            url,
            error: e.message,
            body: requestInfo.postData
          });
        }
      }
    }
  });

  // Monitor network responses
  page.on('response', async response => {
    const url = response.url();
    const status = response.status();
    const headers = response.headers();
    
    if (url.includes('/api/') || headers['content-type']?.includes('json')) {
      try {
        const text = await response.text();
        const responseInfo = {
          url,
          status,
          headers,
          body: text,
          timestamp: new Date().toISOString()
        };
        
        apiCalls.responses.push(responseInfo);
        
        // Try to parse as JSON
        if (headers['content-type']?.includes('json') || text.trim().startsWith('{') || text.trim().startsWith('[')) {
          try {
            const parsed = JSON.parse(text);
            log.debug('API Response JSON', { url, status, data: parsed });
            
            // Check for error responses
            if (parsed.error || parsed.message || status >= 400) {
              log.error('API Error Response', { url, status, response: parsed });
            }
          } catch (e) {
            const errorInfo = {
              url,
              status,
              error: e.message,
              body: text.substring(0, 500),
              timestamp: new Date().toISOString()
            };
            jsonErrors.push(errorInfo);
            log.json('Invalid JSON in response', errorInfo);
          }
        }
      } catch (e) {
        log.warn('Could not read response body', { url, error: e.message });
      }
    }
  });

  // Monitor failed requests
  page.on('requestfailed', request => {
    const url = request.url();
    if (url.includes('/api/')) {
      log.error('API Request Failed', {
        url,
        method: request.method(),
        failure: request.failure()
      });
    }
  });
}

async function testLoginFlow() {
  log.info('Starting Interactive Doctor Dashboard Test');
  log.info('Configuration', { BASE_URL, HEADLESS, TEST_PHONE });
  
  const browser = await puppeteer.launch({
    headless: HEADLESS,
    args: ['--no-sandbox', '--disable-setuid-sandbox'],
    devtools: !HEADLESS
  });
  
  try {
    const page = await browser.newPage();
    await page.setViewport({ width: 1440, height: 900 });
    
    // Set up JSON error detection
    await setupJSONErrorDetection(page);
    
    // Navigate to the application
    log.info('Navigating to application...');
    const response = await page.goto(BASE_URL, {
      waitUntil: ['networkidle0', 'domcontentloaded'],
      timeout: 30000
    });
    
    log.success('Page loaded', { status: response.status() });
    
    // Take initial screenshot
    await page.screenshot({ path: 'test-screenshots/1-initial-load.png' });
    
    // Wait for the phone input to be visible
    log.info('Looking for phone input field...');
    const phoneSelector = 'input[placeholder="+91 98193 04846"], input[aria-label*="Phone Number"], .MuiTextField-root input';
    
    try {
      await page.waitForSelector(phoneSelector, { timeout: 5000 });
      log.success('Phone input field found');
      
      // Click on the phone input
      await page.click(phoneSelector);
      
      // Clear any existing value and type the phone number
      await page.evaluate((selector) => {
        const input = document.querySelector(selector);
        input.value = '';
      }, phoneSelector);
      
      await page.type(phoneSelector, TEST_PHONE, { delay: 50 });
      log.success('Phone number entered', { phone: TEST_PHONE });
      
      // Take screenshot after entering phone
      await page.screenshot({ path: 'test-screenshots/2-phone-entered.png' });
      
      // Find and click the submit button
      log.info('Looking for submit button...');
      const submitButton = await page.evaluateHandle(() => {
        // Look for various button patterns
        const buttons = Array.from(document.querySelectorAll('button'));
        return buttons.find(btn => {
          const text = btn.textContent.toLowerCase();
          return text.includes('send') || text.includes('login') || text.includes('continue') || text.includes('otp');
        });
      });
      
      if (submitButton && await submitButton.evaluate(el => el !== null)) {
        log.success('Submit button found');
        
        // Set up promise to wait for navigation or OTP field
        const waitForNext = Promise.race([
          page.waitForNavigation({ waitUntil: 'networkidle0', timeout: 10000 }).catch(() => null),
          page.waitForSelector('input[maxlength="6"], input[placeholder*="otp" i], input[name="otp"]', { timeout: 10000 }).catch(() => null),
          page.waitForFunction(() => {
            // Wait for any error messages
            const errors = document.querySelectorAll('.error, .alert, [class*="error"], [class*="alert"]');
            return errors.length > 0;
          }, { timeout: 10000 }).catch(() => null)
        ]);
        
        // Click the button
        await submitButton.click();
        log.info('Submit button clicked, waiting for response...');
        
        // Wait for something to happen
        await waitForNext;
        
        // Wait a bit for any async operations
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        // Take screenshot after submission
        await page.screenshot({ path: 'test-screenshots/3-after-submit.png' });
        
        // Check what happened
        const currentUrl = page.url();
        const pageContent = await page.content();
        
        // Check for OTP field
        const otpField = await page.$('input[maxlength="6"], input[placeholder*="otp" i], input[name="otp"]');
        if (otpField) {
          log.success('OTP field appeared - login request successful');
        }
        
        // Check for error messages
        const errorMessages = await page.evaluate(() => {
          const errors = [];
          document.querySelectorAll('.error, .alert, [class*="error"], [class*="alert"], [role="alert"]').forEach(el => {
            if (el.textContent.trim()) {
              errors.push(el.textContent.trim());
            }
          });
          return errors;
        });
        
        if (errorMessages.length > 0) {
          log.error('Error messages found on page', errorMessages);
        }
        
      } else {
        log.error('Submit button not found');
        
        // Debug: list all buttons on the page
        const buttons = await page.evaluate(() => {
          return Array.from(document.querySelectorAll('button')).map(btn => ({
            text: btn.textContent,
            type: btn.type,
            classes: btn.className
          }));
        });
        log.debug('Available buttons', buttons);
      }
      
    } catch (error) {
      log.error('Error during login flow', {
        message: error.message,
        stack: error.stack
      });
      
      // Take error screenshot
      await page.screenshot({ path: 'test-screenshots/error-state.png' });
      
      // Get page content for debugging
      const content = await page.content();
      log.debug('Page HTML preview', content.substring(0, 1000));
    }
    
    // Final summary
    log.info('\n=== TEST SUMMARY ===');
    log.info(`Total API Requests: ${apiCalls.requests.length}`);
    log.info(`Total API Responses: ${apiCalls.responses.length}`);
    log.info(`JSON Errors Detected: ${jsonErrors.length}`);
    
    if (jsonErrors.length > 0) {
      log.error('\n=== JSON ERRORS DETAIL ===');
      jsonErrors.forEach((error, index) => {
        log.json(`Error ${index + 1}:`, error);
      });
    }
    
    if (apiCalls.responses.length > 0) {
      log.info('\n=== API RESPONSES ===');
      apiCalls.responses.forEach(response => {
        log.info(`${response.method || 'GET'} ${response.url} - Status: ${response.status}`);
        if (response.body && response.body.length < 200) {
          log.debug('Response body:', response.body);
        }
      });
    }
    
  } finally {
    await browser.close();
  }
}

// Create screenshots directory
const fs = require('fs');
if (!fs.existsSync('test-screenshots')) {
  fs.mkdirSync('test-screenshots');
}

// Run the test
testLoginFlow()
  .then(() => {
    if (jsonErrors.length > 0) {
      log.error(`\nTest completed with ${jsonErrors.length} JSON errors`);
      process.exit(1);
    } else {
      log.success('\nTest completed successfully - No JSON errors detected');
      process.exit(0);
    }
  })
  .catch(error => {
    log.error('\nTest failed:', error);
    process.exit(1);
  });