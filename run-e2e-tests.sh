#!/bin/bash

# Doctor Dashboard E2E Test Runner Script
# This script runs the comprehensive E2E test suite with proper setup and cleanup

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Default values
DEFAULT_PHONE="+919819304846"
DEFAULT_BASE_URL="http://localhost:3000"
DEFAULT_HEADLESS="true"

# Function to display usage
usage() {
    echo -e "${CYAN}Doctor Dashboard E2E Test Runner${NC}"
    echo -e "${YELLOW}Usage:${NC} $0 [options] [test-suites...]"
    echo ""
    echo -e "${YELLOW}Options:${NC}"
    echo "  -h, --help          Show this help message"
    echo "  -p, --phone         Test phone number (default: $DEFAULT_PHONE)"
    echo "  -o, --otp           OTP code for automated entry"
    echo "  -u, --url           Base URL (default: $DEFAULT_BASE_URL)"
    echo "  -H, --headed        Run in headed mode (show browser)"
    echo "  -s, --suite         Run specific test suite(s)"
    echo "  -c, --cleanup       Cleanup old test results before running"
    echo "  -v, --verbose       Enable verbose logging"
    echo "  -r, --no-clear      Skip Redis rate limit clearing"
    echo ""
    echo -e "${YELLOW}Available Test Suites:${NC}"
    echo "  - authentication"
    echo "  - dashboard-navigation"
    echo "  - messaging"
    echo "  - patient-cases"
    echo "  - research"
    echo "  - quick-facts"
    echo "  - error-handling"
    echo "  - all (default)"
    echo ""
    echo -e "${YELLOW}Examples:${NC}"
    echo "  $0                                    # Run all tests"
    echo "  $0 -s authentication                  # Run only authentication tests"
    echo "  $0 -H -s authentication messaging     # Run specific tests in headed mode"
    echo "  $0 -o 123456 -c                      # Run all tests with OTP and cleanup"
}

# Parse command line arguments
TEST_PHONE=$DEFAULT_PHONE
TEST_BASE_URL=$DEFAULT_BASE_URL
TEST_HEADLESS=$DEFAULT_HEADLESS
TEST_OTP=""
CLEANUP=false
VERBOSE=false
CLEAR_REDIS=true
TEST_SUITES=()

while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            usage
            exit 0
            ;;
        -p|--phone)
            TEST_PHONE="$2"
            shift 2
            ;;
        -o|--otp)
            TEST_OTP="$2"
            shift 2
            ;;
        -u|--url)
            TEST_BASE_URL="$2"
            shift 2
            ;;
        -H|--headed)
            TEST_HEADLESS="false"
            shift
            ;;
        -s|--suite)
            shift
            while [[ $# -gt 0 && ! "$1" =~ ^- ]]; do
                TEST_SUITES+=("$1")
                shift
            done
            ;;
        -c|--cleanup)
            CLEANUP=true
            shift
            ;;
        -v|--verbose)
            VERBOSE=true
            shift
            ;;
        -r|--no-clear)
            CLEAR_REDIS=false
            shift
            ;;
        *)
            TEST_SUITES+=("$1")
            shift
            ;;
    esac
done

# Function to clear Redis rate limits
clear_rate_limits() {
    echo -e "${CYAN}Clearing Redis rate limits for test phone...${NC}"
    if [ -f "rate-limit-manager.js" ]; then
        node rate-limit-manager.js clear
        if [ $? -eq 0 ]; then
            echo -e "${GREEN}✓ Rate limits cleared${NC}"
            return 0
        else
            echo -e "${YELLOW}⚠ Could not clear rate limits${NC}"
            return 1
        fi
    else
        echo -e "${YELLOW}⚠ rate-limit-manager.js not found - skipping rate limit clearing${NC}"
        return 1
    fi
}

# Function to check if server is running
check_server() {
    echo -e "${CYAN}Checking if server is running at $TEST_BASE_URL...${NC}"
    if curl -s -o /dev/null -w "%{http_code}" "$TEST_BASE_URL" | grep -q "200"; then
        echo -e "${GREEN}✓ Server is running${NC}"
        return 0
    else
        echo -e "${RED}✗ Server is not running at $TEST_BASE_URL${NC}"
        return 1
    fi
}

# Function to cleanup old test results
cleanup_results() {
    echo -e "${CYAN}Cleaning up old test results...${NC}"
    rm -rf test-results/screenshots/*
    rm -rf test-results/*.json
    rm -rf test-logs/*
    echo -e "${GREEN}✓ Cleanup complete${NC}"
}

# Function to ensure directories exist
setup_directories() {
    mkdir -p test-results/screenshots
    mkdir -p test-logs
}

# Function to run tests
run_tests() {
    local suites="$1"
    
    # Export environment variables
    export TEST_PHONE
    export TEST_BASE_URL
    export TEST_HEADLESS
    [[ -n "$TEST_OTP" ]] && export TEST_OTP
    [[ "$VERBOSE" == "true" ]] && export DEBUG="puppeteer:*"
    
    echo -e "${CYAN}Running E2E tests...${NC}"
    echo -e "${BLUE}Configuration:${NC}"
    echo -e "  Phone: $TEST_PHONE"
    echo -e "  URL: $TEST_BASE_URL"
    echo -e "  Headless: $TEST_HEADLESS"
    [[ -n "$TEST_OTP" ]] && echo -e "  OTP: $TEST_OTP"
    echo -e "  Suites: ${suites:-all}"
    echo ""
    
    # Run the tests
    if [[ -n "$suites" ]]; then
        node run-all-tests.js $suites
    else
        node run-all-tests.js
    fi
    
    return $?
}

# Function to display test results
display_results() {
    local exit_code=$1
    
    echo ""
    if [[ $exit_code -eq 0 ]]; then
        echo -e "${GREEN}════════════════════════════════════════════════════${NC}"
        echo -e "${GREEN}✓ All tests passed successfully!${NC}"
        echo -e "${GREEN}════════════════════════════════════════════════════${NC}"
    else
        echo -e "${RED}════════════════════════════════════════════════════${NC}"
        echo -e "${RED}✗ Some tests failed. Check the logs for details.${NC}"
        echo -e "${RED}════════════════════════════════════════════════════${NC}"
    fi
    
    # Show report location
    echo ""
    echo -e "${CYAN}Test reports saved to:${NC}"
    echo -e "  - Screenshots: ${PURPLE}test-results/screenshots/${NC}"
    echo -e "  - JSON Report: ${PURPLE}test-results/comprehensive-report-*.json${NC}"
    echo -e "  - Logs: ${PURPLE}test-logs/${NC}"
}

# Main execution
main() {
    echo -e "${PURPLE}╔════════════════════════════════════════════════════╗${NC}"
    echo -e "${PURPLE}║          Doctor Dashboard E2E Test Runner          ║${NC}"
    echo -e "${PURPLE}╚════════════════════════════════════════════════════╝${NC}"
    echo ""
    
    # Check if npm dependencies are installed
    if [[ ! -d "node_modules" ]]; then
        echo -e "${YELLOW}Installing dependencies...${NC}"
        npm install
    fi
    
    # Check if server is running
    if ! check_server; then
        echo -e "${YELLOW}Would you like to start the server? (y/n)${NC}"
        read -r response
        if [[ "$response" == "y" ]]; then
            echo -e "${CYAN}Starting server...${NC}"
            npm run dev &
            SERVER_PID=$!
            sleep 5
            if ! check_server; then
                echo -e "${RED}Failed to start server${NC}"
                exit 1
            fi
        else
            echo -e "${RED}Server must be running to execute tests${NC}"
            exit 1
        fi
    fi
    
    # Clear Redis rate limits before tests (if enabled)
    if [[ "$CLEAR_REDIS" == "true" ]]; then
        clear_rate_limits
    fi
    
    # Cleanup if requested
    [[ "$CLEANUP" == "true" ]] && cleanup_results
    
    # Setup directories
    setup_directories
    
    # Run tests
    run_tests "${TEST_SUITES[*]}"
    TEST_EXIT_CODE=$?
    
    # Display results
    display_results $TEST_EXIT_CODE
    
    # Cleanup server if we started it
    if [[ -n "$SERVER_PID" ]]; then
        echo ""
        echo -e "${YELLOW}Stopping server...${NC}"
        kill $SERVER_PID 2>/dev/null
    fi
    
    exit $TEST_EXIT_CODE
}

# Run main function
main