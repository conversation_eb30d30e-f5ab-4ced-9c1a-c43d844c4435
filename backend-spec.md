# Doctor Dashboard Backend API Specification

## Overview

This document outlines the backend API specification for the Doctor Dashboard application authentication system. The authentication system will be implemented using Next.js API routes.

## Technology Stack

- **Framework**: Next.js 15 with App Router API routes
- **Database**: PostgreSQL (existing database)
- **Authentication**: JWT-based authentication with refresh tokens
- **Phone Verification**: SMS service (Twilio, AWS SNS, or similar)

## Database Schema

### Users Table

The existing users table will be used with the following configuration for doctors:

```sql
create table users
(
    id                        text not null,
    phone                     text not null,
    name                      text,
    sex                       text,
    dob                       date,
    email                     text,
    weight                    real,
    height                    real,
    waist                     real,
    body_fat                  real,
    diet                      text,
    drinks                    boolean,
    hormonal_ctps             boolean   default false,
    menopausal_state          text,
    pregnancy                 boolean   default false,
    smokes                    boolean,
    sleep_sensitivity         real,
    sleep_time                real,
    wakeup_time               real,
    sleep_normalize_max_limit real,
    created_at                timestamp default now(),
    updated_at                timestamp default now(),
    source                    text,
    access                    boolean   default false,  -- Changed: false by default for doctors
    status                    text,
    report_prompt             text,
    survey_prompt             text,
    report_id                 varchar(255),
    survey_id                 varchar(255),
    role                      text      default 'doctor', -- Changed: default role is doctor
    meta                      jsonb     default '{}'::jsonb,
    first_name                varchar,
    last_name                 varchar,
    tenant_id                 varchar   default '5005'::character varying, -- Changed: tenant 5005 for doctors
    user_id                   varchar(255),
    chronic_condition         text
);
```

### Meta Field Structure for Doctors

The `meta` JSONB field in the users table will store doctor-specific information:

```json
{
  "organization": "General Hospital",
  "license_number": "MD123456",
  "specialization": "Internal Medicine",
  "years_of_experience": 10,
  "verified_at": "2024-01-15T10:30:00Z",
  "verification_status": "pending", // pending, approved, rejected
  "verification_notes": "Verified license with state board"
}
```

### Existing Tables Used for Authentication

#### Verification Token Table (for OTP storage)
```sql
create table verification_token
(
    identifier text                     not null,
    expires    timestamp with time zone not null,
    token      text                     not null,
    primary key (identifier, token)
);
```
**Usage**: Store OTP codes with phone number as identifier

#### Web Chat Sessions Table (for JWT storage)
```sql
create table web_chat_sessions
(
    id         uuid                     not null primary key,
    user_id    uuid                     not null,
    phone      varchar(50)              not null,
    tenant     varchar(255)             not null,
    created_at timestamp with time zone default CURRENT_TIMESTAMP,
    expires_at timestamp with time zone not null
);
```
**Usage**: Store active JWT sessions with expiration tracking

#### Refresh Tokens Table
```sql
create table refresh_tokens
(
    user_id           uuid not null primary key,
    refresh_token_jti uuid not null unique,
    created_at        timestamp default CURRENT_TIMESTAMP
);
```
**Usage**: Store refresh tokens for JWT renewal


## Next.js API Routes for Authentication

All authentication endpoints will be implemented as Next.js API routes in the `/src/app/api/` directory.

### 1. Request Phone Verification

**File**: `/src/app/api/auth/request-otp/route.js`
**Endpoint**: `POST /api/auth/request-otp`

**Request Body**:
```json
{
  "phone": "+**********"
}
```

**Response**:
```json
{
  "success": true,
  "message": "OTP sent successfully",
  "expires_in": 300
}
```

**Error Response**:
```json
{
  "success": false,
  "error": "Invalid phone number format"
}
```

**Implementation Notes**:
- Generate 6-digit OTP code
- Store in `verification_token` table:
  - `identifier`: phone number
  - `token`: OTP code
  - `expires`: 5 minutes from now
- Send SMS via chosen SMS provider
- Limit attempts to prevent abuse (max 3 requests per phone per hour)
- Clean up expired tokens periodically

### 2. Verify Phone and Register

**File**: `/src/app/api/auth/verify-and-register/route.js`
**Endpoint**: `POST /api/auth/verify-and-register`

**Request Body**:
```json
{
  "phone": "+**********",
  "otp": "123456",
  "name": "Dr. John Smith",
  "organization": "General Hospital",
  "license_number": "MD123456",
  "specialization": "Internal Medicine" // optional
}
```

**Response**:
```json
{
  "success": true,
  "message": "Registration submitted for approval",
  "user_id": "dr-uuid-here"
}
```

**Error Responses**:
```json
{
  "success": false,
  "error": "Invalid or expired OTP"
}
```

```json
{
  "success": false,
  "error": "Phone number already registered"
}
```

**Implementation Notes**:
- Verify OTP from `verification_token` table using phone as identifier
- Create user record with `access: false`, `role: 'doctor'`, `tenant_id: '5005'`
- Store doctor information in `meta` field:
  ```json
  {
    "organization": "General Hospital",
    "license_number": "MD123456", 
    "specialization": "Internal Medicine",
    "verification_status": "pending"
  }
  ```
- Clean up used verification token
- Send notification to admin for manual approval

### 3. Login with Phone Verification

**File**: `/src/app/api/auth/login/route.js`
**Endpoint**: `POST /api/auth/login`

**Request Body**:
```json
{
  "phone": "+**********"
}
```

**Response** (if not verified):
```json
{
  "success": true,
  "message": "OTP sent for login verification",
  "requires_otp": true
}
```

**Response** (if account pending):
```json
{
  "success": false,
  "error": "Account pending approval",
  "status": "pending_approval"
}
```

### 4. Verify Login OTP

**File**: `/src/app/api/auth/verify-login/route.js`
**Endpoint**: `POST /api/auth/verify-login`

**Request Body**:
```json
{
  "phone": "+**********",
  "otp": "123456"
}
```

**Response**:
```json
{
  "success": true,
  "access_token": "jwt-access-token-here",
  "refresh_token": "jwt-refresh-token-here",
  "expires_in": 3600,
  "user": {
    "id": "dr-uuid",
    "name": "Dr. John Smith",
    "phone": "+**********",
    "role": "doctor",
    "meta": {
      "organization": "General Hospital",
      "license_number": "MD123456",
      "specialization": "Internal Medicine",
      "verification_status": "approved",
      "verified_at": "2024-01-15T10:30:00Z"
    }
  }
}
```

**Implementation Notes**:
- Verify OTP from `verification_token` table
- Check user access is approved (`access: true`)
- Generate JWT access token (1 hour expiry) and refresh token
- Store session in `web_chat_sessions`:
  - `user_id`: user UUID
  - `phone`: user phone
  - `tenant`: '5005'
  - `expires_at`: 1 hour from now
- Store refresh token in `refresh_tokens` table
- Clean up used verification token
```

**Error Response**:
```json
{
  "success": false,
  "error": "Account not approved yet",
  "status": "pending_approval"
}
```

### 5. Get Current User

**File**: `/src/app/api/auth/me/route.js`
**Endpoint**: `GET /api/auth/me`

**Headers**: `Authorization: Bearer <jwt-token>`

**Response**:
```json
{
  "success": true,
  "user": {
    "id": "dr-uuid",
    "name": "Dr. John Smith",
    "phone": "+**********",
    "role": "doctor",
    "meta": {
      "organization": "General Hospital",
      "license_number": "MD123456",
      "specialization": "Internal Medicine",
      "verification_status": "approved",
      "verified_at": "2024-01-15T10:30:00Z"
    }
  }
}
```

### 6. Refresh Token

**File**: `/src/app/api/auth/refresh/route.js`
**Endpoint**: `POST /api/auth/refresh`

**Request Body**:
```json
{
  "refresh_token": "jwt-refresh-token-here"
}
```

**Response**:
```json
{
  "success": true,
  "access_token": "new-jwt-access-token",
  "expires_in": 3600
}
```

**Implementation Notes**:
- Validate refresh token from `refresh_tokens` table
- Generate new access token with 1 hour expiry
- Update session in `web_chat_sessions` with new expiry
- Return new access token

### 7. Logout

**File**: `/src/app/api/auth/logout/route.js`
**Endpoint**: `POST /api/auth/logout`

**Headers**: `Authorization: Bearer <jwt-token>`

**Response**:
```json
{
  "success": true,
  "message": "Logged out successfully"
}
```

**Implementation Notes**:
- Decode JWT to get session information
- Remove session from `web_chat_sessions` table
- Remove refresh token from `refresh_tokens` table
- Invalidate JWT (add to blacklist if needed)

## Admin API Routes (for Manual Approval)

### 1. List Pending Registrations

**File**: `/src/app/api/admin/pending-registrations/route.js`
**Endpoint**: `GET /api/admin/pending-registrations`

**Headers**: `Authorization: Bearer <admin-jwt-token>`

**Response**:
```json
{
  "success": true,
  "registrations": [
    {
      "id": "dr-uuid",
      "name": "Dr. John Smith",
      "phone": "+**********",
      "role": "doctor",
      "created_at": "2024-01-15T10:30:00Z",
      "meta": {
        "organization": "General Hospital",
        "license_number": "MD123456",
        "specialization": "Internal Medicine",
        "verification_status": "pending"
      }
    }
  ]
}
```

### 2. Approve/Reject Registration

**File**: `/src/app/api/admin/review-registration/route.js`
**Endpoint**: `POST /api/admin/review-registration`

**Headers**: `Authorization: Bearer <admin-jwt-token>`

**Request Body**:
```json
{
  "user_id": "dr-uuid",
  "action": "approve", // or "reject"
  "notes": "Verified license with state board"
}
```

**Response**:
```json
{
  "success": true,
  "message": "Registration approved successfully"
}
```


## Error Handling

### Standard Error Response Format

```json
{
  "success": false,
  "error": "Error message",
  "code": "ERROR_CODE",
  "details": {} // optional additional details
}
```

### Common Error Codes

- `INVALID_PHONE`: Phone number format is invalid
- `OTP_EXPIRED`: OTP has expired
- `OTP_INVALID`: OTP code is incorrect
- `ACCOUNT_PENDING`: Account is pending approval
- `ACCOUNT_REJECTED`: Account registration was rejected
- `UNAUTHORIZED`: Invalid or expired JWT token
- `FORBIDDEN`: Insufficient permissions

## Security Considerations

### Authentication
- Use JWT access tokens with 1 hour expiration
- Use refresh tokens with 7 day expiration
- Store JWT secrets securely (environment variables)
- Implement proper session management and cleanup

### Phone Verification
- Limit OTP requests per phone number (3 per hour)
- Use secure random generation for OTP codes
- Implement exponential backoff for failed attempts
- Clean up expired verification tokens

### Data Protection
- Hash sensitive data where appropriate
- Use HTTPS for all endpoints
- Implement rate limiting
- Log security events for monitoring
- Validate all input data

## Rate Limiting

### OTP Requests
- 3 requests per phone number per hour
- 10 requests per IP address per hour

### API Calls
- 1000 requests per user per hour
- 100 requests per IP per minute

## Environment Variables

```bash
# Database
DATABASE_URL=postgresql://user:password@localhost:5432/doctor_dashboard
DATABASE_SSL=true

# JWT
JWT_ACCESS_SECRET=your-super-secret-access-jwt-key
JWT_REFRESH_SECRET=your-super-secret-refresh-jwt-key
JWT_ACCESS_EXPIRES_IN=1h
JWT_REFRESH_EXPIRES_IN=7d

# SMS Provider (Twilio example)
TWILIO_ACCOUNT_SID=your-twilio-sid
TWILIO_AUTH_TOKEN=your-twilio-token
TWILIO_PHONE_NUMBER=+**********

# Admin
ADMIN_EMAIL=<EMAIL>
ADMIN_NOTIFICATION_WEBHOOK=https://your-slack-webhook-url
```


## Next.js Specific Implementation Notes

### Database Connection
- Use a PostgreSQL client library (pg, @vercel/postgres, or Prisma)
- Implement connection pooling for production
- Store database URL in environment variables

### Session Management
- Use Next.js built-in session handling or implement JWT tokens
- Store sessions securely (encrypted cookies or secure storage)
- Implement proper session expiration and refresh

### API Route Structure
```
src/app/api/
├── auth/
│   ├── request-otp/
│   │   └── route.js
│   ├── verify-and-register/
│   │   └── route.js
│   ├── login/
│   │   └── route.js
│   ├── verify-login/
│   │   └── route.js
│   ├── refresh/
│   │   └── route.js
│   ├── me/
│   │   └── route.js
│   └── logout/
│       └── route.js
└── admin/
    ├── pending-registrations/
    │   └── route.js
    └── review-registration/
        └── route.js
```

### Error Handling
- Use Next.js error handling patterns
- Return proper HTTP status codes
- Implement consistent error response format
- Log errors for debugging

### Environment Configuration
- Use `.env.local` for development
- Secure environment variables in production
- Validate required environment variables on startup

### Middleware
- Implement authentication middleware for protected routes
- Add rate limiting middleware
- CORS configuration for API routes

## Future Enhancements

### Medical License Verification
- Integration with state medical board APIs when available
- Automated license verification where possible
- Periodic re-verification of active licenses

### Analytics and Monitoring
- User activity tracking
- API performance metrics
- Error rate monitoring
- Usage analytics for admin dashboard

This specification provides a comprehensive foundation for implementing the Next.js backend API routes required for the Doctor Dashboard application. The authentication system ensures proper verification of healthcare professionals while the messaging integration with Gatekeeper handles AI interactions efficiently.