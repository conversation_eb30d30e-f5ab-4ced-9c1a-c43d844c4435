#!/usr/bin/env python3
"""
Test Research API - Automatically uses .env file
"""

import requests
import time
from pathlib import Path
from dotenv import load_dotenv
import os

# Load .env file
env_path = Path(__file__).parent / '.env'
load_dotenv(env_path)

def test_research_api():
    """Test the research API"""
    
    # Test query
    query = "dosage adjustments of ceftriaxone in patients with severe renal impairment"
    
    print("🏥 Testing Research API")
    print("=" * 50)
    print(f"Query: {query}")
    print(f"Using VectorX Index: {os.getenv('VECTORX_INDEX_NAME')}")
    print()
    
    # Check if service is running
    try:
        health = requests.get("http://localhost:8000/health", timeout=2)
        if health.status_code == 200:
            print("✅ Service is running")
        else:
            print("❌ Service returned non-200 status")
            return
    except:
        print("❌ Service not running! Start it with:")
        print("   cd /Users/<USER>/doctor-dashboard/doctor-dashboard")
        print("   bash run_research_api.sh")
        return
    
    # Make research request
    print("\n🔍 Sending research query...")
    start_time = time.time()
    
    try:
        response = requests.post(
            "http://localhost:8000/api/research",
            json={
                "query": query,
                "top_k": 10,
                "similarity_threshold": 0.7
            },
            timeout=120
        )
        
        elapsed = time.time() - start_time
        
        if response.status_code == 200:
            data = response.json()
            
            print(f"✅ Request successful in {elapsed:.2f}s!")
            print(f"\n📊 Results:")
            print(f"  - Sources found: {len(data.get('sources', []))}")
            print(f"  - Processing time: {data.get('processing_time', 0):.2f}s")
            
            # Show summary
            if data.get('summary'):
                print(f"\n📝 Summary:")
                print("-" * 50)
                print(data['summary'][:500] + "..." if len(data['summary']) > 500 else data['summary'])
            
            # Show top 3 sources
            if data.get('sources'):
                print(f"\n📚 Top Sources:")
                print("-" * 50)
                for i, source in enumerate(data['sources'][:3], 1):
                    print(f"\n{i}. {source.get('title', 'No title')}")
                    print(f"   PMID: {source.get('pmid', 'N/A')}")
                    print(f"   Score: {source.get('score', 0):.3f}")
                    if source.get('content'):
                        print(f"   Preview: {source['content'][:200]}...")
            
        else:
            print(f"❌ Request failed with status {response.status_code}")
            print(f"Error: {response.text}")
            
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    test_research_api()