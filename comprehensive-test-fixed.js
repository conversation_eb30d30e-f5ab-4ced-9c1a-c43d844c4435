const puppeteer = require('puppeteer');
const fs = require('fs');

// Ensure directories exist
const ensureDir = (dir) => {
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
  }
};

ensureDir('test-results/screenshots');
ensureDir('test-results/reports');

// Test configuration
const CONFIG = {
  baseUrl: 'http://localhost:3000',
  testPhone: '+919819304846',
  testOTP: '123456',
  headless: process.env.TEST_HEADLESS !== 'false',
  defaultTimeout: 30000
};

// Helper to take screenshot
const screenshot = async (page, name) => {
  const path = `test-results/screenshots/${name}.png`;
  await page.screenshot({ path, fullPage: true });
  console.log(`   📸 Screenshot: ${name}`);
};

// Main test
(async () => {
  console.log('🚀 Comprehensive Doctor Dashboard Test');
  console.log('Testing all features and interactions\n');
  
  const browser = await puppeteer.launch({
    headless: CONFIG.headless,
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });
  
  const page = await browser.newPage();
  await page.setViewport({ width: 1280, height: 800 });
  page.setDefaultTimeout(CONFIG.defaultTimeout);
  
  // Monitor console
  page.on('console', msg => {
    if (msg.type() === 'error') {
      console.log('   🔴 Console error:', msg.text());
    }
  });
  
  const results = {
    passed: 0,
    failed: 0,
    tests: []
  };
  
  const runTest = async (name, testFn) => {
    console.log(`\n▶️  ${name}`);
    try {
      await testFn();
      console.log(`   ✅ PASSED`);
      results.passed++;
      results.tests.push({ name, status: 'passed' });
    } catch (error) {
      console.log(`   ❌ FAILED: ${error.message}`);
      results.failed++;
      results.tests.push({ name, status: 'failed', error: error.message });
      await screenshot(page, `error-${name.replace(/\s+/g, '-').toLowerCase()}`);
    }
  };
  
  try {
    // AUTHENTICATION TESTS
    console.log('============================================================');
    console.log('📋 AUTHENTICATION TESTS');
    console.log('============================================================');
    
    await runTest('Navigate to login page', async () => {
      await page.goto(CONFIG.baseUrl);
      await page.waitForSelector('input[placeholder*="98193"]');
    });
    
    await runTest('Enter phone number', async () => {
      await page.type('input[placeholder*="98193"]', CONFIG.testPhone);
      await screenshot(page, 'phone-entered');
    });
    
    await runTest('Request OTP', async () => {
      await page.click('button[type="submit"]');
      await page.waitForSelector('input[maxlength="6"]', { timeout: 10000 });
      await screenshot(page, 'otp-screen');
    });
    
    await runTest('Enter and submit OTP', async () => {
      await page.type('input[maxlength="6"]', CONFIG.testOTP);
      await screenshot(page, 'otp-entered');
      
      // Find and click verify button
      const buttons = await page.$$('button');
      for (const button of buttons) {
        const text = await page.evaluate(el => el.textContent, button);
        if (text.includes('Verify') || text.includes('Login')) {
          await button.click();
          break;
        }
      }
      
      // Wait for dashboard
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      // Verify we're logged in
      const hasTextarea = await page.$('textarea');
      if (!hasTextarea) throw new Error('Dashboard not loaded');
      
      await screenshot(page, 'dashboard-loaded');
    });
    
    // DASHBOARD NAVIGATION TESTS
    console.log('\n============================================================');
    console.log('📋 DASHBOARD NAVIGATION TESTS');
    console.log('============================================================');
    
    await runTest('Check dashboard layout', async () => {
      // Check for key elements
      const elements = {
        'New Conversation button': await page.$('button:has-text("New Conversation"), button[class*="new"]') || 
                                  await page.evaluateHandle(() => 
                                    Array.from(document.querySelectorAll('button')).find(b => 
                                      b.textContent.includes('New Conversation'))),
        'Thread list': await page.evaluate(() => 
          document.body.textContent.includes('Today') || 
          document.body.textContent.includes('Yesterday')),
        'Message input': await page.$('textarea'),
        'Thread type buttons': await page.evaluate(() => {
          const text = document.body.textContent;
          return text.includes('Patient Case') && 
                 text.includes('Research') && 
                 text.includes('Quick Facts');
        })
      };
      
      for (const [name, exists] of Object.entries(elements)) {
        if (!exists) throw new Error(`${name} not found`);
        console.log(`   ✓ ${name} present`);
      }
      
      await screenshot(page, 'dashboard-components');
    });
    
    // THREAD CREATION TESTS
    console.log('\n============================================================');
    console.log('📋 THREAD CREATION TESTS');
    console.log('============================================================');
    
    await runTest('Create new Patient Case thread', async () => {
      // Click Patient Case button
      const patientCaseBtn = await page.evaluateHandle(() => 
        Array.from(document.querySelectorAll('button')).find(b => 
          b.textContent.includes('Patient Case')));
      
      if (patientCaseBtn) {
        await patientCaseBtn.click();
        console.log('   ✓ Clicked Patient Case button');
      }
      
      // Type a message
      const textarea = await page.$('textarea');
      if (!textarea) throw new Error('Textarea not found');
      
      await textarea.click();
      await page.keyboard.type('45-year-old male with Type 2 Diabetes. HbA1c is 8.2%. What treatment options would you recommend?');
      console.log('   ✓ Typed patient case');
      
      await screenshot(page, 'patient-case-typed');
    });
    
    await runTest('Send message and wait for response', async () => {
      // Send message (Enter key or send button)
      await page.keyboard.press('Enter');
      console.log('   ✓ Message sent');
      
      // Wait for AI response
      await new Promise(resolve => setTimeout(resolve, 5000));
      
      // Check if response received
      const messages = await page.evaluate(() => {
        return document.querySelectorAll('[class*="message"], [class*="Message"], p').length;
      });
      
      if (messages < 2) throw new Error('AI response not received');
      console.log('   ✓ AI response received');
      
      await screenshot(page, 'ai-response');
    });
    
    await runTest('Send follow-up message', async () => {
      const textarea = await page.$('textarea');
      await textarea.click();
      await textarea.click({ clickCount: 3 }); // Select all
      await page.keyboard.type('What about lifestyle modifications?');
      await page.keyboard.press('Enter');
      
      await new Promise(resolve => setTimeout(resolve, 3000));
      await screenshot(page, 'follow-up-message');
    });
    
    // THREAD MANAGEMENT TESTS
    console.log('\n============================================================');
    console.log('📋 THREAD MANAGEMENT TESTS');
    console.log('============================================================');
    
    await runTest('Create new conversation', async () => {
      // Click New Conversation button
      const newConvBtn = await page.evaluateHandle(() => 
        Array.from(document.querySelectorAll('button')).find(b => 
          b.textContent.includes('New Conversation')));
      
      if (!newConvBtn) throw new Error('New Conversation button not found');
      
      await newConvBtn.click();
      console.log('   ✓ Clicked New Conversation');
      
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Verify new conversation started
      const textarea = await page.$('textarea');
      const value = await page.evaluate(el => el.value, textarea);
      if (value !== '') throw new Error('Textarea not cleared');
      
      await screenshot(page, 'new-conversation');
    });
    
    await runTest('Switch to Research type', async () => {
      // Click Research button
      const researchBtn = await page.evaluateHandle(() => 
        Array.from(document.querySelectorAll('button')).find(b => 
          b.textContent === 'Research'));
      
      if (!researchBtn) throw new Error('Research button not found');
      
      await researchBtn.click();
      console.log('   ✓ Switched to Research');
      
      // Send research query
      const textarea = await page.$('textarea');
      await textarea.click();
      await page.keyboard.type('Latest clinical trials for SGLT2 inhibitors in heart failure');
      await page.keyboard.press('Enter');
      
      await new Promise(resolve => setTimeout(resolve, 3000));
      await screenshot(page, 'research-query');
    });
    
    await runTest('Switch to Quick Facts type', async () => {
      // Click Quick Facts button
      const quickFactsBtn = await page.evaluateHandle(() => 
        Array.from(document.querySelectorAll('button')).find(b => 
          b.textContent === 'Quick Facts'));
      
      if (!quickFactsBtn) throw new Error('Quick Facts button not found');
      
      await quickFactsBtn.click();
      console.log('   ✓ Switched to Quick Facts');
      
      // Send quick fact query
      const textarea = await page.$('textarea');
      await textarea.click();
      await textarea.click({ clickCount: 3 });
      await page.keyboard.type('Normal range for HbA1c?');
      await page.keyboard.press('Enter');
      
      await new Promise(resolve => setTimeout(resolve, 2000));
      await screenshot(page, 'quick-facts-query');
    });
    
    await runTest('Navigate between threads', async () => {
      // Click on a thread in the sidebar
      const threads = await page.$$('[class*="thread"], [class*="Thread"], div:has(> span)');
      
      if (threads.length > 1) {
        await threads[1].click();
        console.log('   ✓ Clicked on thread');
        await new Promise(resolve => setTimeout(resolve, 1000));
        await screenshot(page, 'thread-navigation');
      } else {
        console.log('   ⚠️  Not enough threads to test navigation');
      }
    });
    
    // FILE ATTACHMENT TEST
    console.log('\n============================================================');
    console.log('📋 FILE ATTACHMENT TEST');
    console.log('============================================================');
    
    await runTest('Check file attachment button', async () => {
      // Look for file attachment button/icon
      const attachBtn = await page.evaluateHandle(() => {
        // Look for paperclip icon or file input
        return Array.from(document.querySelectorAll('button, label')).find(el => 
          el.querySelector('svg') || 
          el.querySelector('input[type="file"]') ||
          el.getAttribute('aria-label')?.includes('attach')
        );
      });
      
      if (!attachBtn) {
        console.log('   ⚠️  File attachment button not found');
      } else {
        console.log('   ✓ File attachment feature available');
      }
    });
    
    // USER MENU TEST
    console.log('\n============================================================');
    console.log('📋 USER MENU TEST');
    console.log('============================================================');
    
    await runTest('Access user menu and logout', async () => {
      // Look for user avatar or menu
      const userMenu = await page.evaluateHandle(() => {
        return Array.from(document.querySelectorAll('button, div')).find(el => 
          el.textContent.includes('Dr.') || 
          el.querySelector('img[alt*="avatar"]') ||
          el.textContent.includes('Logout')
        );
      });
      
      if (userMenu) {
        await userMenu.click();
        console.log('   ✓ Opened user menu');
        await new Promise(resolve => setTimeout(resolve, 500));
        
        // Look for logout
        const logoutBtn = await page.evaluateHandle(() => 
          Array.from(document.querySelectorAll('button, a')).find(el => 
            el.textContent.includes('Logout') || el.textContent.includes('Sign out')
          ));
        
        if (logoutBtn) {
          await screenshot(page, 'user-menu');
          await logoutBtn.click();
          console.log('   ✓ Clicked logout');
          
          // Wait for redirect
          await new Promise(resolve => setTimeout(resolve, 2000));
          
          // Check if we're back at login
          const loginInput = await page.$('input[placeholder*="98193"]');
          if (!loginInput) throw new Error('Did not return to login page');
          
          console.log('   ✓ Successfully logged out');
          await screenshot(page, 'logged-out');
        }
      } else {
        console.log('   ⚠️  User menu not found');
      }
    });
    
  } catch (criticalError) {
    console.error('\n💥 Critical error:', criticalError.message);
    await screenshot(page, 'critical-error');
  } finally {
    // Print summary
    console.log('\n============================================================');
    console.log('📊 TEST SUMMARY');
    console.log('============================================================');
    console.log(`Total Tests: ${results.passed + results.failed}`);
    console.log(`✅ Passed: ${results.passed}`);
    console.log(`❌ Failed: ${results.failed}`);
    console.log(`📈 Success Rate: ${((results.passed / (results.passed + results.failed)) * 100).toFixed(1)}%`);
    
    if (results.failed > 0) {
      console.log('\n❌ Failed Tests:');
      results.tests.filter(t => t.status === 'failed').forEach(test => {
        console.log(`   - ${test.name}: ${test.error}`);
      });
    }
    
    // Save report
    const report = {
      timestamp: new Date().toISOString(),
      config: CONFIG,
      results: results,
      summary: {
        total: results.passed + results.failed,
        passed: results.passed,
        failed: results.failed,
        successRate: ((results.passed / (results.passed + results.failed)) * 100).toFixed(1) + '%'
      }
    };
    
    const reportPath = `test-results/reports/comprehensive-test-${Date.now()}.json`;
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
    console.log(`\n📄 Report saved to: ${reportPath}`);
    
    console.log('\n✨ Test completed!');
    await browser.close();
  }
})();