const axios = require('axios');

// Test GET Endpoints without JWT
async function testGetEndpoints() {
  console.log('\n🔍 TEST 8: Test GET Endpoints (Without JWT)');
  console.log('=' + '='.repeat(60));
  console.log('Purpose: Demonstrate JWT requirement for data retrieval');
  console.log('Expected: 401 Unauthorized errors');
  console.log('=' + '='.repeat(60));
  
  const baseUrl = 'https://gatekeeper-staging.getbeyondhealth.com';
  
  // Test 1: Get All Dialogues
  console.log('\n📋 Test 8.1: Get All Dialogues');
  console.log('URL: GET /user/practitioner-dashboard/get-chats-by-dialogueId?limit=100&dialogue_id=');
  console.log('Purpose: Retrieve all conversations for the user');
  
  try {
    const response = await axios.get(
      `${baseUrl}/user/practitioner-dashboard/get-chats-by-dialogueId`,
      {
        params: { limit: 100, dialogue_id: '' },
        headers: { 'Content-Type': 'application/json' }
      }
    );
    console.log('✅ Unexpected Success:', response.data);
  } catch (error) {
    console.log('❌ Expected Error:', error.response?.status, error.response?.statusText);
    console.log('Response:', JSON.stringify(error.response?.data, null, 2));
  }
  
  // Test 2: Get Messages for Specific Dialogue
  console.log('\n💬 Test 8.2: Get Messages by Dialogue ID');
  console.log('URL: GET /user/practitioner-dashboard/get-chats-by-dialogueId?limit=30&dialogue_id=test-id');
  console.log('Purpose: Retrieve message history for a specific conversation');
  
  try {
    const response = await axios.get(
      `${baseUrl}/user/practitioner-dashboard/get-chats-by-dialogueId`,
      {
        params: { limit: 30, dialogue_id: 'test-dialogue-id' },
        headers: { 'Content-Type': 'application/json' }
      }
    );
    console.log('✅ Unexpected Success:', response.data);
  } catch (error) {
    console.log('❌ Expected Error:', error.response?.status, error.response?.statusText);
    console.log('Response:', JSON.stringify(error.response?.data, null, 2));
  }
  
  // Test 3: With Invalid JWT
  console.log('\n🔐 Test 8.3: With Invalid JWT Token');
  console.log('Using malformed JWT to show error details');
  
  try {
    const response = await axios.get(
      `${baseUrl}/user/practitioner-dashboard/get-chats-by-dialogueId`,
      {
        params: { limit: 10, dialogue_id: '' },
        headers: { 
          'Content-Type': 'application/json',
          'Authorization': 'Bearer invalid-jwt-token'
        }
      }
    );
    console.log('✅ Unexpected Success:', response.data);
  } catch (error) {
    console.log('❌ Expected Error:', error.response?.status, error.response?.statusText);
    console.log('Response:', JSON.stringify(error.response?.data, null, 2));
  }
  
  console.log('\n📊 Summary:');
  console.log('- All GET endpoints require valid JWT authentication');
  console.log('- Without JWT: 401 Unauthorized');
  console.log('- With invalid JWT: 401 with "jwt malformed" error');
  console.log('- To access these endpoints:');
  console.log('  1. Request OTP → 2. Verify OTP → 3. Use returned JWT');
}

testGetEndpoints();