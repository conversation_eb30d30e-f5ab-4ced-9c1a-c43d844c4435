const puppeteer = require('puppeteer');
const fs = require('fs');
const path = require('path');

// Ensure directories exist
const ensureDir = (dir) => {
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
  }
};

ensureDir('test-results/screenshots');
ensureDir('test-results/reports');

(async () => {
  console.log('🚀 Comprehensive Doctor Dashboard Demo\n');
  console.log('This demo showcases the full E2E testing capabilities');
  console.log('=' .repeat(60) + '\n');
  
  const browser = await puppeteer.launch({
    headless: process.env.TEST_HEADLESS !== 'false',
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });
  
  const page = await browser.newPage();
  await page.setViewport({ width: 1280, height: 800 });
  
  // Test results collector
  const testResults = {
    timestamp: new Date().toISOString(),
    tests: [],
    totalPassed: 0,
    totalFailed: 0
  };
  
  // Helper function to run a test
  const runTest = async (name, testFn) => {
    console.log(`\n📋 ${name}`);
    const startTime = Date.now();
    try {
      await testFn();
      console.log(`   ✅ Passed`);
      testResults.tests.push({ name, status: 'passed', duration: Date.now() - startTime });
      testResults.totalPassed++;
    } catch (error) {
      console.log(`   ❌ Failed: ${error.message}`);
      await page.screenshot({ 
        path: `test-results/screenshots/error-${name.replace(/\s+/g, '-')}.png` 
      });
      testResults.tests.push({ 
        name, 
        status: 'failed', 
        error: error.message,
        duration: Date.now() - startTime 
      });
      testResults.totalFailed++;
    }
  };
  
  try {
    // Phase 1: Authentication Flow
    console.log('\n🔐 PHASE 1: Authentication Testing');
    console.log('-'.repeat(40));
    
    await runTest('Navigate to home page', async () => {
      await page.goto('http://localhost:3000');
      const title = await page.title();
      if (!title.includes('Doctor Dashboard')) {
        throw new Error('Invalid page title');
      }
    });
    
    await runTest('Enter phone number', async () => {
      const phoneInput = await page.waitForSelector('input[placeholder*="98193"]');
      await phoneInput.click({ clickCount: 3 });
      await phoneInput.type('+919819304846');
      await page.screenshot({ path: 'test-results/screenshots/phone-entered.png' });
    });
    
    await runTest('Submit OTP request', async () => {
      // Wait for button to be enabled
      await page.waitForFunction(() => {
        const btn = document.querySelector('button[type="submit"]');
        return btn && !btn.disabled;
      });
      
      // Click submit
      await page.click('button[type="submit"]');
      
      // Wait for OTP field or error
      await page.waitForFunction(() => {
        const otpField = document.querySelector('input[maxlength="6"]');
        const errorMsg = document.querySelector('[class*="error"]');
        return otpField || errorMsg;
      }, { timeout: 10000 });
      
      const hasOtpField = await page.$('input[maxlength="6"]');
      const errorMsg = await page.evaluate(() => {
        const error = document.querySelector('[class*="error"]');
        return error ? error.textContent : null;
      });
      
      if (errorMsg && errorMsg.includes('rate limit')) {
        throw new Error('Rate limited - please wait before testing');
      }
      
      if (!hasOtpField) {
        throw new Error('OTP field not displayed');
      }
      
      await page.screenshot({ path: 'test-results/screenshots/otp-screen.png' });
    });
    
    // Phase 2: UI Component Testing
    console.log('\n🎨 PHASE 2: UI Component Testing');
    console.log('-'.repeat(40));
    
    await runTest('Check OTP input formatting', async () => {
      const otpInputs = await page.$$('input[maxlength="1"]');
      if (otpInputs.length === 6) {
        console.log('   ℹ️  Individual OTP inputs detected');
      } else {
        const singleInput = await page.$('input[maxlength="6"]');
        if (singleInput) {
          console.log('   ℹ️  Single OTP input detected');
        }
      }
    });
    
    await runTest('Test "Use Different Number" link', async () => {
      const diffNumberLink = await page.$('button:has-text("Use Different Number"), a:has-text("Use Different Number")');
      if (!diffNumberLink) {
        // Try evaluating in page context
        const hasLink = await page.evaluate(() => {
          return Array.from(document.querySelectorAll('button, a')).some(el => 
            el.textContent.includes('Use Different Number')
          );
        });
        if (!hasLink) {
          throw new Error('Use Different Number link not found');
        }
      }
    });
    
    // Phase 3: Error Handling
    console.log('\n⚠️  PHASE 3: Error Handling Testing');
    console.log('-'.repeat(40));
    
    await runTest('Navigate back to login', async () => {
      await page.goto('http://localhost:3000');
      await page.waitForSelector('input[placeholder*="98193"]');
    });
    
    await runTest('Test invalid phone validation', async () => {
      const phoneInput = await page.$('input[placeholder*="98193"]');
      await phoneInput.click({ clickCount: 3 });
      await phoneInput.type('123'); // Invalid phone
      
      const submitBtn = await page.$('button[type="submit"]');
      const isDisabled = await page.evaluate(btn => btn.disabled, submitBtn);
      
      console.log(`   ℹ️  Submit button disabled: ${isDisabled}`);
    });
    
    // Phase 4: Performance Testing
    console.log('\n⚡ PHASE 4: Performance Testing');
    console.log('-'.repeat(40));
    
    await runTest('Measure page load performance', async () => {
      const navigationStart = Date.now();
      await page.goto('http://localhost:3000');
      await page.waitForSelector('h1');
      const loadTime = Date.now() - navigationStart;
      
      console.log(`   ℹ️  Page load time: ${loadTime}ms`);
      
      const metrics = await page.metrics();
      console.log(`   ℹ️  JS Heap Size: ${(metrics.JSHeapUsedSize / 1048576).toFixed(2)}MB`);
      console.log(`   ℹ️  DOM Nodes: ${metrics.Nodes}`);
    });
    
    await runTest('Test responsive design', async () => {
      const viewports = [
        { name: 'mobile', width: 375, height: 667 },
        { name: 'tablet', width: 768, height: 1024 },
        { name: 'desktop', width: 1920, height: 1080 }
      ];
      
      for (const viewport of viewports) {
        await page.setViewport({ width: viewport.width, height: viewport.height });
        await page.screenshot({ 
          path: `test-results/screenshots/responsive-${viewport.name}.png` 
        });
        console.log(`   ℹ️  ${viewport.name} screenshot captured`);
      }
    });
    
    // Phase 5: Accessibility Testing
    console.log('\n♿ PHASE 5: Accessibility Testing');
    console.log('-'.repeat(40));
    
    await runTest('Check ARIA labels', async () => {
      const ariaElements = await page.evaluate(() => {
        const elements = document.querySelectorAll('[aria-label], [role]');
        return elements.length;
      });
      console.log(`   ℹ️  Found ${ariaElements} elements with ARIA attributes`);
    });
    
    await runTest('Check form labels', async () => {
      const labeledInputs = await page.evaluate(() => {
        const inputs = document.querySelectorAll('input');
        let labeled = 0;
        inputs.forEach(input => {
          if (input.id && document.querySelector(`label[for="${input.id}"]`)) {
            labeled++;
          } else if (input.getAttribute('aria-label')) {
            labeled++;
          }
        });
        return { total: inputs.length, labeled };
      });
      console.log(`   ℹ️  ${labeledInputs.labeled}/${labeledInputs.total} inputs properly labeled`);
    });
    
  } catch (error) {
    console.error('\n💥 Unexpected error:', error.message);
  } finally {
    // Generate summary report
    console.log('\n' + '='.repeat(60));
    console.log('📊 TEST SUMMARY');
    console.log('='.repeat(60));
    console.log(`Total Tests: ${testResults.tests.length}`);
    console.log(`✅ Passed: ${testResults.totalPassed}`);
    console.log(`❌ Failed: ${testResults.totalFailed}`);
    console.log(`📈 Success Rate: ${(testResults.totalPassed / testResults.tests.length * 100).toFixed(1)}%`);
    
    // Save detailed report
    const reportPath = `test-results/reports/demo-report-${Date.now()}.json`;
    fs.writeFileSync(reportPath, JSON.stringify(testResults, null, 2));
    console.log(`\n📄 Detailed report saved to: ${reportPath}`);
    
    // Show failed tests
    if (testResults.totalFailed > 0) {
      console.log('\n❌ Failed Tests:');
      testResults.tests.filter(t => t.status === 'failed').forEach(test => {
        console.log(`   - ${test.name}: ${test.error}`);
      });
    }
    
    console.log('\n✨ Demo completed!');
    console.log('\n📁 Artifacts:');
    console.log('   - Screenshots: test-results/screenshots/');
    console.log('   - Reports: test-results/reports/');
    
    await browser.close();
  }
})();